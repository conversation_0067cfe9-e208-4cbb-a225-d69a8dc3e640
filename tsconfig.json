{"compilerOptions": {"module": "commonjs", "declaration": true, "esModuleInterop": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "paths": {"@src/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["express", "multer", "node"]}, "exclude": ["node_modules", "dist"]}