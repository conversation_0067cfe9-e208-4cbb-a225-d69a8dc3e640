FROM registry-vpc.cn-qingdao.aliyuncs.com/yqz/node:16-alpine
LABEL app-name="reportService"
# Create app directory
WORKDIR /app

# Install app dependencies
COPY ./.npmrc ./
COPY ./package*.json ./
# 跳过ssl验证
RUN npm config set "strict-ssl" false -g \
    && npm install --verbose 
# RUN npm config set registry https://registry.npm.taobao.org \
#  &&  npm i --verbose 

# build
COPY . .
RUN npm run build      
    

CMD [ "npm", "run", "start" ]
