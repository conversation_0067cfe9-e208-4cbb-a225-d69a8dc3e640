{"defaultSeverity": "error", "extends": ["tslint:recommended"], "jsRules": {"no-unused-expression": true}, "rules": {"quotemark": [true, "double"], "member-access": [false], "ordered-imports": [false], "max-line-length": [true, 150], "member-ordering": [false], "interface-name": [false], "arrow-parens": false, "object-literal-sort-keys": false, "no-console": false, "indent": [true, "spaces", 2], "trailing-comma": [false]}, "rulesDirectory": []}