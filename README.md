# 初始化环境配置.env

.env文件不需要添加到git代码管理，在构建发布的时候，通过jenkins配置，本地开发，用如下的环境配置

```
ln -s env.dev.env .env
```

## .env文件配置
需要替换***<username>***和***<password>***成自己的账号
```
YQZ_TARGET_ENV=dev
TZ=Asia/Shanghai
YQZ_SCREEN_CAPTURE_URL=https://***********/yqz_com_owner_h5_dev/app/#/screen-shot
YQZ_MAP_API_KEY=T3OBZ-OOKRP-2MTDI-LUDQ4-XGXOZ-FQFJN
YQZ_SERVICE_NAME=report-service
YQZ_SERVICE_USER={"userType":"yqz-service","username":"report-service"}
# database
TYPEORM_CONNECTION=mysql
TYPEORM_HOST=rds.timontech.cn
TYPEORM_USERNAME=<username>
TYPEORM_PASSWORD=<password>
TYPEORM_DATABASE=bgw-dev
TYPEORM_PORT=3306
TYPEORM_SYNCHRONIZE=false
TYPEORM_LOGGING=false
TYPEORM_ENTITIES=dist/**/*.entity.js
TYPEORM_CONNECTION_LIMIT=30
TYPEORM_CHARSET=utf8mb4
# constatns
YQZ_PORT=3000
YQZ_ERROR_CODE=201
YQZ_OK_CODE=200
TRACER_SERVICE_NAME=report-service-dev
COLLECTOR_END_POINT=http://tracing-analysis-dc-sh.aliyuncs.com/adapt_c6a30eb76x@58ab9008a817119_c6a30eb76x@53df7ad2afe8301/api/traces
## redis settings
YQZ_REDIS_HOST=gitlab.timontech.cn
YQZ_REDIS_PORT=3680
YQZ_REDIS_PWD=tP1dFsbCoHAUdcSv
YQZ_REDIS_DB=1
YQZ_SERVICE_USER=dev
# api
YQZ_BINGOHOME_API=https://cs.1qizhuang.com/bingohome
YQZ_GIF_SERVICE_API=https://cs.1qizhuang.com/api/gif-service
YQZ_MEDIA_API=http://cs.1qizhuang.com/api/media
YQZ_MP_PLATFORM_API=http://cs.1qizhuang.com/api/mp-platform
YQZ_AGILE_SERVICE_API=http://cs.1qizhuang.com/api/agile
# cron
# 生成日报
CRON_PROJECT_REPORT_GENERATE=0 0 2 * * *||0 40 4 * * *
# 公司设备
CRON_COMPANY_DEVICE_USAGE=0 0 14 * * *||0 30 14 * * *
# 设备每日切片
CRON_ETL_DIM_DEVICE=0 30 1 * * *||0 35 1 * * *
# 部门每日切片
CRON_ETL_DIM_DEPARTMENT=0 10 1 * * *||0 10 3 * * *
# 无人脸识别工地更新
CRON_ETL_PROJECT_NO_FACESIGN_EVENT=0 15 1 * * *||0 20 1 * * *
# 节点过期事件更新
CRON_ETL_NODE_OVERDUE_EVENT=0 30 1 * * *||0 50 1 * * *

#mongodb配置
YQZ_MONGO_DB_CONN_STRING=mongodb://root:<EMAIL>:3717,dds-m5ec68b66a7564e42119-pub.mongodb.rds.aliyuncs.com:3717/admin?replicaSet=mgset-66257873
YQZ_MONGO_DB_BGW=camera_dev
```