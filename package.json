{"name": "report-service", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "scripts": {"typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "format": "prettier --write \"src/**/*.ts\"", "lint": "tslint --fix -p tsconfig.json -c tslint.json", "build": "tsc -p tsconfig.build.json", "start-ts": "ts-node -r tsconfig-paths/register src/main.ts", "test": "ts-node -r tsconfig-paths/register -r dotenv/config test/test.ts", "start": "node -r dotenv/config dist/main.js", "dev": "cross-env NODE_ENV=dev tsc-watch --onSuccess \"nodemon\"", "prd": "cross-env NODE_ENV=prd tsc-watch --onSuccess \"nodemon\""}, "dependencies": {"@chankamlam/nest-jaeger": "^1.0.16", "@golevelup/nestjs-rabbitmq": "^3.3.0", "@nestjs/common": "^9.1.4", "@nestjs/core": "^9.1.4", "@nestjs/jwt": "^9.0.0", "@nestjs/microservices": "^9.1.4", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.1.4", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^6.1.2", "@nestjs/typeorm": "^9.0.1", "@types/multer": "^1.4.12", "@yqz/base": "0.0.5", "@yqz/nest": "^0.0.333", "axios": "^1.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "connect-timeout": "^1.9.0", "csv-parse": "^5.6.0", "date-fns": "^2.29.3", "dotenv": "^16.0.3", "exceljs": "^4.4.0", "form-data": "4.0.0", "iconv-lite": "^0.6.3", "jaeger-client": "^3.19.0", "lodash": "^4.17.21", "md5": "^2.3.0", "module-alias": "^2.2.2", "mongodb": "^5.0.1", "multer": "^1.4.5-lts.2", "mysql": "^2.18.1", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "qs": "^6.11.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.5.7", "source-map-support": "^0.5.21", "typeorm": "^0.3.10", "uuid": "^9.0.0", "webpack-merge": "^5.8.0"}, "devDependencies": {"@nestjs/testing": "^9.1.4", "@types/connect-timeout": "0.0.36", "@types/express": "^4.17.14", "@types/lodash": "^4.14.186", "@types/md5": "^2.3.2", "@types/node": "^16.18.0", "@types/qs": "^6.9.7", "@types/supertest": "^2.0.12", "@types/uuid": "^8.3.4", "@yqz/dev-util": "0.0.17", "colors": "^1.4.0", "concurrently": "^7.4.0", "cross-env": "^7.0.3", "dotenv-flow": "^4.1.0", "nodemon": "^2.0.20", "prettier": "^2.7.1", "shelljs": "^0.8.5", "supertest": "^6.3.0", "ts-node": "10.9.1", "tsc-watch": "^5.0.3", "tsconfig-paths": "4.1.0", "tslint": "5.16.0", "typescript": "^4.8.4", "wait-on": "^6.0.1"}, "_moduleAliases": {"@src": "./dist"}}