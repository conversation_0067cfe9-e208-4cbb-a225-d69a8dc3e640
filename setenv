#!/usr/bin/env node
const shell = require('shelljs')
const colors = require('colors')

console.log()

const targetEnv = process.argv[2]
if(!targetEnv) {
    console.error('missing env')
    process.exit(1)
}

const projectName = require('./package.json').name


console.log(`deleting existing ".env" if any\n`.yellow)
shell.exec('rm -f .env')

console.log(`linking .env\n`.yellow)
shell.exec(`ln -s  ~/yqz/env/${projectName}/${targetEnv}.env .env`)


const envContent = shell.exec('cat .env', {silent:true}).stdout
console.log(envContent.grey)
console.log('done'.green)

// # set local development environment configs
// targetEnv=$1
// echo 
// echo "setting $1"
// rm -f .env
// ln -s  ~/zxrk/env/product/$1.env .env
// echo 'done'
// echo 
console.log()