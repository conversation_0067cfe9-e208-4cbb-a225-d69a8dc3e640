import { ApiProperty } from '@nestjs/swagger';

export namespace ExternalMerchant {
    export class MerchantInfo {
        @ApiProperty({ description: "商户ID" })
        id: string;

        @ApiProperty({ description: "商户名称" })
        name: string;

        @ApiProperty({ description: "商户账号" })
        account: string;
    }
    
    // 带关联公司信息的商户数据
    export class MerchantWithCompanies extends MerchantInfo {
        @ApiProperty({ description: "商户密码" })
        password: string;
        
        @ApiProperty({ description: "创建时间" })
        createTime: string;
        
        @ApiProperty({ description: "关联公司列表" })
        companyIds: string[];

        @ApiProperty({ description: "公司名" })
        companyNames: string;

        @ApiProperty({ description: "工地数量" })
        projectNum: number;
    }
    
    // 获取外部商户列表请求
    export class GetMerchantsRequest {
        @ApiProperty({ description: "商户名称，用于模糊查询", required: false })
        merchantName?: string;

        @ApiProperty({ description: "账号", required: false })
        account?: string;

        @ApiProperty({ description: "公司ID", required: false })
        companyId?: string;
        
        @ApiProperty({ description: "页码", required: false, default: 1 })
        pageNo?: number;
        
        @ApiProperty({ description: "每页条数", required: false, default: 10 })
        pageSize?: number;
    }

    // 公司下拉选项数据
    export class CompanyOption {
        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "公司名称" })
        companyName: string;
    }

    export class CompanyRelation {
        @ApiProperty({ description: "关联ID" })
        id: string;

        @ApiProperty({ description: "商户ID" })
        merchantId: string;

        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "公司名称" })
        companyName: string;
    }

    // 获取公司管理的外部商户列表请求
    export class GetCompanyMerchantsRequest {
        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "商户名称，用于模糊查询", required: false })
        merchantName?: string;

        @ApiProperty({ description: "页码", required: false, default: 1 })
        pageNo?: number;

        @ApiProperty({ description: "每页条数", required: false, default: 10 })
        pageSize?: number;
    }

    // 获取公司管理的外部商户列表响应
    export class MerchantWithProjectCount extends MerchantInfo {
        @ApiProperty({ description: "已授权工地数量" })
        authorizedProjectCount: number;
        
        @ApiProperty({ description: "关联时间" })
        createTime: string;
    }

    export class ProjectRelation {
        @ApiProperty({ description: "关联ID" })
        id: string;

        @ApiProperty({ description: "商户ID" })
        merchantId: string;

        @ApiProperty({ description: "工地ID" })
        projectId: string;

        @ApiProperty({ description: "工地名称/地址" })
        projectName: string;

        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "公司名称" })
        companyName: string;

        @ApiProperty({ description: "授权时间" })
        addedAt: Date;
    }

    // 导入商户请求
    export class ImportMerchantRequest {
        // 移除了csvContent字段，将通过文件上传方式提供
    }

    // 导入商户响应
    export class ImportMerchantResponse {
        @ApiProperty({ description: "是否成功" })
        success: boolean;

        @ApiProperty({ description: "导入记录数" })
        count: number;

        @ApiProperty({ description: "错误信息" })
        errorMessage?: string;
    }

    // 工地授权请求
    export class ProjectAuthorizationRequest {
        @ApiProperty({ description: "商户ID" })
        merchantId: string;

        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "工地ID列表" })
        projectIds: string[];

        @ApiProperty({ description: "是否授权(true:授权, false:解除授权)" })
        authorize: boolean;
    }

    // 获取可授权工地请求
    export class GetAuthorizableProjectsRequest {
        @ApiProperty({ description: "商户ID" })
        merchantId?: string;

        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "工地名称，用于模糊查询", required: false })
        projectName?: string;

        @ApiProperty({ description: "页码", required: false, default: 1 })
        pageNo?: number;

        @ApiProperty({ description: "每页条数", required: false, default: 10 })
        pageSize?: number;
    }

    // 获取已授权工地请求
    export class GetAuthorizedProjectsRequest {
        @ApiProperty({ description: "商户ID" })
        merchantId: string;

        @ApiProperty({ description: "公司ID", required: false })
        companyId?: string;

        @ApiProperty({ description: "工地名称，用于模糊查询", required: false })
        projectName?: string;

        @ApiProperty({ description: "页码", required: false, default: 1 })
        pageNo?: number;

        @ApiProperty({ description: "每页条数", required: false, default: 10 })
        pageSize?: number;
    }

    // 分页响应基类
    export class PageResponse<T> {
        @ApiProperty({ description: "总记录数" })
        total: number;

        @ApiProperty({ description: "数据列表" })
        items: T[];
    }

    // 商户登录请求
    export class MerchantLoginRequest {
        @ApiProperty({ description: "账号" })
        account: string;

        @ApiProperty({ description: "密码" })
        password: string;
    }

    // 商户登录响应
    export class MerchantLoginResponse {
        @ApiProperty({ description: "商户ID" })
        merchantId: string;

        @ApiProperty({ description: "商户名称" })
        merchantName: string;

        @ApiProperty({ description: "商户账号" })
        account: string;
    }

    // 工地信息
    export class ProjectInfo {
        @ApiProperty({ description: "工地ID" })
        projectId: string;

        @ApiProperty({ description: "工地名称/地址" })
        projectAddress: string;

        @ApiProperty({ description: "公司ID" })
        companyId: string;

        @ApiProperty({ description: "公司名称" })
        companyName: string;

        @ApiProperty({ description: "开工时间" })
        startTime?: Date;

        @ApiProperty({ description: "当前阶段" })
        stageName?: string;

        @ApiProperty({ description: "是否授权" })
        isAuthorized?: boolean;

        @ApiProperty({ description: "授权数" })
        authorizedNum?: string;
    }

    // 工地设备信息
    export class ProjectDeviceInfo {
        @ApiProperty({ description: "设备ID" })
        deviceId: string;

        @ApiProperty({ description: "设备名称/位置" })
        deviceRemark: string;

        @ApiProperty({ description: "封面图URL" })
        coverImageUrl?: string;

        @ApiProperty({ description: "是否在线", enum: ["Y", "N"] })
        isOnline: string;
    }

    // 获取工地直播信息响应
    export class ProjectStreamInfo {
        @ApiProperty({ description: "工地信息" })
        project: ProjectInfo;

        @ApiProperty({ description: "设备列表" })
        devices: ProjectDeviceInfo[];
    }
}