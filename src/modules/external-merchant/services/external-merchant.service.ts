import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MyLogger } from '@src/modules/logger/my-logger.service';
import { ExternalMerchantDao } from '../dao/external-merchant.dao';
import { ExternalMerchant } from '../dto/external-merchant.dto';
import { ExternalMerchantEntity } from '@src/modules/report/entity/external-merchant/external-merchant.entity';
import { ExternalMerchantProjectRelationEntity } from '@src/modules/report/entity/external-merchant/external-merchant-project-relation.entity';
import { parse as csvParse } from 'csv-parse/sync';
import * as _ from 'lodash';
import { YqzException } from '@yqz/nest';
import { ExternalProjectDao } from '@src/modules/report/domain/bgw/project/dao/external-project.dao';
import * as DateFns from 'date-fns';
import { parseAndValidateCsvContent } from '../../../util/csv.util';
import { Constants } from '@src/types/common.constant';

@Injectable()
export class ExternalMerchantService {
    private readonly logger = new MyLogger(ExternalMerchantService.name);

    constructor(
        private dataSource: DataSource,
        private merchantDao: ExternalMerchantDao,
        private projectDao: ExternalProjectDao,
    ) { }
    
    // 获取所有未删除的外部商户及其关联公司列表
    async getAllMerchants(
        merchantName?: string,
        account?: string,
        companyId?: string,
        pageNo: number = 1,
        pageSize: number = 10
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.MerchantWithCompanies>> {
        try {
            // 获取所有未删除的商户
            const [merchants, total] = await this.merchantDao.findAllMerchants(merchantName, account, companyId, pageNo, pageSize);
            
            // 获取每个商户的关联公司
            const merchantsWithCompanies: ExternalMerchant.MerchantWithCompanies[] = [];

            const merchantIds = merchants.map(merchant => merchant.id);

            if (_.isEmpty(merchantIds)) {
                return {
                    total,
                    items: merchantsWithCompanies
                };
            }

            const companyRelations = await this.merchantDao.findMerchantCompanyRelationList(merchantIds);
            const companyMap = _.keyBy(companyRelations, 'merchantId');

            merchants.forEach(merchant => {
                const companyRelations = companyMap[merchant.id];
                merchantsWithCompanies.push({
                    id: merchant.id,
                    name: merchant.name,
                    account: merchant.account,
                    password: merchant.password,
                    createTime: merchant.createTime? DateFns.format(merchant.createTime, 'yyyy-MM-dd HH:mm:ss') : '',
                    companyNames: companyRelations?.companyNames || '',
                    projectNum: companyRelations?.projectNum || 0,
                    companyIds: companyRelations?.companyIds || ""
                })
            })
            
            return {
                total,
                items: merchantsWithCompanies
            };
        } catch (error) {
            this.logger.error(`获取外部商户列表失败: ${error.message}`, error.stack);
            throw new YqzException(`获取外部商户列表失败: ${error.message}`);
        }
    }

    // 导入外部商户数据(CSV格式)
    async importMerchants(csvContent: string): Promise<ExternalMerchant.ImportMerchantResponse> {
        try {
            // 1. 定义表头映射（中文->英文）
            const headerMap = {
                '外部商户名称': 'name',
                '账号': 'account',
                '密码': 'password',
                '关联公司id': 'companyIds',
            };

            // 2. 解析、校验并映射CSV内容
            let mappedRecords: any[];
            try {
                mappedRecords = parseAndValidateCsvContent(csvContent, headerMap);
            } catch (err) {
                throw new YqzException({
                    code: "202",
                    msg: err.message || '导入失败'
                });
            }

            // 3. 预处理和验证数据
            const merchantsToProcess: {
                name: string;
                account: string;
                password: string;
                companyIds: string[];
            }[] = [];

            for (const record of mappedRecords) {
                const { name, account, password, companyIds: companyIdsRaw } = record;
                // 分割公司IDs
                const companyIds = (companyIdsRaw || '').split('、').map(id => id.trim()).filter(Boolean);

                // 基本验证
                if (!name || !account || !password) {
                    throw new YqzException({
                        code: "202",
                        msg: `记录存在空值: ${JSON.stringify(record)}`
                    });
                }

                // 验证每个公司ID是否存在
                for (const companyId of companyIds) {
                    const exists = await this.projectDao.checkCompanyExists(companyId);
                    if (!exists) {
                        throw new YqzException({
                            code: "202",
                            msg: `公司ID不存在: ${companyId}`
                        });
                    }
                }

                merchantsToProcess.push({
                    name,
                    account,
                    password,
                    companyIds
                });
            }


            // 检查已存在的账号和商户名
            for (const merchant of merchantsToProcess) {
                // 检查账号是否存在且名称不一致
                const existingMerchantByAccount = await this.merchantDao.findMerchantByAccount(merchant.account);
                if (existingMerchantByAccount && existingMerchantByAccount.name !== merchant.name) {
                    throw new YqzException({
                        code: "202",
                        msg: `已有相同账号 ${merchant.account}`
                    });
                }
                
                // 检查商户名是否已存在但账号不同
                const existingMerchantByName = await this.merchantDao.findMerchantByName(merchant.name);
                if (existingMerchantByName && existingMerchantByName.account !== merchant.account) {
                    throw new YqzException({
                        code: "202",
                        msg: `已有相同名称外部商户 ${merchant.name}`
                    });
                }
            }

            // 开始事务处理
            const queryRunner = this.dataSource.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                for (const merchant of merchantsToProcess) {
                    let merchantEntity: ExternalMerchantEntity;

                    // 检查商户是否已存在
                    const existingMerchant = await this.merchantDao.findMerchantByAccount(merchant.account);

                    if (existingMerchant) {
                        // // 如果已存在，更新密码
                        // await this.merchantDao.updateMerchant(existingMerchant.id, {
                        //     password: merchant.password,
                        //     updateTime: new Date()
                        // });
                        merchantEntity = existingMerchant;
                    } else {
                        // 如果不存在，创建新商户
                        // 再次检查是否存在相同名称的商户
                        const existingMerchantWithSameName = await this.merchantDao.findMerchantByName(merchant.name);
                        if (existingMerchantWithSameName) {
                            throw new YqzException({
                                code: "202",
                                msg: `商户名称 "${merchant.name}" 已存在，对应账号为 "${existingMerchantWithSameName.account}"`
                            });
                        }
                        
                        merchantEntity = await this.merchantDao.createMerchant({
                            name: merchant.name,
                            account: merchant.account,
                            password: merchant.password,
                            createTime: new Date(),
                            updateTime: new Date()
                        });
                    }

                    // 创建新的公司关联（仅添加不存在的关联）
                    for (const companyId of merchant.companyIds) {
                        // 检查关联是否已存在
                        const relationExists = await this.merchantDao.checkCompanyRelationExists(
                            merchantEntity.id,
                            companyId
                        );

                        // 如果关联不存在，则创建
                        if (!relationExists) {
                            await this.merchantDao.createMerchantCompanyRelation({
                                merchantId: merchantEntity.id,
                                companyId: companyId,
                                createTime: new Date(),
                                updateTime: new Date()
                            });
                        }
                    }
                }

                // 提交事务
                await queryRunner.commitTransaction();

                return {
                    success: true,
                    count: merchantsToProcess.length
                };
            } catch (error) {
                // 回滚事务
                await queryRunner.rollbackTransaction();

                this.logger.error(`导入商户数据失败: ${error.message}`, error.stack);
                throw new YqzException({
                    code: "202",
                    msg: `${error.message}`
                });
            } finally {
                // 释放查询运行器
                await queryRunner.release();
            }
        } catch (error) {
            this.logger.error(`解析CSV失败: ${error.message}`, error.stack);
            throw new YqzException({
                code: "202",
                msg: `${error.message}`
            });
        }
    }


    async getAuthorizableProjectsWithStatus(
        merchantId: string,
        companyId: string,
        projectAddress: string | undefined,
        pageNo: number,
        pageSize: number,
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.ProjectInfo>> {
        return await this.getAuthorizableProjects(
            companyId,
            merchantId,
            projectAddress,
            pageNo,
            pageSize,
        );
    }

    // 获取公司可授权的工地列表
    async getAuthorizableProjects(
        companyId: string,
        merchantId: string,
        projectAddress?: string,
        pageNo: number = 1,
        pageSize: number = 10
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.ProjectInfo>&{authorizedNum: number}> {
        // 验证商户是否存在
        const merchant = await this.merchantDao.findMerchantById(merchantId);
        if (!merchant) {
            throw new YqzException('商户不存在');
        }

        // 验证公司是否存在
        const companyExists = await this.projectDao.checkCompanyExists(companyId);
        if (!companyExists) {
            throw new YqzException('公司不存在');
        }

        // 验证商户是否关联该公司
        const companyRelations = await this.merchantDao.findMerchantCompanyRelations(merchantId);
        const hasRelation = companyRelations.some(rel => rel.companyId === companyId);
        if (!hasRelation) {
            throw new YqzException('商户与该公司无关联关系');
        }

        // 获取该商户已授权的工地ID列表
        const [authorizedRelations,authorizedNum] = await this.merchantDao.findMerchantProjectRelations(merchantId, companyId);
        const authorizedProjectIds = new Set(authorizedRelations.map(p => p.projectId));

        // 查询可授权的工地列表(已排除已授权的)
        const projects = await this.projectDao.findCompanyProjects(
            companyId,
            projectAddress,
            pageNo,
            pageSize
        );

        // 检查工地是否绑定设备
        const projectIds = projects.items.map(project => project.projectId);
        const deviceBindingMap = await this.projectDao.checkProjectsBindingDevices(projectIds);

        // 不再过滤未绑定设备的工地，显示所有工地
        return {
            total: projects.total,
            items: projects.items.map(project => ({
                projectId: project.projectId,
                projectAddress: project.projectAddress,
                companyId: project.companyId,
                companyName: project.companyName,
                stageName: project.stageName,
                isAuthorized: authorizedProjectIds.has(project.projectId),
                // 如果工地未绑定设备，设置deviceNum为0
                deviceNum: deviceBindingMap[project.projectId] ? project.deviceNum : 0,
                // 如果工地未绑定设备，设置isOnline为空字符串
                isOnline: deviceBindingMap[project.projectId] ? (project.isOnline&&Number(project.isOnline)==1?"Y":"N") : "",
                coverImageUrl: !!project?.coverImageUrl ? project.coverImageUrl : Constants.DEFAULT_DEVICE_COVER_IMG,
            })),
            authorizedNum: authorizedNum
        };
    }

    // 授权/取消授权工地
    async authorizeProjects(
        merchantId: string,
        companyId: string,
        projectIds: string[],
        authorize: boolean
    ): Promise<boolean> {
        if (!merchantId || !companyId || !projectIds.length) {
            throw new YqzException('参数不完整');
        }

        // 验证商户是否存在
        const merchant = await this.merchantDao.findMerchantById(merchantId);
        if (!merchant) {
            throw new YqzException('商户不存在');
        }

        // 验证公司是否存在
        const companyExists = await this.projectDao.checkCompanyExists(companyId);
        if (!companyExists) {
            throw new YqzException('公司不存在');
        }

        // 验证商户是否关联该公司
        const companyRelations = await this.merchantDao.findMerchantCompanyRelations(merchantId);
        const hasRelation = companyRelations.some(rel => rel.companyId === companyId);
        if (!hasRelation) {
            throw new YqzException('商户与该公司无关联关系');
        }

        // 验证每个工地是否存在且属于指定公司
        for (const projectId of projectIds) {
            const projectExists = await this.projectDao.checkProjectExists(projectId);
            if (!projectExists) {
                throw new YqzException(`工地ID不存在: ${projectId}`);
            }

            const belongsToCompany = await this.projectDao.checkProjectBelongsToCompany(projectId, companyId);
            if (!belongsToCompany) {
                throw new YqzException(`工地(ID: ${projectId})不属于指定公司`);
            }
        }

        // 开始事务处理
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            if (authorize) {
                // 授权操作
                const now = new Date();
                const relations: Partial<ExternalMerchantProjectRelationEntity>[] = [];
                const projectIdsSet = new Set(projectIds);

                for (const projectId of projectIdsSet) {
                    // 检查是否已存在授权关系
                    const exists = await this.merchantDao.checkProjectRelationExists(merchantId, projectId);
                    if (!exists) {
                        relations.push({
                            merchantId: merchantId,
                            projectId: projectId,
                            companyId: companyId,
                            addedAt: now,
                            createTime: now,
                            updateTime: now
                        });
                    }
                }

                if (relations.length > 0) {
                    await this.merchantDao.createMerchantProjectRelations(relations);
                }
            } else {
                // 取消授权操作
                await this.merchantDao.softDeleteProjectRelations(merchantId, projectIds);
            }

            // 提交事务
            await queryRunner.commitTransaction();
            return true;
        } catch (error) {
            // 回滚事务
            await queryRunner.rollbackTransaction();
            this.logger.error(`工地授权/取消授权失败: ${error.message}`, error.stack);
            throw new YqzException(`操作失败: ${error.message}`);
        } finally {
            // 释放查询运行器
            await queryRunner.release();
        }
    }

    // 获取商户已授权的工地列表
    async getAuthorizedProjects(
        merchantId: string,
        companyId?: string,
        projectName?: string,
        pageNo: number = 1,
        pageSize: number = 10,
        includeNoDeviceProjects: boolean = true
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.ProjectInfo>> {
        // 验证商户是否存在
        const merchant = await this.merchantDao.findMerchantById(merchantId);
        if (!merchant) {
            throw new YqzException('商户不存在');
        }

        // 如果指定了公司ID，验证商户是否关联该公司
        if (companyId) {
            const companyExists = await this.projectDao.checkCompanyExists(companyId);
            if (!companyExists) {
                throw new YqzException('公司不存在');
            }

            const companyRelations = await this.merchantDao.findMerchantCompanyRelations(merchantId);
            const hasRelation = companyRelations.some(rel => rel.companyId === companyId);
            if (!hasRelation) {
                throw new YqzException('商户与该公司无关联关系');
            }
        }

        // 查询已授权的工地列表
        const projects = await this.projectDao.findAuthorizedProjects(
            merchantId,
            companyId,
            projectName,
            pageNo,
            pageSize,
            includeNoDeviceProjects
        );

        // 检查工地是否绑定设备
        const projectIds = projects.items.map(project => project.projectId);
        const deviceBindingMap = await this.projectDao.checkProjectsBindingDevices(projectIds);


        return {
            total: projects.total,
            items: projects.items.map(project => ({
                projectId: project.projectId,
                projectAddress: project.projectAddress,
                companyId: project.companyId,
                companyName: project.companyName,
                stageName: project.stageName,
                // 未绑定设备的工地，isOnline为空字符串
                isOnline: deviceBindingMap[project.projectId] ? (project.isOnline?"Y":"N") : "",
                coverImageUrl: !!project?.coverImageUrl ? project.coverImageUrl : Constants.DEFAULT_DEVICE_COVER_IMG,
                // 未绑定设备的工地，deviceNum为0
                deviceNum: deviceBindingMap[project.projectId] ? project.deviceNum : 0
            }))
        };
    }

    // 商户登录
    async merchantLogin(account: string, password: string): Promise<ExternalMerchant.MerchantLoginResponse> {
        const merchant = await this.merchantDao.findMerchantByAccount(account);
        const defResult = {
            merchantId: "",
            merchantName: "",
            account: "",
        }
        if (!merchant) {
            defResult['errMsgType'] = "1"
            defResult['errMsg'] = "账号不存在！"
            return defResult
        }

        if (merchant.password !== password) {
            defResult['errMsgType'] = "2"
            defResult['errMsg'] = "密码错误！"
            return defResult
        }

        return {
            merchantId: merchant.id,
            merchantName: merchant.name,
            account: merchant.account
        };
    }

    // 获取工地直播信息
    async getProjectStreamInfo(merchantId: string, projectId: string): Promise<ExternalMerchant.ProjectStreamInfo> {
        // 验证商户是否存在
        const merchant = await this.merchantDao.findMerchantById(merchantId);
        if (!merchant) {
            throw new YqzException('商户不存在');
        }

        // 验证工地是否存在
        const projectExists = await this.projectDao.checkProjectExists(projectId);
        if (!projectExists) {
            throw new YqzException('工地不存在');
        }

        // 验证商户是否有访问该工地的权限
        const hasAccess = await this.merchantDao.checkProjectAccess(merchantId, projectId);
        if (!hasAccess) {
            throw new YqzException('没有权限访问该工地');
        }

        // 获取工地详情
        const projectDetail = await this.projectDao.findProjectDetail(projectId);
        if (!projectDetail) {
            throw new YqzException('工地详情获取失败');
        }

        // 获取工地的设备列表
        const devices = await this.projectDao.findProjectDevices(projectId);

        return {
            project: {
                projectId: projectDetail.projectId,
                projectAddress: projectDetail.projectAddress,
                companyId: projectDetail.companyId,
                companyName: projectDetail.companyName,
                stageName: projectDetail.stageName
            },
            devices: devices.map(device => ({
                deviceId: device.deviceId,
                deviceRemark: device.deviceRemark,
                coverImageUrl: device.coverImageUrl,
                isOnline: Boolean(device.isOnline)?"Y":"N",
                deviceSerial: device.deviceSerial
            }))
        };
    }

    // 获取已授权工地的公司下拉列表
    async getAuthorizedCompanyOptions(merchantId: string): Promise<ExternalMerchant.CompanyOption[]> {
        // 验证商户是否存在
        const merchant = await this.merchantDao.findMerchantById(merchantId);
        if (!merchant) {
            throw new YqzException('商户不存在');
        }

        // 查询商户已关联的公司
        const companyRelations = await this.merchantDao.findMerchantCompanyRelations(merchantId);
        
        if (companyRelations.length === 0) {
            return [];
        }

        // 提取所有公司ID
        const companyIds = companyRelations.map(relation => relation.companyId);
        
        // 批量获取公司信息
        const companiesInfo = await this.projectDao.findCompaniesByIds(companyIds);
        
        // 将查询结果转换为Map便于快速查找
        const companyMap = new Map<string, any>();
        companiesInfo.forEach(company => {
            companyMap.set(company.id, company);
        });
        
        // 构建结果
        const result: ExternalMerchant.CompanyOption[] = [];
        for (const relation of companyRelations) {
            const companyInfo = companyMap.get(relation.companyId);
            if (companyInfo) {
                result.push({
                    companyId: relation.companyId,
                    companyName: companyInfo.name || `公司(${relation.companyId})`
                });
            }
        }

        return result;
    }

    // 获取公司管理的外部商户列表
    async getCompanyMerchants(
        companyId: string,
        merchantName?: string,
        pageNo: number = 1,
        pageSize: number = 10
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.MerchantWithProjectCount>> {
        try {
            // 1. 获取该公司关联的所有商户
            const [merchants, total] = await this.merchantDao.findMerchantsByCompany(
                companyId,
                merchantName,
                pageNo,
                pageSize
            );

            if (!merchants || merchants.length === 0) {
                return {
                    total: 0,
                    items: []
                };
            }

            // 2. 获取每个商户的已授权工地数量
            const merchantIds = merchants.map(m => m.merchantId);
            const projectCountsMap = await this.merchantDao.getMerchantProjectCounts(merchantIds, companyId);

            // 3. 构建响应
            const items: ExternalMerchant.MerchantWithProjectCount[] = merchants.map(relation => {
                const merchant = relation.merchant;
                return {
                    id: merchant.id,
                    name: merchant.name,
                    account: merchant.account,
                    authorizedProjectCount: projectCountsMap.get(merchant.id) || 0,
                    createTime: DateFns.format(relation.createTime, 'yyyy-MM-dd HH:mm:ss')
                };
            });

            return {
                total,
                items
            };
        } catch (error) {
            this.logger.error(`获取公司商户列表失败: ${error.message}`, error.stack);
            throw new Error(`获取公司商户列表失败: ${error.message}`);
        }
    }
}