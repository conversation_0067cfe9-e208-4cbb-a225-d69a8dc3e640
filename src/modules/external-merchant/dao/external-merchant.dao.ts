import { Injectable } from '@nestjs/common';
import { Repository, QueryFailedError } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { MyLogger } from '@src/modules/logger/my-logger.service';
import { ExternalMerchantEntity } from '@src/modules/report/entity/external-merchant/external-merchant.entity';
import { ExternalMerchantCompanyRelationEntity } from '@src/modules/report/entity/external-merchant/external-merchant-company-relation.entity';
import { ExternalMerchantProjectRelationEntity } from '@src/modules/report/entity/external-merchant/external-merchant-project-relation.entity';
import { DeleteFlag } from '@src/types/delete-flag.enum';
import { In } from 'typeorm';
import { CompanyEntity } from '@src/modules/report/entity/bgw/company.entity';
import { ProjectEntity } from '@src/modules/report/entity/bgw/project.entity';

@Injectable()
export class ExternalMerchantDao {
    private readonly logger = new MyLogger(ExternalMerchantDao.name);

    constructor(
        @InjectRepository(ExternalMerchantEntity)
        private merchantRepository: Repository<ExternalMerchantEntity>,
        @InjectRepository(ExternalMerchantCompanyRelationEntity)
        private companyRelationRepository: Repository<ExternalMerchantCompanyRelationEntity>,
        @InjectRepository(ExternalMerchantProjectRelationEntity)
        private projectRelationRepository: Repository<ExternalMerchantProjectRelationEntity>,
    ) {}

    // 根据账号查询商户 (ExternalMerchantEntity 使用 delete_flag)
    async findMerchantByAccount(account: string): Promise<ExternalMerchantEntity | null> {
        return this.merchantRepository.findOne({
            where: {
                account,
                delete_flag: DeleteFlag.N
            }
        });
    }

    // 根据ID查询商户 (ExternalMerchantEntity 使用 delete_flag)
    async findMerchantById(id: string): Promise<ExternalMerchantEntity | null> {
        return await this.merchantRepository.findOne({
            where: {
                id,
                delete_flag: DeleteFlag.N
            }
        });
    }
    
    // 根据名称查询商户 (ExternalMerchantEntity 使用 delete_flag)
    async findMerchantByName(name: string): Promise<ExternalMerchantEntity | null> {
        return await this.merchantRepository.findOne({
            where: {
                name,
                delete_flag: DeleteFlag.N
            }
        });
    }
    
    // 查询所有未删除的商户，支持按名称模糊查询
    async findAllMerchants(
        merchantName?: string,
        account?: string,
        companyId?: string,
        pageNo: number = 1,
        pageSize: number = 10
    ): Promise<[ExternalMerchantEntity[], number]> {
        const queryBuilder = this.merchantRepository.createQueryBuilder('merchant')
            .leftJoin(
                ExternalMerchantCompanyRelationEntity,
                'companyRelation',
                'companyRelation.merchant_id = merchant.id'
            )
            .where('merchant.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            .andWhere('companyRelation.delete_flag = :companyRelationDeleteFlag', { companyRelationDeleteFlag: DeleteFlag.N });
            
        if (merchantName) {
            queryBuilder.andWhere('merchant.name LIKE :name', { name: `%${merchantName}%` });
        }

        if (account) {
            queryBuilder.andWhere('merchant.account = :account', { account: account });
        }

        if (companyId) {
            queryBuilder.andWhere('companyRelation.company_id = :companyId', { companyId: companyId });
        }
        
        const skip = (pageNo - 1) * pageSize;
        queryBuilder.skip(skip).take(pageSize);
        
        return queryBuilder.getManyAndCount();
    }

    // 创建商户 (ExternalMerchantEntity 使用 delete_flag)
    async createMerchant(merchant: Partial<ExternalMerchantEntity>): Promise<ExternalMerchantEntity> {
        merchant.is_delete = merchant.is_delete === undefined ? 0 : merchant.is_delete;
        if (merchant.delete_flag === undefined) {
            merchant.delete_flag = DeleteFlag.N;
        }
        const newMerchant = this.merchantRepository.create(merchant);
        return this.merchantRepository.save(newMerchant);
    }

    // 更新商户 (ExternalMerchantEntity 使用 delete_flag)
    async updateMerchant(id: string, data: Partial<ExternalMerchantEntity>): Promise<void> {
        await this.merchantRepository.update(
            { id, delete_flag: DeleteFlag.N },
            data
        );
    }

    // 逻辑删除商户 (更新 is_delete)
    async softDeleteMerchant(id: string): Promise<void> {
        await this.merchantRepository.update(
            { id, delete_flag: DeleteFlag.N },
            { is_delete: Date.now(), updateTime: new Date(), delete_flag: DeleteFlag.Y }
        );
    }

    // 查询商户已关联的公司
    async findMerchantCompanyRelations(merchantId: string): Promise<ExternalMerchantCompanyRelationEntity[]> {
        return this.companyRelationRepository.find({
            where: {
                merchantId: merchantId,
                delete_flag: DeleteFlag.N
            }
        });
    }

    // 查询商户已关联的公司
    async findMerchantCompanyRelationList(merchantIds: string[]) {
        const qb = this.companyRelationRepository.createQueryBuilder('relation')
          .select([
                'relation.merchant_id as merchantId',
                'group_concat(distinct company.COMPANY_ID SEPARATOR ",") as companyIds',
                'group_concat(distinct company.COMPANY_NAME SEPARATOR "、") as companyNames',
                'count(distinct projectRelation.id) as projectNum'
          ])
          .innerJoin(
                CompanyEntity,
              'company',
              'relation.company_id = company.COMPANY_ID'
            )
            .leftJoin(
                ExternalMerchantProjectRelationEntity,
                'projectRelation',
                'projectRelation.merchant_id = relation.merchant_id and projectRelation.company_id = relation.company_id and projectRelation.delete_flag = :projectRelationDeleteFlag',
                { projectRelationDeleteFlag: DeleteFlag.N }
            )
            .leftJoin(
                ProjectEntity,
                'project',
                'projectRelation.project_id = project.PROJECT_ID and project.delete_flag = :projectDeleteFlag',
                { projectDeleteFlag: DeleteFlag.N }
            )
          .where('relation.merchant_id IN (:...merchantIds)', { merchantIds })
          .andWhere('relation.delete_flag = :isDelete', { isDelete: DeleteFlag.N })
          .groupBy('relation.merchant_id');
        return qb.getRawMany();
    }

    // 检查商户与公司是否已关联
    async checkCompanyRelationExists(merchantId: string, companyId: string): Promise<boolean> {
        const count = await this.companyRelationRepository.count({
            where: {
                merchantId: merchantId,
                companyId: companyId,
                delete_flag: DeleteFlag.N
            }
        });
        return count > 0;
    }

    // 创建商户-公司关系（用于创建新的活动关系，依赖唯一约束）
    async createMerchantCompanyRelationAtomically(relation: Partial<ExternalMerchantCompanyRelationEntity>): Promise<ExternalMerchantCompanyRelationEntity | null> {
        try {
            relation.is_delete = 0;
            if (relation.delete_flag === undefined) {
                relation.delete_flag = DeleteFlag.N;
            }
            
            const newRelation = this.companyRelationRepository.create(relation);
            return await this.companyRelationRepository.save(newRelation);
        } catch (error) {
            if (error instanceof QueryFailedError && 
                (error.message.includes('ER_DUP_ENTRY') || 
                 error.message.includes('duplicate key') || 
                 error.message.includes('UNIQUE constraint failed'))) {
                this.logger.log(`关系已存在 (merchantId=${relation.merchantId}, companyId=${relation.companyId}, is_delete=0)`);
                return this.companyRelationRepository.findOne({where: {merchantId: relation.merchantId, companyId: relation.companyId, is_delete: 0}});
            } else {
                throw error;
            }
        }
    }

    // 创建商户-公司关系 (普通创建，is_delete 默认为0)
    async createMerchantCompanyRelation(relation: Partial<ExternalMerchantCompanyRelationEntity>): Promise<ExternalMerchantCompanyRelationEntity> {
        relation.is_delete = relation.is_delete === undefined ? 0 : relation.is_delete;
        if (relation.delete_flag === undefined) {
            relation.delete_flag = DeleteFlag.N;
        }
        const newRelation = this.companyRelationRepository.create(relation);
        return this.companyRelationRepository.save(newRelation);
    }

    // 逻辑删除商户-公司关系 (更新现有活动记录的 is_delete)
    async softDeleteMerchantCompanyRelation(merchantId: string, companyId: string): Promise<void> {
        await this.companyRelationRepository.update(
            { merchantId: merchantId, companyId: companyId, is_delete: 0 }, 
            { is_delete: Date.now(), updateTime: new Date(), delete_flag: DeleteFlag.Y } 
        );
    }
    
    // 批量逻辑删除 (更新 is_delete)
    async softDeleteMerchantCompanyRelationsByMerchantId(merchantId: string): Promise<void> {
        await this.companyRelationRepository.update(
            { merchantId: merchantId, is_delete: 0 },
            { is_delete: Date.now(), updateTime: new Date(), delete_flag: DeleteFlag.Y }
        );
    }

    // 原子方式标记删除商户-公司关系 (创建一条新的历史记录标记为删除)
    async markMerchantCompanyRelationAsDeletedAtomically(merchantId: string, companyId: string): Promise<ExternalMerchantCompanyRelationEntity> {
        const deletionRecord: Partial<ExternalMerchantCompanyRelationEntity> = {
            merchantId: merchantId,
            companyId: companyId,
            is_delete: Date.now(), 
            delete_flag: DeleteFlag.Y, 
            createTime: new Date(),
            updateTime: new Date()
        };
        return this.companyRelationRepository.save(this.companyRelationRepository.create(deletionRecord));
    }

    // 查询商户已授权的工地 (使用 is_delete)
    async findMerchantProjectRelations(
        merchantId: string,
        companyId?: string,
        pageNo: number = 1,
        pageSize: number = 10
    ): Promise<[ExternalMerchantProjectRelationEntity[], number]> {
        const queryBuilder = this.projectRelationRepository.createQueryBuilder('relation')
            .where('relation.merchant_id = :merchantId', { merchantId })
            .andWhere('relation.delete_flag = :isDelete', { isDelete: DeleteFlag.N }); // Active relations

        if (companyId) {
            queryBuilder.andWhere('relation.company_id = :companyId', { companyId });
        }

        const skip = (pageNo - 1) * pageSize;
        queryBuilder.skip(skip).take(pageSize);
        
        return queryBuilder.getManyAndCount();
    }

    // 创建商户-工地授权关系 (普通创建，is_delete 默认为0)
    async createMerchantProjectRelation(relation: Partial<ExternalMerchantProjectRelationEntity>): Promise<ExternalMerchantProjectRelationEntity> {
        relation.is_delete = relation.is_delete === undefined ? 0 : relation.is_delete;
        if (relation.delete_flag === undefined) {
            relation.delete_flag = DeleteFlag.N;
        }
        const newRelation = this.projectRelationRepository.create(relation);
        return this.projectRelationRepository.save(newRelation);
    }

    // 批量创建商户-工地授权关系
    async createMerchantProjectRelations(relations: Partial<ExternalMerchantProjectRelationEntity>[]): Promise<ExternalMerchantProjectRelationEntity[]> {
        relations.forEach(relation => {
            relation.is_delete = relation.is_delete === undefined ? 0 : relation.is_delete;
            if (relation.delete_flag === undefined) {
                relation.delete_flag = DeleteFlag.N;
            }
        });
        
        const newRelations = relations.map(relation => this.projectRelationRepository.create(relation));
        return this.projectRelationRepository.save(newRelations);
    }

    // 检查工地授权关系是否存在 (使用 is_delete)
    async checkProjectRelationExists(merchantId: string, projectId: string): Promise<boolean> {
        const count = await this.projectRelationRepository.count({
            where: {
                merchantId: merchantId,
                projectId: projectId,
                is_delete: 0 
            }
        });
        return count > 0;
    }

    // 逻辑删除商户-工地授权关系 (更新 is_delete)
    async softDeleteProjectRelation(merchantId: string, projectId: string): Promise<void> {
        await this.projectRelationRepository.update(
            { merchantId: merchantId, projectId: projectId, is_delete: 0 },
            { is_delete: Date.now(), updateTime: new Date(), delete_flag: DeleteFlag.Y }
        );
    }

    // 批量逻辑删除商户-工地授权关系 (更新 is_delete)
    async softDeleteProjectRelations(merchantId: string, projectIds: string[]): Promise<void> {
        await this.projectRelationRepository.update(
            { merchantId: merchantId, projectId: In(projectIds), is_delete: 0 },
            { is_delete: Date.now(), updateTime: new Date(), delete_flag: DeleteFlag.Y }
        );
    }

    // 查询商户是否有工地的访问权限 (使用 is_delete)
    async checkProjectAccess(merchantId: string, projectId: string): Promise<boolean> {
        const count = await this.projectRelationRepository.count({
            where: {
                merchantId: merchantId,
                projectId: projectId,
                is_delete: 0
            }
        });
        return count > 0;
    }

    // 查询公司关联的所有商户
    async findMerchantsByCompany(
        companyId: string,
        merchantName?: string,
        pageNo: number = 1,
        pageSize: number = 10
    ): Promise<[any[], number]> {
        const queryBuilder = this.companyRelationRepository.createQueryBuilder('relation')
            .innerJoinAndSelect(
                ExternalMerchantEntity,
                'merchant',
                'relation.merchant_id = merchant.id AND merchant.delete_flag = :deleteFlag',
                { deleteFlag: DeleteFlag.N }
            )
            .where('relation.company_id = :companyId', { companyId })
            .andWhere('relation.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N });

        if (merchantName) {
            queryBuilder.andWhere('merchant.name LIKE :merchantName', { merchantName: `%${merchantName}%` });
        }

        const skip = (pageNo - 1) * pageSize;
        queryBuilder
            .select([
                'relation.id as id', 
                'relation.merchant_id as merchantId', 
                'relation.company_id as companyId',
                'relation.createTime as createTime',
                'merchant.id as merchantId', 
                'merchant.name as name', 
                'merchant.account as account'
            ])
            .skip(skip)
            .take(pageSize)
            .orderBy('relation.createTime', 'DESC');

        // 计算总数
        const countQuery = this.companyRelationRepository.createQueryBuilder('relation')
            .innerJoin(
                'external_merchant',
                'merchant',
                'relation.merchant_id = merchant.id AND merchant.delete_flag = :deleteFlag',
                { deleteFlag: DeleteFlag.N }
            )
            .where('relation.company_id = :companyId', { companyId })
            .andWhere('relation.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N });

        if (merchantName) {
            countQuery.andWhere('merchant.name LIKE :merchantName', { merchantName: `%${merchantName}%` });
        }

        const total = await countQuery.getCount();
        const result = await queryBuilder.getRawMany();

        // 转换结果格式
        const merchants = result.map(item => ({
            id: item.id,
            merchantId: item.merchantId,
            companyId: item.companyId,
            createTime: item.createTime,
            merchant: {
                id: item.merchantId,
                name: item.name,
                account: item.account
            }
        }));

        return [merchants, total];
    }

    // 获取商户已授权的工地数量
    async getMerchantProjectCounts(merchantIds: string[], companyId: string): Promise<Map<string, number>> {
        const queryBuilder = this.projectRelationRepository.createQueryBuilder('relation')
            .select('relation.merchant_id', 'merchantId')
            .addSelect('COUNT(relation.id)', 'count')
            .where('relation.merchant_id IN (:...merchantIds)', { merchantIds })
            .andWhere('relation.company_id = :companyId', { companyId })
            .andWhere('relation.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            .groupBy('relation.merchant_id');

        const results = await queryBuilder.getRawMany();

        // 将结果转换为Map
        const countMap = new Map<string, number>();
        results.forEach(item => {
            countMap.set(item.merchantId, parseInt(item.count));
        });

        return countMap;
    }
}