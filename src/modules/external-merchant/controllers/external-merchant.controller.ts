import { Controller, Post, Body, UseInterceptors, HttpException, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiResponse } from '@nestjs/swagger';
import { ExternalMerchantService } from '../services/external-merchant.service';
import { ExternalMerchant } from '../dto/external-merchant.dto';
import { YqzException } from '@yqz/nest';

@ApiTags('external-merchant')
@Controller('external-merchant')
export class ExternalMerchantController {
    constructor(private readonly merchantService: ExternalMerchantService) {}

    @Post('login')
    @ApiOperation({ summary: '外部商户登录' })
    @ApiResponse({ status: 200, description: '登录成功' })
    async login(@Body() req: ExternalMerchant.MerchantLoginRequest) {
        return await this.merchantService.merchantLogin(req.account, req.password);
    }

    @Post('project/list')
    @ApiOperation({ summary: '获取商户可查看的工地列表' })
    async getProjectList(@Body() req: ExternalMerchant.GetAuthorizedProjectsRequest): Promise<ExternalMerchant.PageResponse<ExternalMerchant.ProjectInfo>> {
        try {
            return await this.merchantService.getAuthorizedProjects(
                req.merchantId,
                req.companyId,
                req.projectName,
                req.pageNo || 1,
                req.pageSize || 10,
                false
            );
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `${error.message}`,
            });
        }
    }

    @Post('project/device/info')
    @ApiOperation({ summary: '获取工地直播信息' })
    @ApiResponse({ status: 200, description: '获取成功' })
    async getProjectStreamInfo(@Body() req: { merchantId: string; projectId: string }): Promise<ExternalMerchant.ProjectStreamInfo> {
        try {
            return await this.merchantService.getProjectStreamInfo(req.merchantId, req.projectId);
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `${error.message}`,
            });
        }
    }

    @Post('project/company/options')
    @ApiOperation({ summary: '获取已授权工地的公司下拉列表' })
    @ApiResponse({ status: 200, description: '获取成功' })
    async getCompanyOptions(@Body() req: { merchantId: string }): Promise<ExternalMerchant.CompanyOption[]> {
        try {
            return await this.merchantService.getAuthorizedCompanyOptions(req.merchantId);
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `${error.message}`,
            });
        }
    }
} 