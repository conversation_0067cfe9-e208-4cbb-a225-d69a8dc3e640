import { Controller, Post, Body } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiResponse } from '@nestjs/swagger';
import { ExternalMerchantService } from '../services/external-merchant.service';
import { ExternalMerchant } from '../dto/external-merchant.dto';
import { JwtUserGw, MyUser, YqzException } from '@yqz/nest';

@ApiTags('company')
@Controller('company/external-merchant')
export class ExternalMerchantCompanyController {
    constructor(private readonly merchantService: ExternalMerchantService) {}

    @Post('list')
    @ApiOperation({ summary: '获取公司管理的外部商户列表' })
    async getCompanyMerchants(
        @Body() req: ExternalMerchant.GetCompanyMerchantsRequest,
        @MyUser() user: JwtUserGw
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.MerchantWithProjectCount>> {
        try {
            req.companyId = String(user.targetCompany.companyId);
            return await this.merchantService.getCompanyMerchants(
                req.companyId,
                req.merchantName,
                req.pageNo || 1,
                req.pageSize || 10
            );
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new YqzException({
                code: "202",
                msg: `${errorMessage}`,
            });
        }
    }

    @Post('project/list')
    @ApiOperation({ summary: '获取可授权的工地列表' })
    async getAuthorizableProjects(
        @Body() req: ExternalMerchant.GetAuthorizableProjectsRequest,
        @MyUser() user: JwtUserGw
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.ProjectInfo>> {
        try {
            req.companyId = String(user.targetCompany.companyId);
            return await this.merchantService.getAuthorizableProjectsWithStatus(
                req.merchantId,
                req.companyId,
                req.projectName,
                req.pageNo || 1,
                req.pageSize || 10
            );
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new YqzException({
                code: "202",
                msg: `${errorMessage}`,
            });
        }
    }

    @Post('project/authorize')
    @ApiOperation({ summary: '添加/删除工地授权' })
    async authorizeProjects(@Body() req: ExternalMerchant.ProjectAuthorizationRequest, @MyUser() user: JwtUserGw): Promise<boolean> {
        try {
            req.companyId = String(user.targetCompany.companyId);
            return await this.merchantService.authorizeProjects(
                req.merchantId,
                req.companyId,
                req.projectIds,
                req.authorize
            );
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `操作失败: ${error.message}`,
            });
        }
    }

    @Post('project/authorize/list')
    @ApiOperation({ summary: '获取已授权的工地列表' })
    async getAuthorizedProjects(@Body() req: ExternalMerchant.GetAuthorizedProjectsRequest, @MyUser() user: JwtUserGw): Promise<ExternalMerchant.PageResponse<ExternalMerchant.ProjectInfo>> {
        try {
            req.companyId = String(user.targetCompany.companyId);
            return await this.merchantService.getAuthorizedProjects(
                req.merchantId,
                req.companyId,
                req.projectName,
                req.pageNo || 1,
                req.pageSize || 10
            );
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `${error.message}`,
            });
        }
    }
} 