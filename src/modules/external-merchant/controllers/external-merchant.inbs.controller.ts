import { Controller, Post, Body, UseInterceptors, UploadedFile, Query } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiResponse, ApiConsumes, ApiQuery } from '@nestjs/swagger';
import { ExternalMerchantService } from '../services/external-merchant.service';
import { ExternalMerchant } from '../dto/external-merchant.dto';
import { YqzException } from '@yqz/nest';
import { FileInterceptor } from '@nestjs/platform-express';
import { decodeCsvBufferAutoEncoding, parseXlsxToCSV } from '../../../util/csv.util';

@ApiTags('external-merchant-inbs')
@Controller('inbs/external-merchant')
export class ExternalMerchantInbsController {
    constructor(private readonly merchantService: ExternalMerchantService) {}

    @Post('import')
    @ApiOperation({ summary: '导入外部商户及关联公司(CSV/XLSX文件上传)' })
    @ApiConsumes('multipart/form-data')
    @UseInterceptors(FileInterceptor('file'))
    async importMerchants(
        @UploadedFile() file: Express.Multer.File
    ): Promise<ExternalMerchant.ImportMerchantResponse> {
        try {
            if (!file) {
                throw new YqzException('未上传文件');
            }
            
            const fileExt = file.originalname.toLowerCase().split('.').pop();
            
            // 验证文件类型
            if (fileExt !== 'csv' && fileExt !== 'xlsx') {
                throw new YqzException('请上传CSV或XLSX格式文件');
            }
            
            let csvContent: string;
            
            if (fileExt === 'csv') {
                // 自动检测并处理CSV文件编码（支持UTF-8和GBK）
                csvContent = decodeCsvBufferAutoEncoding(file.buffer);
            } else {
                // 处理XLSX文件
                csvContent = await parseXlsxToCSV(file.buffer);
            }
            
            return await this.merchantService.importMerchants(csvContent);
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `${error.message}`,
            });
        }
    }
    
    @Post('list')
    @ApiOperation({ summary: '查询导入账号和外部商户及关联公司的列表' })
    @ApiResponse({ status: 200, description: '查询成功' })
    async getMerchantsList(
        @Body() req: ExternalMerchant.GetMerchantsRequest
    ): Promise<ExternalMerchant.PageResponse<ExternalMerchant.MerchantWithCompanies>> {
        try {
            return await this.merchantService.getAllMerchants(
                req.merchantName,
                req.account,
                req.companyId,
                req.pageNo || 1,
                req.pageSize || 10
            );
        } catch (error) {
            throw new YqzException({
                code: "202",
                msg: `查询失败: ${error.message}`,
            });
        }
    }
}