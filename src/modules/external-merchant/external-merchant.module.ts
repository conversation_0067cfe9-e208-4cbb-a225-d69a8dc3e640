import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExternalMerchantEntity } from '@src/modules/report/entity/external-merchant/external-merchant.entity';
import { ExternalMerchantCompanyRelationEntity } from '@src/modules/report/entity/external-merchant/external-merchant-company-relation.entity';
import { ExternalMerchantProjectRelationEntity } from '@src/modules/report/entity/external-merchant/external-merchant-project-relation.entity';
import { ExternalMerchantDao } from './dao/external-merchant.dao';
import { ExternalMerchantService } from './services/external-merchant.service';
import { ExternalMerchantController } from './controllers/external-merchant.controller';
import { ExternalMerchantInbsController } from './controllers/external-merchant.inbs.controller';
import { ExternalMerchantCompanyController } from './controllers/external-merchant.company.controller';
import { ReportModule } from '../report/report.module';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            ExternalMerchantEntity,
            ExternalMerchantCompanyRelationEntity,
            ExternalMerchantProjectRelationEntity
        ]),
        ReportModule,
    ],
    controllers: [
        ExternalMerchantInbsController,
        ExternalMerchantCompanyController,
        ExternalMerchantController
    ],
    providers: [
        ExternalMerchantDao,
        ExternalMerchantService
    ],
    exports: [
        ExternalMerchantService
    ]
})
export class ExternalMerchantModule {} 