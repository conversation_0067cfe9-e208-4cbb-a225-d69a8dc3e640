// import { Face } from "./camera/face.collection";
// import { Body } from "./camera/body.collection";
// import * as mongoDB from 'mongodb';
// import { DateReportBoard } from "../domain/corp/data-report/dto/data-report-board.dto";
// import { DateReportModule } from "../domain/corp/data-report/dto/data-report-module.dto";
// import { DateReportConfig } from "../domain/corp/data-report/dto/data-report-config.dto";
// import { CompanyConfig } from "../domain/corp/data-old-report/type/company-config.type";
// import { Sql } from "../domain/corp/sql-generate/type/sql.type";
// import { Dispatch } from "./camera/dispatch.collection";
// import { TagHirachy } from "./camera/tags_hirachy.collection";
// import { DashboardTemplate } from "../domain/corp/data-report/dto/dashboard-template.dto";
// import { DataReportModuleFilter } from "../domain/corp/data-report/dto/data-report-module-filter.dto";

// // 配置选项
// const mongoOptions: mongoDB.MongoClientOptions = {
//   maxPoolSize: 50,        // 连接池最大连接数
//   minPoolSize: 10,        // 连接池最小连接数
//   maxIdleTimeMS: 30000,   // 连接最大空闲时间
//   connectTimeoutMS: 10000, // 连接超时时间
//   socketTimeoutMS: 45000, // Socket超时时间
//   retryWrites: true,      // 启用重试写入
//   retryReads: true,       // 启用重试读取
// };

// const camera: {
//   body?: mongoDB.Collection<Body>,
//   dispatch?: mongoDB.Collection<Dispatch>,
//   face?: mongoDB.Collection<Face>,
//   tags_hirachy?: mongoDB.Collection<TagHirachy>,
// } = {}

// const bgw: {
//   dataReportBoard?: mongoDB.Collection<DateReportBoard.DataReportBoardEntity>,
//   dataReportModule?: mongoDB.Collection<DateReportModule.DataReportModuleEntity>,
//   dataReportModuleFilter?: mongoDB.Collection<DataReportModuleFilter.DataReportModuleFilterEntity>,
//   dataReportColumnConfig?: mongoDB.Collection<Sql.ColumnConfig>,
//   dataReportMenuConfig?: mongoDB.Collection<DateReportConfig.MenuConfig>,
//   dataReportDataConfig?: mongoDB.Collection<DateReportConfig.DataConfig>,
//   companyConfig?: mongoDB.Collection<CompanyConfig.CompanyConfigDoc>,
//   dashboardTemplates?: mongoDB.Collection<DashboardTemplate.DashboardTemplateEntity>,
//   userDashboardSettings?: mongoDB.Collection<DashboardTemplate.UserDashboardSettingsEntity>,
// } = {}

// const client: mongoDB.MongoClient = new mongoDB.MongoClient(
//   process.env.YQZ_MONGO_DB_CONN_STRING,
//   mongoOptions
// );

// // 添加连接监控
// client.on('connectionPoolCreated', (event) => {
//   console.log('[MongoDB] Connection pool created', {
//     poolSize: event.options.maxPoolSize,
//   });
// });

// client.on('connectionPoolClosed', (event) => {
//   console.log('[MongoDB] Connection pool closed');
// });

// // 优化连接初始化
// async function initializeCollections() {
//   try {
//     // 初始化 camera 数据库集合
//     const cameraDb = client.db(process.env.YQZ_MONGO_DB_CAMERA);
//     camera.body = cameraDb.collection('body');
//     camera.face = cameraDb.collection('face');
//     camera.dispatch = cameraDb.collection('dispatch');
//     camera.tags_hirachy = cameraDb.collection('tags_hirachy');

//     // 初始化 bgw 数据库集合
//     const bgwDb = client.db(process.env.YQZ_MONGO_DB_BGW);
//     bgw.dataReportBoard = bgwDb.collection('data_report_board');
//     bgw.dataReportModule = bgwDb.collection('data_report_module');
//     bgw.dataReportColumnConfig = bgwDb.collection('data_report_column_config');
//     bgw.dataReportMenuConfig = bgwDb.collection('data_report_menu_config');
//     bgw.dataReportDataConfig = bgwDb.collection('data_report_data_config');
//     bgw.companyConfig = bgwDb.collection("company_config");
//     bgw.dashboardTemplates = bgwDb.collection("dashboard_templates");
//     bgw.userDashboardSettings = bgwDb.collection("user_dashboard_settings");
//     bgw.dataReportModuleFilter = bgwDb.collection("data_report_module_filter");
//   } catch (error) {
//     console.error('[MongoDB] Failed to initialize collections:', error);
//     throw error;
//   }
// }

// /**
//  * mongodb 连接
//  */
// async function clientConnect() {
//   try {
//     await client.connect();
//     console.log('[MongoDB] Connected successfully');
//     await initializeCollections();
//     // 添加进程退出时的清理
//     process.on('SIGINT', async () => {
//       try {
//         await client.close();
//         console.log('[MongoDB] Connection closed through app termination');
//         process.exit(0);
//       } catch (err) {
//         console.error('[MongoDB] Failed to close connection:', err);
//         process.exit(1);
//       }
//     });
//   } catch (error) {
//     console.error('[MongoDB] Connection error:', error);
//     throw error;
//   }
// }

// //健康检查方法
// async function healthCheck(): Promise<boolean> {
//   try {
//     await client.db().admin().ping();
//     return true;
//   } catch (error) {
//     console.error('[MongoDB] Health check failed:', error);
//     return false;
//   }
// }

// //加获取连接池统计信息
// async function getPoolStats() {
//   return client.db().admin().serverStatus();
// }

// export const MyMongoDb = {
//   clientConnect,
//   collections: { camera, bgw },
//   healthCheck,
//   getPoolStats,
// }
