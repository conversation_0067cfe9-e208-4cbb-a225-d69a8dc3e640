import * as mongoDB from 'mongodb';
import { DateReportBoard } from "../domain/corp/data-report/dto/data-report-board.dto";
import { DateReportModule } from "../domain/corp/data-report/dto/data-report-module.dto";
import { DateReportConfig } from "../domain/corp/data-report/dto/data-report-config.dto";
import { CompanyConfig } from "../domain/corp/data-old-report/type/company-config.type";
import { Sql } from "../domain/corp/sql-generate/type/sql.type";
import { Dispatch } from "./camera/dispatch.collection";
import { TagHirachy } from "./camera/tags_hirachy.collection";
import { DashboardTemplate } from "../domain/corp/data-report/dto/dashboard-template.dto";
import { DataReportModuleFilter } from "../domain/corp/data-report/dto/data-report-module-filter.dto";
import { Face } from './camera/face.collection';


class MongoDBConnection {
    private static instance: MongoDBConnection;
    private client: mongoDB.MongoClient;
    private isConnected: boolean = false;

    public readonly collections: {
        camera: {
            body?: mongoDB.Collection<Body>,
            dispatch?: mongoDB.Collection<Dispatch>,
            face?: mongoDB.Collection<Face>,
            tags_hirachy?: mongoDB.Collection<TagHirachy>,
        },
        bgw: {
            dataReportBoard?: mongoDB.Collection<DateReportBoard.DataReportBoardEntity>,
            dataReportModule?: mongoDB.Collection<DateReportModule.DataReportModuleEntity>,
            dataReportModuleFilter?: mongoDB.Collection<DataReportModuleFilter.DataReportModuleFilterEntity>,
            dataReportColumnConfig?: mongoDB.Collection<Sql.ColumnConfig>,
            dataReportMenuConfig?: mongoDB.Collection<DateReportConfig.MenuConfig>,
            dataReportDataConfig?: mongoDB.Collection<DateReportConfig.DataConfig>,
            companyConfig?: mongoDB.Collection<CompanyConfig.CompanyConfigDoc>,
            dashboardTemplates?: mongoDB.Collection<DashboardTemplate.DashboardTemplateEntity>,
            userDashboardSettings?: mongoDB.Collection<DashboardTemplate.UserDashboardSettingsEntity>,
        }
    };

    // 单例模式
    private constructor() {
        this.collections = {
            camera: {},
            bgw: {}
        };
        const mongoOptions: mongoDB.MongoClientOptions = {
            maxPoolSize: 50,        // 连接池最大连接数
            minPoolSize: 10,        // 连接池最小连接数
            maxIdleTimeMS: 30000,   // 连接最大空闲时间
            connectTimeoutMS: 10000, // 连接超时时间
            socketTimeoutMS: 45000, // Socket超时时间
            retryWrites: true,      // 启用重试写入
            retryReads: true,       // 启用重试读取
        };
        this.client = new mongoDB.MongoClient(
            process.env.YQZ_MONGO_DB_CONN_STRING,
            mongoOptions
        );
    }

    // 获取单例
    public static getInstance(): MongoDBConnection {
        if (!MongoDBConnection.instance) {
            MongoDBConnection.instance = new MongoDBConnection();
        }
        return MongoDBConnection.instance;
    }

    // 连接
    public async connect(): Promise<void> {
        if (this.isConnected) {
            console.log('[MongoDB] Already connected');
            return;
        }
        try {
            await this.client.connect();
            this.isConnected = true;
            console.log('[MongoDB] Connected successfully');
            // 初始化集合
            await this.initializeCollections();
            // 添加进程退出时的清理
            process.on('SIGINT', async () => {
                await this.close();
                process.exit(0);
            });
        } catch (error) {
            console.error('[MongoDB] Connection error:', error);
            throw error;
        }
    }

    // 初始化集合
    private async initializeCollections(): Promise<void> {
        try {
            // camera collections
            const cameraDb = this.client.db(process.env.YQZ_MONGO_DB_CAMERA);
            this.collections.camera = {
                body: cameraDb.collection('body'),
                face: cameraDb.collection('face'),
                dispatch: cameraDb.collection('dispatch'),
                tags_hirachy: cameraDb.collection('tags_hirachy')
            };
            // bgw collections
            const bgwDb = this.client.db(process.env.YQZ_MONGO_DB_BGW);
            this.collections.bgw = {
                dataReportBoard: bgwDb.collection('data_report_board'),
                dataReportModule: bgwDb.collection('data_report_module'),
                dataReportColumnConfig: bgwDb.collection('data_report_column_config'),
                dataReportMenuConfig: bgwDb.collection('data_report_menu_config'),
                dataReportDataConfig: bgwDb.collection('data_report_data_config'),
                companyConfig: bgwDb.collection("company_config"),
                dashboardTemplates: bgwDb.collection("dashboard_templates"),
                userDashboardSettings: bgwDb.collection("user_dashboard_settings"),
                dataReportModuleFilter: bgwDb.collection("data_report_module_filter"),
            };
        } catch (error) {
            console.error('[MongoDB] Failed to initialize collections:', error);
            throw error;
        }
    }

    // 关闭连接
    public async close(): Promise<void> {
        if (!this.isConnected) return;
        try {
            await this.client.close();
            this.isConnected = false;
            console.log('[MongoDB] Connection closed successfully');
        } catch (error) {
            console.error('[MongoDB] Error closing connection:', error);
            throw error;
        }
    }

    // 获取客户端
    public getClient(): mongoDB.MongoClient {
        return this.client;
    }
}

// 导出单例
export const MyMongoDb = {
    clientConnect: async () => {
        await MongoDBConnection.getInstance().connect();
    },
    collections: MongoDBConnection.getInstance().collections
};