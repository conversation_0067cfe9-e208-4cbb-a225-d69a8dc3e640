import { FaceDbType } from "./face.collection";

export const enum IdentityType {
    Face = 'face',
    Attribute = 'attribute',
    // Unkown = 'unkown',
    MANUAL = 'manual',
    LBS = 'lbs'
}

export class BodyIdentity {
    faceDbType?: FaceDbType;
    faceEntityId?: string // managerId欧哲临时id
    trustedIdentity: boolean; //已经确定的:人脸,属性或者手工
    tags?: string;
}

export type BodyLocation = {
    score?: number;
    height: number;
    left: number;
    top: number;
    width: number;
}

export type DetectionScore<T = string> = {
    location?: BodyLocation;
    score: number;
    name: T;
}

export type BodyAttributeCollection = {
    gender: DetectionScore;
    smoke: DetectionScore;
    upper_color: DetectionScore;
    lower_wear: DetectionScore;
    lower_color: DetectionScore;
    lower_cut: DetectionScore;
    upper_cut: DetectionScore<'有上方截断' | '无上方截断'>//无上方截断、有上方截断
    is_human: DetectionScore;
    orientation: DetectionScore<'正面' | '左侧面' | '右侧面' | '背面'>;
    face_mask: DetectionScore<'无口罩' | '戴口罩' | '不确定'>;
    bag: DetectionScore;
    glasses: DetectionScore;
    headwear: DetectionScore;
    upper_wear: DetectionScore;
    upper_wear_fg: DetectionScore;
    upper_wear_texture: DetectionScore;
    age: DetectionScore;
};

export type PersonInfoDTO = {
    attributes: BodyAttributeCollection;
    location: BodyLocation;
}

export type FaceLocation = {
    X: number
    Y: number
    Height: number;
    Width: number
}

export type FaceMatch = {
    FaceItems: FaceItemExt[]
    QualitieScore: number;
    Location: FaceLocation
}

export type FaceItem = {
    EntityId: string;
    FaceId: string;
    Score: string;
    Confidence: number;
    DbName: string;
    ExtraData: string;
}

export type FaceItemExt = FaceItem & { one2oneConfidence?: number, faceDbType?: FaceDbType }

export class Body extends BodyIdentity {
    companyId: string;
    projectId: string;
    bodyId: string;
    eventId: string;
    eventTime: Date;
    faceIdentifyType?: IdentityType
    bodyUrl?: string;
    eventUrl?: string;
    faceUrl?: string;
    humanData?: PersonInfoDTO
    faceData?: FaceMatch;
    faceConfidence?: number;
    one2one_confidence?: number;
    yqz_attr_match_rate?: number
    createAt?: Date;
    updateAt?: Date;
    createBy?: string;
}