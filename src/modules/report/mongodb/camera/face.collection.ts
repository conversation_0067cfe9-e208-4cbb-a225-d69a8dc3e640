export const enum FaceDbType {
    Formal = 'formal',
    Temp = 'temp',
    None = 'none'
}

export const enum BodyTag {
    Employee = '员工',
    UNTAGGED = '未分类',
    Child = '儿童'
}

export type AddressData = {
    id: string;
    title: string;
    address: string;
    category: string;
    type: number;
    location: Location;
    adcode: number;
    province: string;
    city: string;
    district: string;
}

export class Face {
    companyId: string;
    faceEntityId: string;
    faceDbType?: FaceDbType;
    name?: string;
    faces?: { faceUrl: string, faceQuality }[];
    tags?: string;
    rank?: string;
    homeAddress?: AddressData;
    history?: any[]
    refBody?: Body;
    createAt: Date;
    updateAt?: Date;
    createBy?: string;
}