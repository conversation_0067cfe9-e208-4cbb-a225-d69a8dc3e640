export type DpStatus = 'open' | 'assigned' | 'closed'

export class Dispatch {
    status: DpStatus;
    companyId: string;
    projectId: string;
    faceEntityId?: string;
    baselineStart?: Date;
    baselineRole?: string;
    planStart?: Date;
    actualStart?: Date;
    startDiffReason?: string;
    planEnd?: Date;
    actualEnd?: Date;
    endDiffReason?: string;
    desc?: string;
    ref?: {
        dpTplId?: string;
        nodeId?: string;
        offset?: number;
    }
    reminders?: { type: 'sms', time: Date, success: boolean }[]
    accepted?: boolean;
    acceptedAt?: Date;
    deletion?: boolean
    updateAt?: Date;
    updateBy?: string;
    createAt: Date;
    createBy: string;
    duration?: string;
    skipHoliday?: 'Y' | 'N';
}