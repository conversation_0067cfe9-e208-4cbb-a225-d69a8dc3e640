import { Injectable } from "@nestjs/common";
import { MyTask } from "@yqz/nest";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { FactCompanyDeviceEtl } from "../domain/etl-task/fact-company-device/etl";
import { DimDepartmentEtl } from "../domain/etl-task/dim-department/etl";
import { ProjectReportEtl } from "../domain/etl-task/project-report/etl";
import { DimDeviceEtl } from "../domain/etl-task/dim-device/etl";
import { ProjectNoFacesignEventEtl } from "../domain/etl-task/project-no-facesign-event/etl";
import { NodeOverdueEventEtl } from "../domain/etl-task/node-overdue-event/etl";

@Injectable()
export class CronService {
  private logger = new MyLogger(CronService.name)
  constructor(
    private readonly factCompanyDeviceEtl:FactCompanyDeviceEtl,
    private readonly dimDepartmentEtl: DimDepartmentEtl,
    private readonly projectReportEtl: ProjectReportEtl,
    private readonly dimDeviceEtl:DimDeviceEtl,
    private readonly projectNoFaceSignEventEtl: ProjectNoFacesignEventEtl,
    private readonly nodeOverDueEtl: NodeOverdueEventEtl
  ) { }

  @MyTask({
    task_id: 'report-service:etl-company-device-usage',
    task_desc: '生成公司设备使用统计',
    start_cron: process.env.CRON_COMPANY_DEVICE_USAGE.split("||")[0],
    end_cron: process.env.CRON_COMPANY_DEVICE_USAGE.split("||")[1],
    ttl_seconds: 10*60
  })  
  async etlCompanyDevice() {
    await this.factCompanyDeviceEtl.etl();
  }

  @MyTask({
    task_id: 'report-service:generate-daily-report',
    task_desc: '生成日报',
    start_cron: process.env.CRON_PROJECT_REPORT_GENERATE.split("||")[0],
    end_cron: process.env.CRON_PROJECT_REPORT_GENERATE.split("||")[1],
    ttl_seconds: 2*60*60
  })  
  async generateProjectReport() {
    await this.projectReportEtl.etl();
  }

  @MyTask({
    task_id:'report-service:department-etl',
    task_desc: '每日抽取部门合集数据dim_department',
    start_cron: process.env.CRON_ETL_DIM_DEPARTMENT.split("||")[0],
    end_cron: process.env.CRON_ETL_DIM_DEPARTMENT.split("||")[1],
    ttl_seconds: 2*60*60
  })
  async etlDepartment(){
    await this.dimDepartmentEtl.etl();
  }

  @MyTask({
    task_id:'report-service:etl:dim-device',
    task_desc: '每日抽取device数据dim_device',
    start_cron: process.env.CRON_ETL_DIM_DEVICE.split("||")[0],
    end_cron: process.env.CRON_ETL_DIM_DEVICE.split("||")[1],
    ttl_seconds: 5*60
  })  
  async etlDimDevice(){
    await this.dimDeviceEtl.etl();
  }

  @MyTask({
    task_id:'report-service:etl:project-no-facesign-event',
    task_desc: '工地无人脸签到事件更新',
    start_cron: process.env.CRON_ETL_PROJECT_NO_FACESIGN_EVENT.split("||")[0],
    end_cron: process.env.CRON_ETL_PROJECT_NO_FACESIGN_EVENT.split("||")[1],
    ttl_seconds: 5*60
  })  
  async etlProjectNoFaceSign(){
    await this.projectNoFaceSignEventEtl.etl();
  }  

  @MyTask({
    task_id:'report-service:etl:node-overdue-event',
    task_desc: '节点过期事件更新',
    start_cron: process.env.CRON_ETL_NODE_OVERDUE_EVENT.split("||")[0],
    end_cron: process.env.CRON_ETL_NODE_OVERDUE_EVENT.split("||")[1],
    ttl_seconds: 5*60
  })  
  async etlNodeOverdueEvent(){
    await this.nodeOverDueEtl.etl();
  }    
}