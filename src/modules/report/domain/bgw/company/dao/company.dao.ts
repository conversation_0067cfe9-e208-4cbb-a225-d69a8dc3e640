import { Injectable } from "@nestjs/common";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { MyLogger } from "@yqz/nest";
import { Flag } from "@yqz/nest/lib/types/common.type";
import _ from "lodash";
import { DataSource } from "typeorm";

@Injectable()
export class CompanyDao {
    private readonly logger = new MyLogger(CompanyDao.name)
    constructor(
        private dataSource: DataSource
    ){}

    async searchCompanyList(params: { companyIds: string[] }) {
        if (_.isEmpty(params.companyIds)) return [];
        const qb = this.dataSource.getRepository(CompanyEntity).createQueryBuilder('c')
            .select([
                'company_id id',
                'short_name name',
                'company_name companyName'
            ])
            .where('delete_flag = "N"')
            .andWhere('company_id in (:...companyIdList)', { companyIdList: params.companyIds })
            .groupBy('company_id')
        return await qb.getRawMany<{ id: string, name: string, companyName?: string }>()
    }

    async find(params: {companyId: string}){
        return await this.dataSource.getRepository(CompanyEntity).findOne({
            where: {
                COMPANY_ID: params.companyId,
                DELETE_FLAG: Flag.N
            }
        })

    }

    async getCompanyMemberRoleByRoleIds(roleIdList: string[]){
        if(_.isEmpty(roleIdList)) return [];
        const qb = this.dataSource.getRepository(CompanyMemberRoleEntity).createQueryBuilder('cmr')
        .select(['cmr.company_member_role_id as roleId','cmr.role_name as roleName'])
        .where('cmr.company_member_role_id in (:...roleIdList)',{roleIdList})
        .andWhere('cmr.delete_flag = "N"')
        return await qb.getRawMany<{ roleId: string, roleName: string }>()
    }
}
