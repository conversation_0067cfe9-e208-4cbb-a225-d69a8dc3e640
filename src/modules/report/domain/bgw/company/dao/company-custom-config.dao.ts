import { Injectable } from '@nestjs/common';
import { DevTimeout } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import { DeleteFlag } from '@src/types/delete-flag.enum';
import { DataSource } from 'typeorm';
import { CompanyCustomConfigEntity } from '@src/modules/report/entity/bgw/company-custom-config.entity';

@Injectable()
export class CompanyCustomConfigDao {
    private readonly logger = new MyLogger(CompanyCustomConfigDao.name)
    constructor(
        private datasource: DataSource
    ) { }

    @DevTimeout(1000)
    test() {
        // this.findByCustomType('project_address').then(res => console.log(res))
    }

    async findByCustomType(customType: string) {
        return await this.datasource.manager
            .find(CompanyCustomConfigEntity, {
                where: {
                    customType: customType,
                    deleteFlag: DeleteFlag.N
                }
            });
    }

}
