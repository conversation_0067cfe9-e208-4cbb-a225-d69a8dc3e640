import { Injectable } from '@nestjs/common';
import { DevTimeout } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { CompanyDao } from '../dao/company.dao';
import { Company } from '../dto/company.dto';

@Injectable()
export class CompanyService {
    private readonly logger = new MyLogger(CompanyService.name)

    constructor(
        private companyDao: CompanyDao,
    ) { }

    // @DevTimeout(1000)
    async test() {
        // const res = await this.searchCompanyInfoMap({ companyIds: ['65'] });
        // console.log("🚀 ~ CompanyService ~ test ~ res:", res)
    }

    /**
     * 查询公司类型 默认返回普通公司
     * @param param0 
     * @returns 
     */
    async searchCompanyType({ companyId }: { companyId: string }): Promise<Company.CompanyType> {
        if (!companyId) return Company.CompanyType.BasicCompany;
        const companyInfo = await this.companyDao.find({ companyId });
        return companyInfo.COMPANY_TYPE;
    }

    async searchCompanyInfoMap({ companyIds }: { companyIds: string[] }) {
        if (_.isEmpty(companyIds)) return {};
        const companyInfos = await this.companyDao.searchCompanyList({ companyIds });
        const result = {};
        companyInfos.map(res => {
            result[res.id] = { companyId: res.id, companyName: res.name };
        })
        return result;
    }

}