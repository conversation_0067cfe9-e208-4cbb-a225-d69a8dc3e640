import { Injectable } from '@nestjs/common';
import { DevTimeout } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { CompanyCustomConfigDao } from '../dao/company-custom-config.dao';

@Injectable()
export class CompanyCustomConfigService {
    private readonly logger = new MyLogger(CompanyCustomConfigService.name)

    constructor(
        private companyCustomConfigDao: CompanyCustomConfigDao,
    ) { }

    @DevTimeout(1000)
    test() {
        // this.isCustomCompany({ companyId: '66', customType: 'project_address' }).then(res => console.log(res))
    }

    /**
     * 判断是否是定制公司
     * @param param0 
     * @returns 
     */
    async isCustomCompany({ companyId, customType }: { companyId: string, customType: string }): Promise<Boolean> {
        if (!companyId) return false;
        const configModelList = await this.companyCustomConfigDao.findByCustomType(customType);
        const customCompany = configModelList.map(res => String(res.companyId));
        return customCompany.includes(String(companyId));
    }

}
