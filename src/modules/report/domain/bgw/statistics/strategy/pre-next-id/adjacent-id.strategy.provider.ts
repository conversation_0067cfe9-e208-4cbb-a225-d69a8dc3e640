import { Injectable } from '@nestjs/common';
import { YqzException } from '@yqz/nest';
import { AdjacentIdStrategyInterface } from './adjacent-id.strategy.interface';
import { ProjectProblemIdStrategy } from './impl/project-problem-id.strategy';

@Injectable()
export class AdjacentIdStrategyProvider {
  private readonly strategies: Map<string, AdjacentIdStrategyInterface> = new Map();

  constructor(
    private readonly problemStrategy: ProjectProblemIdStrategy,
  ) {
    // 注册策略
    this.strategies.set('problem', this.problemStrategy);
  }

  /**
   * 根据类型获取对应的策略
   * @param type 业务类型
   * @returns 对应的策略实例
   */
  getStrategy(type: string): AdjacentIdStrategyInterface {
    const strategy = this.strategies.get(type);
    if (!strategy) {
      throw new YqzException(`不支持的业务类型: ${type}`);
    }
    return strategy;
  }

  /**
   * 获取所有支持的业务类型
   * @returns 支持的类型列表
   */
  getSupportedTypes(): string[] {
    return Array.from(this.strategies.keys());
  }
}
