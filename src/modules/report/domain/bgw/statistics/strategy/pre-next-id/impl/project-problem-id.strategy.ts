import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { AdjacentIdStrategyInterface } from '../adjacent-id.strategy.interface';
import { ProjectProblemEntity } from '@src/modules/report/entity/bgw/project-problem.entity';
import { Filter<PERSON>hain } from '../../../../filter/chain/filter.chain';
import { Statistics } from '../../../types/statistics.type';
import { ProjectFilter } from '../../../../filter/handler/abstract/project-filter.handle.abstract';
import * as _ from 'lodash';
import { DeptProjectFilter } from '../../../../filter/handler/impl/dept-project-filter.handler';
import { ScopeProjectFilter } from '../../../../filter/handler/impl/scope-project-filter.handler';
import { ProjectAddressFilter } from '../../../../filter/handler/impl/address-project-filter.handler';

@Injectable()
export class ProjectProblemIdStrategy implements AdjacentIdStrategyInterface {
  constructor(
    private readonly dataSource: DataSource,
    private readonly deptProjectFilter: DeptProjectFilter,
    private readonly scopeProjectFilter: ScopeProjectFilter,
    private readonly projectAddressFilter: ProjectAddressFilter
  ) {}

  /**
   * 获取项目问题ID列表
   * @param params 筛选参数
   * @returns 项目问题ID数组
   */
  async getIdList(params: any): Promise<number[]> {
    // 1. 预处理筛选参数
    const processedParams = await this.preprocessParams(params);

    // 2. 如果项目筛选后没有可用项目，直接返回空数组
    if (_.isEmpty(processedParams.projectIds)) {
      return [];
    }

    // 3. 构建并执行查询
    const queryBuilder = this.buildQuery(processedParams);
    const results = await queryBuilder.getRawMany();

    // 4. 转换结果
    return this.transformResults(results);
  }

  /**
   * 预处理筛选参数
   * @param params 原始参数
   * @returns 处理后的参数
   */
  private async preprocessParams(params: any): Promise<any> {
    const processedParams = { ...params };

    // 执行项目筛选器链
    const projectIds = await this.applyProjectFilters(processedParams);
    processedParams.projectIds = projectIds;

    return processedParams;
  }

  /**
   * 应用项目筛选器链
   * @param params 筛选参数
   * @returns 筛选后的项目ID列表
   */
  private async applyProjectFilters(params: any): Promise<string[]> {
    const initialList: string[] = ['*'];

    // 构建项目筛选器链
    const projectChain = new FilterChain<string, Statistics.ProblemReq, ProjectFilter<string, Statistics.ProblemReq>>();
    projectChain
      .addHandler(this.deptProjectFilter)
      .addHandler(this.scopeProjectFilter)
      .addHandler(this.projectAddressFilter);

    // 执行筛选器链
    return await projectChain.handle(params, initialList);
  }

  /**
   * 构建查询
   * @param params 处理后的参数
   * @returns QueryBuilder
   */
  private buildQuery(params: any) {
    // 创建基础查询
    const queryBuilder = this.createBaseQuery();

    // 应用筛选条件
    this.applyFilters(queryBuilder, params);

    // 应用排序
    this.applySorting(queryBuilder, params);

    return queryBuilder;
  }

  /**
   * 创建基础查询
   * @returns QueryBuilder
   */
  private createBaseQuery() {
    return this.dataSource
      .createQueryBuilder()
      .select('pp.project_problem_id', 'id')
      .from(ProjectProblemEntity, 'pp')
      .leftJoin('project', 'p', 'pp.project_id = p.project_id')
      .leftJoin('manager', 'm', 'm.manager_id = pp.problem_record_person')
      .leftJoin('problem_solved', 'ps', 'ps.id = pp.problem_solved_id');
  }

  /**
   * 转换查询结果
   * @param results 原始查询结果
   * @returns 转换后的ID数组
   */
  private transformResults(results: any[]): number[] {
    return results.map(row => parseInt(row.id));
  }

  /**
   * 应用筛选条件 - 基于 patrolReportList 方法的完整筛选逻辑
   */
  private applyFilters(queryBuilder: any, params: any) {
    // 基础筛选条件
    queryBuilder.where("pp.DELETE_FLAG = 'N' AND p.DELETE_FLAG = 'N' ");

    // 公司ID筛选（必需）
    if (params.companyId) {
      queryBuilder.andWhere('p.company_id = :companyId', { companyId: params.companyId });
    }

    // 问题ID筛选（单条查询）
    if (params.problemId) {
      queryBuilder.andWhere('pp.project_problem_id = :problemId', { problemId: params.problemId });
    }

    // 问题类型筛选
    if (params.type) {
      queryBuilder.andWhere('pp.problem_type = :type', { type: params.type });
    }

    // 问题状态筛选
    if (params.status && Array.isArray(params.status) && params.status.length > 0) {
      queryBuilder.andWhere('pp.problem_status IN (:...statusList)', { statusList: params.status });
    }

    // 问题来源筛选
    if (params.source) {
      queryBuilder.andWhere('pp.problem_source = :source', { source: params.source });
    }

    // 项目负责人筛选
    if (params.director) {
      queryBuilder.andWhere('p.project_director = :director', { director: params.director });
    }

    // 项目ID列表筛选
    if (params.projectIds && Array.isArray(params.projectIds) && params.projectIds.length > 0 && !params.projectIds.includes("*")) {
      queryBuilder.andWhere('pp.project_id IN (:...projectIds)', { projectIds: params.projectIds });
    }

    // 部门ID列表筛选
    if (params.departmentIdList && Array.isArray(params.departmentIdList) && params.departmentIdList.length > 0) {
      queryBuilder.andWhere('p.department_id IN (:...departmentIdList)', { departmentIdList: params.departmentIdList });
    }

    // 人脸ID筛选
    if (params.faceId) {
      queryBuilder.andWhere('pp.wrongdoer_face_id = :faceId', { faceId: params.faceId });
    }

    // 报告人筛选
    if (params.reporter) {
      queryBuilder.andWhere(
        "(CASE WHEN pp.problem_source = '3' AND pp.problem_record_person IS NULL AND pp.problem_record_owner IS NOT NULL " +
        "THEN pp.problem_record_owner = :reporter ELSE m.person_id = :reporter END)",
        { reporter: params.reporter }
      );
    }

    // 报告时间范围筛选
    if (params.reportStartTime && params.reportEndTime) {
      queryBuilder.andWhere('pp.problem_time >= :reportStartTime', { reportStartTime: `${params.reportStartTime} 00:00:00` });
      queryBuilder.andWhere('pp.problem_time <= :reportEndTime', { reportEndTime: `${params.reportEndTime} 23:59:59` });
    }

    // 解决时间范围筛选
    if (params.solveStartTime && params.solveEndTime) {
      queryBuilder.andWhere('ps.time >= :solveStartTime', { solveStartTime: `${params.solveStartTime} 00:00:00` });
      queryBuilder.andWhere('ps.time <= :solveEndTime', { solveEndTime: `${params.solveEndTime} 23:59:59` });
    }

    // 解决截止时间范围筛选
    if (params.solveDeadlineStartTime && params.solveDeadlineEndTime) {
      queryBuilder.andWhere('pp.solve_deadline_time >= :solveDeadlineStartTime', { solveDeadlineStartTime: `${params.solveDeadlineStartTime} 00:00:00` });
      queryBuilder.andWhere('pp.solve_deadline_time <= :solveDeadlineEndTime', { solveDeadlineEndTime: `${params.solveDeadlineEndTime} 23:59:59` });
    }

    // 装修方式筛选
    if (params.decorationModel) {
      queryBuilder.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
    }

    // 添加 HAVING 子句用于复杂筛选
    this.applyHavingFilters(queryBuilder, params);
  }

  /**
   * 应用 HAVING 筛选条件
   */
  private applyHavingFilters(queryBuilder: any, params: any) {
    const havingConditions: string[] = [];
    const havingParams: any = {};

    // 整改时长筛选（分钟）
    if (params.solveDurationStart && params.solveDurationStart > 0) {
      havingConditions.push(
        "(CASE WHEN pp.problem_status = '3' THEN TIMESTAMPDIFF(MINUTE, pp.problem_time, ps.time) " +
        "WHEN pp.problem_status = '2' OR pp.problem_status = '1' THEN TIMESTAMPDIFF(MINUTE, pp.problem_time, NOW()) END) > :solveDurationStart"
      );
      havingParams.solveDurationStart = params.solveDurationStart;
    }

    // 超时状态筛选
    if (params.timeoutStatus) {
      switch (params.timeoutStatus) {
        case 'noDeadline':
          havingConditions.push(
            "pp.problem_status != '3' AND pp.problem_status != '0' AND pp.solve_deadline_hours IS NOT NULL " +
            "AND TIMESTAMPDIFF(HOUR, COALESCE(pp.problem_solved_time, NOW()), pp.solve_deadline_time) >= 0"
          );
          break;
        case 'solved':
          havingConditions.push(
            "pp.problem_solved_id IS NOT NULL AND pp.problem_status = '3' AND pp.solve_deadline_hours IS NOT NULL " +
            "AND TIMESTAMPDIFF(HOUR, COALESCE(pp.problem_solved_time, NOW()), pp.solve_deadline_time) >= 0"
          );
          break;
        case 'deadlineSolved':
          havingConditions.push(
            "pp.problem_solved_id IS NOT NULL AND pp.problem_status = '3' AND pp.solve_deadline_hours IS NOT NULL " +
            "AND TIMESTAMPDIFF(HOUR, COALESCE(pp.problem_solved_time, NOW()), pp.solve_deadline_time) < 0"
          );
          break;
        case 'deadlineUnsolved':
          havingConditions.push(
            "pp.problem_status != '3' AND pp.problem_status != '0' AND pp.solve_deadline_hours IS NOT NULL " +
            "AND TIMESTAMPDIFF(HOUR, COALESCE(pp.problem_solved_time, NOW()), pp.solve_deadline_time) < 0"
          );
          break;
      }
    }

    if (havingConditions.length > 0) {
      queryBuilder.having(havingConditions.join(' AND '), havingParams);
    }
  }

  /**
   * 应用排序 - 基于 patrolReportList 方法的完整排序逻辑
   */
  private applySorting(queryBuilder: any, params: any) {

    // 根据排序参数决定排序方式
    if (params.sort) {
      switch (String(params.sort || '')) {
        case '1': // 整改时长升序
          queryBuilder
            .addSelect(
              "CASE WHEN pp.problem_status = '3' THEN TIMESTAMPDIFF(MINUTE, pp.problem_time, ps.time) " +
              "WHEN pp.problem_status = '2' OR pp.problem_status = '1' THEN TIMESTAMPDIFF(MINUTE, pp.problem_time, NOW()) END",
              'solveDuration'
            )
            .orderBy('solveDuration IS NULL')
            .addOrderBy('solveDuration', 'ASC')
            .addOrderBy('pp.problem_time', 'DESC')
            .addOrderBy('pp.project_problem_id', 'DESC');
          break;
        case '2': // 整改时长倒序
          queryBuilder
            .addSelect(
              "CASE WHEN pp.problem_status = '3' THEN TIMESTAMPDIFF(MINUTE, pp.problem_time, ps.time) " +
              "WHEN pp.problem_status = '2' OR pp.problem_status = '1' THEN TIMESTAMPDIFF(MINUTE, pp.problem_time, NOW()) END",
              'solveDuration'
            )
            .orderBy('solveDuration IS NULL')
            .addOrderBy('solveDuration', 'DESC')
            .addOrderBy('pp.problem_time', 'DESC')
            .addOrderBy('pp.project_problem_id', 'DESC');
          break;
        case '3': // 问题时间升序
          queryBuilder
            .orderBy('pp.problem_time', 'ASC')
            .addOrderBy('pp.project_problem_id', 'ASC');
          break;
        case '4': // 问题时间倒序
          queryBuilder
            .orderBy('pp.problem_time', 'DESC')
            .addOrderBy('pp.project_problem_id', 'DESC');
          break;
        default:
          // 默认排序
          queryBuilder
            .orderBy('pp.problem_time', 'DESC')
            .addOrderBy('pp.project_problem_id', 'DESC');
          break;
      }
    } else {
      // 默认按问题时间降序，项目问题ID降序
      queryBuilder
        .orderBy('pp.problem_time', 'DESC')
        .addOrderBy('pp.project_problem_id', 'DESC');
    }
  }
}
