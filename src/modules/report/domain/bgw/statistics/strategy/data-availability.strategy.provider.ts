import { Injectable } from "@nestjs/common";
import { DataAvailabilityStrategyInterface } from "./data-availability.strategy.interface";
import { ProjectViewAvailabilityStrategy } from "./impl/project-view-availability.strategy";
import { ProjectProblemAvailabilityStrategy } from "./impl/project-problem-availability.strategy";
import { ProjectSignAvailabilityStrategy } from "./impl/project-sign-availability.strategy";
import { AiSignAvailabilityStrategy } from "./impl/ai-sign-availability.strategy";
import { ProjectSecurityAvailabilityStrategy } from "./impl/project-security-availability.strategy";
import { ProjectTidinessAvailabilityStrategy } from "./impl/project-tidiness-availability.strategy";
import { LiveSilhouettePatrolAvailabilityStrategy } from "./impl/live-silhouette-patrol.strategy";

@Injectable()
export class DataAvailabilityStrategyProvider {
    constructor(
        private readonly projectViewStrategy: ProjectViewAvailabilityStrategy,
        private readonly projectProblemStrategy: ProjectProblemAvailabilityStrategy,
        private readonly projectSignStrategy: ProjectSignAvailabilityStrategy,
        private readonly aiSignStrategy: AiSignAvailabilityStrategy,
        private readonly projectSecurityStrategy: ProjectSecurityAvailabilityStrategy,
        private readonly projectTidinessStrategy: ProjectTidinessAvailabilityStrategy,
        private readonly liveSilhouettePatrolAvailabilityStrategy: LiveSilhouettePatrolAvailabilityStrategy,

    ) { }

    getStrategy(listType: string): DataAvailabilityStrategyInterface | null {
        switch (listType) {
            case '1'://工地相册
                return this.projectViewStrategy;
            case '2'://工地问题
                return this.projectProblemStrategy;
            case '3'://巡检详情-工地剪影
                return this.liveSilhouettePatrolAvailabilityStrategy;
            case '4'://工地签到
                return this.projectSignStrategy;
            case '5'://智慧签到
                return this.aiSignStrategy;
            case '6'://整洁度
                return this.projectTidinessStrategy;
            case '7'://文明施工
                return this.projectSecurityStrategy;
            default:
                return null;
        }
    }
}