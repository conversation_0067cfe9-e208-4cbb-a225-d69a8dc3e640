import { Injectable } from "@nestjs/common";
import { DataAvailabilityStrategyInterface } from "../data-availability.strategy.interface";
import { DataAvailabilityDao } from "../../dao/data-availability.dao";
import { MyLogger, YqzException } from "@yqz/nest";

@Injectable()
export class LiveSilhouettePatrolAvailabilityStrategy implements DataAvailabilityStrategyInterface {
    private readonly logger = new MyLogger(LiveSilhouettePatrolAvailabilityStrategy.name)

    constructor(
        private readonly dataAvailabilityDao: DataAvailabilityDao,
    ) { }

    /**
     * 巡检列表详情页，工地剪影
     * @param filters 
     * @returns 
     */
    async getDataAvailability(filters: { projectId: string, yqzDeviceId?: string }): Promise<string[]> {
        const { projectId } = filters
        if (!projectId) {
            this.logger.error("error: 巡检列表详情页，工地剪影 必传参数为空")
            return []
        }
        const list = await this.dataAvailabilityDao.searchLiveSilhouettePatrolDateList(filters)
        return list
    }
}