import { Injectable } from "@nestjs/common";
import { DataAvailabilityStrategyInterface } from "../data-availability.strategy.interface";
import { DataAvailabilityDao } from "../../dao/data-availability.dao";
import { YqzException } from "@yqz/nest";
import { Filter<PERSON>hain } from "../../../filter/chain/filter.chain";
import { ProjectFilter } from "../../../filter/handler/abstract/project-filter.handle.abstract";
import { Statistics } from "../../types/statistics.type";
import { ProjectAddressFilter } from "../../../filter/handler/impl/address-project-filter.handler";
import { DeptProjectFilter } from "../../../filter/handler/impl/dept-project-filter.handler";
import * as _ from "lodash";

@Injectable()
export class ProjectSignAvailabilityStrategy implements DataAvailabilityStrategyInterface {

    constructor(
        private readonly dataAvailabilityDao: DataAvailabilityDao,
        private readonly deptProjectFilter: DeptProjectFilter,
        private readonly projectAddressFilter: ProjectAddressFilter,
    ) { }

    async getDataAvailability(filters: Statistics.SignInReq): Promise<string[]> {
        const { companyId } = filters
        if (!companyId) throw new YqzException("必传参数不能为空")
        let list = []
        // 构建工地id筛选器
        const projectChain = new FilterChain<string, Statistics.SignInReq, ProjectFilter<string, Statistics.ProblemReq>>()
        projectChain.addHandler(this.deptProjectFilter)
                    .addHandler(this.projectAddressFilter)
        // 执行筛选器
        filters.projectIds = await projectChain.handle(filters, list)
        if (_.isEmpty(filters.projectIds)) {
            return []
        }
        // 查询全部
        if (filters.projectIds.includes("*")) {
            filters.projectIds = []
        }
        filters.types = ["1","2"] // 签到类型不筛选误判
        return await this.dataAvailabilityDao.searchProjectSignInDateList(filters)
    }
}