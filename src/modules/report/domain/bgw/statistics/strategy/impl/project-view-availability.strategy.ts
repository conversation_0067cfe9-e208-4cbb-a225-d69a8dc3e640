import { Injectable } from '@nestjs/common';
import { DataAvailabilityStrategyInterface } from '../data-availability.strategy.interface';
import { DataAvailabilityDao } from '../../dao/data-availability.dao';
import { format } from 'date-fns';
import { MyLogger, YqzException } from '@yqz/nest';

@Injectable()
export class ProjectViewAvailabilityStrategy implements DataAvailabilityStrategyInterface {
  private readonly logger = new MyLogger(ProjectViewAvailabilityStrategy.name)

  constructor(
    private readonly dateAvailabilityDao: DataAvailabilityDao,
  ) { }

  async getDataAvailability(filters: { companyId: string, projectId: string, photoType?: string, yqzDeviceId?: string, eventType?: string }): Promise<string[]> {
    const { projectId, photoType } = filters
    if (!projectId) {
      this.logger.error("error: 巡检列表详情页，工地剪影 必传参数为空")
      return []
    }
    if (photoType && photoType=== 'all') {
      delete filters.photoType
    }
    return await this.dateAvailabilityDao.searchProjectViewDateList(filters)
  }
}