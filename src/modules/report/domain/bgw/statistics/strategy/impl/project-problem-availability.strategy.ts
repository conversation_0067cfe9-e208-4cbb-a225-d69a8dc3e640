import { Injectable } from "@nestjs/common";
import { DataAvailabilityStrategyInterface } from "../data-availability.strategy.interface";
import { DataAvailabilityDao } from "../../dao/data-availability.dao";
import { DevTimeout, MyLogger, YqzException } from "@yqz/nest";
import { <PERSON><PERSON><PERSON>hain } from "../../../filter/chain/filter.chain";
import { ScopeProjectFilter } from "../../../filter/handler/impl/scope-project-filter.handler";
import { ProjectAddressFilter } from "../../../filter/handler/impl/address-project-filter.handler";
import { DeptProjectFilter } from "../../../filter/handler/impl/dept-project-filter.handler";
import { Statistics } from "../../types/statistics.type";
import * as _ from "lodash";
import { ProjectFilter } from "../../../filter/handler/abstract/project-filter.handle.abstract";

@Injectable()
export class ProjectProblemAvailabilityStrategy implements DataAvailabilityStrategyInterface {
    private readonly logger = new MyLogger(ProjectProblemAvailabilityStrategy.name)

    constructor(
        private readonly dataAvailabilityDao: DataAvailabilityDao,
        private readonly deptProjectFilter: DeptProjectFilter,
        private readonly scopeProjectFilter: ScopeProjectFilter,
        private readonly projectAddressFilter: ProjectAddressFilter
    ) { }

    async getDataAvailability(filters: Statistics.ProblemReq): Promise<string[]> {
        const { companyId, listType } = filters
        if (!companyId) {
            this.logger.error("error: 巡检列表详情页，工地剪影 必传参数为空")
            return []
        }
        let list = []
        // 构建工地id筛选器
        const projectChain = new FilterChain<string, Statistics.ProblemReq, ProjectFilter<string, Statistics.ProblemReq>>()
        projectChain.addHandler(this.deptProjectFilter)
                    .addHandler(this.scopeProjectFilter)
                    .addHandler(this.projectAddressFilter)
        // 执行筛选器
        filters.projectIds = await projectChain.handle(filters, list)
        if (_.isEmpty(filters.projectIds)) {
            return []
        }
        // 查询全部
        if (filters.projectIds.includes("*")) {
            filters.projectIds = []
        }
        if (listType && listType === '2') {//整改日期
            list = await this.dataAvailabilityDao.searchProblemSolvedDateList(filters)
        } else {//问题产生日期
            list = await this.dataAvailabilityDao.searchProjectProblemDateList(filters)
        }
        return list
    }
}