import { IsString, IsObject, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AdjacentIdDto {
  @ApiProperty({
    description: '业务类型',
    example: 'project',
    enum: ['project', 'device', 'manager'] // TODO: 根据实际业务扩展
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: '筛选和排序参数',
    example: {
      sort: 'createTime_desc,id_asc',
      status: 'active',
      companyId: '123' // 会被自动填充
    }
  })
  @IsObject()
  params: any;

  @ApiProperty({
    description: '当前记录ID',
    example: 12345
  })
  @IsNumber()
  currentId: number;
}
