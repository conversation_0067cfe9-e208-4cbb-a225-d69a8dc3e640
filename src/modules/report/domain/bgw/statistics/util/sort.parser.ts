import { YqzException } from '@yqz/nest';

/**
 * 字段白名单映射（前端字段名 -> 数据库字段名）
 * TODO: 根据实际业务需求扩展字段映射
 */
const FIELD_MAPPING: Record<string, string> = {
  id: 'id',
  createTime: 'create_time',
  updateTime: 'update_time',
  name: 'name',
  status: 'status'
};

/**
 * 允许的排序方向
 */
const ALLOWED_DIRECTIONS = ['asc', 'desc'];

/**
 * 把 "createTime_desc,id_asc" 解析为
 *   { orderBy: 't.create_time DESC, t.id ASC' }
 */
export function parseSort(sort: string, alias = 't'): { orderBy: string } {
  if (!sort || sort.trim() === '') {
    return { orderBy: `${alias}.id ASC` }; // 默认按ID升序
  }

  try {
    const sortItems = sort.split(',').map(item => item.trim()).filter(item => item);
    const orderClauses: string[] = [];

    for (const item of sortItems) {
      const parts = item.split('_');
      if (parts.length !== 2) {
        throw new YqzException(`排序格式错误: ${item}，应为 "字段名_方向"`);
      }

      const [field, direction] = parts;

      // 验证字段名
      if (!FIELD_MAPPING[field]) {
        throw new YqzException(`不支持的排序字段: ${field}`);
      }

      // 验证排序方向
      if (!ALLOWED_DIRECTIONS.includes(direction.toLowerCase())) {
        throw new YqzException(`不支持的排序方向: ${direction}，只支持 asc/desc`);
      }

      const dbField = FIELD_MAPPING[field];
      const dbDirection = direction.toUpperCase();
      orderClauses.push(`${alias}.${dbField} ${dbDirection}`);
    }

    return { orderBy: orderClauses.join(', ') };
  } catch (error) {
    if (error instanceof YqzException) {
      throw error;
    }
    throw new YqzException(`排序解析失败: ${error.message}`);
  }
}

/**
 * 获取支持的排序字段列表
 */
export function getSupportedSortFields(): string[] {
  return Object.keys(FIELD_MAPPING);
}
