import { BaseFilterReq } from "../../filter/types/filter.type";
import { UtilType } from "../../types/util.type";

export namespace Statistics {

    export class ProblemReq implements BaseFilterReq {
        companyId: string;
        listType: string;
        departmentId?: string;
        projectAddress?: string;
        scope?: string;
        personId?: string;
        projectId?: string;
        projectIds?: string[];
    }

    export const CustomerAggregateDataCsvList: UtilType.propertyCsv[] = [
        { key: "companyId", value: "公司id" },
        { key: "companyName", value: "公司名称" },
        { key: "productVersion", value: "版本" },
        { key: "expireDate", value: "版本到期日" },
        { key: "sales", value: "销售" },
        { key: "operators", value: "CSM" },
        { key: "projectNum", value: "在建工地数" },
        { key: "earlyDate", value: "设备最早到期日" },
        { key: "lastDate", value: "设备最晚到期日" },
        { key: "deviceNum", value: "已发货总数" },
        { key: "projectManagerNum", value: "领用总数" },
        { key: "bindProjectNum", value: "绑定工地总数" },
        { key: "onlineNum", value: "在线设备数" },
        { key: "offlineNum", value: "离线设备数" },
        { key: "managerNum", value: "成员数" },
    ]
    export class SignInReq implements BaseFilterReq {
        companyId: string;
        departmentId?: string;
        projectAddress?: string;
        projectId?: string;
        projectIds?: string[];
        types?: string[];
    }
}