import { Body, Controller, Post } from "@nestjs/common";
import { ApiOperation, ApiTags, ApiResponse } from "@nestjs/swagger";
import { DataAvailabilityService } from "./service/data-availability.service";
import { JwtUserGw, MyUser, YqzException } from "@yqz/nest";
import { StatisticsService } from "./service/statistics.service";
import { AdjacentIdDto } from "./dto/adjacent-id.dto";

@ApiTags("统计数据")
@Controller("statistics")
export class StatisticsController {
    constructor(
        private readonly dataAvailabilityService: DataAvailabilityService,
        private readonly statisticsService: StatisticsService,
    ) { }

    @Post("availability-date/list")
    async searchDataAvailability(@Body() body: {type: string, params: any},@MyUser() user: JwtUserGw): Promise<{ date: string[] }> {
        body.params.companyId = user?.targetCompany?.companyId;
        body.params.personId = user?.personId;
        return await this.dataAvailabilityService.getDataAvailability(body.type, body.params);
    }

    @Post("customer-aggregate/list")
    async searchCustomerAggregateData(@Body() body: {format?: string}) {
        const res = await this.statisticsService.searchCustomerAggregateData(body);
        return res
    }

    @Post('adjacent-id')
    @ApiOperation({ summary: '获取相邻记录ID', description: '根据当前记录ID和筛选条件，获取前一个和后一个记录的ID' })
    async getAdjacentId(
        @Body() dto: AdjacentIdDto,
        @MyUser() user: JwtUserGw,
    ) {
        try {
            // 参数验证
            if (!dto.params) {
                dto.params = {};
            }

            // 补公司/人员信息
            dto.params.companyId = user?.targetCompany?.companyId;
            dto.params.personId = user?.personId;

            // 调用 Service
            const result = await this.statisticsService.resolveAdjacentId(dto);

            return result;
        } catch (error) {
            if (error instanceof YqzException) {
                throw error;
            }
            throw new YqzException(`获取相邻ID失败: ${error.message}`);
        }
    }
}