import { Injectable } from "@nestjs/common";
import { ProblemSolvedEntity } from "@src/modules/report/entity/bgw/problem-solved.entity";
import { ProjectProblemEntity } from "@src/modules/report/entity/bgw/project-problem.entity";
import { ProjectSignInEntity } from "@src/modules/report/entity/bgw/project-sign-in.entity";
import { ProjectUpdateEntity } from "@src/modules/report/entity/bgw/project-update.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { MotiondetectEventEntity } from "@src/modules/report/entity/camera/motiondetect-event.entity";
import { ProjectCameraLiveSilhouetteEntity } from "@src/modules/report/entity/camera/project-camera-live-silhouette.entity";
import { DeleteFlag } from "@src/types/delete-flag.enum";
import * as _ from "lodash";
import { DataSource } from "typeorm";
import { Statistics } from "../types/statistics.type";
import { DevTimeout } from "@yqz/nest";

@Injectable()
export class DataAvailabilityDao {

    constructor(
        private dataSource: DataSource
    ) { }

    /**
     * 工地相册日期
     * @param params 
     */
    async searchProjectViewDateList(params: { companyId: string, projectId: string, photoType?: string, yqzDeviceId?: string, eventType?: string }): Promise<string[]> {
        const { companyId, projectId, photoType, yqzDeviceId, eventType } = params
        const e = this.dataSource.getRepository(MotiondetectEventEntity)
            .createQueryBuilder('e')
            .select([
                'e.event_time time',
                '"3" type'
            ])
            .where('e.company_id = :companyId', { companyId })
            .andWhere('e.project_id = :projectId', { projectId })
            .andWhere('e.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
        if (yqzDeviceId) {
            e.andWhere('e.yqz_device_id = :yqzDeviceId', { yqzDeviceId })
        }
        if (eventType) {
            e.andWhere('e.event_type = :eventType', { eventType })
        }
        const cls = this.dataSource.getRepository(ProjectCameraLiveSilhouetteEntity)
            .createQueryBuilder('cls')
            .select([
                'cls.generate_time time',
                '"1" type'
            ])
            .where('cls.project_id = :projectId', { projectId })
            .andWhere('cls.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
        if (yqzDeviceId) {
            cls.andWhere('cls.yqz_device_id = :yqzDeviceId', { yqzDeviceId })
        }
        const u = this.dataSource.getRepository(ProjectUpdateEntity)
            .createQueryBuilder('pu')
            .select([
                'pu.project_update_time time',
                '"2" type'
            ])
            .where('pu.project_id = :projectId', { projectId })
            .andWhere('pu.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            .andWhere('project_update_type = "12" and project_update_media_count > 0')
        const sql = e.getQuery() + ' UNION ALL ' + cls.getQuery() + ' UNION ALL ' + u.getQuery()
        const qb = this.dataSource.manager.createQueryBuilder()
            .select("date_format(tt.time, '%Y-%m-%d') time")
            .from("(" + sql + ")", "tt")
        if (photoType) {
            qb.where("tt.type = :photoType", { photoType })
        }
        qb.groupBy("date(tt.time)")
        qb.orderBy("tt.time", "DESC")
        qb.setParameters(e.getParameters())
        qb.setParameters(cls.getParameters())
        qb.setParameters(u.getParameters())
        const res = await qb.getRawMany()
        return res.map(item => item.time)
    }

    /**
     * 问题产生日期
     * @param params 
     */
    async searchProjectProblemDateList(params: { companyId: string, listType: string, projectId?: string, projectIds?: string[], departmentIds?: string[] }): Promise<string[]> {
        const { companyId, projectId, projectIds, departmentIds } = params
        const qb = this.dataSource.getRepository(ProjectProblemEntity)
            .createQueryBuilder('pp')
            .select([
                'date_format(pp.problem_time, "%Y-%m-%d") time',
            ])
            .leftJoin(ProjectEntity, 'p', 'pp.project_id = p.project_id')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id and ps.delete_flag = "N"')
            .where('p.company_id = :companyId', { companyId })
            .andWhere('pp.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            .andWhere('p.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
        if (projectId) {
            qb.andWhere('p.project_id = :projectId', { projectId })
        }
        if (!_.isEmpty(projectIds)) {
            qb.andWhere('p.project_id in (:projectIds)', { projectIds })
        }
        if (!_.isEmpty(departmentIds)) {
            qb.andWhere('p.department_id in (:departmentIds)', { departmentIds })
        }
        qb.groupBy('date(pp.problem_time)')
        qb.orderBy('pp.problem_time', 'DESC')
        const res = await qb.getRawMany()
        return res.map(item => item.time)
    }

    /**
     * 问题整改日期
     * @param params 
     */
    async searchProblemSolvedDateList(params: { companyId: string, listType: string, projectId?: string, projectIds?: string[], departmentIds?: string[] }): Promise<string[]> {
        const { companyId, projectId, projectIds, departmentIds } = params
        const qb = this.dataSource.getRepository(ProjectProblemEntity)
            .createQueryBuilder('pp')
            .select([
                'date_format(ps.time, "%Y-%m-%d") time',
            ])
            .leftJoin(ProjectEntity, 'p', 'pp.project_id = p.project_id')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('p.company_id = :companyId', { companyId })
            .andWhere('pp.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            .andWhere('p.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            .andWhere('ps.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
        if (projectId) {
            qb.andWhere('p.project_id = :projectId', { projectId })
        }
        if (!_.isEmpty(projectIds)) {
            qb.andWhere('p.project_id in (:projectIds)', { projectIds })
        }
        if (!_.isEmpty(departmentIds)) {
            qb.andWhere('p.department_id in (:departmentIds)', { departmentIds })
        }
        qb.groupBy('date(ps.time)')
        qb.orderBy('ps.time', 'DESC')
        const res = await qb.getRawMany()
        return res.map(item => item.time)
    }

    /**
     * 工地签到日期
     * @param params 
     */
    async searchProjectSignInDateList(params: Statistics.SignInReq): Promise<string[]> {
        const { companyId, projectId, projectIds, types } = params
        const qb = this.dataSource.getRepository(ProjectSignInEntity)
            .createQueryBuilder('psi')
            .select([
                "date_format(psi.sign_in_date, '%Y-%m-%d') time",
            ])
            .leftJoin(ProjectEntity, 'p', 'psi.project_id = p.project_id')
            .where('p.company_id = :companyId', { companyId })
            .andWhere('psi.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            // .andWhere('p.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
            // .andWhere('psi.type in ("1","2")')//不筛选误判
        if (projectId) {
            qb.andWhere('psi.project_id = :projectId', { projectId })
        }
        if (!_.isEmpty(types)) {
            qb.andWhere('psi.type in (:...types)', { types })
        }
        if (!_.isEmpty(projectIds)) {
            qb.andWhere('p.project_id in (:...projectIds)', { projectIds })
        }
        qb.groupBy('psi.sign_in_date')
        qb.orderBy('psi.sign_in_time', 'DESC')
        const res = await qb.getRawMany()
        return res.map(item => item.time)
    }

    /**
     * 巡检详情-工地剪影日期
     * @param params 
     */
    async searchLiveSilhouettePatrolDateList(params: { projectId: string, yqzDeviceId?: string }): Promise<string[]> {
        const { projectId, yqzDeviceId } = params
        const qb = this.dataSource.getRepository(ProjectCameraLiveSilhouetteEntity)
            .createQueryBuilder('cls')
            .select([
                'date_format(cls.generate_time, "%Y-%m-%d") time',
            ])
            .where('cls.project_id = :projectId', { projectId })
            .andWhere('cls.delete_flag = :deleteFlag', { deleteFlag: DeleteFlag.N })
        if (yqzDeviceId) {
            qb.andWhere('cls.yqz_device_id = :yqzDeviceId', { yqzDeviceId })
        }
        qb.groupBy('date(cls.generate_time)')
        qb.orderBy('cls.generate_time', 'DESC')
        const res = await qb.getRawMany()
        return res.map(item => item.time)
    }

}