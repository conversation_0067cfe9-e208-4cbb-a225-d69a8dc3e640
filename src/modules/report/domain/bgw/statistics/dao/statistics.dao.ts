import { Injectable } from "@nestjs/common";
import { CompanyDecorationEntity } from "@src/modules/report/entity/bgw/company-decoration.entity";
import { CompanyProductVersionEntity } from "@src/modules/report/entity/bgw/company-product-version.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DeviceStatusEntity } from "@src/modules/report/entity/camera/device-status.entity";
import { DataSource } from "typeorm";

@Injectable()
export class StatisticsDao {

    constructor(
        private dataSource: DataSource
    ) { }

    async searchCustomerAggregateData() {
        const projectNum = this.dataSource.getRepository(ProjectEntity)
            .createQueryBuilder("p")
            .select("COUNT(1)")
            .where("p.company_id = c.COMPANY_ID and p.delete_flag = 'N' and p.PROJECT_STATUS = 'N'")
        const managerNum = this.dataSource.getRepository(ManagerEntity)
            .createQueryBuilder("m")
            .select("COUNT(1)")
            .where("m.company_id = c.COMPANY_ID and m.delete_flag = 'N' and m.manager_type not in ('5')")
        const qb = this.dataSource.getRepository(DeviceStatusEntity)
            .createQueryBuilder("ds")
            .select([
                "ds.COMPANY_ID companyId",
                "c.COMPANY_NAME companyName",
                "case when cpv.product_version = 1 then '试用版' when cpv.product_version = 2 then '专业版' when cpv.product_version = 3 then '高级版' end productVersion",
                "date_format(cpv.expire_time, '%Y-%m-%d') expireDate,cd.SALES sales,cd.OPERATORS operators",
                "(" + projectNum.getQuery() + ") projectNum",
                "date_format(min(ds.end_time), '%Y-%m-%d') earlyDate",
                "date_format(max(ds.end_time), '%Y-%m-%d') lastDate",
                "count(ds.yqz_device_id) deviceNum",
                "SUM(CASE WHEN ds.project_manager_id IS NOT NULL THEN 1 ELSE 0 END) projectManagerNum",
                "SUM(CASE WHEN ds.project_id IS NOT NULL THEN 1 ELSE 0 END) bindProjectNum",
                "SUM(ds.is_online) onlineNum",
                "count(ds.yqz_device_id) - SUM(ds.is_online) offlineNum",
                "(" + managerNum.getQuery() + ") managerNum"
            ])
            .innerJoin(CompanyEntity, "c", "c.COMPANY_ID = ds.COMPANY_ID")
            .leftJoin(CompanyDecorationEntity, "cd", "ds.COMPANY_ID = cd.COMPANY_ID")
            .leftJoin(CompanyProductVersionEntity, "cpv", "cpv.company_decoration_id = cd.COMPANY_DECORATION_ID")
            .where("ds.delete_flag = 'N'")
            .groupBy("ds.company_id")
        const result = await qb.getRawMany()
        return result
    }

}