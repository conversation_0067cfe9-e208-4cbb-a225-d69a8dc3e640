import { Injectable } from "@nestjs/common";
import { StatisticsDao } from "../dao/statistics.dao";
import { CheckFormatCsv, YqzException } from "@yqz/nest";
import { Statistics } from "../types/statistics.type";
import { AdjacentIdDto } from "../dto/adjacent-id.dto";
import { AdjacentIdStrategyProvider } from "../strategy/pre-next-id/adjacent-id.strategy.provider";

@Injectable()
export class StatisticsService {
    constructor(
        private readonly statisticsDao: StatisticsDao,
        private readonly adjacentIdStrategyProvider: AdjacentIdStrategyProvider
    ) { }

    @CheckFormatCsv(Statistics.CustomerAggregateDataCsvList)
    async searchCustomerAggregateData(params: {format?: string}) {
        const items = await this.statisticsDao.searchCustomerAggregateData();
        return {
            items
        }
    }

    async resolveAdjacentId(dto: AdjacentIdDto): Promise<{preId: number | null, nextId: number | null}> {
        const { type, params, currentId } = dto;

        // 参数验证
        if (!type || typeof type !== 'string') {
            throw new YqzException('业务类型不能为空');
        }

        if (!currentId || typeof currentId !== 'number') {
            throw new YqzException('当前记录ID不能为空且必须为数字');
        }

        if (!params || typeof params !== 'object') {
            throw new YqzException('查询参数不能为空');
        }

        try {
            // 根据类型获取对应的策略
            const strategy = this.adjacentIdStrategyProvider.getStrategy(type);

            // 通过策略获取ID列表
            const idList = await strategy.getIdList(params);

            // 在ID列表中查找相邻ID
            const result = this.findAdjacentIds(idList, currentId);

            return result;
        } catch (error) {
            if (error instanceof YqzException) {
                throw error;
            }
            throw new YqzException(`获取相邻ID失败: ${error.message}`);
        }
    }

    /**
     * 在ID列表中查找相邻ID
     * @param idList ID列表（已按指定顺序排列）
     * @param currentId 当前记录ID
     * @returns 前后ID结果
     */
    private findAdjacentIds(
        idList: number[],
        currentId: number
    ): {preId: number | null, nextId: number | null} {
        // 查找当前ID在列表中的索引
        const currentIndex = idList.indexOf(currentId);

        if (currentIndex === -1) {
            // throw new YqzException('CURRENT_NOT_FOUND');
            return { preId: null, nextId: null };
        }

        // 获取前一个ID
        const preId = currentIndex > 0 ? idList[currentIndex - 1] : null;

        // 获取后一个ID
        const nextId = currentIndex < idList.length - 1 ? idList[currentIndex + 1] : null;

        return { preId, nextId };
    }
}