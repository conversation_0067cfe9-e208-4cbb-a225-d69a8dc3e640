import { Injectable } from '@nestjs/common';
import { DataAvailabilityStrategyInterface } from '../strategy/data-availability.strategy.interface';
import { YqzException } from '@yqz/nest';
import { DataAvailabilityStrategyProvider } from '../strategy/data-availability.strategy.provider';

@Injectable()
export class DataAvailabilityService {
    constructor(
        private readonly strategyProvider: DataAvailabilityStrategyProvider
    ) { }

    /**
     * 类型列表查询有数据的日期
     * @param type 类型
     * @param filters 筛选条件
     * @param dateRange 时间范围
     * @returns date
     */
    async getDataAvailability(type: string, params: any): Promise<{date: string[]}> {
        const strategy: DataAvailabilityStrategyInterface = this.strategyProvider.getStrategy(type);

        if (!strategy) {
            throw new YqzException(`List type ${type} is not supported.`);
        }

        const list = await strategy.getDataAvailability(params);
        return {
            date: list?.filter(item => item && item !== null && item !== '') // 过滤出有数据的日期
        };
    }
}