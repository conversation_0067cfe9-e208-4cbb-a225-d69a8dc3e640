import { ApiProperty } from "@nestjs/swagger";
import { Media } from "@yqz/nest";

//用户默认头像
export enum DefaultUrl {
  UserProfilePicture = "https://oss.1qizhuang.com/image/app/default-person.png"
}

export class YqzMedia {
  @ApiProperty({ description: 'id' })
  mediaId: string;
  @ApiProperty({ description: '类型', enum: Media.YqzMediaType })
  mediaType: Media.YqzMediaType;
  @ApiProperty({ description: '封面' })
  mediaUri: string; //封面
  @ApiProperty({ description: '资源地址' })
  mediaResourceUri: string; //资源地址
  @ApiProperty()
  creationDate: string;
  @ApiProperty()
  address: string;
  @ApiProperty({ description: '描述' })
  description?: string;//描述
  constructor({ id, type, cover, resourceUrl, creationDate, address, description }: { id: string, type: any, cover?: string, resourceUrl?: string, creationDate?: string, address?: string, description?: string }) {
    this.mediaId = id;
    this.mediaType = type;
    this.mediaUri = cover;
    this.mediaResourceUri = resourceUrl || cover;
    this.creationDate = creationDate;
    this.address = address;
    this.description = description;
  }

}