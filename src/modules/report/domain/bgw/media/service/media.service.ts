import { Injectable } from "@nestjs/common";
import * as _ from "lodash";
import { YqzMedia } from "../type/media.type";
import { YqzMediaType } from "@yqz/nest/lib/types/media.type";
import { CmsPhotoDao } from "../../common/cms-photo.dao";
import { formatDate } from "@src/util/datetime.util";

@Injectable()
export class MediaService {
    constructor(
        private readonly cmsPhotoDao: CmsPhotoDao,
    ) { }

    /**
     * 查询图片信息 范式化(key:mediaId,value:YqzMedia)
     * @param param0 
     * @returns 
     */
    async searchParadigm({ mediaIdList }: { mediaIdList: string[] }) {
        if (_.isEmpty(mediaIdList)) return {};
        const list = await this.search({ mediaIdList });
        return _.keyBy(list, 'mediaId');
    }

    /**
     * 查询图片信息
     * @param param0 
     * @returns 
     */
    async search({ mediaIdList }: { mediaIdList: string[] }): Promise<YqzMedia[]> {
        const entities = await this.cmsPhotoDao.searchList({ photoIdList: mediaIdList })
        return entities.map(e => {
            const media = new YqzMedia({
                id: e.photoId,
                type: e.mediaType,
                cover: e.ossUrl,
                resourceUrl: (e.mediaType == YqzMediaType.INTERNAL_PANORAMA ? e?.resourceUrl : e?.mediaResourceUrl) || e.ossUrl,
                creationDate: formatDate(e.creationDate || e.publishTime),
                address: e.address
            });
            return media
        })
    }
}