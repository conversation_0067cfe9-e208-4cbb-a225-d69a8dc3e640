import { Body, Controller, Post } from "@nestjs/common";
import { Api<PERSON>earerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { CheckFormatCsv, JwtUserGw, MyApiOkResponse, MyUser, TransformInterceptor } from "@yqz/nest";
import { ProjectSignUpdateService } from "./service/project-sign-update.service";
import { ProjectSignUpdate } from "./dto/project-sign-update.dto";

/**
 * 工地看板 - 工地更新
 */
@ApiBearerAuth()
@Controller("project-sign/update")
export class ProjectSignUpdateController {
    constructor(
        private readonly projectSignUpdateService: ProjectSignUpdateService,
    ) { }

    @ApiTags("工地看板 - 工地更新")
    @ApiOperation({ summary: '工地看板更新统计' })
    @Post("statistics")
    @MyApiOkResponse(ProjectSignUpdate.StatisticsRes)
    async projectUpdateStatistics(@Body() body: ProjectSignUpdate.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignUpdateService.projectUpdateStatistics(body);
    }

    @ApiTags("工地看板 - 工地更新")
    @ApiOperation({ summary: '工地看板更新人分布' })
    @Post("manager/group")
    @CheckFormatCsv(ProjectSignUpdate.ManagerGroupCsv)
    async projectUpdateManagerGroup(@Body() body: ProjectSignUpdate.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignUpdateService.projectUpdateManagerGroup(body);
    }

    @ApiTags("工地看板 - 工地更新")
    @ApiOperation({ summary: '工地看板更新阶段分布' })
    @Post("stage/group")
    async projectUpdateStageGroup(@Body() body: ProjectSignUpdate.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignUpdateService.projectUpdateStageGroup(body);
    }

    @ApiTags("工地看板 - 工地更新")
    @ApiOperation({ summary: '工地看板更新列表' })
    @Post("list")
    @CheckFormatCsv(ProjectSignUpdate.ListCsv)
    async projectUpdateList(@Body() body: ProjectSignUpdate.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignUpdateService.projectUpdateList(body);
    }

    @ApiTags("工地看板 - 工地更新")
    @ApiOperation({ summary: '工地看板更新列表筛选项' })
    @Post("list/options")
    async projectUpdateListOptions(@Body() body: ProjectSignUpdate.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignUpdateService.projectUpdateListOptions(body);
    }

}