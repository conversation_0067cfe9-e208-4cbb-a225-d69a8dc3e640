import { Injectable } from '@nestjs/common';
import { ProjectDao } from '../dao/project.dao';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DevTimeout, MyL<PERSON><PERSON>, YqzException } from '@yqz/nest';
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { CsvUtil } from '@src/util/csv.util';
import { ManagerDao } from '../../manager/dao/manager.dao';
import { Project } from '../dto/project.dto';
import { IdentityType } from '@src/modules/report/mongodb/camera/body.collection';
import { intervalToDuration } from 'date-fns';
import { ProjectInactiveRecordDao } from '../../../camera/project-inactive-record/dao/project-inactive-record.dao';
import { NodeOverdueEventDao } from '../../node-overdue-event/dao/node-overdue-event.dao';
import { ProjectProblemDao } from '../../project-problem/dao/project-problem.dao';
import { DataReportConfigService } from '../../../corp/data-report/service/data-report-config.service';
import { DateReport } from '../../../corp/data-report/dto/data-report.dto';

@Injectable()
export class ProjectService {
    private readonly logger = new MyLogger(ProjectService.name)
    constructor(
        private readonly projectDao: ProjectDao,
        private readonly managerDao: ManagerDao,
        private readonly projectInactiveRecordDao: ProjectInactiveRecordDao,
        private readonly nodeOverdueEventDao: NodeOverdueEventDao,
        private readonly projectProblemDao: ProjectProblemDao,
        private readonly dataReportConfigService: DataReportConfigService,
    ) { };

    // @DevTimeout(100)
    async test() {
        // this.getProjectIdsByAddress({ projectAddress: '333', projectIds: ['58149'] }).then(res => console.log(res))
        // const address = await this.getProjectAddressByProjectIds({ projectIds: ['58149'] });
        // const res = await this.projectDao.getSignedPersonCount({ companyId: '65', projectId: '58149' });
        // console.log(res)
        // const res2 = await this.projectDao.getSignedPersonList({ companyId: '65', projectId: '58149' });
        // console.log(res2)
        // const res3 = await this.projectDao.getSignedList({ companyId: '65', projectId: '58149', pageNo:5, pageSize:2 });
        // console.log(res3)

        //处理手动签到数据
        // const signList = await this.projectDao.getSignInList({ type: '1' });
        // const bodyIdList = signList.map(sign => `s${sign.projectSignInId}-1-1`);
        // const bodyList = await MyMongoDb.collections.camera.body.find({ bodyId: { $in: bodyIdList } }).toArray();
        // const bodyIds = [];
        // bodyList.map(res => bodyIds.push(res.bodyId));
        // console.log(bodyIds);
        // const saveList = [];
        // let num = 0;
        // for (const sign of signList) {
        //     num++;
        //     console.log(`总计： ${signList.length}, 剩余：${signList.length - num}, 需插入：${saveList.length}`);
        //     if (bodyIds.includes(`s${sign.projectSignInId}-1-1`)) continue;
        //     saveList.push({
        //         companyId: String(sign.companyId),
        //         projectId: String(sign.projectId),
        //         faceEntityId: String(sign.managerId),
        //         eventId: String(sign.projectSignInId),
        //         eventTime: new Date(sign.signInTime),
        //         tags: sign.roleName,
        //         trustedIdentity: true,
        //         faceIdentifyType: IdentityType.LBS,
        //         createAt: new Date(),
        //         bodyId: `s${sign.projectSignInId}-1-1`
        //     });
        // }
        // if (0 === saveList.length) return;
        /** 每次最多 1000 条 批量插入数据 */
        // if (saveList.length > 0) {
        //     const batchSize = 1000;
        //     for (let i = 0; i < saveList.length; i += batchSize) {
        //         const endIndex = Math.min(i + batchSize, saveList.length);
        //         const bodyDocs = saveList.slice(i, endIndex);
        //         await MyMongoDb.collections.camera.body.insertMany(bodyDocs);
        //     }
        // }
        // console.log(saveList.map(res => res.bodyId).join(','));
        // const res = await this.searchProjectInfoMap({ projectIds: ['58149'], businessCategory: 'construction' });
        // console.log("🚀 ~ ProjectService ~ test ~ res:", res)
    }

    async searchProjectInfoMap({ projectIds, businessCategory = 'home_decor' }: { projectIds: string[], businessCategory: 'home_decor' | 'construction' }) {
        if (_.isEmpty(projectIds)) return {};
        let projectInfos = [];
        if (businessCategory === 'home_decor') projectInfos = await this.projectDao.searchHomeDecorProjectInfo({ projectIds });
        if (businessCategory === 'construction') projectInfos = await this.projectDao.searchConstructionProjectInfo({ projectIds });
        const result = {};
        projectInfos.map(res => { result[res.projectId] = res; });
        return result;
    }

    async searchInactiveInfoMap({ projectIds, startTime, endTime }: { projectIds: string[], startTime: string, endTime: string }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime) return {};
        const list = await this.projectInactiveRecordDao.searchInactiveProjectList({ projectIds, startTime, endTime });
        const result = {};
        list.map(res => { result[res.projectId] = res; });
        return result;
    }

    async searchOverdueInfoMap({ projectIds, startTime, endTime, companyId }: { projectIds: string[], startTime: string, endTime: string, companyId: string }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime) return {};
        //查询逾期配置信息(逾期工地)
        const overdueConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        const delayOverdue = overdueConfig?.configs[0]?.delayOverdue;
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const delayOverdue = matterSet?.isNodeDelayOverdue;
        const list = await this.nodeOverdueEventDao.searchProjectOverdueInfo({ projectIds, startTime, endTime, delayOverdue, overdueTime: '1' });
        const result: { [key: string]: { projectId: string, overdueTime: string } } = {};
        list.map(res => { result[res.projectId] = res; });
        return result;
    }

    async searchProblemInfoMap({ projectIds, startTime, endTime }: { projectIds: string[], startTime: string, endTime: string }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime) return {};
        const list = await this.projectProblemDao.searchProblemProjectList({ projectIds, startTime, endTime });
        const result = {};
        list.map(res => { result[res.projectId] = res; });
        return result;
    }

    async getOwnerFocusProjectIds({ companyId, projectIds, startTime, endTime }: { companyId: string, startTime: string, endTime: string, projectIds: string[] }): Promise<string[]> {
        if (!companyId || _.isEmpty(projectIds) || !startTime || !endTime) return [];
        const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Project });
        const ownerFocusProjectScope = config?.configs[0]?.ownerFocusProjectScope as 'one_of' | 'all';
        const ownerFocusProjectOneDay = config?.configs[0]?.ownerFocusProjectOneDay;
        const ownerFocusProjectTime = config?.configs[0]?.ownerFocusProjectTime;
        if (!ownerFocusProjectScope || !ownerFocusProjectOneDay || !ownerFocusProjectTime) return [];
        const promiseList = [];
        //查询单日内查看直播超过N次的工地idList
        promiseList.push(this.projectDao.searchProjectIdListByOwnerFocusOneDay({ projectIds, ownerFocusProjectOneDay, startTime, endTime }));
        //查询所选时间段内查看直播超过N次的工地idList
        promiseList.push(this.projectDao.searchProjectIdListByOwnerFocusTime({ projectIds, ownerFocusProjectTime, startTime, endTime }));
        const [ownerFocusOneDayProjectIds, ownerFocusTimeProjectIds] = await Promise.all(promiseList);
        //取交集
        if (ownerFocusProjectScope === 'all') return _.uniq(_.intersection(ownerFocusOneDayProjectIds, ownerFocusTimeProjectIds));
        //取并集
        if (ownerFocusProjectScope === 'one_of') return _.uniq(_.union(ownerFocusOneDayProjectIds, ownerFocusTimeProjectIds));
        return [];
    }

    async getOwnerNegativeReviewProjectInfoMap({ companyId, projectIds, startTime, endTime }: { companyId: string, startTime: string, endTime: string, projectIds: string[] }): Promise<{ [projectId: string]: string[] }> {
        if (!companyId || _.isEmpty(projectIds) || !startTime || !endTime) return {};
        const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Evaluate });
        const badEvaluationScope = config?.configs[0]?.badEvaluationScope as 'one_of' | 'all';
        const overallScore = config?.configs[0]?.overallScore;
        const individualScore = config?.configs[0]?.individualScore;
        if (!badEvaluationScope || !overallScore || !individualScore) return {};
        //综合评分差评工地idList
        const overallScoreProjectRes = await this.projectDao.searchProjectListByOverallScore({ projectIds, overallScore, startTime, endTime });
        const overallScoreProjectMap = {};
        const overallScoreProjectIds = [];
        overallScoreProjectRes.map(res => {
            overallScoreProjectIds.push(res.projectId);
            overallScoreProjectMap[res.projectId] = (res?.evaluateIds || "").split(',');
        });
        //单项评分差评工地idList
        const individualScoreProjectRes = await this.projectDao.searchProjectListByIndividualScore({ projectIds, individualScore, startTime, endTime });
        const individualScoreProjectMap = {};
        const individualScoreProjectIds = [];
        individualScoreProjectRes.map(res => {
            individualScoreProjectIds.push(res.projectId);
            individualScoreProjectMap[res.projectId] = (res?.evaluateIds || "").split(',');
        });
        let badEvaluationProjectIds = [];
        //取交集
        if (badEvaluationScope === 'all') badEvaluationProjectIds = _.uniq(_.intersection(overallScoreProjectIds, individualScoreProjectIds));
        //取并集
        if (badEvaluationScope === 'one_of') badEvaluationProjectIds = _.uniq(_.union(overallScoreProjectIds, individualScoreProjectIds));
        if (_.isEmpty(badEvaluationProjectIds)) return {};
        const projectMap = {};
        for (const badEvaluationProjectId of badEvaluationProjectIds) {
            //初始化
            if (!projectMap[badEvaluationProjectId]) projectMap[badEvaluationProjectId] = [];
            //综合评分差评
            if (overallScoreProjectMap[badEvaluationProjectId]) {
                projectMap[badEvaluationProjectId].push(...overallScoreProjectMap[badEvaluationProjectId]);
            }
            //单项评分差评
            if (individualScoreProjectMap[badEvaluationProjectId]) {
                projectMap[badEvaluationProjectId].push(...individualScoreProjectMap[badEvaluationProjectId]);
            }
            //去重
            projectMap[badEvaluationProjectId] = _.uniq(projectMap[badEvaluationProjectId]);
        }
        return projectMap;
    }

    /**
     * 根据工地id获取工地地址
     * @param param0 
     */
    async getProjectAddressByProjectIds({ projectIds }: { projectIds: string[] }) {
        const result = {}
        projectIds = projectIds.filter(res => res);
        if (_.isEmpty(projectIds)) return result;
        const list = await this.projectDao.addressSearch({ projectIdList: projectIds });
        list.map(res => {
            result[res.projectId] = res.projectAddress;
        })
        return result;
    }

    /**
     * 根据工地地址筛选对应的工地id list
     * @param param0 
     * @returns 
     */
    async getProjectIdsByAddress({ projectAddress, projectIds }: { projectAddress: string, projectIds: string[] }) {
        if (_.isEmpty(projectIds)) return [];
        const list = await this.projectDao.getProjectIds({ projectAddress, projectIds });
        return list.map(res => res.projectId);
    }

    /**
     * 根据工地地址筛选对应的工地id list
     * @param param0 
     * @returns 
     */
    async getProjectIdListByAddress({ projectAddress, projectIds, companyId }: { companyId: string, projectAddress?: string, projectIds?: string[] }) {
        if (!companyId) return [];
        const list = await this.projectDao.getProjectIdsByCompanyId({ projectAddress, projectIds, companyId });
        return list.map(res => res.projectId);
    }


    /**
     * 成员工地签到统计 & 成员签到天数排名列表
     * @param params.companyId - 公司id.
     * @param params.projectId - 工地id.
     * @param params.startDate - 开始日期 (optional).
     * @param params.endDate - 截止日期,需要和开始日期同时出现(optional).
     * @param params.pageSize
     * @param params.pageNo
     * @returns
     */
    async getProjectMemberSignInCount(params: Project.ProjectMemberSignInReq): Promise<{ managerCount: number, dateCount: number, total: number, list: Project.SignInManagerGroupRes[] }> {
        const { companyId, projectId, startDate, endDate, pageSize, pageNo } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        const result = { managerCount: 0, dateCount: 0, total: 0, list: [] };
        //签到统计
        const countList = await this.projectDao.getSignedPersonCount({ companyId, projectId, startDate, endDate });
        result.dateCount = countList[0]?.dateCount || 0;
        result.managerCount = countList[0]?.managerCount || 0;
        //查询body签到数据
        const bodyList = await this.projectDao.getSignedPersonList({ companyId, projectId, startDate, endDate });
        const bodyResList = _.orderBy(bodyList, ['signInCount'], 'desc');
        const bodyIdList = bodyResList.map(res => res.faceEntityId);
        //查询工地成员（排除掉body中有签到manager）
        const memberList = await this.projectDao.getProjectMemberList({ companyId, projectId, excludeManager: bodyIdList });
        //查询body的签到人昵称、岗位、头像等信息
        const bodyManagerMap: { [key: string]: { id: string, name: string, roleName: string, avatarUrl: string } } = {};
        if (!_.isEmpty(bodyIdList)) {
            const managerList = await this.managerDao.searchManagerList({ managerIdList: bodyIdList, includeDel: true });
            (managerList || []).map(res => { return bodyManagerMap[res.id] = res; });
        }
        const list: Project.SignInManagerGroupRes[] = [];
        let ranking = 0; //初始排名
        for (const body of bodyResList) { //组装body数据
            const managerInfo = bodyManagerMap[body?.faceEntityId];
            ranking++;//处理排名
            list.push({
                managerId: managerInfo?.id || '',
                nickName: managerInfo?.name || '',
                roleName: managerInfo?.roleName || '',
                signInCount: String(body?.signInCount || 0),
                avatarUrl: managerInfo?.avatarUrl || '',
                ranking: String(ranking),
                signInDays: body?.signInDays || [],
            });
        }
        for (const member of memberList) {  //组装工地成员数据
            ranking++;//处理排名
            list.push({
                managerId: member?.managerId,
                nickName: member?.nickName,
                roleName: member?.roleName,
                signInCount: member?.signInCount || '0',
                avatarUrl: member?.avatarUrl,
                ranking: String(ranking),
                signInDays: []
            });
        }
        result.total = list.length;//总数
        result.list = (pageSize && pageNo) ? list.splice((pageNo - 1) * pageSize, pageSize) : list;//分页
        return result;
    }

    /**
     * 成员工地签到列表
     * @param params 
     */
    async projectMemberSignInList(params: Project.ProjectMemberSignInReq): Promise<{ total: number, items: Project.SignInRes[] }> {
        const { companyId, projectId, startDate, endDate, managerId, pageSize, pageNo } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        const result = { total: 0, items: [] };
        const bodyList = await this.projectDao.getSignedList({ companyId, projectId, startDate, endDate, managerId, pageSize, pageNo });
        const bodyIdList = bodyList?.items?.map(res => res.faceEntityId);
        //查询body的签到人昵称、岗位、头像等信息
        const bodyManagerMap: { [key: string]: { id: string, name: string, roleName: string, avatarUrl: string, departmentName: string } } = {};
        if (!_.isEmpty(bodyIdList)) {
            const managerList = await this.managerDao.searchManagerList({ managerIdList: bodyIdList, includeDel: true });
            (managerList || []).map(res => { return bodyManagerMap[res.id] = res; });
        }
        const items: Project.SignInRes[] = [];
        for (const body of (bodyList?.items || [])) { //组装body数据
            const managerInfo = bodyManagerMap[body?.faceEntityId];
            const signInType = _.isEmpty(body?.faceIdentifyTypes) ? ["摄像头签到"] : [];//ai识人不会有faceIdentifyType，但是属于摄像头签到
            if (!_.isEmpty(body?.faceIdentifyTypes)) {
                body?.faceIdentifyTypes.map(type => {
                    if (type && type === IdentityType.LBS && !signInType.includes("app签到")) {
                        signInType.push("app签到");
                    }
                    if (type && type !== IdentityType.LBS && !signInType.includes("摄像头签到")) {
                        signInType.push("摄像头签到");
                    }
                });
            }
            const duration = intervalToDuration({ start: 0, end: body?.workTime })
            const hours = String(duration.hours).padStart(2, '0');
            const minutes = String(duration.minutes).padStart(2, '0');
            const seconds = String(duration.seconds).padStart(2, '0');
            items.push({
                managerId: managerInfo?.id || '',
                nickName: managerInfo?.name || '',
                roleName: managerInfo?.roleName || '',
                signInTime: body?.signInTime || "",
                avatarUrl: managerInfo?.avatarUrl || '',
                departmentName: managerInfo?.departmentName || '',
                arrivalTime: DateFns.format(body?.arrivalTime, 'HH:mm:ss'),
                leaveTime: DateFns.format(body?.leaveTime, 'HH:mm:ss'),
                workTime: `${hours}小时${minutes}分${seconds}秒`,
                signInType: !_.isEmpty(signInType) ? _.sortBy(signInType).join("+") : ''//固定返回顺序,
            });
        }
        result.total = bodyList.total;
        result.items = items;
        return result;
    }
    async projectMemberSignInListByday(params: Project.ProjectMemberSignInReq) {
        const dateList = await this.projectDao.getSignedDayList(params);
        if (dateList.length === 0) return [];
        const res = await this.projectMemberSignInList({ ...params, startDate: dateList[dateList.length - 1].date, endDate: dateList[0].date, pageNo: undefined, pageSize: undefined })
        const data = dateList.map((date) => {
            return {
                date: date.date,
                list: res.items.filter(item => item.signInTime === date.date)
            }
        })
        return data;
    }
    /**
     * 成员工地签到列表筛选项
     * @param params 
     * @returns 
     */
    async projectMemberSignInListOptions(params: Project.ProjectMemberSignInReq) {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        const bodyList = await this.projectDao.getSignedPersonList({ companyId, projectId });
        const bodyIdList = bodyList.map(res => res.faceEntityId);
        const manager = [];
        //查询body的签到人昵称、岗位、头像等信息
        if (!_.isEmpty(bodyIdList)) {
            const managerList = await this.managerDao.searchManagerList({ managerIdList: bodyIdList, includeDel: true });
            (managerList || []).map(res => { manager.push({ managerId: res?.id, nickName: res?.name }) });
        }
        return { manager };
    }

    // 获取工地今日和总签到数
    async getProjectSignCount(params: { companyId: string, projectId: string }) {
        if (!params?.projectId) throw new YqzException("必传参数不能为空");
        const { companyId, projectId } = params;
        const result = { total: 0, todayCount: 0 };
        //总计签到统计
        const count = await this.projectDao.getSignedPersonCount({ companyId, projectId });
        if (_.isEmpty(count) || (!_.isEmpty(count) && count[0]?.managerCount == 0)) return result;
        result.total = count[0]?.managerCount || 0;
        //今日签到统计
        const date = DateFns.format(new Date(), 'yyyy-MM-dd');
        const todayCount = await this.projectDao.getSignedPersonCount({ companyId, projectId, startDate: date, endDate: date });
        if (_.isEmpty(todayCount) || (!_.isEmpty(todayCount) && todayCount[0]?.managerCount == 0)) return result;
        result.todayCount = todayCount[0]?.managerCount || 0;
        return result;
    }

    /**
   * 获取工地在指定日期内的开工记录
   *
   * @param params.projectId
   * @param params.startDate
   * @param params.endDate
   * @param params.pageSize
   * @param params.pageNo
   * @param params.format - 'csv' (optional).
   * @returns
   */
    async getProjectActiveHistory(params: { projectId: string, startDate?: string, endDate?: string, pageSize?: number, pageNo?: number, format?: string }) {
        if (!params?.projectId) throw new YqzException("必传参数不能为空");
        const res = await this.projectDao.getProjectActiveHistory(params);
        // const res = await this.projectDao.getProjectActiveHistory({projectId: '56626',pageNo:1,pageSize:15});
        if (params?.format === 'csv') {
            return CsvUtil.convert2Csv(res.list.map(item => ({
                '日期': item.time ? item.time.toISOString().split('T')[0] : '',
                '施工状态': '施工',
            })))
        } else {
            const result = {
                total: res.total,
                list: res.list.map(
                    item => ({
                        ...item,
                        date: item.time ? item.time.toISOString().split('T')[0] : null
                    })
                )
            }
            return result
        }

    }
    // 获取工地开工统计
    async getProjectActiveStatistics(params: { projectId: string }) {
        if (!params?.projectId) throw new YqzException("必传参数不能为空");
        return await this.projectDao.getProjectActiveStatistics(params);
    }

    /**
   * 工地整洁度记录
   * 
   * @param params.projectId - The ID of the project.
   * @param params.startDate - The start date for filtering the history (optional).
   * @param params.endDate - The end date for filtering the history (optional).
   * @param params.pageSize - The number of records to retrieve per page (optional).
   * @param params.pageNo
   * @param params.format - 'csv' (optional).
   * 
   */
    async getProjectTidinessHistory(params: { projectId: string, startDate?: string, endDate?: string, pageSize?: number, pageNo?: number, format?: string }) {
        if (!params?.projectId) throw new YqzException("必传参数不能为空");
        const res = await this.projectDao.getProjectTidinessHistory(params);
        // const res = await this.projectDao.getProjectTidnessHistory({projectId:'343766',pageNo:2,pageSize:8});
        if (params?.format === 'csv') {
            const map = {
                1: '优',
                2: '良',
                3: '差',
                4: '未知',
            }
            return CsvUtil.convert2Csv(res.list.map(item => ({
                '日期': item.detectionTime ? item.detectionTime.toISOString().split('T')[0] : null,
                '整洁度': map[item.tidinessType],
                '图片': item.ossPhotoUrl.replace(process.env.OSS_CAMERA_DEFAULT_URL, process.env.OSS_CAMERA_CUSTOM_URL)
            })))
        } else {
            const result = {
                total: res.total,
                list: res.list.map(item => ({
                    ...item,
                    date: item.detectionTime ? item.detectionTime.toISOString().split('T')[0] : null
                }))
            }
            return result;
        }
    }

    // 获取工地整洁度统计
    async getProjectTidinessStatistics(params: { projectId: string }) {
        if (!params?.projectId) throw new YqzException("必传参数不能为空");
        return await this.projectDao.getProjectTidinessStatistics(params);
    }

    /**
     * 工地相册列表
     * @param params 
     * @returns 
     */
    async getProjectAlbumListDetail(params: Project.ProjectAlbumListDetailReq) {
        const { companyId, projectId, yqzDeviceId, photoType, eventType, generateSource, materialType, startTime, endTime, pageNo = 1, pageSize = 50 } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        if (startTime) params.startTime = DateFns.format(new Date(startTime), 'yyyy-MM-dd 00:00:00');
        if (endTime) params.endTime = DateFns.format(new Date(endTime), 'yyyy-MM-dd 23:59:59');
        let albumParams: Project.ProjectAlbumListDetailReq = null;
        //查询全部图片: 开始时间、结束时间、分页
        if (!photoType) albumParams = { companyId, projectId, photoTypes: [Project.PhotoType.CAMERA_IMAGE, Project.PhotoType.LIVE_IMAGE, Project.PhotoType.UPLOAD_IMAGE], startTime: params.startTime, endTime: params.endTime, pageNo, pageSize };
        //查询摄像头图片: 开始时间、结束时间、分页、设备、抓拍类型
        if (photoType == Project.PhotoType.CAMERA_IMAGE) albumParams = { companyId, projectId, photoTypes: [Project.PhotoType.CAMERA_IMAGE], startTime: params.startTime, endTime: params.endTime, pageNo, pageSize, yqzDeviceId, eventType };
        //查询工地剪影: 开始时间、结束时间、分页、设备、剪影类型、文件格式
        if (photoType == Project.PhotoType.LIVE_IMAGE) albumParams = { companyId, projectId, photoTypes: [Project.PhotoType.LIVE_IMAGE], startTime: params.startTime, endTime: params.endTime, pageNo, pageSize, yqzDeviceId, generateSource, materialType };
        //查询手动上传图片: 开始时间、结束时间、分页
        if (photoType == Project.PhotoType.UPLOAD_IMAGE) albumParams = { companyId, projectId, photoTypes: [Project.PhotoType.UPLOAD_IMAGE], startTime: params.startTime, endTime: params.endTime, pageNo, pageSize };
        if (!albumParams) return { total: 0, items: [] };
        //查询工地相册
        const total = await this.projectDao.getProjectAlbumListCount(albumParams);
        if (total == 0) return { total, items: [] };
        const items = await this.projectDao.getProjectAlbumListDetail(albumParams);
        return {
            total,
            items: items.map(item => {
                return {
                    ...item,
                    albumType: item.albumType ? String(item.albumType) : null,
                    mediaType: item.mediaType ? String(item.mediaType) : null,
                    eventType: item.eventType ? String(item.eventType) : null
                }
            })
        };
    }
}