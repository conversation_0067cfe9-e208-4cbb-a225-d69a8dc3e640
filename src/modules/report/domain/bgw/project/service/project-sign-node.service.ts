

import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DevTimeout, MyLogger, YqzException } from '@yqz/nest';
import * as _ from "lodash";
import { ProjectSignNode } from '../dto/project-sign-node.dto';
import { ProjectSignNodeDao } from '../dao/project-sign-node.dao';
import { RoleDao } from '../../role/dao/role.dao';
import { CmsPhotoEntity } from '@src/modules/report/entity/bgw/cms-photo.entity';
import { CmsPhotoDao } from '../../common/cms-photo.dao';
import { YqzMedia } from '../../media/type/media.type';
import * as DateFns from "date-fns";

@Injectable()
export class ProjectSignNodeService {
    private readonly logger = new MyLogger(ProjectSignNodeService.name)
    constructor(
        private readonly projectSignNodeDao: ProjectSignNodeDao,
        private readonly roleDao: <PERSON>Dao,
        private readonly cmsPhotoDao: CmsPhotoDao
    ) { };

    @DevTimeout(100)
    async test() {
    }

    /**
     * 工地看板节点完成/未完成分布
     * @param params 
     * @returns 
     */
    async projectNodeFinishGroup(params: ProjectSignNode.Req): Promise<{ total: number, items: ProjectSignNode.FinishGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        const items = await this.projectSignNodeDao.projectNodeFinishGroup(params);
        return { total: _.sumBy(items, (item) => parseInt(item?.total || '0')), items: items.map(item => { return { ...item, status: item.isFinish === 'Y' ? '完成' : '未完成' } }) };
    }

    /**
     * 工地看板已完成节点逾期/未逾期分布
     * @param params 
     * @returns 
     */
    async projectNodeFinishOverdueGroup(params: ProjectSignNode.Req): Promise<{ total: number, items: ProjectSignNode.OverdueGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        params.isFinish = 'Y';
        // 查询是否开启节点延期配置(不需要了，暂不开启)
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const isNodeDelayOverdue = matterSet?.isNodeDelayOverdue;
        const isNodeDelayOverdue = 'N';
        const items = await this.projectSignNodeDao.projectNodeOverdueGroup({...params, isNodeDelayOverdue});
        return { total: _.sumBy(items, (item) => parseInt(item?.total || '0')), items: items.map(item => { return { ...item, status: item.isOverdue === 'Y' ? '逾期' : '未逾期' } }) };
    }

    /**
     * 工地看板待完成节点逾期/未逾期分布
     * @param params 
     * @returns 
     */
    async projectNodeUnFinishOverdueGroup(params: ProjectSignNode.Req): Promise<{ total: number, items: ProjectSignNode.OverdueGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        params.isFinish = 'N';
        // 查询是否开启节点延期配置(不需要了，暂不开启)
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const isNodeDelayOverdue = matterSet?.isNodeDelayOverdue;
        const isNodeDelayOverdue = 'N';
        const items = await this.projectSignNodeDao.projectNodeOverdueGroup({...params, isNodeDelayOverdue});
        return { total: _.sumBy(items, (item) => parseInt(item?.total || '0')), items: items.map(item => { return { ...item, status: item.isOverdue === 'Y' ? '逾期' : '未逾期' } }) };
    }

    /**
     * 工地看板节点列表
     * @param params 
     * @returns 
     */
    async projectNodeList(params: ProjectSignNode.Req): Promise<{ total: number, items: ProjectSignNode.ListRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");  
        // 查询是否开启节点延期配置(不需要了，暂不开启)
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const isNodeDelayOverdue = matterSet?.isNodeDelayOverdue;
        const isNodeDelayOverdue = 'N';
        const items = await this.projectSignNodeDao.projectNodeList({...params, isNodeDelayOverdue});
        const total = await this.projectSignNodeDao.projectNodeCount({...params, isNodeDelayOverdue});
        const roleIdList: string[] = [];
        const photoNodeIdList: string[] = [];
        items.map(res => {
            photoNodeIdList.push(res.projectNodeId);
            if (res?.roleIds) roleIdList.push(...res.roleIds.split(","));
        });
        //查询岗位信息
        const roleList = !_.isEmpty(photoNodeIdList) ? await this.roleDao.searchList({ids: roleIdList}) : [];
        const roleMap: { [key: string]: string } = {};
        roleList?.map(role => roleMap[role.companyMemberRoleId] = role.roleName);
        //查询调整信息
        const modifiedList = !_.isEmpty(photoNodeIdList) ? await this.projectSignNodeDao.searchNodeModified({ photoNodeIdList }) : [];
        const modifiedMap = _.groupBy(modifiedList, 'projectNodeId');
        //查询调整凭证图片
        const photoIdList: string[] = [];
        modifiedList.map(res => { if (res?.photoList) photoIdList.push(...res.photoList.split(",")) });
        const mediaMap: { [key: string]: CmsPhotoEntity } = {};
        const mediaList = !_.isEmpty(photoIdList) ? await this.cmsPhotoDao.searchList({ photoIdList }) : [];
        mediaList?.map(media => mediaMap[media.photoId] = media);
        //组装数据
        items.map(res => {
            if (res.isOverdue === 'Y') res.overdue = `节点逾期${res.overdueDays}天`;
            const roleNameList: string[] = [];
            if (res.roleIds) {
                const roleIds = res.roleIds.split(",");
                roleIds.map(roleId => { if (roleMap[roleId]) roleNameList.push(roleMap[roleId]); })
            }
            res.roleName = roleNameList.join(",");
            res.modifiedTotal = '0';
            res.modifiedDays = '';
            res.modifiedManager = '';
            res.modifiedTime = '';
            res.modifiedDescription = '';
            res.modifiedPhotos = [];
            if (modifiedMap[res.projectNodeId]) {
                res.modifiedTotal = String(modifiedMap[res.projectNodeId].length);
                const modifiedResList = modifiedMap[res.projectNodeId];
                modifiedResList.map(modified => {
                    if (modified.type) res.modifiedDays += modifiedResList.length > 1 ? `(${modified.type === 'delay' ? '延期' : '提前'}${modified.day}天)` : `${modified.type === 'delay' ? '延期' : '提前'}${modified.day}天`;
                    if (modified.modifiedPerson) res.modifiedManager += modifiedResList.length > 1 ? `(${modified.modifiedPerson})` : modified.modifiedPerson;
                    if (modified.modifiedTime) res.modifiedTime += modifiedResList.length > 1 ? `(${modified.modifiedTime})` : modified.modifiedTime;
                    if (modified.description) res.modifiedDescription += modifiedResList.length > 1 ? `(${modified.description})` : modified.description;
                    if (modified.photoList) {
                        const modifiedPhotos: YqzMedia[] = [];
                        modified.photoList?.split(",").map(mediaId => {
                            if (mediaMap[mediaId]) modifiedPhotos.push(new YqzMedia({ id: mediaMap[mediaId]?.photoId, type: mediaMap[mediaId]?.mediaType, cover: mediaMap[mediaId]?.ossUrl, resourceUrl: mediaMap[mediaId]?.mediaResourceUrl, creationDate: mediaMap[mediaId]?.createdDate ? DateFns.format(mediaMap[mediaId].createdDate, 'yyyy-MM-dd HH:mm:ss') : null, address: mediaMap[mediaId]?.address }));
                        });
                        res.modifiedPhotos = [...res.modifiedPhotos, ...modifiedPhotos];
                    }
                });

            }
        });
        return { total, items };
    }

    /**
     * 工地看板节点列表筛选项
     * @param params 
     * @returns 
     */
    async projectNodeListOptions(params: ProjectSignNode.Req): Promise<ProjectSignNode.ListOptionsRes> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        const stage = await this.projectSignNodeDao.projectNodeStageList(params);
        const isFinish: ProjectSignNode.OptionsDto[] = [{ value: 'Y', name: '完成' }, { value: 'N', name: '未完成' }];
        const isOverdue: ProjectSignNode.OptionsDto[] = [{ value: 'Y', name: '逾期' }, { value: 'N', name: '未逾期' }];
        return { stage, isFinish, isOverdue };
    }

}