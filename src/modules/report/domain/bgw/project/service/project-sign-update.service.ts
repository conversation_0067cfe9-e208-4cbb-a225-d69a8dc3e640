

import { Injectable } from '@nestjs/common';
import { DevTimeout, MyLogger, YqzException } from '@yqz/nest';
import { ProjectSignUpdateDao } from '../dao/project-sign-update.dao';
import { CmsPhotoDao } from '../../common/cms-photo.dao';
import { DefaultUrl, YqzMedia } from '../../media/type/media.type';
import { CmsPhotoEntity } from '@src/modules/report/entity/bgw/cms-photo.entity';
import { YqzMediaType } from '@yqz/nest/lib/types/media.type';
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { ProjectSignUpdate } from '../dto/project-sign-update.dto';

@Injectable()
export class ProjectSignUpdateService {
    private readonly logger = new MyLogger(ProjectSignUpdateService.name)
    constructor(
        private readonly projectSignUpdateDao: ProjectSignUpdateDao,
        private readonly cmsPhotoDao: CmsPhotoDao
    ) { };

    @DevTimeout(100)
    async test() {
        // const res1 = await this.projectSignUpdateDao.projectUpdateStatistics({companyId:'412',projectId:'369638'})
        // console.log(res1)
        // const res2 = await this.projectSignUpdateDao.projectUpdateList({companyId:'412',projectId:'369638'})
        // console.log(res2)
    }

    /**
     * 工地看板更新统计
     * @param params 
     * @returns 
     */
    async projectUpdateStatistics(params: ProjectSignUpdate.Req): Promise<ProjectSignUpdate.StatisticsRes> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        const result = await this.projectSignUpdateDao.projectUpdateStatistics(params);
        return result ? result : { totalPerson: '0', totalUpdate: '0' };
    }

    /**
     * 工地更新根据manager分组（包含工地更新的manager和工地成员）
     * 注：工地更新不一定是工地成员，工地成员即使没更新也要展示
     * @param params 
     * @returns 
     */
    async projectUpdateManagerGroup(params: ProjectSignUpdate.Req): Promise<{ total: number, items: ProjectSignUpdate.ManagerGroupRes[] }> {
        const { companyId, projectId, pageNo, pageSize } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        //查询总数
        const total = await this.projectSignUpdateDao.projectUpdateManagerGroupCount(params);
        //查询列表
        const result = await this.projectSignUpdateDao.projectUpdateManagerGroup(params);
        //查询头像信息
        const avatarMap: { [key: string]: CmsPhotoEntity } = {};
        const photoIdList = result.map(res => res.profilePhoto);
        const avatarList = !_.isEmpty(photoIdList) ? await this.cmsPhotoDao.searchList({ photoIdList }) : [];
        avatarList?.map(avatar => avatarMap[avatar.photoId] = avatar);
        const ranking = pageNo && pageSize ? (pageNo - 1) * pageSize : 0; //初始排名
        //处理结果
        const items = result.map((res, index) => {
            const avatar = avatarMap[res.profilePhoto];
            return {
                ...res,
                ranking: String(ranking + index + 1), //处理排名
                avatar: avatar ?//处理头像，没有返回默认头像
                    new YqzMedia({ id: avatar?.photoId, type: avatar?.mediaType, cover: avatar?.ossUrl, resourceUrl: avatar?.mediaResourceUrl, creationDate: avatar?.createdDate ? DateFns.format(avatar.createdDate, 'yyyy-MM-dd HH:mm:ss') : null, address: avatar?.address }) :
                    new YqzMedia({ id: res.profilePhoto, type: YqzMediaType.IMAGE, cover: DefaultUrl.UserProfilePicture, resourceUrl: DefaultUrl.UserProfilePicture, creationDate: null, address: null })
            }
        });
        return { total, items };
    }

    /**
     * 工地看板更新阶段分布（如果工地更新的阶段不等于工地当前阶段，统计为其他）
     * @param params 
     * @returns 
     */
    async projectUpdateStageGroup(params: ProjectSignUpdate.Req): Promise<{ total: number, items: ProjectSignUpdate.StageGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        //查询列表
        const items = await this.projectSignUpdateDao.projectUpdateStageGroup(params);
        return { total: _.sumBy(items, (item) => parseInt(item?.totalUpdate || '0')), items };
    }

    /**
     * 工地看板更新列表
     * @param params 
     * @returns 
     */
    async projectUpdateList(params: ProjectSignUpdate.Req): Promise<{ total: number, items: ProjectSignUpdate.ListRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        const items = await this.projectSignUpdateDao.projectUpdateList(params);
        const total = await this.projectSignUpdateDao.projectUpdateCount(params);
        if (_.isEmpty(items)) return { total, items: [] };
        //查询更新图片信息
        // const photoIdList: string[] = [];
        // items.map(res => { if (res?.projectUpdateMediaList) photoIdList.push(...res.projectUpdateMediaList.split(",")) });
        // const mediaMap: { [key: string]: CmsPhotoEntity } = {};
        // const mediaList = !_.isEmpty(photoIdList) ? await this.cmsPhotoDao.searchList({ photoIdList }) : [];
        // mediaList?.map(media => mediaMap[media.photoId] = media);
        const updateIds = items.map(res => res.projectUpdateId);
        const mediaList = !_.isEmpty(updateIds) ? await this.projectSignUpdateDao.searchProjectUpdateMediaByUpdateIds(updateIds) : [];
        const mediaMap = _.groupBy(mediaList, "id");
        return {
            total,
            items: items.map(res => {
                // const mediaIdList = res?.projectUpdateMediaList ? res.projectUpdateMediaList.split(",") : [];
                // const mediaList: YqzMedia[] = [];
                // mediaIdList.map(mediaId => {
                //     if (mediaMap[mediaId]) mediaList.push(new YqzMedia({ id: mediaMap[mediaId]?.photoId, type: mediaMap[mediaId]?.mediaType, cover: mediaMap[mediaId]?.ossUrl, resourceUrl: mediaMap[mediaId]?.mediaResourceUrl, creationDate: mediaMap[mediaId]?.createdDate ? DateFns.format(mediaMap[mediaId].createdDate, 'yyyy-MM-dd HH:mm:ss') : null, address: mediaMap[mediaId]?.address }))
                // });
                const updateMediaList = mediaMap[res.projectUpdateId] || [];
                // 图片&vr
                const updatePhotoList: YqzMedia[] = []
                updateMediaList?.
                filter(media => media.mediaType !== YqzMediaType.VIDEO)?.
                map(media => {
                    updatePhotoList.push(new YqzMedia({ id: media?.photoId, type: media?.mediaType, cover: media?.ossUrl, resourceUrl: media?.mediaResourceUrl, creationDate: media?.createdDate ? DateFns.format(media.createdDate, 'yyyy-MM-dd HH:mm:ss') : null, address: media?.address }))
                })
                // 视频
                const updateVideoList: YqzMedia[] = []
                updateMediaList?.
                filter(media => media.mediaType === YqzMediaType.VIDEO)?.
                map(media => {
                    updateVideoList.push(new YqzMedia({ id: media?.photoId, type: media?.mediaType, cover: media?.ossUrl, resourceUrl: media?.mediaResourceUrl, creationDate: media?.createdDate ? DateFns.format(media.createdDate, 'yyyy-MM-dd HH:mm:ss') : null, address: media?.address }))
                })
                return {
                    ...res,
                    mediaTotal: String(updatePhotoList.length),
                    mediaList: updatePhotoList,
                    videoTotal: String(updateVideoList.length),
                    videoList: updateVideoList
                };
            })
        };
    }

    /**
     * 工地看板更新列表筛选项
     * @param params 
     * @returns 
     */
    async projectUpdateListOptions(params: ProjectSignUpdate.Req): Promise<ProjectSignUpdate.ListOptionsRes> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        const stage = await this.projectSignUpdateDao.projectUpdateStageList(params);
        const manager = await this.projectSignUpdateDao.projectUpdateManagerList(params);
        return { stage, manager };
    }

}