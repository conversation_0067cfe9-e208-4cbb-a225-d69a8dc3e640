

import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DevTimeout, MyLog<PERSON>, YqzException } from '@yqz/nest';
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { ProjectSignProblem } from '../dto/project-sign-problem.dto';
import { ProjectSignProblemDao } from '../dao/project-sign-problem.dao';
import { CommonParameterDao } from '../../common/common-parameter.dao';
import { YqzTargetType } from '../../types/target.type';

@Injectable()
export class ProjectSignProblemService {
    private readonly logger = new MyLogger(ProjectSignProblemService.name)
    constructor(
        private readonly projectSignProblemDao: ProjectSignProblemDao,
        private readonly commonParameterDao: CommonParameterDao,
    ) { };

    @DevTimeout(100)
    async test() {
    }

    /**
     * 工地看板问题统计
     * @param params 
     * @returns 
     */
    async projectProblemStatistics(params: ProjectSignProblem.Req): Promise<ProjectSignProblem.StatisticsRes> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        const result = await this.projectSignProblemDao.projectProblemStatistics(params);
        return result ? result : { total: '0' };
    }

    /**
     * 工地看板问题来源分布
     * @param params 
     * @returns 
     */
    async projectProblemSourceGroup(params: ProjectSignProblem.Req): Promise<{ total: number, items: ProjectSignProblem.SourceGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        //查询列表
        const items = await this.projectSignProblemDao.projectProblemSourceGroup(params);
        //查询问题来源
        const problemSource = await this.commonParameterDao.search('problem_source');
        const problemSourceMap = {};
        (problemSource || []).map(res => { return problemSourceMap[res.parameterCode] = res.parameterName });
        return { total: _.sumBy(items, (item) => parseInt(item?.total || '0')), items: items.map(item => { return { ...item, source: problemSourceMap[item.sourceCode] } }) };
    }

    /**
     * 工地看板问题分类分布
     * @param params 
     * @returns 
     */
    async projectProblemTypeGroup(params: ProjectSignProblem.Req): Promise<{ total: number, items: ProjectSignProblem.TypeGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        //查询列表
        const items = await this.projectSignProblemDao.projectProblemTypeGroup(params);
        //查询问题类型
        const problemType = await this.commonParameterDao.search('problem_type');
        const problemTypeMap = {};
        (problemType || []).map(res => { return problemTypeMap[res.parameterCode] = res.parameterName });
        return { total: _.sumBy(items, (item) => parseInt(item?.total || '0')), items: items.map(item => { return { ...item, type: problemTypeMap[item.typeCode] } }) };
    }

    /**
     * 工地看板问题状态分布
     * @param params 
     * @returns 
     */
    async projectProblemStatusGroup(params: ProjectSignProblem.Req): Promise<{ total: number, items: ProjectSignProblem.StatusGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        //查询列表
        const items = await this.projectSignProblemDao.projectProblemStatusGroup(params);
        //查询问题状态
        const problemStatus = await this.commonParameterDao.search('problem_status');
        const problemStatusMap = {};
        (problemStatus || []).map(res => { return problemStatusMap[res.parameterCode] = res.parameterName });
        return { total: _.sumBy(items, (item) => parseInt(item?.total || '0')), items: items.map(item => { return { ...item, status: problemStatusMap[item.statusCode] } }) };
    }

    /**
     * 工地看板问题摄像头捕捉问题分布
     * @param params 
     * @returns 
     */
    async projectProblemDeviceResultTypeGroup(params: ProjectSignProblem.Req): Promise<{ total: number, items: ProjectSignProblem.DeviceResultTypeGroupRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        //查询列表
        const items = await this.projectSignProblemDao.projectProblemDeviceResultTypeGroup(params);
        return {
            total: _.sumBy(items, (item) => parseInt(item?.total || '0')),
            items: items.map(item => {
                if (item?.problemSourceType === '1') {//文明施工
                    return { ...item, deviceResultType: ProjectSignProblem.RiskTypeCode[item.deviceResultTypeCode] };
                }
                if (item?.problemSourceType === '2') {//整洁度
                    return { ...item, deviceResultType: `整洁度${ProjectSignProblem.TidinessTypeCode[item.deviceResultTypeCode]}` };
                }
            })
        };
    }

    /**
     * 工地看板问题列表
     * @param params 
     * @returns 
     */
    async projectProblemList(params: ProjectSignProblem.Req): Promise<{ total: number, items: ProjectSignProblem.ListRes[] }> {
        const { companyId, projectId } = params;
        if (!companyId || !projectId) throw new YqzException("必传参数不能为空");
        //格式化筛选日期
        if (params.startTime) params.startTime = DateFns.format(DateFns.startOfDay(new Date(params.startTime)), 'yyyy-MM-dd HH:mm:ss');
        if (params.endTime) params.endTime = DateFns.format(DateFns.endOfDay(new Date(params.endTime)), 'yyyy-MM-dd HH:mm:ss');
        const items = await this.projectSignProblemDao.projectProblemList(params);
        const total = await this.projectSignProblemDao.projectProblemCount(params);
        const wrongdoerList: string[] = [];
        const mediaAnnotationList: { targetType: string, targetId: string, mediaIdList: string[] }[] = [];
        items.map(res => {
            if (res.wrongdoerFaceId) wrongdoerList.push(res.wrongdoerFaceId);
            if (res.problemImg) {
                mediaAnnotationList.push({
                    targetType: YqzTargetType.PROJECT_PROBLEM_41,
                    targetId: res.projectProblemId,
                    mediaIdList: res.problemImg.split(",")
                });
            }
            if (res.solvedImg) {
                mediaAnnotationList.push({
                    targetType: YqzTargetType.PROBLEM_SOLVED_42,
                    targetId: res.problemSolvedId,
                    mediaIdList: res.solvedImg.split(",")
                });
            }
        });
        //查询图片及注解
        const mediaAnnotations = !_.isEmpty(mediaAnnotationList) ? await AgileApi.searchMediaAnnotationList({ list: mediaAnnotationList }) : [];
        const mediaAnnotationMap = (mediaAnnotations || [])?.reduce((result, info) => {
            const key = `${info.targetType}_${info.targetId}`;
            if (!result[key]) result[key] = [];
            result[key].push(info);
            return result;
        }, {});
        //查询标记用户
        const wrongdoerMap = {};
        const wrongdoers = !_.isEmpty(wrongdoerList) ? await AgileApi.getOrgFaceSearch({ companyId, _ids: wrongdoerList }) : [];
        (wrongdoers?.members || [])?.map(wrongdoer => { wrongdoerMap[wrongdoer?.faceId] = wrongdoer; });
        const problemType = await this.commonParameterDao.search('problem_type');
        const problemTypeMap = {};
        (problemType || []).map(res => { return problemTypeMap[res.parameterCode] = res.parameterName });
        const problemStatus = await this.commonParameterDao.search('problem_status');
        const problemStatusMap = {};
        (problemStatus || []).map(res => { return problemStatusMap[res.parameterCode] = res.parameterName });
        const problemSource = await this.commonParameterDao.search('problem_source');
        const problemSourceMap = {};
        (problemSource || []).map(res => { return problemSourceMap[res.parameterCode] = res.parameterName });
        return {
            total,
            items: items.map(res => {
                return {
                    ...res,
                    statusName: problemStatusMap[res.status] || '',
                    sourceName: problemSourceMap[res.source] || '',
                    typeName: problemTypeMap[res.type] || '',
                    problemPhoto: mediaAnnotationMap[`${YqzTargetType.PROJECT_PROBLEM_41}_${res.projectProblemId}`] || [],
                    solvePhoto: mediaAnnotationMap[`${YqzTargetType.PROBLEM_SOLVED_42}_${res.problemSolvedId}`] || [],
                    wrongdoer: wrongdoerMap[res.wrongdoerFaceId],
                };
            })
        };
    }

    /**
     * 工地看板问题列表筛选项
     * @param params 
     * @returns 
     */
    async projectProblemListOptions(): Promise<ProjectSignProblem.ListOptionsRes> {
        const problemType = await this.commonParameterDao.search('problem_type');
        const problemStatus = await this.commonParameterDao.search('problem_status');
        const problemSource = await this.commonParameterDao.search('problem_source');
        return {
            status: problemStatus.map(res => { return { name: res.parameterCode, value: res.parameterName } }),
            type: problemType.map(res => { return { name: res.parameterCode, value: res.parameterName } }),
            source: problemSource.map(res => { return { name: res.parameterCode, value: res.parameterName } }),
        };
    }

}