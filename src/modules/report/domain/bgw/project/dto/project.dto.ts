import { ApiProperty } from "@nestjs/swagger";
import { BaseReportReq } from "@src/types/report-req.type";
import { UtilType } from "../../types/util.type";
import { IsNotEmpty } from "class-validator";
import { ProjectDateReport } from "../../../corp/data-report/service/basic/dto/project-data-report.dto";
import { DataReportModuleFilter } from "../../../corp/data-report/dto/data-report-module-filter.dto";

export namespace Project {
    export enum MemberRole {
        Admin = '2',
        Member = '1',
        Owner = '3'
    }
    export class Search {
        @ApiProperty()
        companyId?: string;
        companyIds?: string;
        @ApiProperty()
        departmentId?: string;
        @ApiProperty()
        projectStatus?: 'no_finish' | 'no_work' | 'finish';
        @ApiProperty()
        projectIds?: string[];

        @ApiProperty()
        memberPersonIdList?: string[]

        @ApiProperty({ type: [MemberRole] })
        memberRoleList?: MemberRole[]
        @ApiProperty()
        projectDirectorIdList?: string[]

        @ApiProperty()
        startTime?: string;
        @ApiProperty()
        endTime?: string;
        @ApiProperty()
        isNodeDelayOverdue?: 'Y' | 'N';
    }

    export class ProjectDetailReq extends BaseReportReq<ProjectDetail> {
        @ApiProperty({ description: "工地地址", required: false })
        projectAddress?: string;
        @ApiProperty({ description: "工地类型 commonProject-普通工地,wisdomProject-智慧工地", enum: ['commonProject', 'wisdomProject'], required: false })
        projectType?: string;
        @ApiProperty({ description: "施工状态 Y-正常施工,N-无人施工", enum: ['Y', 'N'], required: false })
        isWork?: 'Y' | 'N';
        @ApiProperty({ description: "逾期状态 Y-逾期,N-正常", enum: ['Y', 'N'], required: false })
        isOverdue?: 'Y' | 'N';
        @ApiProperty({ description: "工地负责人Id", required: false })
        projectManagerId?: string;
        @ApiProperty({ description: "所属公司Id", required: false })
        linkCompanyId?: string;
        @ApiProperty({ description: "最后施工时间 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        lastWorkTimeSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "无人施工天数 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        noWorkDaySort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "逾期时间 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        overdueTimeSort?: 'DESC' | 'ASC';
        projectIds?: string[];
        startTime?: string;
        endTime?: string;
    }

    export class ProjectDetail {
        @ApiProperty({ description: "工地地址" })
        projectAddress: string;
        @ApiProperty({ description: "工地类型 commonProject-普通工地,wisdomProject-智慧工地", enum: ['commonProject', 'wisdomProject'] })
        projectType: string;
        @ApiProperty({ description: "施工状态 Y-正常施工,N-无人施工", enum: ['Y', 'N'] })
        isWork: 'Y' | 'N' | '--';
        @ApiProperty({ description: "最后施工日期" })
        lastWorkTime: string;
        @ApiProperty({ description: "无人施工天数" })
        noWorkDay: string;
        @ApiProperty({ description: "逾期状态 Y-逾期,N-正常", enum: ['Y', 'N'] })
        isOverdue: 'Y' | 'N';
        @ApiProperty({ description: "逾期时间" })
        overdueTime: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId: string;
        @ApiProperty({ description: "工地负责人name" })
        projectManagerName: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId: string;
        @ApiProperty({ description: "所属公司name" })
        linkCompanyName: string;
        linkCompanyCreateTime: string;//所属公司创建时间
        @ApiProperty({ description: "创建工地时间" })
        projectCreateTime: string;//创建工地时间
        projectId: string;//工地id
    }

    export class BindingProjectReq {
        companyId?: string;
        companyIds?: string[];
        projectIds?: string[];
        projectAddress?: string;
        projectDirectorId?: string;
        addressType?: string;
    }

    export class ProjectMemberSignInReq {
        @IsNotEmpty({ message: '工地id不能为空' })
        @ApiProperty({ description: '工地id' })
        projectId: string;
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '开始时间' })
        startDate?: string;
        @ApiProperty({ description: '结束时间' })
        endDate?: string;
        @ApiProperty({ description: '返回格式 json or csv，默认json', enum: ['csv', 'json'] })
        format?: "csv" | 'json';
        @ApiProperty({ description: '签到人managerId' })
        managerId?: string;
        pageSize?: number;
        pageNo?: number;
    }

    export class SignInRes {
        @ApiProperty({ description: 'managerId' })
        managerId: string;
        @ApiProperty({ description: '昵称' })
        nickName: string;
        @ApiProperty({ description: '岗位名' })
        roleName: string;
        @ApiProperty({ description: '签到次数' })
        signInTime: string;
        @ApiProperty({ description: "头像" })
        avatarUrl: string;
        @ApiProperty({ description: "签到方式" })
        signInType: string;
        @ApiProperty({ description: "到场时间" })
        arrivalTime?: string;
        @ApiProperty({ description: "离场时间" })
        leaveTime?: string;
        @ApiProperty({ description: "工作时长" })
        workTime?: string;
        @ApiProperty({ description: "部门名" })
        departmentName?: string;
    }

    export class SignInManagerGroupRes {
        @ApiProperty({ description: 'managerId' })
        managerId: string;
        @ApiProperty({ description: '昵称' })
        nickName: string;
        @ApiProperty({ description: '岗位名' })
        roleName: string;
        @ApiProperty({ description: '签到次数' })
        signInCount: string;
        @ApiProperty({ description: "头像" })
        avatarUrl: string;
        @ApiProperty({ description: "排名" })
        ranking: string;
        @ApiProperty({ description: '签到日期列表' })
        signInDays: string[];
    }

    export const SignInManagerGroupCsv: UtilType.propertyCsv[] = [
        {
            key: "ranking",
            value: "排名",
            options: { defValue: "" }
        }, {
            key: "roleName",
            value: "岗位名",
            options: { defValue: "" }
        }, {
            key: "nickName",
            value: "更新人",
            options: { defValue: "" }
        }, {
            key: "signInCount",
            value: "签到次数",
            options: { defValue: "" }
        }
    ];

    export const SignInListCsv: UtilType.propertyCsv[] = [
        {
            key: "signInTime",
            value: "签到日期",
            options: { defValue: "" }
        }, {
            key: "nickName",
            value: "签到人",
            options: { defValue: "" }
        }, {
            key: "signInType",
            value: "签到方式",
            options: { defValue: "" }
        }
    ];

    export class SearchProjectReq {
        projectIdList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        departmentId?: string;//所属部门
        decorationModel?: string;//装修方式
        projectStatus?: string;//工地状态
        projectAddress?: string;//工地地址
        stageList?: string[];//当前阶段（实时数据特有）
        customKeyPositionRoleIds?: string[];//关键岗位角色ID列表
    }

    export class SearchRealProjectReq {
        companyType: string;//公司类型
        companyId: string;//当前登录的工地
        businessCategory: 'home_decor' | 'construction';
        projectIdList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        decorationModel?: string;//装修方式
        projectStatus?: string;//工地状态
        projectAddress?: string;//工地地址
        pageNo?: number;
        pageSize?: number;
        stageList?: string[];//当前阶段
    }

    export class SearchEtlProjectReq {
        companyType: string;//公司类型
        companyId: string;//当前登录的工地
        businessCategory: 'home_decor' | 'construction';
        projectIdList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        decorationModel?: string;//装修方式
        projectStatus?: string;//工地状态
        projectAddress?: string;//工地地址
        stageList?: string[];//当前阶段
        workStatus?: string;//施工状态
        overdueStatus?: string;//逾期状态
        delayStatus?: string;//延期状态
        pageNo?: number;
        pageSize?: number;
        //周期范围
        startTime: string;
        endTime: string;
    }

    export class SearchProjectNodeProjectReq {
        projectIdList: string[];
        projectDirectorId?: string;//工地负责人
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectTimeStart?: string;//开工开始时间
        projectTimeEnd?: string;//开工结束时间
        nodeTemplateName?: string;//节点模版名称
        stage?: string;//节点阶段
        nodeName?: string;//节点名称
        role?: string;//节点对应岗位
        deadlineStartTime?: string;//节点截止日期开始时间
        deadlineEndTime?: string;//节点截止日期结束时间
        isCompleted?: 'Y' | 'N';//是否完成
        overdueStatus?: 'Y' | 'N';//是否逾期
        isScheduleModify?: 'Y' | 'N';//是否调整工期
        scheduleType?: string;//工期调整类型
        reasonType?: string;//工期调整原因类型
        deviceDepartmentId?: string;//服务厂商
        pageNo?: number;
        pageSize?: number;
        // delayOverdue: 'Y' | 'N';//延期是否算逾期
    }

    export class SearchProjectNodeNodeReq {
        nodeIdList: string[];
        projectDirectorId?: string;//工地负责人
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectTimeStart?: string;//开工开始时间
        projectTimeEnd?: string;//开工结束时间
        projectTime?: DataReportModuleFilter.DateRangeFilterValue;
        nodeTemplateName?: string;//节点模版名称
        stage?: string;//节点阶段
        nodeName?: string;//节点名称
        role?: string;//节点对应岗位
        deadlineStartTime?: string;//节点截止日期开始时间
        deadlineEndTime?: string;//节点截止日期结束时间
        deadlineTime?: DataReportModuleFilter.DateRangeFilterValue;
        isCompleted?: 'Y' | 'N';//是否完成
        overdueStatus?: 'Y' | 'N';//是否逾期
        isScheduleModify?: 'Y' | 'N';//是否调整工期
        scheduleType?: string;//工期调整类型
        reasonType?: string;//工期调整原因类型
        deviceDepartmentId?: string;//服务厂商
        pageNo?: number;
        pageSize?: number;
    }

    export class SearchProjectNodeRes {
        linkCompanyId: string;
        projectId: string;
        projectNodeId: string;
        projectStartTime: string;
        nodeTemplateName: string;
        startTime: string;
        deadlineTime: string;
        beforeDeadlineTime: string;
        nodeStatus: string;
        completedTime: string;
        roleIdStr: string;
        nodeName: string;
        scheduleModifyDay: string;
        stageName: string;
    }

    export class CommonFilterReq {
        companyId: string;
        topic: ProjectDateReport.Topic;
        //周期范围
        startTime: string;
        endTime: string;
        projectIdList: string[];//报告ID列表
        projectType?: string;//工地类型
        deviceDepartmentId?: string;//服务厂商
        workStatus?: ProjectDateReport.WorkStatus;//是否施工（实时数据特有）
        delayStatus?: ProjectDateReport.DelayStatus;//是否延期（实时数据特有）
        overdueStatus?: ProjectDateReport.OverdueStatus;//逾期状态（切片数据特有）
        isNodeDelayOverdue?: 'Y' | 'N';//是否节点延期
        custom_keyPosition_1?: string;//关键岗位1
        custom_keyPosition_2?: string;//关键岗位2
        custom_keyPosition_3?: string;//关键岗位3
    }

    export class EvaluateSearchReq {
        idList: string[];//ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        stage?: string;//阶段
        score?: string;//得分
        projectAddress?: string;//工地地址
        pageNo?: number;
        pageSize?: number;
    }

    export enum PhotoType {
        LIVE_IMAGE = '1',//工地剪影
        UPLOAD_IMAGE = '2', //手动上传
        CAMERA_IMAGE = '3', //摄像头图片
    }

    export enum GenerateSource {
        SYSTEM = '1',//系统生成
        CUSTOM = '2' //自定义生成
    }

    export enum MaterialType {
        IMAGE = '1',
        GIF = '2',
        VIDEO = '3'
    }

    export class ProjectAlbumListDetailReq {
        companyId: string;
        projectId: string;
        yqzDeviceId?: string;
        photoType?: PhotoType;
        eventType?: string;
        startTime?: string;
        endTime?: string;
        pageNo?: number;
        pageSize?: number;
        photoTypes?: PhotoType[];
        generateSource?: GenerateSource;
        materialType?: MaterialType;
    }

}