import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { YqzMedia } from "../../media/type/media.type";
import { UtilType } from "../../types/util.type";

export namespace ProjectSignUpdate {

    export class Req {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @IsNotEmpty({ message: '工地id不能为空' })
        @ApiProperty({ description: '工地id' })
        projectId: string;
        @ApiProperty({ description: '开始时间' })
        startTime?: string;
        @ApiProperty({ description: '结束时间' })
        endTime?: string;
        @ApiProperty({ description: '阶段模板id' })
        stageTemplateId?: string;
        @ApiProperty({ description: '阶段id' })
        businessStageId?: string;
        @ApiProperty({ description: '更新人managerId' })
        managerId?: string;
        @ApiProperty({ description: '返回格式 json or csv，默认json', enum: ['csv', 'json'] })
        format?: "csv" | 'json'
        pageNo?: number;
        pageSize?: number;
    }

    export class StatisticsRes {
        @ApiProperty({ description: '更新人数' })
        totalPerson: string;
        @ApiProperty({ description: '更新次数' })
        totalUpdate: string;
    }

    export class ManagerGroupRes {
        @ApiProperty({ description: 'managerId' })
        managerId: string;
        @ApiProperty({ description: '昵称' })
        nickName: string;
        @ApiProperty({ description: '头像id' })
        profilePhoto: string;
        @ApiProperty({ description: '岗位id' })
        roleId: string;
        @ApiProperty({ description: '岗位名' })
        roleName: string;
        @ApiProperty({ description: '更新次数' })
        totalUpdate: string;
        @ApiProperty({ description: "头像", type: YqzMedia })
        avatar: YqzMedia;
        @ApiProperty({ description: "排名" })
        ranking: string;
    }

    export class StageGroupRes {
        @ApiProperty({ description: '阶段id' })
        stageId: string;
        @ApiProperty({ description: '阶段' })
        stageName: string;
        @ApiProperty({ description: '更新次数' })
        totalUpdate: string;
    }

    export class ListRes {
        @ApiProperty({ description: '阶段名' })
        stageName: string;
        @ApiProperty({ description: '阶段模板id' })
        stageTemplateId: string;
        @ApiProperty({ description: '阶段id' })
        businessStageId: string;
        @ApiProperty({ description: 'managerId' })
        managerId: string;
        @ApiProperty({ description: '昵称' })
        nickName: string;
        @ApiProperty({ description: '岗位' })
        roleName: string;
        @ApiProperty({ description: '岗位id' })
        roleId: string;
        @ApiProperty({ description: '更新时间' })
        projectUpdateTime: string;
        @ApiProperty({ description: '更新文本' })
        projectUpdateText: string;
        @ApiProperty({ description: '更新图片id' })
        projectUpdateMediaList: string;
        @ApiProperty({ description: '更新图片数' })
        mediaTotal: string;
        @ApiProperty({ description: '更新图片数', isArray: true, type: YqzMedia })
        mediaList: YqzMedia[];
        projectUpdateId: string;
    }

    export class StageOptions {
        @ApiProperty({ description: '阶段名' })
        stageName: string;
        @ApiProperty({ description: '阶段模板id' })
        stageTemplateId: string;
        @ApiProperty({ description: '阶段id' })
        businessStageId: string;
    }

    export class ManagerOptions {
        @ApiProperty({ description: '昵称' })
        nickName: string;
        @ApiProperty({ description: 'managerId' })
        managerId: string;
    }

    export class ListOptionsRes {
        @ApiProperty({ description: '阶段选项', isArray: true, type: StageOptions })
        stage: StageOptions[];
        @ApiProperty({ description: '更新人选项', isArray: true, type: ManagerOptions })
        manager: ManagerOptions[];
    }

    export const ManagerGroupCsv: UtilType.propertyCsv[] = [
        {
            key: "ranking",
            value: "排名",
            options: { defValue: "" }
        }, {
            key: "roleName",
            value: "岗位名",
            options: { defValue: "" }
        }, {
            key: "nickName",
            value: "更新人",
            options: { defValue: "" }
        }, {
            key: "totalUpdate",
            value: "更新次数",
            options: { defValue: "" }
        }
    ];

    export const ListCsv: UtilType.propertyCsv[] = [
        {
            key: "stageName",
            value: "所属阶段",
            options: { defValue: "" }
        }, {
            key: "projectUpdateTime",
            value: "更新时间",
            options: { defValue: "" }
        }, {
            key: "nickName",
            value: "更新人",
            options: { defValue: "" }
        }, {
            key: "roleName",
            value: "岗位",
            options: { defValue: "" }
        }, {
            key: "mediaTotal",
            value: "更新图片数",
            options: { defValue: "" }
        }, {
            key: "mediaList[*].mediaResourceUri",
            value: "更新图片",
            options: { defValue: "" }
        }, {
            key: "videoTotal",
            value: "更新视频数",
            options: { defValue: "" }
        }, {
            key: "videoList[*].mediaResourceUri",
            value: "更新视频",
            options: { defValue: "" }
        }, {
            key: "projectUpdateText",
            value: "更新内容",
            options: { defValue: "" }
        }
    ];

}