import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { YqzMedia } from "../../media/type/media.type";
import { UtilType } from "../../types/util.type";

export namespace ProjectSignProblem {

    export enum RiskType {
        SMOKE = '0',//抽烟
        FLAME = '1',//明火
        THICK_SMOKE = '2',//浓烟
        NORMAL = '3',//正常
        NOT_WORK_CLOTHES = '4',//未穿工服
        SLIPPERS = '5',//穿拖鞋
        NON_WORKER = '6',//非工作人员
        ELECTRO_MOBILE = '7',//停放电瓶车
        SAFETY_HELMET = "8",//安全帽
        CLIMB_WORK = '9',//登高
        ANGLE_GRINDER = '10',//角磨机
        CUTTING_MACHINE = '11',//切割机
        FIRE_EXTINGUISHER = '12',//灭火器
        TEMPORARY_ELECTRICITY = '13',//临时用电
        ACTIVE_FIRE = '14',//动火
        SAFETY_MESH = '15',//脚手架未装防护网
        SAFETY_HOLE = '16',//临边洞口无防护
        SAFETY_STAIRS = '17',//楼梯无防护
    }

    export const RiskTypeCode: Readonly<{ [key in RiskType]: string }> = {
        [RiskType.SMOKE]: '抽烟',
        [RiskType.FLAME]: '明火',
        [RiskType.THICK_SMOKE]: "浓烟",
        [RiskType.NORMAL]: "正常",
        [RiskType.NOT_WORK_CLOTHES]: "未穿工服",
        [RiskType.SLIPPERS]: "穿拖鞋",
        [RiskType.NON_WORKER]: "非工作人员",
        [RiskType.ELECTRO_MOBILE]: "停放电瓶车",
        [RiskType.SAFETY_HELMET]: "安全帽",
        [RiskType.CLIMB_WORK]: "登高",
        [RiskType.ANGLE_GRINDER]: "角磨机",
        [RiskType.CUTTING_MACHINE]: "切割机",
        [RiskType.FIRE_EXTINGUISHER]: "灭火器",
        [RiskType.TEMPORARY_ELECTRICITY]: "临时用电",
        [RiskType.ACTIVE_FIRE]: "动火",
        [RiskType.SAFETY_MESH]: "脚手架未装防护网",
        [RiskType.SAFETY_HOLE]: "临边洞口无防护",
        [RiskType.SAFETY_STAIRS]: "楼梯无防护",
    }


    export const TidinessTypeCode = {
        "1": "优",
        "2": "良",
        "3": "差",
        "4": "未知",
    }

    export class Req {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @IsNotEmpty({ message: '工地id不能为空' })
        @ApiProperty({ description: '工地id' })
        projectId: string;
        @ApiProperty({ description: '开始时间' })
        startTime?: string;
        @ApiProperty({ description: '结束时间' })
        endTime?: string;
        @ApiProperty({ description: '问题类型' })
        type?: string;
        @ApiProperty({ description: '问题来源' })
        source?: string;
        @ApiProperty({ description: '问题状态' })
        status?: string;
        @ApiProperty({ description: '返回格式 json or csv，默认json', enum: ['csv', 'json'] })
        format?: "csv" | 'json';
        pageNo?: number;
        pageSize?: number;
    }

    export class StatisticsRes {
        @ApiProperty({ description: '问题数' })
        total: string;
    }

    export class SourceGroupRes {
        @ApiProperty({ description: '问题来源code' })
        sourceCode: string;
        @ApiProperty({ description: '问题来源' })
        source: string;
        @ApiProperty({ description: '问题数' })
        total: string;
    }

    export class TypeGroupRes {
        @ApiProperty({ description: '问题类型code' })
        typeCode: string;
        @ApiProperty({ description: '问题类型' })
        type: string;
        @ApiProperty({ description: '问题数' })
        total: string;
    }

    export class StatusGroupRes {
        @ApiProperty({ description: '问题状态code' })
        statusCode: string;
        @ApiProperty({ description: '问题状态' })
        status: string;
        @ApiProperty({ description: '问题数' })
        total: string;
    }

    export class DeviceResultTypeGroupRes {
        @ApiProperty({ description: '摄像头捕捉类型code' })
        deviceResultTypeCode: string;
        @ApiProperty({ description: '问题来源类型: 1-安全,2-整洁度,3-相册-更新图片,4-相册-摄像头图片,5-相册-剪影图片' })
        problemSourceType: string;
        @ApiProperty({ description: '摄像头捕捉类型' })
        deviceResultType: string;
        @ApiProperty({ description: '问题数' })
        total: string;
    }

    export class WrongdoerDto {
        name: string;
        managerRole: string;
        nickName: string;
        headImg: string;
        deptRefId: string;
        faceId: string;
    }

    export class ListRes {
        @ApiProperty({ description: '问题id' })
        projectProblemId: string;
        @ApiProperty({ description: '问题整改id' })
        problemSolvedId: string;
        @ApiProperty({ description: '问题来源' })
        source: string;
        @ApiProperty({ description: '问题来源名' })
        sourceName: string;
        @ApiProperty({ description: '问题状态' })
        status: string;
        @ApiProperty({ description: '问题状态名' })
        statusName: string;
        @ApiProperty({ description: '问题分类' })
        type: string;
        @ApiProperty({ description: '问题分类名' })
        typeName: string;
        @ApiProperty({ description: '问题描述' })
        description: string;
        @ApiProperty({ description: '问题图片id' })
        problemImg: string;
        @ApiProperty({ description: '问题图片' })
        problemPhoto: YqzMedia[];
        @ApiProperty({ description: '报告日期' })
        reportTime: string;
        @ApiProperty({ description: '报告人id' })
        reporter: string;
        @ApiProperty({ description: '报告人' })
        reporterName: string;
        @ApiProperty({ description: '报告人是否是业主' })
        reportIsOwner: string;
        @ApiProperty({ description: '整改人id' })
        solver: string;
        @ApiProperty({ description: '整改人' })
        solverName: string;
        @ApiProperty({ description: '整改日期' })
        solveTime: string;
        @ApiProperty({ description: '整改描述' })
        solveDesc: string;
        @ApiProperty({ description: '整改图片id' })
        solvedImg: string;
        @ApiProperty({ description: '整改图片' })
        solvePhoto: YqzMedia[];
        @ApiProperty({ description: '标记用户id' })
        wrongdoerFaceId: string;
        @ApiProperty({ description: '标记用户' })
        wrongdoer: WrongdoerDto;
    }

    export class OptionsDto {
        @ApiProperty({ description: '选项名' })
        name: string;
        @ApiProperty({ description: '选项值' })
        value: string;
    }

    export class ListOptionsRes {
        @ApiProperty({ description: '问题状态', isArray: true, type: OptionsDto })
        status: OptionsDto[];
        @ApiProperty({ description: '问题分类', isArray: true, type: OptionsDto })
        type: OptionsDto[];
        @ApiProperty({ description: '问题来源', isArray: true, type: OptionsDto })
        source: OptionsDto[];
    }

    export const ListCsv: UtilType.propertyCsv[] = [
        {
            key: "sourceName",
            value: "问题来源",
            options: { defValue: "" }
        }, {
            key: "statusName",
            value: "问题状态",
            options: { defValue: "" }
        }, {
            key: "typeName",
            value: "问题分类",
            options: { defValue: "" }
        }, {
            key: "description",
            value: "问题描述",
            options: { defValue: "" }
        }, {
            key: "problemPhoto[*].mediaResourceUri",
            value: "问题图片",
            options: { defValue: "" }
        }, {
            key: "reportTime",
            value: "报告日期",
            options: { defValue: "" }
        }, {
            key: "reporterName",
            value: "报告人",
            options: { defValue: "" }
        }, {
            key: "solverName",
            value: "整改人",
            options: { defValue: "" }
        }, {
            key: "solveTime",
            value: "整改日期",
            options: { defValue: "" }
        }, {
            key: "solveDesc",
            value: "整改描述",
            options: { defValue: "" }
        }, {
            key: "solvePhoto[*].mediaResourceUri",
            value: "整改图片",
            options: { defValue: "" }
        }, {
            key: "wrongdoer.nickName",
            value: "标记用户",
            options: { defValue: "" }
        }
    ];

}