import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
import { YqzMedia } from "../../media/type/media.type";
import { UtilType } from "../../types/util.type";
import { Common } from "@yqz/nest";

export namespace ProjectSignNode {

    export class Req {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @IsNotEmpty({ message: '工地id不能为空' })
        @ApiProperty({ description: '工地id' })
        projectId: string;
        @ApiProperty({ description: '阶段模板id' })
        stageTemplateId?: string;
        @ApiProperty({ description: '阶段id' })
        businessStageId?: string;
        @ApiProperty({ description: '是否完工' })
        isFinish?: "Y" | "N";
        @ApiProperty({ description: '是否逾期' })
        isOverdue?: "Y" | "N";
        @ApiProperty({ description: '返回格式 json or csv，默认json', enum: ['csv', 'json'] })
        format?: "csv" | 'json';
        pageNo?: number;
        pageSize?: number;
    }

    export class FinishGroupRes {
        @ApiProperty({ description: '是否完成' })
        isFinish: "Y" | "N";
        @ApiProperty({ description: '状态' })
        status: "完成" | "未完成";
        @ApiProperty({ description: '节点数' })
        total: string;
    }

    export class OverdueGroupRes {
        @ApiProperty({ description: '是否逾期' })
        isOverdue: "Y" | "N";
        @ApiProperty({ description: '状态' })
        status: "逾期" | "未逾期";
        @ApiProperty({ description: '节点数' })
        total: string;
    }

    export class ListRes {
        @ApiProperty({ description: '节点id' })
        projectNodeId: string;
        @ApiProperty({ description: '阶段名' })
        stageName: string;
        @ApiProperty({ description: '阶段模板id' })
        stageTemplateId: string;
        @ApiProperty({ description: '阶段id' })
        businessStageId: string;
        @ApiProperty({ description: '节点状态（完成状态）' })
        isFinish: string;
        @ApiProperty({ description: '逾期状态' })
        isOverdue: string;
        @ApiProperty({ description: '逾期天数' })
        overdueDays: string;
        @ApiProperty({ description: '逾期状态拼接 节点逾期x天' })
        overdue: string;
        @ApiProperty({ description: '节点描述' })
        title: string;
        @ApiProperty({ description: '开始日期' })
        startTime: string;
        @ApiProperty({ description: '工期' })
        duration: string;
        @ApiProperty({ description: '截止日期' })
        deadlineTime: string;
        @ApiProperty({ description: '员工职位id' })
        roleIds: string;
        @ApiProperty({ description: '员工职位' })
        roleName: string;
        @ApiProperty({ description: '验收标准' })
        standardDescription: string;
        @ApiProperty({ description: '工期调整次数' })
        modifiedTotal: string;
        @ApiProperty({ description: '调整天数' })
        modifiedDays: string;
        @ApiProperty({ description: '申请人' })
        modifiedManager: string;
        @ApiProperty({ description: '申请时间' })
        modifiedTime: string;
        @ApiProperty({ description: '申请原因' })
        modifiedDescription: string;
        @ApiProperty({ description: '申请凭证' })
        modifiedPhotos: YqzMedia[];
    }

    export class StageOptions {
        @ApiProperty({ description: '阶段名' })
        stageName: string;
        @ApiProperty({ description: '阶段模板id' })
        stageTemplateId: string;
        @ApiProperty({ description: '阶段id' })
        businessStageId: string;
    }

    export class OptionsDto {
        @ApiProperty({ description: '选项名' })
        name: string;
        @ApiProperty({ description: '选项值' })
        value: string;
    }

    export class ListOptionsRes {
        @ApiProperty({ description: '阶段', isArray: true, type: StageOptions })
        stage: StageOptions[];
        @ApiProperty({ description: '完成状态', isArray: true, type: OptionsDto })
        isFinish: OptionsDto[];
        @ApiProperty({ description: '逾期状态', isArray: true, type: OptionsDto })
        isOverdue: OptionsDto[];
    }

    export class NodeModifiedRes {
        projectNodeId: string;
        description: string;
        modifiedPerson: string;
        modifiedTime: string;
        type: string;
        day: string;
        photoList: string;
    }

    export const ListCsv: UtilType.propertyCsv[] = [
        {
            key: "stageName",
            value: "阶段",
            options: { defValue: "" }
        }, {
            key: "isFinish",
            value: "节点状态",
            options: {
                values: [
                    {
                        key: Common.Flag.N,
                        value: "待完成"
                    },
                    {
                        key: Common.Flag.Y,
                        value: "已完成"
                    }
                ]
            }
        }, {
            key: "overdue",
            value: "逾期状态",
            options: { defValue: "" }
        }, {
            key: "title",
            value: "节点描述",
            options: { defValue: "" }
        }, {
            key: "startTime",
            value: "开始日期",
            options: { defValue: "" }
        }, {
            key: "duration",
            value: "x个工作日",
            options: { defValue: "" }
        }, {
            key: "deadlineTime",
            value: "截止日期",
            options: { defValue: "" }
        }, {
            key: "roleName",
            value: "员工职位",
            options: { defValue: "" }
        }, {
            key: "standardDescription",
            value: "验收标准",
            options: { defValue: "" }
        }, {
            key: "modifiedTotal",
            value: "工期调整次数",
            options: { defValue: "" }
        }, {
            key: "modifiedDays",
            value: "调整天数",
            options: { defValue: "" }
        }, {
            key: "modifiedManager",
            value: "申请人",
            options: { defValue: "" }
        }, {
            key: "modifiedTime",
            value: "申请时间",
            options: { defValue: "" }
        }, {
            key: "modifiedDescription",
            value: "申请原因",
            options: { defValue: "" }
        }, {
            key: "modifiedPhotos[*].mediaResourceUri",
            value: "申请凭证",
            options: { defValue: "" }
        },
    ];

}