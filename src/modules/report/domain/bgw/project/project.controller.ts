import { Body, Controller, Post } from "@nestjs/common";
import { Api<PERSON>earerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { CheckFormatCsv, JwtUserGw, MyLogger, MyUser } from "@yqz/nest";
import { ProjectService } from "./service/project.service";
import { Project } from "./dto/project.dto";

@ApiTags("project")
@Controller("project")
@ApiBearerAuth()
export class ProjectController {
    private readonly logger = new MyLogger(ProjectController.name);

    constructor(
        private readonly projectService: ProjectService
    ) { }

    @ApiOperation({ summary: '成员工地签到统计 & 成员签到天数排名列表' })
    @Post("/member/signin/count")
    @CheckFormatCsv(Project.SignInManagerGroupCsv, 'list')
    async projectMemberSignInCount(@Body() body: Project.ProjectMemberSignInReq, @MyUser("v2") user: JwtUserGw) {
        body.companyId = user.targetCompany?.companyId || body.companyId;
        return await this.projectService.getProjectMemberSignInCount(body);
    }

    @ApiOperation({ summary: '成员工地签到列表' })
    @Post("/member/signin/list")
    @CheckFormatCsv(Project.SignInListCsv)
    async projectMemberSignInList(@Body() body: Project.ProjectMemberSignInReq, @MyUser("v2") user: JwtUserGw) {
        body.companyId = user.targetCompany?.companyId || body.companyId;
        return await this.projectService.projectMemberSignInList(body);
    }

    @ApiOperation({ summary: '成员工地签到列表,按天聚合' })
    @Post("/member/signin/list/byday")
    @CheckFormatCsv(Project.SignInListCsv)
    async projectMemberSignInListByday(@Body() body: Project.ProjectMemberSignInReq, @MyUser("v2") user: JwtUserGw) {
        body.companyId = user.targetCompany?.companyId || body.companyId;
        return await this.projectService.projectMemberSignInListByday(body);
    }
    
    @ApiOperation({ summary: '成员工地签到列表筛选项' })
    @Post("/member/signin/list/options")
    async projectMemberSignInListOptions(@Body() body: Project.ProjectMemberSignInReq, @MyUser("v2") user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectService.projectMemberSignInListOptions(body);
    }

    @ApiOperation({ summary: '工地签到统计' })
    @Post("/signin/statistics")
    async projectSignInCount(@Body() body: { companyId: string, projectId: string }, @MyUser("v2") user: JwtUserGw) {
        body.companyId = user.targetCompany?.companyId || body.companyId;
        return await this.projectService.getProjectSignCount(body);
    }

    @ApiOperation({ summary: '工地开工记录' })
    @Post("/active/history")
    async projectActiveHistory(@Body() body: any, @MyUser() user: JwtUserGw) {
        return await this.projectService.getProjectActiveHistory(body);
    }

    @ApiOperation({ summary: '工地开工统计' })
    @Post("/active/statistics")
    async projectActiveStatistics(@Body() body: any) {
        return await this.projectService.getProjectActiveStatistics(body);
    }

    @ApiOperation({ summary: '工地整洁度记录' })
    @Post("/tidness/history")
    async projectTidinessHistory(@Body() body: any) {
        return await this.projectService.getProjectTidinessHistory(body);
    }

    @ApiOperation({ summary: '工地整洁度统计' })
    @Post("/tidness/statistics")
    async projectTidinessStatistics(@Body() body: any) {
        return await this.projectService.getProjectTidinessStatistics(body);
    }

    @ApiOperation({ summary: '工地相册列表' })
    @Post("album/list/detail")
    async projectAlbumListDetail(@Body() body: Project.ProjectAlbumListDetailReq) {
        return await this.projectService.getProjectAlbumListDetail(body);
    }
}