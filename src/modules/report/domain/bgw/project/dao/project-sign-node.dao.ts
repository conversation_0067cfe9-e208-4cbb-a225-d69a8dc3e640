import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { Common, DevTimeout } from "@yqz/nest";
import { ProjectNodeEntity } from "@src/modules/report/entity/bgw/project-node.entity";
import { ProjectSignNode } from "../dto/project-sign-node.dto";
import { StageEntity } from "@src/modules/report/entity/bgw/stage.entity";
import { ProjectScheduleModifiedEntity } from "@src/modules/report/entity/bgw/project-schedule-modified.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";

@Injectable()
export class ProjectSignNodeDao {
    private readonly logger = new MyLogger(ProjectSignNodeDao.name)
    constructor(
        private dataSource: DataSource
    ) { }

    @DevTimeout(500)
    async test() {
    }

    /**
     * 工地看板节点完成情况分布
     * @param request 
     * @returns 
     */
    async projectNodeFinishGroup(request: { projectId: string }) {
        const { projectId } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pn.is_finish isFinish',
                'COUNT(1) as total',
            ])
            .from(ProjectNodeEntity, 'pn')
            .where('pn.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pn.project_id = :projectId', { projectId })
            .groupBy('pn.is_finish').orderBy('pn.is_finish', 'ASC');
        return await qb.getRawMany<ProjectSignNode.FinishGroupRes>();
    }

    /**
     * 工地看板节点逾期情况分布
     * @param request 
     * @returns 
     */
    async projectNodeOverdueGroup(request: { projectId: string, isFinish?: 'Y' | 'N', isNodeDelayOverdue?: 'Y' | 'N' }) {
        const { projectId, isFinish, isNodeDelayOverdue } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                `CASE 
                    WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                        SELECT 1 FROM project_schedule_modified psm2 
                        WHERE psm2.project_node_id = pn.project_node_id 
                        AND psm2.DELETE_FLAG = 'N'
                    ) THEN 
                        CASE WHEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) >= 0 THEN 'N' ELSE 'Y' END
                    ELSE 
                        CASE WHEN DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) >= 0 THEN 'N' ELSE 'Y' END
                END as isOverdue`,
                'COUNT(1) as total',
            ])
            .from(ProjectNodeEntity, 'pn')
            .where('pn.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pn.project_id = :projectId', { projectId })
            .setParameter('isNodeDelayOverdue', isNodeDelayOverdue || 'N');
        if (isFinish) qb.andWhere('pn.is_finish = :isFinish', { isFinish });
        qb.groupBy('isOverdue').orderBy('isOverdue', 'ASC');
        return await qb.getRawMany<ProjectSignNode.OverdueGroupRes>();
    }

    /**
     * 工地看板节点阶段列表
     * @param request 
     * @returns 
     */
    async projectNodeStageList(request: { projectId: string }) {
        const { projectId } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                's.stage_name as stageName',
                's.STAGE_TEMPLATE_ID as stageTemplateId',
                's.BUSINESS_STAGE_ID as businessStageId'
            ])
            .from(ProjectNodeEntity, 'pn')
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pn.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pn.BUSINESS_STAGE_ID')
            .where('pn.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pn.project_id = :projectId', { projectId })
            .groupBy('s.stage_id').orderBy('s.sort', 'ASC');
        return await qb.getRawMany<ProjectSignNode.StageOptions>();
    }

    /**
     * 工地看板节点列表
     * @param request 
     * @returns 
     */
    async projectNodeList(request: { 
        projectId: string, 
        stageTemplateId?: string, 
        businessStageId?: string, 
        isFinish?: string, 
        isOverdue?: string, 
        pageNo?: number, 
        pageSize?: number,
        isNodeDelayOverdue?: 'Y' | 'N'
    }) {
        const { projectId, stageTemplateId, businessStageId, isFinish, isOverdue, pageNo, pageSize, isNodeDelayOverdue } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pn.project_node_id as projectNodeId',
                's.stage_name as stageName',
                's.STAGE_TEMPLATE_ID as stageTemplateId',
                's.BUSINESS_STAGE_ID as businessStageId',
                'pn.is_finish as isFinish',
                `CASE 
                    WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                        SELECT 1 FROM project_schedule_modified psm2 
                        WHERE psm2.project_node_id = pn.project_node_id 
                        AND psm2.DELETE_FLAG = 'N'
                    ) THEN 
                        CASE WHEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) >= 0 THEN 'N' ELSE 'Y' END
                    ELSE 
                        CASE WHEN DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) >= 0 THEN 'N' ELSE 'Y' END
                END as isOverdue`,
                `CASE 
                    WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                        SELECT 1 FROM project_schedule_modified psm2 
                        WHERE psm2.project_node_id = pn.project_node_id 
                        AND psm2.DELETE_FLAG = 'N'
                    ) THEN 
                        CASE 
                            WHEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) >= 0 THEN 0 
                            ELSE ABS(DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())))
                        END
                    ELSE 
                        CASE 
                            WHEN DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) >= 0 THEN 0 
                            ELSE ABS(DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())))
                        END
                END as overdueDays`,
                'pn.title as title',
                'DATE_FORMAT(pn.start_time, "%Y-%m-%d") as startTime',
                'pn.duration as duration',
                'DATE_FORMAT(pn.deadline_time, "%Y-%m-%d") as deadlineTime',
                'pn.company_member_role_id as roleIds',
                'pn.standard_description as standardDescription',
            ])
            .from(ProjectNodeEntity, 'pn')
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pn.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pn.BUSINESS_STAGE_ID')
            .where('pn.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pn.project_id = :projectId', { projectId })
            .setParameter('isNodeDelayOverdue', isNodeDelayOverdue || 'N');

        if (stageTemplateId) qb.andWhere('pn.STAGE_TEMPLATE_ID = :stageTemplateId', { stageTemplateId });
        if (businessStageId) qb.andWhere('pn.BUSINESS_STAGE_ID = :businessStageId', { businessStageId });
        if (isFinish) qb.andWhere('pn.is_finish = :isFinish', { isFinish });
        if (isOverdue) {
            if (isOverdue === 'N') {
                qb.andWhere(`
                    CASE 
                        WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                            SELECT 1 FROM project_schedule_modified psm2 
                            WHERE psm2.project_node_id = pn.project_node_id 
                            AND psm2.DELETE_FLAG = 'N'
                        ) THEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) >= 0
                        ELSE DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) >= 0
                    END
                `);
            } else {
                qb.andWhere(`
                    CASE 
                        WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                            SELECT 1 FROM project_schedule_modified psm2 
                            WHERE psm2.project_node_id = pn.project_node_id 
                            AND psm2.DELETE_FLAG = 'N'
                        ) THEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) < 0
                        ELSE DATEDIFF(pn.deadline_time, ifnull(pn.finish_time,NOW())) < 0
                    END
                `);
            }
        }
        qb.orderBy('s.sort', 'ASC').addOrderBy('pn.sort', 'ASC');
        if (pageSize && pageNo) qb.limit(pageSize).offset((pageNo - 1) * pageSize);
        return await qb.getRawMany<ProjectSignNode.ListRes>();
    }

    /**
     * 工地看板节点列表统计
     * @param request 
     * @returns 
     */
    async projectNodeCount(request: { 
        projectId: string, 
        stageTemplateId?: string, 
        businessStageId?: string, 
        isFinish?: string, 
        isOverdue?: string,
        isNodeDelayOverdue?: 'Y' | 'N'
    }) {
        const { projectId, stageTemplateId, businessStageId, isFinish, isOverdue, isNodeDelayOverdue } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select(['count(1) num'])
            .from(ProjectNodeEntity, 'pn')
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pn.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pn.BUSINESS_STAGE_ID')
            .where('pn.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pn.project_id = :projectId', { projectId })
            .setParameter('isNodeDelayOverdue', isNodeDelayOverdue || 'N');
        if (stageTemplateId) qb.andWhere('pn.STAGE_TEMPLATE_ID = :stageTemplateId', { stageTemplateId });
        if (businessStageId) qb.andWhere('pn.BUSINESS_STAGE_ID = :businessStageId', { businessStageId });
        if (isFinish) qb.andWhere('pn.is_finish = :isFinish', { isFinish });
        if (isOverdue) {
            if (isOverdue === 'N') {
                qb.andWhere(`
                    CASE 
                        WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                            SELECT 1 FROM project_schedule_modified psm2 
                            WHERE psm2.project_node_id = pn.project_node_id 
                            AND psm2.DELETE_FLAG = 'N'
                        ) THEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) >= 0
                        ELSE DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) >= 0
                    END
                `);
            } else {
                qb.andWhere(`
                    CASE 
                        WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
                            SELECT 1 FROM project_schedule_modified psm2 
                            WHERE psm2.project_node_id = pn.project_node_id 
                            AND psm2.DELETE_FLAG = 'N'
                        ) THEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) < 0
                        ELSE DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) < 0
                    END
                `);
            }
        }
        const res = await qb.getRawOne();
        return Number(res?.num || 0);
    }

    /**
     * 查询节点调整记录
     * @param param 
     * @returns 
     */
    async searchNodeModified(request: { photoNodeIdList: string[] }) {
        const { photoNodeIdList } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'psm.project_node_id as projectNodeId',
                'psm.description as description',
                'm.nick_name as modifiedPerson',
                "DATE_FORMAT(psm.modified_time, '%Y-%m-%d') as modifiedTime",
                'psm.type as type',
                'psm.day as day',
                'psm.photo_list as photoList',
            ])
            .from(ProjectScheduleModifiedEntity, 'psm')
            .leftJoin(ManagerEntity, 'm', 'psm.modified_manager_id = m.MANAGER_ID')
            .where('psm.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
            .andWhere('psm.project_node_id in (:...photoNodeIdList)', { photoNodeIdList })
            .orderBy('psm.id', 'DESC');
        return await qb.getRawMany<ProjectSignNode.NodeModifiedRes>();
    }

}