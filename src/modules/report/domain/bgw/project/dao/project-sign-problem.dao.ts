import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { Common, DevTimeout } from "@yqz/nest";
import { ProjectProblemEntity } from "@src/modules/report/entity/bgw/project-problem.entity";
import { ProjectSignProblem } from "../dto/project-sign-problem.dto";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { ProblemSolvedEntity } from "@src/modules/report/entity/bgw/problem-solved.entity";

@Injectable()
export class ProjectSignProblemDao {
    private readonly logger = new MyLogger(ProjectSignProblemDao.name)
    constructor(
        private dataSource: DataSource
    ) { }

    @DevTimeout(500)
    async test() {
    }

    /**
     * 工地看板问题统计
     * @param request 
     * @returns 
     */
    async projectProblemStatistics(request: { projectId?: string, startTime?: string, endTime?: string }) {
        const { projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(1) as total',
            ])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N });
        if (projectId) qb.andWhere('pp.project_id = :projectId', { projectId });
        if (startTime) qb.andWhere('pp.problem_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.problem_time <= :endTime', { endTime });
        qb.groupBy('pp.project_id');
        return await qb.getRawOne<ProjectSignProblem.StatisticsRes>();
    }

    /**
     * 工地看板问题来源分布
     * @param request 
     * @returns 
     */
    async projectProblemSourceGroup(request: { projectId: string, startTime?: string, endTime?: string }) {
        const { projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.problem_source sourceCode',
                'COUNT(1) as total',
            ])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N });
        if (projectId) qb.andWhere('pp.project_id = :projectId', { projectId });
        if (startTime) qb.andWhere('pp.problem_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.problem_time <= :endTime', { endTime });
        qb.groupBy('pp.problem_source').orderBy('pp.problem_source', 'ASC');
        return await qb.getRawMany<ProjectSignProblem.SourceGroupRes>();
    }

    /**
     * 工地看板问题分类分布
     * @param request 
     * @returns 
     */
    async projectProblemTypeGroup(request: { projectId: string, startTime?: string, endTime?: string }) {
        const { projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.problem_type typeCode',
                'COUNT(1) as total',
            ])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N });
        if (projectId) qb.andWhere('pp.project_id = :projectId', { projectId });
        if (startTime) qb.andWhere('pp.problem_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.problem_time <= :endTime', { endTime });
        qb.groupBy('pp.problem_type').orderBy('pp.problem_type', 'ASC');
        return await qb.getRawMany<ProjectSignProblem.TypeGroupRes>();
    }

    /**
     * 工地看板问题状态分布
     * @param request 
     * @returns 
     */
    async projectProblemStatusGroup(request: { projectId: string, startTime?: string, endTime?: string }) {
        const { projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.problem_status statusCode',
                'COUNT(1) as total',
            ])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N });
        if (projectId) qb.andWhere('pp.project_id = :projectId', { projectId });
        if (startTime) qb.andWhere('pp.problem_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.problem_time <= :endTime', { endTime });
        qb.groupBy('pp.problem_status').orderBy('pp.problem_status', 'ASC');
        return await qb.getRawMany<ProjectSignProblem.StatusGroupRes>();
    }

    /**
     * 工地看板问题摄像头捕捉问题分布
     * @param request 
     * @returns 
     */
    async projectProblemDeviceResultTypeGroup(request: { projectId: string, startTime?: string, endTime?: string }) {
        const { projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.problem_source_result_type deviceResultTypeCode',
                'pp.problem_source_type problemSourceType',
                'COUNT(1) as total',
            ])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pp.problem_source = 1')//问题来源: 摄像头
        if (projectId) qb.andWhere('pp.project_id = :projectId', { projectId });
        if (startTime) qb.andWhere('pp.problem_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.problem_time <= :endTime', { endTime });
        qb.groupBy('pp.problem_source_type').groupBy('pp.problem_source_result_type')
        qb.orderBy('pp.problem_source_type', 'ASC').orderBy('pp.problem_source_result_type', 'ASC');
        return await qb.getRawMany<ProjectSignProblem.DeviceResultTypeGroupRes>();
    }

    /**
     * 工地看板问题列表
     * @param request 
     * @returns 
     */
    async projectProblemList(request: { projectId: string, type?: string, source?: string, status?: string, startTime?: string, endTime?: string, pageNo?: number, pageSize?: number }) {
        const { projectId, type, source, status, startTime, endTime, pageNo, pageSize } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.project_problem_id as projectProblemId',
                'pp.problem_solved_id as problemSolvedId',
                'pp.problem_source as source',
                'pp.problem_status as status',
                'pp.problem_type as type',
                'pp.problem_description description',
                'pp.problem_img problemImg',
                "DATE_FORMAT(pp.problem_time, '%Y-%m-%d %H:%i:%s') reportTime",
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, pp.problem_record_owner, m.person_id) as reporter",
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, owner.nick_name, m.nick_name) reporterName",
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, 'Y', 'N') reportIsOwner",
                'ps.person solver',
                'solver.nick_name solverName',
                "DATE_FORMAT(ps.time,'%Y-%m-%d %H:%i:%s') solveTime",
                'ps.description solveDesc',
                'ps.image solvedImg',
                'pp.wrongdoer_face_id wrongdoerFaceId',
            ])
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id and ps.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = pp.problem_record_person')
            .leftJoin(PersonEntity, 'owner', 'owner.person_id = pp.problem_record_owner')
            .leftJoin(ManagerEntity, 'solver', 'solver.manager_id = ps.person')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pp.project_id = :projectId', { projectId });
        if (type) qb.andWhere('pp.problem_type = :type', { type });
        if (source) qb.andWhere('pp.problem_source = :source', { source });
        if (status) qb.andWhere('pp.problem_status = :status', { status });
        if (startTime) qb.andWhere('pp.createTime >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.createTime <= :endTime', { endTime });
        qb.orderBy('pp.problem_time', 'DESC').addOrderBy('pp.project_problem_id', 'DESC');
        if (pageSize && pageNo) qb.limit(pageSize).offset((pageNo - 1) * pageSize);
        return await qb.getRawMany<ProjectSignProblem.ListRes>();
    }

    /**
     * 工地看板问题列表统计
     * @param request 
     * @returns 
     */
    async projectProblemCount(request: { projectId: string, type?: string, source?: string, status?: string, startTime?: string, endTime?: string }) {
        const { projectId, type, source, status, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select(['count(1) num'])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('pp.project_id = :projectId', { projectId });
        if (type) qb.andWhere('pp.problem_type = :type', { type });
        if (source) qb.andWhere('pp.problem_source = :source', { source });
        if (status) qb.andWhere('pp.problem_status = :status', { status });
        if (startTime) qb.andWhere('pp.createTime >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.createTime <= :endTime', { endTime });
        const res = await qb.getRawOne();
        return Number(res?.num || 0);
    }

}