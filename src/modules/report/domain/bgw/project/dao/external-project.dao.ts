import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { MyLogger } from '@src/modules/logger/my-logger.service';
import { ProjectEntity } from '../../../../entity/bgw/project.entity';
import { CompanyEntity } from '../../../../entity/bgw/company.entity';
import { StageEntity } from '../../../../entity/bgw/stage.entity';
import { DeviceProjectInfoEntity } from '../../../../entity/camera/device-project-info.entity';
import { DeviceInfoEntity } from '../../../../entity/camera/device-info.entity';
import { DeviceStatusEntity } from '../../../../entity/camera/device-status.entity';
import { ExternalMerchantProjectRelationEntity } from '@src/modules/report/entity/external-merchant/external-merchant-project-relation.entity';
import * as _ from 'lodash';


@Injectable()
export class ExternalProjectDao {
    private readonly logger = new MyLogger(ExternalProjectDao.name);

    constructor(private dataSource: DataSource) {}

    async findCompanyProjects(
        companyId: string,
        projectName?: string,
        pageNo: number = 1,
        pageSize: number = 10,
        excludeProjectIds: string[] = []
    ): Promise<{ items: any[], total: number }> {
        const queryBuilder = this.dataSource
            .createQueryBuilder(ProjectEntity, 'p')
            .leftJoin(CompanyEntity, 'c', 'p.COMPANY_ID = c.COMPANY_ID')
            .leftJoin(DeviceStatusEntity, 'ds', 'p.PROJECT_ID = ds.project_id and ds.delete_flag = "N"')
            .leftJoin(StageEntity, 'st', 'p.STAGE_TEMPLATE_ID = st.STAGE_TEMPLATE_ID and p.BUSINESS_STAGE_ID = st.BUSINESS_STAGE_ID') 
            .where('p.COMPANY_ID = :companyId', { companyId })
            .andWhere('p.DELETE_FLAG = :projectDeleteFlag', { projectDeleteFlag: 'N' })
            .andWhere('c.DELETE_FLAG = :companyDeleteFlag', { companyDeleteFlag: 'N' })
            .andWhere('st.DELETE_FLAG = :dsDeleteFlag', { dsDeleteFlag: 'N' })

        if (!_.isEmpty(excludeProjectIds.length)) {
            queryBuilder.andWhere('p.PROJECT_ID NOT IN (:...excludeProjectIds)', { excludeProjectIds });
        }

        if (projectName) {
            queryBuilder.andWhere('p.PROJECT_NAME LIKE :projectNameSearch', { projectNameSearch: `%${projectName}%` });
        }

        const countQueryBuilder = queryBuilder.clone();
        const total = await countQueryBuilder.getCount();
        
        const items = await queryBuilder
            .select([
                'p.PROJECT_ID as projectId',
                'p.PROJECT_NAME as projectAddress',
                'p.project_start_time as startTime', 
                'p.COMPANY_ID as companyId',
                'c.COMPANY_NAME as companyName',
                'st.stage_id as stageId', 
                'st.STAGE_NAME as stageName',
                'ds.cover_image_url as coverImageUrl',
                'max(ds.is_online) as isOnline',
                'count(ds.yqz_device_id) as deviceNum',
            ])
            .groupBy('p.PROJECT_ID')
            .orderBy('p.PROJECT_ID', 'DESC')
            .limit(pageSize)
            .offset((pageNo - 1) * pageSize)
            .getRawMany();

        return {
            items: items,
            total: total
        };
    }

    async findProjectDetail(projectId: string): Promise<any> {
        return await this.dataSource
            .createQueryBuilder(ProjectEntity, 'p')
            .select([
                'p.PROJECT_ID as projectId',
                'p.PROJECT_NAME as projectAddress',
                'p.project_start_time as startTime',
                'p.COMPANY_ID as companyId',
                'c.COMPANY_NAME as companyName',
                'st.stage_id as stageId',
                'st.STAGE_NAME as stageName'
            ])
            .leftJoin(CompanyEntity, 'c', 'p.COMPANY_ID = c.COMPANY_ID')
            .leftJoin(StageEntity, 'st', 'p.STAGE_TEMPLATE_ID = st.STAGE_TEMPLATE_ID and p.BUSINESS_STAGE_ID = st.BUSINESS_STAGE_ID')
            .where('p.PROJECT_ID = :projectId', { projectId })
            .andWhere('p.DELETE_FLAG = :projectDeleteFlag', { projectDeleteFlag: 'N' })
            .getRawOne();
    }

    async findProjectDevices(projectId: string): Promise<any[]> {
        return await this.dataSource
            .createQueryBuilder(DeviceProjectInfoEntity, 'dpi')
            .select([
                'di.yqz_device_id as deviceId',     
                'dpi.device_remark as deviceRemark',
                'dpi.cover_image_url as coverImageUrl',
                'ds.is_online as isOnline',
                'di.device_serial as deviceSerial'          
            ])
            .innerJoin(DeviceInfoEntity, 'di', 'dpi.yqz_device_id = di.yqz_device_id') 
            .innerJoin(DeviceStatusEntity, 'ds', 'dpi.yqz_device_id = ds.yqz_device_id')
            .where('dpi.project_id = :projectId', { projectId }) 
            .andWhere('dpi.DELETE_FLAG = :dpiDeleteFlag', { dpiDeleteFlag: 'N' }) 
            .andWhere('di.DELETE_FLAG = :diDeleteFlag', { diDeleteFlag: 'N' })   
            .andWhere('ds.DELETE_FLAG = :dsDeleteFlag', { dsDeleteFlag: 'N' })   
            .getRawMany();
    }

    async checkProjectsBindingDevices(projectIds: string[]): Promise<Record<string, boolean>> {
        if (!projectIds || projectIds.length === 0) {
            return {};
        }

        const results = await this.dataSource
            .createQueryBuilder(DeviceStatusEntity, 'dpi')
            .select('DISTINCT dpi.project_id as projectId') 
            .where('dpi.project_id IN (:...projectIds)', { projectIds })
            .andWhere('dpi.DELETE_FLAG = :deleteFlag', { deleteFlag: 'N' }) 
            .getRawMany();
        
        const bindingMap: Record<string, boolean> = {};
        projectIds.forEach(id => {
            bindingMap[id] = false;
        });
        
        results.forEach((row: any) => {
            if (row.projectId) { 
                bindingMap[row.projectId] = true;
            }
        });
        
        return bindingMap;
    }

    async checkCompanyExists(companyId: string): Promise<boolean> {
        const count = await this.dataSource
            .createQueryBuilder(CompanyEntity, 'company')
            .where('company.COMPANY_ID = :companyId', { companyId })
            .andWhere('company.DELETE_FLAG = :deleteFlag', { deleteFlag: 'N' })
            .getCount();
        return count > 0;
    }

    async checkProjectExists(projectId: string): Promise<boolean> {
        const count = await this.dataSource
            .createQueryBuilder(ProjectEntity, 'project')
            .where('project.PROJECT_ID = :projectId', { projectId })
            .andWhere('project.DELETE_FLAG = :deleteFlag', { deleteFlag: 'N' })
            .getCount();
        return count > 0;
    }

    async checkProjectBelongsToCompany(projectId: string, companyId: string): Promise<boolean> {
        const count = await this.dataSource
            .createQueryBuilder(ProjectEntity, 'project')
            .where('project.PROJECT_ID = :projectId', { projectId })
            .andWhere('project.COMPANY_ID = :companyId', { companyId })
            .andWhere('project.DELETE_FLAG = :deleteFlag', { deleteFlag: 'N' })
            .getCount();
        return count > 0;
    }

    // 获取公司基本信息
    async findCompanyById(companyId: string): Promise<any> {
        return await this.dataSource
            .createQueryBuilder(CompanyEntity, 'company')
            .select([
                'company.COMPANY_ID as id',
                'company.COMPANY_NAME as name'
            ])
            .where('company.COMPANY_ID = :companyId', { companyId })
            .andWhere('company.DELETE_FLAG = :deleteFlag', { deleteFlag: 'N' })
            .getRawOne();
    }

    // 批量获取公司基本信息
    async findCompaniesByIds(companyIds: string[]): Promise<any[]> {
        if (!companyIds || companyIds.length === 0) {
            return [];
        }
        
        return await this.dataSource
            .createQueryBuilder(CompanyEntity, 'company')
            .select([
                'company.COMPANY_ID as id',
                'company.COMPANY_NAME as name'
            ])
            .where('company.COMPANY_ID IN (:...companyIds)', { companyIds })
            .andWhere('company.DELETE_FLAG = :deleteFlag', { deleteFlag: 'N' })
            .getRawMany();
    }

    async findAuthorizedProjects(
        merchantId: string,
        companyId?: string,
        projectName?: string,
        pageNo: number = 1,
        pageSize: number = 10,
        includeNoDeviceProjects?: boolean
    ): Promise<{ items: any[], total: number }> {
        const queryBuilder = this.dataSource
            .createQueryBuilder(ExternalMerchantProjectRelationEntity, 'rel')
            .innerJoin(ProjectEntity, 'p', 'rel.project_id = p.PROJECT_ID')
            .innerJoin(CompanyEntity, 'c', 'p.COMPANY_ID = c.COMPANY_ID')
            .leftJoin(StageEntity, 'st', 'p.STAGE_TEMPLATE_ID = st.STAGE_TEMPLATE_ID and p.BUSINESS_STAGE_ID = st.BUSINESS_STAGE_ID')
            .leftJoin(DeviceStatusEntity, 'ds', 'p.PROJECT_ID = ds.project_id and ds.delete_flag = "N"')

            .where('rel.merchant_id = :merchantId', { merchantId })
            .andWhere('rel.DELETE_FLAG = :relDeleteFlag', { relDeleteFlag: 'N' })
            .andWhere('p.DELETE_FLAG = :pDeleteFlag', { pDeleteFlag: 'N' })
            .andWhere('c.DELETE_FLAG = :cDeleteFlag', { cDeleteFlag: 'N' })
            // .andWhere('ds.DELETE_FLAG = :dsDeleteFlag', { dsDeleteFlag: 'N' })
            .andWhere('st.DELETE_FLAG = :stDeleteFlag', { stDeleteFlag: 'N' });

        if (!includeNoDeviceProjects) {
            queryBuilder.andWhere('ds.DELETE_FLAG = :dsDeleteFlag', { dsDeleteFlag: 'N' })
        }

        if (companyId) {
            queryBuilder.andWhere('rel.company_id = :companyId', { companyId });
        }

        if (projectName) {
            queryBuilder.andWhere('p.PROJECT_NAME LIKE :projectNameSearch', { projectNameSearch: `%${projectName}%` });
        }
        
        queryBuilder.groupBy('p.PROJECT_ID');

        const countQueryBuilder = queryBuilder.clone();
        const groupedResultsForCount = await countQueryBuilder.select('p.PROJECT_ID').getRawMany();
        const total = groupedResultsForCount.length;

        const items = await queryBuilder
            .select([
                'p.PROJECT_ID as projectId',
                'p.PROJECT_NAME as projectAddress',
                'p.COMPANY_ID as companyId',
                'c.COMPANY_NAME as companyName',
                'st.stage_id as stageId',
                'st.STAGE_NAME as stageName',
                'rel.added_at as addedAt',
                'max(ds.is_online) as isOnline',
                'count(ds.yqz_device_id) as deviceNum',
                'ds.cover_image_url as coverImageUrl'
            ])
            .groupBy('p.PROJECT_ID')
            .orderBy('p.PROJECT_ID', 'DESC')
            .limit(pageSize)
            .offset((pageNo - 1) * pageSize)
            .getRawMany();

        return {
            items: items,
            total: total
        };
    }

} 