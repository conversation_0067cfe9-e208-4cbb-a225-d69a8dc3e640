import * as _ from "lodash";
import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ogger } from "@yqz/nest";
import { DataSource, SelectQueryBuilder } from "typeorm";
import { Project } from "../dto/project.dto";
import { ProjectNodeEntity } from "@src/modules/report/entity/bgw/project-node.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { NodeTemplateEntity } from "@src/modules/report/entity/bgw/node-template.entity";
import { StageEntity } from "@src/modules/report/entity/bgw/stage.entity";
import { ProjectScheduleModifiedEntity } from "@src/modules/report/entity/bgw/project-schedule-modified.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { CommonParameterEntity } from "@src/modules/report/entity/bgw/common-parameter.entity";
import { DeviceProjectInfoEntity } from "@src/modules/report/entity/camera/device-project-info.entity";

@Injectable()
export class ProjectNodeDao {
  private readonly logger = new MyLogger(ProjectNodeDao.name)
  constructor(
    private dataSource: DataSource
  ) { }

  async searchProjectNodeIdList(params: { projectIdList: string[] }) {
    if (_.isEmpty(params.projectIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select('pn.project_node_id', 'projectNodeId')
      .from(ProjectEntity, 'p')
      .innerJoin(ProjectNodeEntity, 'pn', 'pn.project_id = p.project_id and pn.delete_flag = "N"')
      .where('p.delete_flag = "N"')
      .andWhere('p.bind_project_node = "Y"')
      .andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList })
      .groupBy('pn.project_node_id')
    const res = await qb.getRawMany<{ projectNodeId: string }>();
    return res.map(item => item.projectNodeId);
  }

  async searchProjectList(params: Project.SearchProjectNodeProjectReq) {
    if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.company_id as linkCompanyId',
        'p.project_id as projectId',
        'pn.project_node_id as projectNodeId',
        'p.project_start_time as projectStartTime',
        'pn.start_time as startTime',//节点开工时间
        'pn.deadline_time as deadlineTime',//节点调整后截止时间
        `IFNULL(pn.origin_deadline_time, pn.deadline_time) as beforeDeadlineTime`,//节点调整前截止时间
        'pn.is_finish as nodeStatus',
        'nt.node_template_name as nodeTemplateName',
        'pn.finish_time as completedTime',
        's.stage_name as stageName',
        'pn.company_member_role_id as roleIdStr',
        'pn.title as nodeName',
        'pn.duration_modified as scheduleModifyDay',

      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList })
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.project_id');
    qb.orderBy('p.project_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    const res = await DaoUtil.getPageRes<Project.SearchProjectNodeRes>(qb);
    return res;
  }

  async searchProjectNodeProjectList(params: Project.SearchProjectNodeNodeReq) {
    if (_.isEmpty(params.nodeIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
      ])
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'pn.project_id = p.project_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: params.nodeIdList })
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('p.project_id');
    return await qb.getRawMany<{ projectId: string }>();
  }

  async searchProjectNodeList(params: Project.SearchProjectNodeNodeReq) {
    if (_.isEmpty(params.nodeIdList)) return { total: 0, items: [] };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.company_id as linkCompanyId',
        'p.project_id as projectId',
        'pn.project_node_id as projectNodeId',
        'p.project_start_time as projectStartTime',
        'pn.start_time as startTime',//节点开工时间
        'pn.deadline_time as deadlineTime',//节点调整后截止时间
        `IFNULL(pn.origin_deadline_time, pn.deadline_time) as beforeDeadlineTime`,//节点调整前截止时间
        'pn.is_finish as nodeStatus',
        'nt.node_template_name as nodeTemplateName',
        'pn.finish_time as completedTime',
        's.stage_name as stageName',
        'pn.company_member_role_id as roleIdStr',
        'pn.title as nodeName',
        'pn.duration_modified as scheduleModifyDay',
      ])
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'pn.project_id = p.project_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: params.nodeIdList })
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('pn.project_node_id');
    qb.orderBy('pn.project_node_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    const res = await DaoUtil.getPageRes<Project.SearchProjectNodeRes>(qb);
    return res;
  }

  async getProjectScheduleModifiedInfo({ projectNodeIds }: { projectNodeIds: string[] }) {
    if (_.isEmpty(projectNodeIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'psm.project_node_id as projectNodeId',
        'count(1) as scheduleModifyNum',
        'group_concat(if(psm.type = "delay", "延期", "提前") SEPARATOR ";") as scheduleType',
        'group_concat(cp.parameter_name SEPARATOR ";") as reasonType',
        'group_concat(psm.description SEPARATOR ";") as reasonDescription',
      ])
      .from(ProjectScheduleModifiedEntity, 'psm')
      .leftJoin(CommonParameterEntity, 'cp', 'cp.parameter_code = psm.reason_type and cp.parameter_type = "PROJECT_SCHEDULE_MODIFIED_REASON_TYPE"')
      .where('psm.project_node_id IN (:...nodeIds)', { nodeIds: projectNodeIds })
      .andWhere('psm.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .groupBy('psm.project_node_id');
    return await qb.getRawMany<{ projectNodeId: string; scheduleModifyNum: string, scheduleType: string; reasonType: string; reasonDescription: string; }>();
  }

  async getProjectIdsByNodeIds(nodeIds: string[]) {
    if (_.isEmpty(nodeIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
    qb.select(['p.project_id'])
      .from(ProjectNodeEntity, 'pn')
      .innerJoin(ProjectEntity, 'p', 'pn.project_id = p.project_id')
      .where(`pn.project_node_id IN (:...nodeIds)`, { nodeIds })
      .andWhere(`p.delete_flag = 'N'`)
      .groupBy('p.project_id');
    const res = await qb.getRawMany();
    return res.map(item => item.project_id);
  }

  async countProjectNodeGroupStage(params: Project.SearchProjectNodeProjectReq) {
    const { projectIdList } = params;
    if (_.isEmpty(projectIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
    qb.select([
      's.stage_name as stageName',
      'count(1) as count'
    ])
      .from(ProjectEntity, 'p')
      .innerJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .innerJoin(StageEntity, 's', 'pn.stage_template_id = s.stage_template_id and pn.business_stage_id = s.business_stage_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where(`p.project_id IN (:...projectIdList)`, { projectIdList })
      .andWhere(`pn.delete_flag = 'N'`)
    this.projectPublicFilter(qb, params);
    qb.groupBy('s.stage_name');
    return await qb.getRawMany<{ stageName: string, count: string }>();
  }

  async countNodeGroupStage(params: Project.SearchProjectNodeNodeReq) {
    const { nodeIdList } = params;
    if (_.isEmpty(nodeIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
    qb.select([
      'pn.stage_template_id as stageTemplateId',
      'pn.business_stage_id as businessStageId',
      's.stage_name as stageName',
      'count(1) as count'
    ])
      .from(ProjectNodeEntity, 'pn')
      .innerJoin(StageEntity, 's', 'pn.stage_template_id = s.stage_template_id and pn.business_stage_id = s.business_stage_id and s.delete_flag = "N"')
      .innerJoin(ProjectEntity, 'p', 'pn.project_id = p.project_id')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where(`pn.project_node_id IN (:...nodeIds)`, { nodeIds: nodeIdList })
      .andWhere(`pn.delete_flag = 'N'`)
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('s.stage_name');
    return await qb.getRawMany<{ stageTemplateId: string, businessStageId: string, stageName: string, count: string }>();
  }
  async countNodeGroupProject(params: Project.SearchProjectNodeNodeReq) {
    const { nodeIdList } = params;
    if (_.isEmpty(nodeIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
    qb.select([
      'p.project_id as projectId',
      'p.project_name as projectName',
      'count(1) as count'
    ])
      .from(ProjectNodeEntity, 'pn')
      .innerJoin(ProjectEntity, 'p', 'pn.project_id = p.project_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where(`pn.project_node_id IN (:...nodeIds)`, { nodeIds: nodeIdList })
      .andWhere(`pn.delete_flag = 'N'`)
      .groupBy('p.project_id');
    this.projectNodePublicFilter(qb, params);
    return await qb.getRawMany<{ projectId: string, projectName: string, count: string }>();
  }
  async getNodeIdsByProjectIds(projectIds: string[]) {
    const qb = this.dataSource.createQueryBuilder()
    qb.select(['pn.project_node_id'])
      .from(ProjectEntity, 'p')
      .innerJoin(ProjectNodeEntity, 'pn', 'p.current_project_node = pn.project_node_id')
      .where(`p.project_id IN (:...projectIds)`, { projectIds })
      .andWhere(`p.delete_flag = 'N'`)
      .groupBy('pn.project_node_id');
    const res = await qb.getRawMany();
    return res.map(item => item.project_node_id);
  }

  async countProject(params: Project.SearchProjectNodeProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return { count: '0' };
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count'
      ])
      .from(ProjectEntity, 'p')
      .innerJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .innerJoin(StageEntity, 's', 'pn.stage_template_id = s.stage_template_id and pn.business_stage_id = s.business_stage_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    return await qb.getRawOne<{ count: string }>();
  }

  async countProjectNode(params: Project.SearchProjectNodeNodeReq) {
    //idList为空直接返回
    if (_.isEmpty(params.nodeIdList)) return { count: '0' };
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count'
      ])
      .from(ProjectNodeEntity, 'pn')
      .innerJoin(ProjectEntity, 'p', 'pn.project_id = p.project_id')
      .innerJoin(StageEntity, 's', 'pn.stage_template_id = s.stage_template_id and pn.business_stage_id = s.business_stage_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .andWhere('pn.project_node_id in (:...nodeIds)', { nodeIds: params.nodeIdList });
    this.projectNodePublicFilter(qb, params);
    return await qb.getRawOne<{ count: string }>();
  }

  async countProjectDeviceDepartmentGroupType(params: Project.SearchProjectNodeProjectReq): Promise<{ deviceDepartmentId: string, deviceDepartmentName: string, count: string }[]> {
    const { projectIdList } = params;
    if (_.isEmpty(projectIdList)) return [];
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'dpi.project_id AS project_id',
        'dpi.department_id AS department_id'
      ])
      .from(DeviceProjectInfoEntity, 'dpi')
      .where('dpi.delete_flag = "N"')
      .andWhere('dpi.project_id in (:...projectIds)', { projectIds: projectIdList });
    subQuery.andWhere(qb => {
      const subQuery = qb.subQuery()
        .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
        .from(DeviceProjectInfoEntity, 'dpi2')
        .where('dpi2.project_id = dpi.project_id')
        .andWhere('dpi2.delete_flag = "N"');
      return `dpi.project_binding_time = (${subQuery.getQuery()})`;
    });
    subQuery.groupBy('dpi.project_id');

    const qb = this.dataSource.createQueryBuilder()
      .select('dpi.department_id', 'deviceDepartmentId')
      .addSelect('dep.department_name', 'deviceDepartmentName')
      .addSelect('COUNT(1)', 'count')
      .from(ProjectEntity, 'p')
      .leftJoin(`(${subQuery.getQuery()})`, 'dpi', 'p.project_id = dpi.project_id') // 确保是最后一条记录
      .leftJoin(DepartmentEntity, 'dep', 'dpi.department_id = dep.department_id')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = "N"')
      .andWhere('p.project_id IN (:...projectIdList)', { projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('dpi.department_id')
    qb.setParameters(subQuery.getParameters());
    const res = await qb.getRawMany<{ deviceDepartmentId: string, deviceDepartmentName: string, count: string }>();
    return res;
  }

  /**
   * 统计工地部门分布
   * @param params 
   * @returns 
   */
  async countProjectDepartmentGroupType(params: Project.SearchProjectNodeProjectReq): Promise<{ departmentId: string, departmentName: string, count: string }[]> {
    const { projectIdList } = params;
    if (_.isEmpty(projectIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select('p.department_id', 'departmentId')
      .addSelect('d.department_name', 'departmentName')
      .addSelect('COUNT(1)', 'count')
      .from(ProjectEntity, 'p')
      .leftJoin(DepartmentEntity, 'd', 'd.department_id = p.department_id')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = "N"')
      .andWhere('p.project_id IN (:...projectIdList)', { projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.department_id')
    const res = await qb.getRawMany<{ departmentId: string, departmentName: string, count: string }>();
    return res;
  }

  async countNodeDeviceDepartmentGroupType(params: Project.SearchProjectNodeNodeReq): Promise<{ deviceDepartmentId: string, deviceDepartmentName: string, count: string }[]> {
    const { nodeIdList } = params;
    if (_.isEmpty(nodeIdList)) return [];
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'dpi.project_id AS project_id',
        'dpi.department_id AS department_id'
      ])
      .from(DeviceProjectInfoEntity, 'dpi')
      .innerJoin(ProjectNodeEntity, 'pn', 'dpi.project_id = pn.project_id')
      .where('dpi.delete_flag = "N"')
      .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: nodeIdList })
    subQuery.andWhere(qb => {
      const subQuery = qb.subQuery()
        .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
        .from(DeviceProjectInfoEntity, 'dpi2')
        .where('dpi2.project_id = dpi.project_id')
        .andWhere('dpi2.delete_flag = "N"');
      return `dpi.project_binding_time = (${subQuery.getQuery()})`;
    });
    subQuery.groupBy('dpi.project_id');

    const qb = this.dataSource.createQueryBuilder()
      .select('dpi.department_id', 'deviceDepartmentId')
      .addSelect('dep.department_name', 'deviceDepartmentName')
      .addSelect('COUNT(1)', 'count')
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
      .leftJoin(`(${subQuery.getQuery()})`, 'dpi', 'p.project_id = dpi.project_id') // 确保是最后一条记录
      .leftJoin(DepartmentEntity, 'dep', 'dpi.department_id = dep.department_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = "N"')
      .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: nodeIdList })
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('dpi.department_id')
    qb.setParameters(subQuery.getParameters());
    const res = await qb.getRawMany<{ deviceDepartmentId: string, deviceDepartmentName: string, count: string }>();
    return res;
  }

  async countNodeDepartmentGroupType(params: Project.SearchProjectNodeNodeReq): Promise<{ departmentId: string, departmentName: string, count: string }[]> {
    const { nodeIdList } = params;
    if (_.isEmpty(nodeIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select('p.department_id', 'departmentId')
      .addSelect('d.department_name', 'departmentName')
      .addSelect('COUNT(1)', 'count')
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
      .leftJoin(DepartmentEntity, 'd', 'd.department_id = p.department_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.delete_flag = "N"')
      .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: nodeIdList })
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('p.department_id')
    const res = await qb.getRawMany<{ departmentId: string, departmentName: string, count: string }>();
    return res;
  }

  async countProjectGroupProjectManager(params: Project.SearchProjectNodeProjectReq) {
    // idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    // 查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        `p.project_manager as projectManagerId`,
        `m.nick_name as projectManagerName`,
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .leftJoin(DepartmentEntity, 'd', 'd.department_id = p.department_id')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .andWhere('p.project_id IN (:...projectIdList)', { projectIdList: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.project_manager');
    return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  }

  async countNodeGroupProjectManager(params: Project.SearchProjectNodeNodeReq) {
    // idList为空直接返回
    if (_.isEmpty(params.nodeIdList)) return [];
    // 查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        `p.project_manager as projectManagerId`,
        `m.nick_name as projectManagerName`,
      ])
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: params.nodeIdList });
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('p.project_manager');
    return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  }

  /**
   * 统计节点分布
   */
  async countProjectGroupNodeTitle(params: Project.SearchProjectNodeProjectReq): Promise<{ nodeName: string, count: string }[]> {
    const { projectIdList } = params;
    if (_.isEmpty(projectIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select('pn.title', 'nodeName')
      .addSelect('COUNT(1)', 'count')
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.project_id IN (:...projectIdList)', { projectIdList })
    this.projectPublicFilter(qb, params);
    qb.groupBy('pn.title')
    return qb.getRawMany<{ nodeName: string, count: string }>();
  }

  async countGroupNodeTitle(params: Project.SearchProjectNodeNodeReq): Promise<{ nodeName: string, count: string }[]> {
    const { nodeIdList } = params;
    if (_.isEmpty(nodeIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select('pn.title', 'nodeName')
      .addSelect('COUNT(1)', 'count')
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('pn.project_node_id IN (:...nodeIds)', { nodeIds: nodeIdList })
    this.projectNodePublicFilter(qb, params);
    qb.groupBy('pn.title')
    return qb.getRawMany<{ nodeName: string, count: string }>();
  }

  async getProjectCurrentNodeRoleIds(params: Project.SearchProjectNodeProjectReq): Promise<{ roleIds: string }> {
    const { projectIdList } = params;
    if (_.isEmpty(projectIdList)) return { roleIds: '' };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'group_concat(pn.company_member_role_id) as roleIds'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.project_id IN (:...projectIdList)', { projectIdList })
      .andWhere('pn.company_member_role_id IS NOT NULL')
    this.projectPublicFilter(qb, params);
    return qb.getRawOne<{ roleIds: string }>();
  }

  async getNodeRoleIds(params: Project.SearchProjectNodeNodeReq): Promise<{ roleIds: string }> {
    const { nodeIdList } = params;
    if (_.isEmpty(nodeIdList)) return { roleIds: '' };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'group_concat(pn.company_member_role_id) as roleIds'
      ])
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
      .leftJoin(StageEntity, 's', 'pn.business_stage_id = s.business_stage_id and pn.stage_template_id = s.stage_template_id and s.delete_flag = "N"')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('pn.project_node_id IN (:...nodeIds)', { nodeIds: nodeIdList })
      .andWhere('pn.company_member_role_id IS NOT NULL')
    this.projectNodePublicFilter(qb, params);
    return qb.getRawOne<{ roleIds: string }>();
  }

  private projectPublicFilter(qb: SelectQueryBuilder<ProjectEntity>, params: Project.SearchProjectNodeProjectReq): SelectQueryBuilder<ProjectEntity> {
    //工地负责人
    if (params.projectDirectorId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectDirectorId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //开工时间
    if (params.projectTimeStart && params.projectTimeEnd) qb.andWhere('p.project_start_time >= :projectTimeStart and p.project_start_time <= :projectTimeEnd', { projectTimeStart: params.projectTimeStart, projectTimeEnd: params.projectTimeEnd });
    //节点模版名称
    if (params.nodeTemplateName) {
      qb.andWhere('nt.node_template_name = :nodeTemplateName', { nodeTemplateName: params.nodeTemplateName });
    }
    //节点阶段名称
    if (params.stage) {
      qb.andWhere('s.stage_name = :stage', { stage: params.stage });
    }
    //节点名称
    if (params.nodeName) {
      qb.andWhere('pn.title = :nodeName', { nodeName: params.nodeName });
    }
    //节点对应岗位
    if (params.role) {
      qb.andWhere('LOCATE(:roleId, pn.company_member_role_id) > 0', { roleId: params.role });
    }
    //节点截止日期
    if (params.deadlineStartTime && params.deadlineEndTime) {
      qb.andWhere('pn.deadline_time >= :deadlineStartTime and pn.deadline_time <= :deadlineEndTime', { deadlineStartTime: params.deadlineStartTime, deadlineEndTime: params.deadlineEndTime });
    }
    //是否完成
    if (params.isCompleted) {
      qb.andWhere('pn.is_finish = :isCompleted', { isCompleted: params.isCompleted });
    }
    //是否逾期
    if (params.overdueStatus === 'Y') {
      qb.andWhere('(pn.finish_time is null and pn.deadline_time < NOW()) or pn.deadline_time < pn.finish_time');
    } else if (params.overdueStatus === 'N') {
      qb.andWhere('(pn.finish_time is null and pn.deadline_time >= NOW()) or pn.deadline_time >= pn.finish_time');
    }
    //是否调整工期
    if (params.isScheduleModify === 'Y') {
      const isScheduleModifyQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id IN (${isScheduleModifyQb.getQuery()})`).setParameters(isScheduleModifyQb.getParameters());
    }
    if (params.isScheduleModify === 'N') {
      const isScheduleModifyQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id NOT IN (${isScheduleModifyQb.getQuery()})`).setParameters(isScheduleModifyQb.getParameters());
    }
    //工期调整类型
    if (params.scheduleType) {
      const scheduleTypeQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.type = :scheduleType', { scheduleType: params.scheduleType })
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id IN (${scheduleTypeQb.getQuery()})`).setParameters(scheduleTypeQb.getParameters());
    }
    //工期调整原因类型
    if (params.reasonType) {
      const reasonTypeQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.reason_type = :reasonType', { reasonType: params.reasonType })
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id IN (${reasonTypeQb.getQuery()})`).setParameters(reasonTypeQb.getParameters());
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .where('dpi.delete_flag = "N"')
        .andWhere('dpi.project_id in (:...projectIds)', { projectIds: params.projectIdList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    return qb;
  }

  private projectNodePublicFilter(qb: SelectQueryBuilder<ProjectNodeEntity>, params: Project.SearchProjectNodeNodeReq): SelectQueryBuilder<ProjectNodeEntity> {
    //工地负责人
    if (params.projectDirectorId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectDirectorId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //开工时间
    if (params.projectTimeStart && params.projectTimeEnd) qb.andWhere('p.project_start_time >= :projectTimeStart and p.project_start_time <= :projectTimeEnd', { projectTimeStart: params.projectTimeStart, projectTimeEnd: params.projectTimeEnd });
    //开工时间
    if (params?.projectTime?.from && params?.projectTime?.to) qb.andWhere('p.project_start_time >= :projectTimeFrom and p.project_start_time <= :projectTimeTo', { projectTimeFrom: params.projectTime.from, projectTimeTo: params.projectTime.to });
    //节点模版名称
    if (params.nodeTemplateName) {
      qb.andWhere('nt.node_template_name = :nodeTemplateName', { nodeTemplateName: params.nodeTemplateName });
    }
    //节点阶段名称
    if (params.stage) {
      qb.andWhere('s.stage_name = :stage', { stage: params.stage });
    }
    //节点名称
    if (params.nodeName) {
      qb.andWhere('pn.title = :nodeName', { nodeName: params.nodeName });
    }
    //节点对应岗位
    if (params.role) {
      qb.andWhere('LOCATE(:roleId, pn.company_member_role_id) > 0', { roleId: params.role });
    }
    //节点截止日期
    if (params.deadlineStartTime && params.deadlineEndTime) {
      qb.andWhere('pn.deadline_time >= :deadlineStartTime and pn.deadline_time <= :deadlineEndTime', { deadlineStartTime: params.deadlineStartTime, deadlineEndTime: params.deadlineEndTime });
    }
    //节点截止日期
    if (params?.deadlineTime?.from && params?.deadlineTime?.to) qb.andWhere('pn.deadline_time >= :deadlineTimeFrom and pn.deadline_time <= :deadlineTimeTo', { deadlineTimeFrom: params.deadlineTime.from, deadlineTimeTo: params.deadlineTime.to });
    //是否完成
    if (params.isCompleted) {
      qb.andWhere('pn.is_finish = :isCompleted', { isCompleted: params.isCompleted });
    }
    //是否调整工期
    if (params.isScheduleModify === 'Y') {
      const isScheduleModifyQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id IN (${isScheduleModifyQb.getQuery()})`).setParameters(isScheduleModifyQb.getParameters());
    }
    if (params.isScheduleModify === 'N') {
      const isScheduleModifyQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id NOT IN (${isScheduleModifyQb.getQuery()})`).setParameters(isScheduleModifyQb.getParameters());
    }
    //工期调整类型
    if (params.scheduleType) {
      const scheduleTypeQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.type = :scheduleType', { scheduleType: params.scheduleType })
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id IN (${scheduleTypeQb.getQuery()})`).setParameters(scheduleTypeQb.getParameters());
    }
    //工期调整原因类型
    if (params.reasonType) {
      const reasonTypeQb = this.dataSource.createQueryBuilder()
        .select(['psm.project_node_id'])
        .from(ProjectScheduleModifiedEntity, 'psm')
        .where('psm.project_node_id = pn.project_node_id')
        .andWhere('psm.reason_type = :reasonType', { reasonType: params.reasonType })
        .andWhere('psm.delete_flag = "N"');
      qb.andWhere(`pn.project_node_id IN (${reasonTypeQb.getQuery()})`).setParameters(reasonTypeQb.getParameters());
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectNodeEntity, 'pn', 'dpi.project_id = pn.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pn.project_node_id IN (:...nodeIds)', { nodeIds: params.nodeIdList })
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    return qb;
  }

  async searchNodeOverdueNum({ companyId, projectIds, projectStatus, isNodeDelayOverdue, startTime, endTime }: { companyId: string, projectIds: string[], projectStatus: 'no_finish' | 'no_work' | 'finish', isNodeDelayOverdue: 'Y' | 'N', startTime?: string, endTime?: string  }) {
    const qb = this.dataSource.getRepository(ProjectNodeEntity).createQueryBuilder('pn')
      .select([
        'count(1) as nodeOverdueNum'
      ])
      .leftJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
      .where('p.company_id = :companyId', { companyId })
      .andWhere('p.delete_flag = :delFlag', { delFlag: 'N' })
      .andWhere('pn.delete_flag = :delFlag', { delFlag: 'N' });
    if (!_.isEmpty(projectIds)) qb.andWhere('p.project_id in (:...projectIds)', { projectIds });
    if (projectStatus) {//工地状态
      if (projectStatus === 'no_finish') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is null or p.project_start_time < (CURDATE() + INTERVAL 1 DAY))`);
      if (projectStatus === 'no_work') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is not null and p.project_start_time >= (CURDATE() + INTERVAL 1 DAY))`);
      if (projectStatus === 'finish') qb.andWhere(`p.project_status = 'Y'`);
    }
    if (isNodeDelayOverdue === 'Y') {
      if (startTime) qb.andWhere('pn.origin_deadline_time >= :startTime', { startTime });
      if (endTime) qb.andWhere('pn.origin_deadline_time <= :endTime', { endTime });
      qb.andWhere('DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) < 0')
    }
    if (isNodeDelayOverdue === 'N') {
      if (startTime) qb.andWhere('pn.deadline_time >= :startTime', { startTime });
      if (endTime) qb.andWhere('pn.deadline_time <= :endTime', { endTime });
      qb.andWhere('DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) < 0')
    }
    return await qb.getRawOne<{ nodeOverdueNum: number }>();
  }

}