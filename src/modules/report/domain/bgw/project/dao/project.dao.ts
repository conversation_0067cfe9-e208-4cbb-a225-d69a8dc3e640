import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DataSource, SelectQueryBuilder } from "typeorm";
import { Project } from "../dto/project.dto";
import * as _ from "lodash";
import { ProjectNodeEntity } from "@src/modules/report/entity/bgw/project-node.entity";
import { Common, DevTimeout } from "@yqz/nest";
import { ProjectMemberEntity } from "@src/modules/report/entity/bgw/project-member.entity";
import { MemberRoleEntity } from "@src/modules/report/entity/bgw/member-role.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { Dao<PERSON><PERSON> } from "@src/util/dao.util"
import { NodeOverdueEventEntity } from "@src/modules/report/entity/bgw/node-overdue-event.entity";
import { ProjectInactiveRecordEntity } from "@src/modules/report/entity/camera/project-inactive-record.entity";
import { StageEntity } from "@src/modules/report/entity/bgw/stage.entity";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { ProjectSignInEntity } from "@src/modules/report/entity/bgw/project-sign-in.entity";
import { format } from "date-fns";
import { ProjectTidinessDetectionEntity } from "@src/modules/report/entity/camera/project-tidiness-detection.entity";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { CmsPhotoEntity } from "@src/modules/report/entity/bgw/cms-photo.entity";
import * as DateFns from "date-fns";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { FaceDbType } from "@src/modules/report/mongodb/camera/face.collection";
import { IdentityType } from "@src/modules/report/mongodb/camera/body.collection";
import { Flag } from "@yqz/nest/lib/types/common.type";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { DeviceProjectInfoEntity } from "@src/modules/report/entity/camera/device-project-info.entity";
import { CommonParameterEntity } from "@src/modules/report/entity/bgw/common-parameter.entity";
import { DeviceStatusEntity } from "@src/modules/report/entity/camera/device-status.entity";
import { ProjectDateReport } from "../../../corp/data-report/service/basic/dto/project-data-report.dto";
import { EvaluateEntity } from "@src/modules/report/entity/bgw/evaluate.entity";
import { ProjectProblemEntity } from "@src/modules/report/entity/bgw/project-problem.entity";
import { StageTemplateEntity } from "@src/modules/report/entity/bgw/stage-template.entity";
import { CodeTableEntity } from "@src/modules/report/entity/bgw/code-table.entity";
import { ProjectScheduleModifiedEntity } from "@src/modules/report/entity/bgw/project-schedule-modified.entity";
import { NodeTemplateEntity } from "@src/modules/report/entity/bgw/node-template.entity";
import { OwnerCameraLiveStreamingEntity } from "@src/modules/report/entity/bgw/owner-camera-live-streaming.entity";
import { ProjectActiveHistory } from "@src/modules/report/entity/camera/project-active-history.entity";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { MotiondetectEventEntity } from "@src/modules/report/entity/camera/motiondetect-event.entity";
import { ProjectCameraLiveSilhouetteEntity } from "@src/modules/report/entity/camera/project-camera-live-silhouette.entity";
import { ProjectUpdateEntity } from "@src/modules/report/entity/bgw/project-update.entity";
import { ProjectUpdateMediaEntity } from "@src/modules/report/entity/bgw/project-update-media.entity";
import { YqzMediaType } from "@yqz/nest/lib/types/media.type";

@Injectable()
export class ProjectDao {
  private readonly logger = new MyLogger(ProjectDao.name)
  constructor(
    private dataSource: DataSource
  ) { }

  @DevTimeout(500)
  async test() {
    //  this.getProjectIds({projectIds:['58325','58324'],projectAddress:'3'}).then(res=>{
    //   this.logger.log(res)
    //  })
    //查询单日内查看直播超过N次的工地idList
    //  const ownerFocusOneDayProjectIds = await this.searchProjectIdListByOwnerFocusOneDay({ projectIds:['343756'], ownerFocusProjectOneDay: '4' });
    // console.log("🚀 ~ ProjectDao ~ test ~ ownerFocusOneDayProjectIds:", ownerFocusOneDayProjectIds)
    // //查询所选时间段内查看直播超过N次的工地idList
    // const ownerFocusTimeProjectIds = await this.searchProjectIdListByOwnerFocusTime({ projectIds: ['343756'], ownerFocusProjectTime: '4', startTime: '2023-10-24', endTime: '2023-10-26' });
    // console.log("🚀 ~ ProjectDao ~ test ~ ownerFocusTimeProjectIds:", ownerFocusTimeProjectIds)
    //  //综合评分差评工地idList
    //  const overallScoreProjectIds = await this.searchProjectIdListByOverallScore({ projectIds: ['58142'], overallScore:'1', startTime: '2024-12-25', endTime: '2024-12-26' });
    //  console.log("🚀 ~ ProjectDao ~ test ~ overallScoreProjectIds:", overallScoreProjectIds)
    //  //单项评分差评工地idList
    //  const individualScoreProjectIds = await this.searchProjectIdListByIndividualScore({ projectIds: ['58142'], individualScore:'2', startTime: '2024-12-25', endTime: '2024-12-26' });
    //  console.log("🚀 ~ ProjectDao ~ test ~ individualScoreProjectIds:", individualScoreProjectIds)
  }

  async searchProject(params: Project.Search): Promise<ProjectEntity[]> {
    const { memberRoleList, memberPersonIdList, projectStatus, projectIds, projectDirectorIdList, startTime, endTime, departmentId } = params || {}
    const qb = this.dataSource
      .createQueryBuilder()
      .select(['p.*'])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectMemberEntity, 'm', 'p.project_id = m.project_id and m.delete_flag = :delFlag')
      .leftJoin(MemberRoleEntity, 'r', 'm.PROJECT_MEMBER_ID = r.PROJECT_MEMBER_ID')
      .where('p.delete_flag  = :delFlag')
      .andWhere('p.company_id = :companyId', { companyId: params.companyId })
      .setParameters({ delFlag: 'N' })
    if (departmentId) qb.andWhere('p.department_id = :departmentId', { departmentId });
    if (!_.isEmpty(projectIds))
      qb.andWhere('p.project_id in (:...projectIds)', { projectIds })
    if (memberPersonIdList?.length > 0)
      qb.andWhere('m.person_id in (:...memberPersonIdList)', { memberPersonIdList })
    if (memberRoleList?.length > 0)
      qb.andWhere('r.ROLE_ID in (:...memberRoleList)', { memberRoleList })
    if (projectDirectorIdList?.length > 0)
      qb.andWhere('p.project_director in (:...projectDirectorIdList)', { projectDirectorIdList })
    if (projectStatus) {
      if (projectStatus === 'no_finish') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is null or p.project_start_time < (CURDATE() + INTERVAL 1 DAY))`);
      if (projectStatus === 'no_work') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is not null and p.project_start_time >= (CURDATE() + INTERVAL 1 DAY))`);
      if (projectStatus === 'finish') qb.andWhere(`p.project_status = 'Y'`);
    }
    // if (startTime && endTime)
    //   qb.andWhere('p.createTime between :startTime and :endTime', { startTime, endTime })
    if (startTime) qb.andWhere('p.createTime >= :startTime', { startTime });
    if (endTime) qb.andWhere('p.createTime <= :endTime', { endTime });
    const result = await qb.getRawMany();
    return result;
  }

  async searchProjectOverdueNum(params: Project.Search): Promise<{ overdueNum: number }> {
    const qb = this.dataSource.getRepository(ProjectEntity)
      .createQueryBuilder('p')
      .select([
        `IFNULL(SUM(
          CASE 
            WHEN :isNodeDelayOverdue = 'Y' AND EXISTS (
              SELECT 1 FROM project_schedule_modified psm2 
              WHERE psm2.project_node_id = pn.project_node_id 
              AND psm2.DELETE_FLAG = 'N'
            ) THEN DATEDIFF(pn.origin_deadline_time, ifnull(pn.finish_time, NOW())) < 0
            ELSE DATEDIFF(pn.deadline_time, ifnull(pn.finish_time, NOW())) < 0
          END
        ), 0) AS overdueNum`
      ])
      .leftJoin(ProjectNodeEntity, 'pn', 'p.project_id = pn.project_id and p.current_project_node = pn.project_node_id')
      .where('p.delete_flag = :delFlag', { delFlag: 'N' })
      .andWhere('pn.delete_flag = :delFlag', { delFlag: 'N' })
      .andWhere('p.company_id = :companyId', { companyId: params.companyId })
      .setParameter('isNodeDelayOverdue', params.isNodeDelayOverdue || 'N');
    if (!_.isEmpty(params.projectIds)) qb.andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIds })
    if (params.projectStatus) {
      if (params.projectStatus === 'no_finish') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is null or p.project_start_time < (CURDATE() + INTERVAL 1 DAY))`);
      if (params.projectStatus === 'no_work') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is not null and p.project_start_time >= (CURDATE() + INTERVAL 1 DAY))`);
      if (params.projectStatus === 'finish') qb.andWhere(`p.project_status = 'Y'`);
    }
    return await qb.getRawOne();
  }

  async searchProjectDirectorList(params: { companyId?: string, projectId?: string, projectIdList?: string[] }) {
    const qb = this.dataSource.getRepository(ProjectEntity).createQueryBuilder('p')
      .select([
        'p.project_director id',
        'm.nick_name name'
      ])
      .leftJoin(ManagerEntity, 'm', 'm.company_id = p.company_id and m.person_id = p.project_director')
      .where('p.delete_flag = "N" and m.delete_flag = "N"')
    if (params.companyId) qb.andWhere('p.company_id = :companyId', { companyId: params.companyId })
    if (params.projectId) qb.andWhere('p.project_id = :projectId', { projectId: params.projectId })
    if (params.projectIdList && params.projectIdList.length > 0) qb.andWhere('p.project_id in (:projectIdList)', { projectIdList: params.projectIdList })
    qb.groupBy('p.project_director')
    return await qb.getRawMany()
  }

  async searchProjectList(params: Project.ProjectDetailReq) {
    params.endTime = new Date(params.endTime) > DateFns.addDays(new Date(), 1) ? DateFns.format(DateFns.addDays(new Date(), 1), 'yyyy-MM-dd') : params.endTime;//如果结束时间超过今天+1天，那结束时间就为今天+1天
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'MAX( ' +
        'CASE ' +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date is NULL THEN DATEDIFF('" + params.endTime + "',pir.from_date) " +
        "WHEN pir.to_date >= '" + params.startTime + "' AND pir.to_date < '" + params.endTime + "' THEN (DATEDIFF(pir.to_date,pir.from_date) + 1) " +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date >= '" + params.endTime + "' THEN DATEDIFF('" + params.endTime + "',pir.from_date) " +
        "END) AS noWorkDay",
        'MAX( ' +
        'CASE ' +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date is NULL THEN DATE_SUB(pir.from_date, INTERVAL 1 DAY) " +
        "WHEN pir.to_date >= '" + params.startTime + "' AND pir.to_date < '" + params.endTime + "'  THEN DATE_SUB('" + params.endTime + "', INTERVAL 1 DAY) " +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date >= '" + params.endTime + "' THEN DATE_SUB(pir.from_date, INTERVAL 1 DAY) " +
        "END) AS lastWorkTime",
        'SUM( ' +
        'CASE ' +
        "WHEN noe.from_date < '" + params.endTime + "' AND noe.to_date is NULL THEN DATEDIFF('" + params.endTime + "',noe.from_date) " +
        "WHEN noe.to_date >= '" + params.startTime + "' AND noe.to_date < '" + params.endTime + "'  THEN noe.duration_in_day " +
        "WHEN noe.from_date < '" + params.endTime + "' AND noe.to_date >= '" + params.endTime + "' THEN DATEDIFF('" + params.endTime + "',noe.from_date) " +
        "END) AS overdueTime",
        'p.project_director as projectManagerId',
        'dir.nick_name as projectManagerName',
        'p.company_id as linkCompanyId',
        'c.SHORT_NAME as linkCompanyName',
        'c.CREATE_TIME as linkCompanyCreateTime',
        'DATE_FORMAT(p.createTime,"%Y-%m-%d %H:%i:%s") as projectCreateTime'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ManagerEntity, 'dir', 'dir.person_id = p.project_director and dir.company_id = p.company_id and dir.delete_flag = "N"')
      .leftJoin(CommunityEntity, 'com', 'p.community_id = com.community_id')
      .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
      .leftJoin(ProjectInactiveRecordEntity, 'pir', 'p.project_id = pir.project_id and pir.delete_flag = "N"')
      .leftJoin(ProjectNodeEntity, 'pn', 'p.project_id = pn.project_id and p.current_project_node = pn.project_node_id and pn.delete_flag = "N"')
      .leftJoin(NodeOverdueEventEntity, 'noe', 'noe.project_node_id = pn.project_node_id and noe.delete_flag = "N"')
      .where('p.project_id in (:...projectIds)', { projectIds: params.projectIds })
    if (params.projectManagerId) qb.andWhere('p.project_director = :projectManagerId', { projectManagerId: params.projectManagerId });
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    // DaoUtil.processPageAndSort(qb, params);
    qb.groupBy('p.project_id');
    if (params.pageNo) {
      const pageSize = params.pageSize || 10;
      qb.offset((params.pageNo - 1) * pageSize).limit(pageSize);
    }
    if (params.sortBy && params.sortBy.length > 0)
      params.sortBy.forEach((e) => {
        if (!e.order) return;
        qb.addOrderBy(
          `${String(e.field)}`,
          e.order === 'ascending' ? 'ASC' : 'DESC'
        );
      });
    return await DaoUtil.getPageRes<Project.ProjectDetail>(qb);
  }

  async getProjectIds(request: Project.BindingProjectReq) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id AS projectId'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID and s.delete_flag = "N"')
      .leftJoin(CommunityEntity, 'c', 'p.COMMUNITY_ID = c.COMMUNITY_ID')
      .leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
    if (!_.isEmpty(request.companyIds))
      qb.where('p.company_id IN (:...companyIds)', { companyIds: request.companyIds })
    if (!_.isEmpty(request.projectIds))
      qb.where('p.project_id IN (:...projectIds)', { projectIds: request.projectIds })
    if (request.projectAddress) {
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(c.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: request.projectAddress });
    }
    if (request.projectDirectorId) {
      qb.andWhere("p.project_manager = :projectDirectorId", { projectDirectorId: request.projectDirectorId })
    }
    qb.groupBy('p.project_id');
    const res = await qb.getRawMany();
    return res
  }

  async searchProjectDirectors({ projectIds }: { projectIds: string[] }) {
    if (_.isEmpty(projectIds)) return []
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_manager AS id',
        'm.nick_name AS name'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ManagerEntity, 'm', 'p.project_manager = m.manager_id')
      .where('p.project_id in (:...projectIds)', { projectIds: projectIds })
      .groupBy('p.project_manager');
    const res = await qb.getRawMany<{ id: string, name: string }>();
    return res
  }

  async searchProjectIdsByDept({ departmentIds }: { departmentIds: string[] }) {
    if (_.isEmpty(departmentIds)) return []
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id AS projectId'
      ])
      .from(ProjectEntity, 'p')
      .where('p.delete_flag = "N"')
    if (!_.isEmpty(departmentIds)) {
      qb.andWhere('p.department_id in (:...departmentIds)', { departmentIds: departmentIds })
    }
    const res = await qb.getRawMany();
    return res
  }

  async getProjectIdsByCompanyId(request: Project.BindingProjectReq) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id AS projectId'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID and s.delete_flag = "N"')
      .innerJoin(CommunityEntity, 'c', 'p.COMMUNITY_ID = c.COMMUNITY_ID')
      .leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      .where('p.delete_flag = "N" and p.company_id = :companyId', { companyId: request.companyId })
    if (!_.isEmpty(request.projectIds)) {
      qb.andWhere('p.project_id in (:...projectIds)', { projectIds: request.projectIds })
    }
    if (request.projectAddress) {
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(c.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: request.projectAddress });
    }
    qb.groupBy('p.project_id');
    const res = await qb.getRawMany();
    return res
  }

  async addressSearch({ projectIdList }: { projectIdList: string[] }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'd.department_id as departmentId',
        'd.department_name as departmentName',
        'date_format(ifnull(p.project_start_time,p.createTime),"%Y-%m-%d") as projectStartTime',
        'date_format(p.completed_time,"%Y-%m-%d") as projectCompletedTime',
        "IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(c.community_name,''),IFNULL(p.HOUSE_NUMBER,''))) as projectAddress"
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(DepartmentEntity, 'd', 'p.department_id = d.department_id')
      .leftJoin(CommunityEntity, 'c', 'p.COMMUNITY_ID = c.COMMUNITY_ID')
      .leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      .where('p.PROJECT_ID in (:...projectIdList)', { projectIdList })
      .groupBy('p.project_id');
    const res = await qb.getRawMany();
    return res
  }

  async getSignInList(params: { companyId?: string, projectId?: string, type?: string }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'psi.PROJECT_SIGN_IN_ID as projectSignInId',
        'p.company_id as companyId',
        'psi.project_id as projectId',
        'psi.manager_id as managerId',
        'psi.SIGN_IN_TIME as signInTime',
        'cmr.role_name as roleName',
        'psi.type as type',
      ])
      .from(ProjectSignInEntity, 'psi')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = psi.manager_id')
      .leftJoin(CompanyMemberRoleEntity, 'cmr', 'cmr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
      .leftJoin(ProjectEntity, 'p', 'psi.project_id = p.project_id')
      .where('psi.delete_flag = "N"')
    if (params?.companyId) qb.andWhere('psi.company_id = :companyId', { companyId: params?.companyId });
    if (params?.projectId) qb.andWhere('psi.project_id = :projectId', { projectId: params?.projectId });
    if (params?.type) qb.andWhere('psi.type = :type', { type: params?.type });
    return await qb.getRawMany();
  }

  /**
   * 查询body表签到统计
   */
  async getSignedPersonCount(params: { companyId: string, projectId: string, startDate?: string, endDate?: string }) {
    const match = {
      companyId: params.companyId,
      projectId: params.projectId,
      trustedIdentity: true,
      faceEntityId: { $ne: null },
      tags: { $ne: null },
      $or: [
        { faceIdentifyType: IdentityType.LBS },
        { faceIdentifyType: { $ne: IdentityType.LBS }, faceDbType: FaceDbType.Formal }
      ]
    };
    const eventTime = {};
    if (params.startDate) Object.assign(eventTime, { $gte: DateFns.startOfDay(new Date(params.startDate)) });
    if (params.endDate) Object.assign(eventTime, { $lte: DateFns.endOfDay(new Date(params.endDate)) });
    if (!_.isEmpty(eventTime)) Object.assign(match, { eventTime });
    const qb = MyMongoDb.collections.camera.body.aggregate([
      { $match: match },//where
      {
        $group: {//group by
          _id: "$projectId",
          distinctManagerCount: { $addToSet: "$faceEntityId" },
          distinctDateCount: { $addToSet: { $dateToString: { format: "%Y-%m-%d", date: "$eventTime", timezone: 'Asia/Shanghai' } } }
        }
      },
      {
        $project: {//select
          _id: 0,
          managerCount: { $size: "$distinctManagerCount" },
          dateCount: { $size: "$distinctDateCount" }
        }
      }
    ], { allowDiskUse: true });
    return await qb.toArray();
  }

  /**
   * 查询body表签到记录
   */
  async getSignedPersonList(params: { companyId: string, projectId: string, startDate?: string, endDate?: string }) {
    const match = {
      companyId: params.companyId,
      projectId: params.projectId,
      trustedIdentity: true,
      faceEntityId: { $ne: null },
      tags: { $ne: null },
      $or: [
        { faceIdentifyType: IdentityType.LBS },
        { faceIdentifyType: { $ne: IdentityType.LBS }, faceDbType: FaceDbType.Formal }
      ]
    };
    const eventTime = {};
    if (params.startDate) Object.assign(eventTime, { $gte: DateFns.startOfDay(new Date(params.startDate)) });
    if (params.endDate) Object.assign(eventTime, { $lte: DateFns.endOfDay(new Date(params.endDate)) });
    if (!_.isEmpty(eventTime)) Object.assign(match, { eventTime });
    const qb = MyMongoDb.collections.camera.body.aggregate([
      { $match: match },//where
      {
        $project: {
          faceEntityId: 1,
          day: { $dateToString: { format: "%Y-%m-%d", date: "$eventTime", timezone: 'Asia/Shanghai' } }
        }
      },
      {
        $group: {//group by
          _id: "$faceEntityId",
          signInDays: { $addToSet: "$day" }
        }
      },
      {
        $project: {//select
          _id: 0,
          faceEntityId: "$_id",
          signInDays: 1,
          signInCount: { $size: "$signInDays" }
        }
      },
      {
        $sort: {//order by
          signInCount: -1, // 按总数（signInCount）降序排序
          faceEntityId: 1 // 按ID（faceEntityId）升序排序
        }
      }
    ], { allowDiskUse: true });
    return await qb.toArray();
  }

  /**
   * 查询body表签到记录
   */
  async getSignedList(params: { companyId: string, projectId: string, startDate?: string, endDate?: string, managerId?: string, pageSize?: number, pageNo?: number }) {
    const match = {
      companyId: params.companyId,
      projectId: params.projectId,
      trustedIdentity: true,
      faceEntityId: { $ne: null },
      tags: { $ne: null },
      $or: [
        { faceIdentifyType: IdentityType.LBS },
        { faceIdentifyType: { $ne: IdentityType.LBS }, faceDbType: FaceDbType.Formal }
      ]
    };
    if (params.managerId) Object.assign(match, { faceEntityId: params.managerId });
    const eventTime = {};
    if (params.startDate) Object.assign(eventTime, { $gte: DateFns.startOfDay(new Date(params.startDate)) });
    if (params.endDate) Object.assign(eventTime, { $lte: DateFns.endOfDay(new Date(params.endDate)) });
    if (!_.isEmpty(eventTime)) Object.assign(match, { eventTime });
    const aggregate: any[] = [
      { $match: match },//where
      {
        $project: {
          faceEntityId: 1,
          faceIdentifyType: 1,
          eventTime: 1,
          day: { $dateToString: { format: "%Y-%m-%d", date: "$eventTime", timezone: 'Asia/Shanghai' } }
        }
      },
      {
        $group: {//group by
          _id: { day: "$day", faceEntityId: "$faceEntityId" },
          arrivalTime: { $min: "$eventTime" },
          leaveTime: { $max: "$eventTime" },
          faceIdentifyTypes: { $addToSet: "$faceIdentifyType" }
        }
      },
      {
        $addFields: {
          leaveTime: {
            $cond: {
              if: { $eq: ["$leaveTime", "$arrivalTime"] },
              then: { $add: ["$leaveTime", 60 * 1000] }, // adding one minute in milliseconds
              else: "$leaveTime"
            }
          },
        }
      },
      {
        $addFields: {
          workTime: {
            $subtract: ["$leaveTime", "$arrivalTime"]
          },
        }
      },
      {
        $project: {//select
          _id: 0,
          faceEntityId: "$_id.faceEntityId",
          signInTime: "$_id.day",
          faceIdentifyTypes: 1,
          arrivalTime: 1,
          leaveTime: 1,
          workTime: 1,
        }
      }
    ];
    //统计
    const countResults = await MyMongoDb.collections.camera.body.aggregate([...aggregate, { $count: 'total' }], { allowDiskUse: true }).toArray();
    const total = !_.isEmpty(countResults) ? Number(countResults[0]?.total || '0') : 0;
    //排序 (按签到时间降序排序、按ID（faceEntityId）升序排序)
    aggregate.push({ $sort: { signInTime: -1, faceEntityId: 1 } });
    //分页
    if (params.pageSize && params.pageNo) {
      aggregate.push({ $skip: params.pageSize * (params.pageNo - 1) });
      aggregate.push({ $limit: params.pageSize });
    }
    const items = await MyMongoDb.collections.camera.body.aggregate(aggregate, { allowDiskUse: true }).toArray();
    return { total, items };
  }
  /**
  * 查询body表有签到记录的日期
  */
  async getSignedDayList(params: { companyId: string, projectId: string, startDate?: string, endDate?: string, managerId?: string, pageSize?: number, pageNo?: number }) {
    const { pageNo = 1, pageSize = 10 } = params;
    const match = {
      companyId: params.companyId,
      projectId: params.projectId,
      trustedIdentity: true,
      faceEntityId: { $ne: null },
      tags: { $ne: null },
      $or: [
        { faceIdentifyType: IdentityType.LBS },
        { faceIdentifyType: { $ne: IdentityType.LBS }, faceDbType: FaceDbType.Formal }
      ]
    };
    if (params.managerId) Object.assign(match, { faceEntityId: params.managerId });
    const eventTime = {};
    if (params.startDate) Object.assign(eventTime, { $gte: DateFns.startOfDay(new Date(params.startDate)) });
    if (params.endDate) Object.assign(eventTime, { $lte: DateFns.endOfDay(new Date(params.endDate)) });
    if (!_.isEmpty(eventTime)) Object.assign(match, { eventTime });
    const aggregate = [
      { $match: match },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$eventTime", timezone: 'Asia/Shanghai' }
          }
        }
      },
      {
        $sort: { _id: -1 }
      },
      {
        $skip: (pageNo - 1) * pageSize
      },
      {
        $limit: pageSize
      },
      {
        $project: { _id: 0, date: "$_id" }
      }
    ]
    const dateList = await MyMongoDb.collections.camera.body.aggregate(aggregate).toArray();
    return dateList;
  }
  /**
   * 获取工地员工在指定日期内或所有日期内的签到次数列表
   *
   * @param params.companyId - 公司id.
   * @param params.projectId - 工地id.
   * @returns
   */
  async getProjectMemberList(params: { companyId: string, projectId: string, excludeManager: string[] }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'm.manager_id as managerId',
        'm.nick_name as nickName',
        'cmr.role_name as roleName',
        'cp.oss_url as avatarUrl',
        '0 as signInCount'
      ])
      .from(ProjectMemberEntity, 'pm')
      .leftJoin(ManagerEntity, 'm', 'pm.person_id = m.person_id and m.company_id = :companyId', { companyId: params.companyId })
      .leftJoin(CompanyMemberRoleEntity, 'cmr', 'cmr.company_member_role_id = m.role_id')
      .leftJoin(CmsPhotoEntity, 'cp', 'cp.photo_id = m.profile_photo')
      .leftJoin(MemberRoleEntity, 'mr', 'mr.PROJECT_MEMBER_ID = pm.PROJECT_MEMBER_ID')
      .where('pm.project_id = :projectId', { projectId: params.projectId })
      .andWhere('pm.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('m.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('mr.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('mr.role_id != 3')
    if (!_.isEmpty(params?.excludeManager)) {
      qb.andWhere('m.manager_id not in (:...excludeManager)', { excludeManager: params.excludeManager });
    }
    qb.orderBy('pm.createTime', 'ASC').addOrderBy('m.manager_id', 'ASC');
    return await qb.getRawMany<{ managerId: string, nickName: string, roleName: string, avatarUrl: string, signInCount: string }>();
  }

  // 获取工地今日和总签到人数
  // async getProjectSignCount(params: { projectId: string }) {
  //   const today = format(new Date(), 'yyyy-MM-dd')
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       'sign_in_time',
  //     ])
  //     .from(ProjectSignInEntity, 'ps')
  //     .where('ps.project_id = :projectId and delete_flag = :deleteFlag', { projectId: params.projectId, deleteFlag: 'N' })
  //     .groupBy('manager_id')
  //   const res = await qb.getRawMany();
  //   const total = res.length || 0;
  //   const todayCount = await qb.andWhere('sign_in_time >= :today', { today }).getCount()
  //   return { total, todayCount }
  // }

  async getProjectSignCount(params: { projectId: string }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(DISTINCT ps.MANAGER_ID) as total',
        'ifnull(sum(sign_in_time >= date(now())), 0) as todayCount'
      ])
      .from(ProjectSignInEntity, 'ps')
      .where('ps.project_id = :projectId', { projectId: params.projectId })
      .andWhere('delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N });
    return await qb.getRawOne();
  }

  /**
  * 获取工地在指定日期内的开工记录
  *
  * @param params.projectId - 工地id.
  * @param params.startDate - 开始日期 (optional).
  * @param params.endDate - 截止日期,需要和开始日期同时出现(optional).
  * @param params.pageSize
  * @param params.pageNo
  * @returns
  */
  async getProjectActiveHistory(params: { projectId: string, startDate?: string, endDate?: string, pageSize?: number, pageNo?: number }) {
    const { projectId, pageNo, pageSize = 10, startDate, endDate } = params;
    let sql = `
    SELECT 'Y' as 'status', last_work_time as 'time'
    FROM ${DaoUtil.getDbName("camera.device_project_status")}
    WHERE PROJECT_ID = ${projectId}
      ${endDate ? `AND '${endDate}'>=CURDATE()` : ''}
      AND DATE(last_work_time) = CURDATE()
      AND delete_flag = 'N'
    UNION ALL
      (
        SELECT 'Y' as 'status', time as 'time'
        FROM ${DaoUtil.getDbName("camera.project_active_history")}
        WHERE PROJECT_ID = ${projectId}
        ${!!endDate ? `AND time >= '${startDate}'
        AND DATE(time) <= '${endDate}'` : ''}
        AND is_active = 1
        AND delete_flag = 'N'
      )
      ORDER BY time desc
    `
    const total = (await this.dataSource.query(sql)).length || 0
    if (pageNo) {
      sql += `LIMIT ${pageSize} OFFSET ${(pageNo - 1) * pageSize}`
    }
    const res = await this.dataSource.query(sql)
    return { total, list: res };
  }

  // 获取工地施工天数
  async getProjectActiveStatistics(params: { projectId: string }) {
    const { projectId } = params;
    // const projectId = 56626;
    const today = format(new Date(), 'yyyy-MM-dd')
    let sql = `
    SELECT 'Y' as 'status', last_work_time as 'time'
    FROM ${DaoUtil.getDbName("camera.device_project_status")}
    WHERE PROJECT_ID = ${projectId}
      AND last_work_time
        > '${today}'
      AND delete_flag = 'N'
    UNION ALL
      (
        SELECT 'Y' as 'status', time as 'time'
        FROM \`camera-dev\`.project_active_history
        WHERE PROJECT_ID = ${projectId}
        AND is_active = 1
        AND delete_flag = 'N'
      )
      ORDER BY time desc
    `
    const res = await this.dataSource.query(sql)
    const total = res.length || 0
    const date = !!res.length ? format(res[0].time, 'yyyy-MM-dd') : null
    return { total, latest: date };
  }

  /**
   * 工地整洁度记录
   * 
   * @param params.projectId
   * @param params.type
   * @param params.startDate
   * @param params.endDate
   * @param params.pageSize
   * @param params.pageNo
   * @returns
   */
  async getProjectTidinessHistory(params: { projectId: string, startDate?: string, endDate?: string, pageSize?: number, pageNo?: number, type?: number }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        "ptd.tidiness_type as tidinessType",
        "ptd.detection_time as detectionTime",
        "ptd.oss_photo_url as ossPhotoUrl",
      ])
      .from(ProjectTidinessDetectionEntity, 'ptd')
      .where('ptd.project_id = :projectId and ptd.delete_flag = :deleteFlag', { projectId: params.projectId, deleteFlag: 'N' })
    if (params.type) {
      qb.andWhere('ptd.tidiness_type = :type', { type: params.type })
    }
    if (params.startDate && params.endDate) {
      qb.andWhere('ptd.detection_time >= :startDate', { startDate: params.startDate })
        .andWhere('ptd.detection_time <= :endDate', { endDate: params.endDate + '23:59:59' });
    }
    const total = await qb.getCount();
    qb.orderBy('ptd.detection_time', 'DESC');
    if (params.pageSize && params.pageNo) {
      qb.offset((params.pageNo - 1) * params.pageSize).limit(params.pageSize);
    }
    const list = await qb.getRawMany();
    return { total, list };
  }

  async getProjectTidinessStatistics(params: { projectId: string }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'tidiness_type as tidinessType',
      ])
      .from(ProjectTidinessDetectionEntity, 'ptd')
      .where('ptd.project_id = :projectId and ptd.delete_flag = :deleteFlag', { projectId: params.projectId, deleteFlag: 'N' })
      .orderBy('ptd.detection_time', 'DESC')
    const total = await qb.getCount();
    const latest = await qb.getRawOne();
    return { total, latestTidnessType: latest.tidinessType };
  }

  async getProjectIdsByCompany(params: { companyId: string }): Promise<string[]> {
    const qb = this.dataSource.createQueryBuilder()
      .select(['project_id as projectId'])
      .from(ProjectEntity, 'p')
      .where('p.company_id = :companyId and p.delete_flag = :deleteFlag', { companyId: params.companyId, deleteFlag: Flag.N })
    const res = await qb.getRawMany();
    return res.map(item => item.projectId);
  }

  async getProjectIdsBySubDept(params: { companyId: string, departmentId: string }): Promise<string[]> {
    const qb = this.dataSource.createQueryBuilder()
      .select(['project_id as projectId'])
      .from(ProjectEntity, 'p')
      .leftJoin(DepartmentEntity, 'd', 'p.department_id = d.department_id')
      .where('p.company_id = :companyId and p.delete_flag = :deleteFlag and d.delete_flag = :deleteFlag', { companyId: params.companyId, deleteFlag: Flag.N })
    qb.andWhere("(d.department_id = :departmentId OR FIND_IN_SET(:departmentId, d.dir))", { departmentId: params.departmentId })
    const res = await qb.getRawMany();
    return res.map(item => item.projectId);
  }

  async getOnsiteProjectInfos(request: { companyId?: string, projectIdList?: string[] }) {
    const { companyId, projectIdList } = request;
    const qb = this.dataSource.createQueryBuilder()
      .from(ProjectEntity, 'project')
      .select([
        'project.project_id AS projectId',
        'project.project_name AS projectName',
        'project.house_number AS houseNumber',
        "comm.community_name AS communityName",
        "ifnull(project.project_start_time,project.createTime) AS projectStartTime",
        "project.completed_time as projectCompletedTime",
        "project.project_manager as projectDirectorId",
        "manager.nick_name as projectDirector",
        "if(ds.device_status_id is null,'N','Y') as isBindDevice"
      ])
      // .where('project.COMPANY_ID = :companyId', { companyId })
      .leftJoin(CommunityEntity, 'comm', 'project.COMMUNITY_ID = comm.COMMUNITY_ID')
      .leftJoin(ManagerEntity, "manager", "project.project_manager = manager.manager_id")
      .leftJoin(DeviceStatusEntity, "ds", "project.project_id = ds.project_id and ds.delete_flag = 'N'")
    qb.where("1=1")
    if (companyId) qb.andWhere("project.COMPANY_ID = :companyId", { companyId });
    if (projectIdList && projectIdList.length != 0) qb.andWhere(" project.project_id IN (:...projectIdList)", { projectIdList: projectIdList });
    const res = await qb.getRawMany();
    return res

  }

  async countProjectProblemGroupCompany(params: { projectProblemIds: string[] }) {
    //idList为空直接返回
    if (_.isEmpty(params.projectProblemIds)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.company_id as companyId',
        'c.company_name as companyName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(CompanyEntity, 'c', 'p.company_id = c.company_id')
      .leftJoin(ProjectProblemEntity, 'pp', 'p.project_id = pp.project_id')
      .andWhere('pp.project_problem_id in (:...projectProblemIds)', { projectProblemIds: params.projectProblemIds })
      .groupBy('p.company_id');
    return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
  }

  async countProjectGroupCompany(params: Project.SearchProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.company_id as companyId',
        'c.company_name as companyName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(CompanyEntity, 'c', 'p.company_id = c.company_id')
      .andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.company_id');
    return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
  }

  private projectPublicFilter(qb: SelectQueryBuilder<ProjectEntity>, params: Project.SearchProjectReq): SelectQueryBuilder<ProjectEntity> {
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //装修模式
    if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //工地状态
    if (params.projectStatus && params.projectStatus === 'no_finish') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is null or p.project_start_time < (CURDATE() + INTERVAL 1 DAY))`);
    if (params.projectStatus && params.projectStatus === 'no_work') qb.andWhere(`p.project_status = 'N' and (p.project_start_time is not null and p.project_start_time >= (CURDATE() + INTERVAL 1 DAY))`);
    if (params.projectStatus && params.projectStatus === 'finish') qb.andWhere(`p.project_status = 'Y'`);
    //当前阶段（实时数据特有）
    if (!_.isEmpty(params.stageList)) {
      // 检查是否已经存在与StageEntity的join
      const hasStageJoin = qb.expressionMap.joinAttributes.some(join => join.entityOrProperty === StageEntity && join.alias.name === 's');
      // 如果没有join过StageEntity，则添加join
      if (!hasStageJoin) qb.leftJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID and s.delete_flag = "N"');
      qb.andWhere('s.stage_name in (:...stages)', { stages: params.stageList });//阶段
    }
    return qb;
  }

  async countProjectGroupDepartment(params: Project.SearchProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.department_id as departmentId',
        'dep.department_name as departmentName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(DepartmentEntity, 'dep', 'p.department_id = dep.department_id')
      .andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.department_id');
    return await qb.getRawMany<{ count: string, departmentId: string, departmentName: string }>();
  }

  async countProject(params: Project.SearchProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return { count: '0' };
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count'
      ])
      .from(ProjectEntity, 'p')
      .andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    return await qb.getRawOne<{ count: string }>();
  }

  async countProjectGroupDeviceDepartment(params: Project.SearchProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'dpi.project_id AS project_id',
        'dpi.department_id AS department_id'
      ])
      .from(DeviceProjectInfoEntity, 'dpi')
      .where('dpi.delete_flag = "N"')
      .andWhere('dpi.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    subQuery.andWhere(qb => {
      const subQuery = qb.subQuery()
        .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
        .from(DeviceProjectInfoEntity, 'dpi2')
        .where('dpi2.project_id = dpi.project_id')
        .andWhere('dpi2.delete_flag = "N"');
      return `dpi.project_binding_time = (${subQuery.getQuery()})`;
    });
    subQuery.groupBy('dpi.project_id');

    // 主查询：关联子查询并完成统计
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) as count',
        'dpi.department_id as deviceDepartmentId',
        'dep.department_name as deviceDepartmentName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(`(${subQuery.getQuery()})`, 'dpi', 'p.project_id = dpi.project_id') // 确保是最后一条记录
      .leftJoin(DepartmentEntity, 'dep', 'dpi.department_id = dep.department_id')
      .andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('dpi.department_id');
    qb.setParameters(subQuery.getParameters());
    return await qb.getRawMany<{ count: string, deviceDepartmentId: string, deviceDepartmentName: string }>();
  }

  async countProjectGroupDecorationModel(params: Project.SearchProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.contract as decorationModel',
        'cp.parameter_name as decorationModelName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(CommonParameterEntity, 'cp', 'cp.parameter_type = "CONTRACT" and cp.parameter_code = p.contract')
      .andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.contract');
    return await qb.getRawMany<{ count: string, decorationModel: string, decorationModelName: string }>();
  }

  async countProjectGroupProjectType(params: Project.SearchProjectReq & { aiProjectIdList: string[] }) {
    // idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    // 查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        _.isEmpty(params?.aiProjectIdList) ?
          `'${ProjectDateReport.ProjectType.CommonProject}' AS projectType` :
          `if(p.project_id IN (${params?.aiProjectIdList.join(',')}), '${ProjectDateReport.ProjectType.WisdomProject}', '${ProjectDateReport.ProjectType.CommonProject}') AS projectType`,
      ])
      .from(ProjectEntity, 'p')
      .andWhere('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    // 按工地类型分组统计
    qb.groupBy('projectType');
    return await qb.getRawMany<{ count: string, projectType: ProjectDateReport.ProjectType }>();
  }

  async countProjectGroupProjectStatus(params: Project.SearchProjectReq) {
    // idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    // 查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        `CASE WHEN (p.project_status = 'N' and (p.project_start_time is not null and p.project_start_time >= (CURDATE() + INTERVAL 1 DAY))) THEN '${ProjectDateReport.ProjectStatus.NoWork}' 
              WHEN (p.project_status = 'N' and (p.project_start_time is null or p.project_start_time < (CURDATE() + INTERVAL 1 DAY))) THEN '${ProjectDateReport.ProjectStatus.NoFinish}' 
              WHEN (p.project_status = 'Y') THEN '${ProjectDateReport.ProjectStatus.Finish}' END AS projectStatus`,
      ])
      .from(ProjectEntity, 'p')
      .andWhere('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('projectStatus');
    return await qb.getRawMany<{ count: string, projectStatus: ProjectDateReport.ProjectStatus }>();
  }

  async countProjectGroupStage(params: Project.SearchProjectReq) {
    // idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    // 查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        `s.stage_id as stageId`,
        's.stage_template_id as stageTemplateId',
        's.business_stage_id as businessStageId',
        `s.stage_name as stageName`,
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(StageEntity, 's', 's.stage_template_id = p.stage_template_id and s.business_stage_id = p.business_stage_id and s.delete_flag = "N"')
      .andWhere('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('s.stage_name');
    return await qb.getRawMany<{ count: string, stageId: string, stageTemplateId: string, businessStageId: string, stageName: string }>();
  }

  async countProjectGroupCustomKeyPosition(params: Project.SearchProjectReq) {
    // idList为空直接返回
    if (_.isEmpty(params.projectIdList) || _.isEmpty(params.customKeyPositionRoleIds)) return [];
    //查询工地关键岗位（取最早加入的）
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'm.manager_id managerId',
        'm.nick_name nickName',
        'p.project_id projectId'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectMemberEntity, 'pm', 'pm.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.company_id = p.COMPANY_ID and m.PERSON_ID = pm.PERSON_ID')
      .where('pm.DELETE_FLAG = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('m.DELETE_FLAG = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList })
      .andWhere('m.role_id in (:...roleIdList)', { roleIdList: params.customKeyPositionRoleIds })
      .orderBy('pm.createTime', 'ASC').addOrderBy('pm.project_member_id', 'ASC')
      .having("1=1");
    //根据projectId聚合
    const groupQb = this.dataSource.createQueryBuilder()
      .select([
        'tt.projectId projectId',
        'tt.managerId managerId',
        'tt.nickName nickName'
      ])
      .from(`(${subQuery.getQuery()})`, 'tt')
      .groupBy('tt.projectId');
    groupQb.setParameters(subQuery.getParameters());
    //筛选、根据managerId聚合
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        'sub.managerId as managerId',
        'sub.nickName as nickName',
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(`(${groupQb.getQuery()})`, 'sub', 'sub.projectId = p.project_id')
      .andWhere('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('sub.managerId');
    qb.setParameters(groupQb.getParameters());
    return await qb.getRawMany<{ count: string, managerId: string, nickName: string }>();
  }

  async countProjectGroupProjectManager(params: Project.SearchProjectReq) {
    // idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return [];
    // 查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        `p.project_manager as projectManagerId`,
        `m.nick_name as projectManagerName`,
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .andWhere('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList });
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.project_manager');
    return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  }

  /**
   * 统计人员分布
   */
  async countProjectGroupMember(params: { idList: string[] }): Promise<{ memberId: string, memberName: string, count: string }[]> {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];

    const qb = this.dataSource.createQueryBuilder()
      .select('pm.person_id', 'memberId')
      .addSelect('m.nick_name', 'memberName')
      .addSelect('COUNT(DISTINCT p.project_id)', 'count')
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectMemberEntity, 'pm', 'pm.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.person_id = pm.person_id AND m.company_id = p.company_id')
      .where('p.project_id IN (:...idList)', { idList })
      .andWhere('pm.delete_flag = "N"')
      .groupBy('pm.person_id');

    return qb.getRawMany();
  }

  async countFinishStatusGroup(params: { idList: string[] }): Promise<{ finishStatus: string, count: string }[]> {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];

    const qb = this.dataSource.createQueryBuilder()
      .select('p.project_status', 'finishStatus')
      .addSelect('COUNT(p.project_id)', 'count')
      .from(ProjectEntity, 'p')
      .where('p.project_id IN (:...idList)', { idList })
      .groupBy('p.project_status');

    return qb.getRawMany();
  }

  async countEvaluate(params: Project.EvaluateSearchReq): Promise<{ count: string }> {
    const { idList } = params;
    if (_.isEmpty(idList)) return { count: '0' };
    const qb = this.dataSource.createQueryBuilder()
      .select('COUNT(1) AS count')
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .where('e.evaluate_id IN (:...idList)', { idList })
      .andWhere('e.delete_flag = "N"')
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    if (params.stage) qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    // if (params.score) qb.andWhere('e.CONSTRUCT_VALUE = :score', { score: params.score });
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    return qb.getRawOne();
  }

  /**
   * 统计评价工地部门分布
   */
  async countEvaluateProjectDepartmentGroup(params: Project.EvaluateSearchReq): Promise<{ departmentId: string, departmentName: string, count: string }[]> {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select('p.department_id', 'departmentId')
      .addSelect('d.department_name', 'departmentName')
      .addSelect('COUNT(1)', 'count')
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .leftJoin(DepartmentEntity, 'd', 'd.department_id = p.department_id')
      .where('e.evaluate_id IN (:...idList)', { idList })
      .andWhere('e.delete_flag = "N"')
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    if (params.stage) qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    // if (params.score) qb.andWhere('e.CONSTRUCT_VALUE = :score', { score: params.score });
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    qb.groupBy('p.department_id');
    return qb.getRawMany();
  }

  /**
   * 统计评价评价阶段分布
   */
  async countEvaluateProjectStageGroup(params: Project.EvaluateSearchReq): Promise<{ stageLabel: string, count: string }[]> {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        "REPLACE(e.stage_label, '阶段评价', '') AS stageLabel", // 移除"阶段评价"两个字
      ])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .where('e.evaluate_id IN (:...idList)', { idList })
      .andWhere('e.delete_flag = "N"')
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    if (params.stage) qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    // if (params.score) qb.andWhere('e.CONSTRUCT_VALUE = :score', { score: params.score });
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    qb.groupBy('stageLabel')
    return qb.getRawMany<{ stageLabel: string, count: string }>();
  }

  /**
   * 统计评价工地负责人分布
   */
  async countEvaluateProjectManagerGroup(params: Project.EvaluateSearchReq): Promise<{ projectManagerId: string, projectManagerName: string, count: string }[]> {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName'
      ])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .where('e.evaluate_id IN (:...idList)', { idList })
      .andWhere('e.delete_flag = "N"')
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    if (params.stage) qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    // if (params.score) qb.andWhere('e.CONSTRUCT_VALUE = :score', { score: params.score });
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    qb.groupBy('p.project_manager');
    return qb.getRawMany();
  }

  /**
   * 统计评价评价类型分布
   */
  async countEvaluateProjectGoodBadGroup(params: Project.EvaluateSearchReq & { ownerNegativeIdList: string[] }): Promise<{ type: string, name: string, count: string }[]> {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        !_.isEmpty(params.ownerNegativeIdList) ? `if (e.evaluate_id NOT IN (${params.ownerNegativeIdList.join(",")}), "good", "bad") as type` : `"good" as type`,
        !_.isEmpty(params.ownerNegativeIdList) ? `if (e.evaluate_id NOT IN (${params.ownerNegativeIdList.join(",")}), "好评", "差评") as name` : `"好评" as name`,
      ])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .where('e.evaluate_id IN (:...idList)', { idList })
      .andWhere('e.delete_flag = "N"')
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    if (params.stage) qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    // if (params.score) qb.andWhere('e.CONSTRUCT_VALUE = :score', { score: params.score });
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    qb.groupBy('type')
    return qb.getRawMany();
  }

  async countNodeTemplateGroup(params: { projectIds: string[] }): Promise<{ templateName: string, count: string }[]> {
    const { projectIds } = params;
    if (_.isEmpty(projectIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(1) AS count',
        'nt.node_template_name as templateName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(NodeTemplateEntity, 'nt', 'nt.node_template_id = p.node_template_id')
      .where('p.project_id IN (:...projectIds)', { projectIds })
      .andWhere('p.delete_flag = "N"')
      .groupBy('nt.node_template_name')
    return qb.getRawMany<{ templateName: string, count: string }>();
  }

  async searchHomeDecorProjectInfo(params: { projectIds: string[] }) {
    if (_.isEmpty(params.projectIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        "IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))) as projectAddress",
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName',
        'p.department_id as departmentId',
        'd.department_name as departmentName',
        's.stage_name as stageName',
        'p.stage_template_id as stageTemplateId',
        'p.business_stage_id as businessStageId',
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(DepartmentEntity, 'd', 'p.department_id = d.department_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .leftJoin(StageEntity, 's', 's.stage_template_id = p.stage_template_id and s.business_stage_id = p.business_stage_id and s.delete_flag = "N"')
      .leftJoin(CommunityEntity, 'com', 'p.community_id = com.community_id')
      .leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      .where('p.project_id IN (:...projectIds)', { projectIds: params.projectIds })
      .groupBy('p.project_id');
    return qb.getRawMany<{ projectId: string, projectAddress: string, projectManagerId: string, projectManagerName: string, departmentId: string, departmentName: string, stageName: string, stageTemplateId: string, businessStageId: string }>();
  }

  async searchConstructionProjectInfo(params: { projectIds: string[] }) {
    if (_.isEmpty(params.projectIds)) return [];
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'dpi.project_id AS project_id',
        'dpi.department_id AS department_id'
      ])
      .from(DeviceProjectInfoEntity, 'dpi')
      .where('dpi.delete_flag = "N"');
    if (!_.isEmpty(params.projectIds)) subQuery.andWhere('dpi.project_id in (:...projectIds)', { projectIds: params.projectIds });
    subQuery.andWhere(qb => {
      const subQuery = qb.subQuery()
        .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
        .from(DeviceProjectInfoEntity, 'dpi2')
        .where('dpi2.project_id = dpi.project_id')
        .andWhere('dpi2.delete_flag = "N"');
      return `dpi.project_binding_time = (${subQuery.getQuery()})`;
    });
    subQuery.groupBy('dpi.project_id');
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        "IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))) as projectAddress",
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName',
        'p.department_id as departmentId',
        'd.department_name as departmentName',
        'dpi.department_id as deviceDepartmentId',
        'dep.department_name as deviceDepartmentName',
        's.stage_name as stageName',
        'p.stage_template_id as stageTemplateId',
        'p.business_stage_id as businessStageId',
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(DepartmentEntity, 'd', 'p.department_id = d.department_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .leftJoin(CommunityEntity, 'com', 'p.community_id = com.community_id')
      .leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      .leftJoin(`(${subQuery.getQuery()})`, 'dpi', 'p.project_id = dpi.project_id') // 确保是最后一条记录
      .leftJoin(DepartmentEntity, 'dep', 'dpi.department_id = dep.department_id')
      .leftJoin(StageEntity, 's', 's.stage_template_id = p.stage_template_id and s.business_stage_id = p.business_stage_id and s.delete_flag = "N"')
      .where('p.project_id IN (:...projectIds)', { projectIds: params.projectIds })
      .groupBy('p.project_id');
    return qb.getRawMany<{ projectId: string, projectAddress: string, projectManagerId: string, projectManagerName: string, departmentId: string, departmentName: string, deviceDepartmentId: string, deviceDepartmentName: string, stageName: string, stageTemplateId: string, businessStageId: string }>();
  }

  async searchRealProjectList(params: Project.SearchRealProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'p.company_id as linkCompanyId',
        'p.project_number as projectNumber',
        'date_format(p.createTime, "%Y-%m-%d") as createDate',
        'date_format(p.project_start_time, "%Y-%m-%d") as startDate',
        'date_format(p.plan_completed_time, "%Y-%m-%d") as planCompletedDate',
        'date_format(p.completed_time, "%Y-%m-%d") as completedDate',
        'p.CONTRACT as decorationModel',
        'SUM(if(pp.project_problem_id is not null, 1, 0)) as noRectifiedProblemCount',
        's.stage_id as stageId',
        's.stage_name as stageName',
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectProblemEntity, 'pp', 'p.project_id = pp.project_id and pp.problem_status in ("1", "2") and pp.delete_flag = "N"')
      .leftJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID and s.delete_flag = "N"')
      .where('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList })
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.project_id');
    qb.orderBy('p.project_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<{ projectId: string, linkCompanyId: string, projectNumber: string, createTime: string, startDate: string, planCompletedDate: string, completedDate: string, decorationModel: string, projectType: string, noRectifiedProblemCount: string, stageId: string, stageName: string }>(qb);
  }

  async searchEtlProjectList(params: Project.SearchEtlProjectReq) {
    //idList为空直接返回
    if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'p.company_id as linkCompanyId',
        'p.project_number as projectNumber',
        'date_format(p.createTime, "%Y-%m-%d") as createDate',
        'date_format(p.project_start_time, "%Y-%m-%d") as startDate',
        'date_format(p.plan_completed_time, "%Y-%m-%d") as planCompletedDate',
        'date_format(p.completed_time, "%Y-%m-%d") as completedDate',
        'p.CONTRACT as decorationModel',
        's.stage_id as stageId',
        's.stage_name as stageName',
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID and s.delete_flag = "N"')
      .where('p.project_id IN (:...projectIds)', { projectIds: params.projectIdList })
    this.projectPublicFilter(qb, params);
    qb.groupBy('p.project_id');
    qb.orderBy('p.project_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<{ projectId: string, linkCompanyId: string, projectNumber: string, createTime: string, startDate: string, planCompletedDate: string, completedDate: string, decorationModel: string, projectType: string, stageId: string, stageName: string }>(qb);
  }

  /**
   * 统计节点修改原因类型分布
   */
  async countNodeModifiedReasonTypeGroup(params: { nodeIds: string[] }) {
    if (_.isEmpty(params.nodeIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'psm.reason_type as reasonType',
        'cp.parameter_code as reasonName'
      ])
      .from(ProjectNodeEntity, 'pn')
      .leftJoin(ProjectScheduleModifiedEntity, 'psm', 'psm.project_node_id = pn.project_node_id')
      .leftJoin(CommonParameterEntity, 'cp', 'cp.parameter_code = psm.reason_type and cp.parameter_type = "PROJECT_SCHEDULE_MODIFIED_REASON_TYPE"')
      .where('pn.project_node_id IN (:...projectNodeIds)', { projectNodeIds: params.nodeIds })
      .andWhere('pn.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere('psm.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .groupBy('psm.reason_type');
    return await qb.getRawMany<{ count: string; reasonType: string; reasonName: string; }>();
  }

  async countProjectNodeModifiedReasonTypeGroup(params: { projectIds: string[] }) {
    if (_.isEmpty(params.projectIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'psm.reason_type as reasonType',
        'cp.parameter_code as reasonName'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectNodeEntity, 'pn', 'pn.project_node_id = p.current_project_node')
      .leftJoin(ProjectScheduleModifiedEntity, 'psm', 'psm.project_node_id = pn.project_node_id')
      .leftJoin(CommonParameterEntity, 'cp', 'cp.parameter_code = psm.reason_type and cp.parameter_type = "PROJECT_SCHEDULE_MODIFIED_REASON_TYPE"')
      .where('p.project_id IN (:...projectIds)', { projectIds: params.projectIds })
      .andWhere('pn.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere('psm.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .groupBy('psm.reason_type');
    return await qb.getRawMany<{ count: string; reasonType: string; reasonName: string; }>();
  }

  async searchProjectIdListByTodoCompleted({ projectIds, todoCompleted }: { projectIds: string[], todoCompleted: string }) {
    if (_.isEmpty(projectIds) || !todoCompleted) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select(['p.project_id as projectId'])
      .from(ProjectEntity, 'p')
      .where(`p.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`p.delete_flag = 'N'`)
      .andWhere(`p.project_status = 'N'`)
      .andWhere(`p.project_start_time is not null`)
      .andWhere(`DATEDIFF(DATE(now()), DATE(p.project_start_time)) >= :todoCompleted`, { todoCompleted });
    const res = await qb.getRawMany<{ projectId: string }>();
    return res.map(item => item.projectId);
  }

  async searchProjectIdListByOwnerFocusOneDay({ projectIds, ownerFocusProjectOneDay, startTime, endTime }: { projectIds: string[], ownerFocusProjectOneDay: string, startTime: string, endTime: string }) {
    if (_.isEmpty(projectIds) || !ownerFocusProjectOneDay) return [];
    //子查询：单日业主查看直播次数
    const subqb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) num',
        'date(ocls.live_streaming_time) group_date',
        'ocls.project_id project_id',
      ])
      .from(OwnerCameraLiveStreamingEntity, 'ocls')
      .where(`ocls.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`ocls.delete_flag = 'N'`)
      .andWhere(`ocls.live_streaming_time >= :startTime`, { startTime })
      .andWhere(`ocls.live_streaming_time < :endTime`, { endTime })
      .groupBy('group_date');
    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select(['p.project_id as projectId'])
      .from(ProjectEntity, 'p')
      .leftJoin(`(${subqb.getQuery()})`, 'sub', 'sub.project_id = p.project_id')
      .where(`p.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`p.delete_flag = 'N'`)
      .andWhere(`sub.num >= :ownerFocusProjectOneDay`, { ownerFocusProjectOneDay })
      .groupBy('p.project_id');
    qb.setParameters(subqb.getParameters());
    const res = await qb.getRawMany<{ projectId: string }>();
    return res.map(item => item.projectId);
  }

  async searchProjectIdListByOwnerFocusTime({ projectIds, ownerFocusProjectTime, startTime, endTime }: { projectIds: string[], ownerFocusProjectTime: string, startTime: string, endTime: string }) {
    if (_.isEmpty(projectIds) || !ownerFocusProjectTime) return [];
    //子查询：时间范围业主查看直播次数
    const subqb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) num',
        'ocls.project_id project_id',
      ])
      .from(OwnerCameraLiveStreamingEntity, 'ocls')
      .where(`ocls.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`ocls.delete_flag = 'N'`)
      .andWhere(`ocls.live_streaming_time >= :startTime`, { startTime })
      .andWhere(`ocls.live_streaming_time < :endTime`, { endTime })
      .groupBy('ocls.project_id');
    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select(['p.project_id as projectId'])
      .from(ProjectEntity, 'p')
      .leftJoin(`(${subqb.getQuery()})`, 'sub', 'sub.project_id = p.project_id')
      .where(`p.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`p.delete_flag = 'N'`)
      .andWhere(`sub.num >= :ownerFocusProjectTime`, { ownerFocusProjectTime })
      .groupBy('p.project_id');
    qb.setParameters(subqb.getParameters());
    const res = await qb.getRawMany<{ projectId: string }>();
    return res.map(item => item.projectId);
  }

  async searchEvaluateListScore({ idList }: { idList: string[] }) {
    if (_.isEmpty(idList)) return [];
    //子查询：工地综合得分
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'EVALUATE_ID as evaluateId',
        `COALESCE(CAST(NULLIF(CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) as constructValue`,//施工评分
        `COALESCE(CAST(NULLIF(DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) as desighValue`,//设计评分
        `COALESCE(CAST(NULLIF(COST_VALUE, '') AS DECIMAL(10,2)), 0) as constValue`,//价格评分
        `(COALESCE(CAST(NULLIF(CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3 AS averageValue`,//综合得分
      ])
      .from(EvaluateEntity, 'e')
      .where(`e.evaluate_id in (:...idList)`, { idList })
      .andWhere(`e.evaluate_type = '3'`)
      .andWhere(`e.delete_flag = 'N'`)
    return await qb.getRawMany<{ constructValue: string, desighValue: string, constValue: string, averageValue: string, evaluateId: string }>();
  }

  // async searchEvaluateByIndividualScore({ idList, startTime, endTime }: { idList: string[], individualScore: string, startTime: string, endTime: string }) {
  //   if (_.isEmpty(idList)) return [];
  //   //子查询：工地单项最低得分
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       `COALESCE(CAST(NULLIF(CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) as constructValue`,//施工评分
  //       `COALESCE(CAST(NULLIF(DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) as desighValue`,//设计评分
  //       `COALESCE(CAST(NULLIF(COST_VALUE, '') AS DECIMAL(10,2)), 0) as constValue`,//价格评分
  //       'EVALUATE_ID as evaluateId'
  //     ])
  //     .from(EvaluateEntity, 'e')
  //     .where(`e.evaluate_id in (:...idList)`, { idList })
  //     .andWhere(`e.evaluate_type = '3'`)
  //     .andWhere(`e.delete_flag = 'N'`)
  //     .andWhere(`e.EVALUATE_TIME >= :startTime`, { startTime })
  //     .andWhere(`e.EVALUATE_TIME < :endTime`, { endTime })
  //     // .having(`((constructValue < :individualScore) or (desighValue < :individualScore)  or (constValue < :individualScore))`, { individualScore })
  //   return await qb.getRawMany<{ constructValue: string, desighValue: string, constValue: string, evaluateId: string }>();
  // }

  async searchProjectListByOverallScore({ projectIds, overallScore, startTime, endTime }: { projectIds: string[], overallScore: string, startTime: string, endTime: string }) {
    if (_.isEmpty(projectIds) || !overallScore) return [];
    //子查询：工地综合得分
    const subqb = this.dataSource.createQueryBuilder()
      .select([
        `(COALESCE(CAST(NULLIF(CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3 AS average_value`,//综合得分
        'e.TARGET_ID project_id',
        'EVALUATE_ID as evaluate_id'
      ])
      .from(EvaluateEntity, 'e')
      .where(`e.TARGET_ID in (:...projectIds)`, { projectIds })
      .andWhere(`e.evaluate_type = '3'`)
      .andWhere(`e.delete_flag = 'N'`)
      .andWhere(`e.EVALUATE_TIME >= :startTime`, { startTime })
      .andWhere(`e.EVALUATE_TIME < :endTime`, { endTime });
    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select(['p.project_id as projectId', 'group_concat(sub.evaluate_id) as evaluateIds'])
      .from(ProjectEntity, 'p')
      .leftJoin(`(${subqb.getQuery()})`, 'sub', 'sub.project_id = p.project_id')
      .where(`p.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`p.delete_flag = 'N'`)
      .andWhere(`sub.average_value < :overallScore`, { overallScore })
      .groupBy('p.project_id');
    qb.setParameters(subqb.getParameters());
    return await qb.getRawMany<{ projectId: string, evaluateIds: string }>();
  }

  async searchProjectListByIndividualScore({ projectIds, individualScore, startTime, endTime }: { projectIds: string[], individualScore: string, startTime: string, endTime: string }) {
    if (_.isEmpty(projectIds) || !individualScore) return [];
    //子查询：工地单项最低得分
    const subqb = this.dataSource.createQueryBuilder()
      .select([
        `COALESCE(CAST(NULLIF(CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) as construct_value`,//施工评分
        `COALESCE(CAST(NULLIF(DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) as desigh_value`,//设计评分
        `COALESCE(CAST(NULLIF(COST_VALUE, '') AS DECIMAL(10,2)), 0) as const_value`,//价格评分
        'e.TARGET_ID project_id',
        'EVALUATE_ID as evaluate_id'
      ])
      .from(EvaluateEntity, 'e')
      .where(`e.TARGET_ID in (:...projectIds)`, { projectIds })
      .andWhere(`e.evaluate_type = '3'`)
      .andWhere(`e.delete_flag = 'N'`)
      .andWhere(`e.EVALUATE_TIME >= :startTime`, { startTime })
      .andWhere(`e.EVALUATE_TIME < :endTime`, { endTime })
    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select(['p.project_id as projectId', 'group_concat(sub.evaluate_id) as evaluateIds'])
      .from(ProjectEntity, 'p')
      .leftJoin(`(${subqb.getQuery()})`, 'sub', 'sub.project_id = p.project_id')
      .where(`p.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`p.delete_flag = 'N'`)
      .andWhere(`((sub.construct_value < :individualScore) or (sub.desigh_value < :individualScore)  or (sub.const_value < :individualScore))`, { individualScore })
      .groupBy('p.project_id');
    qb.setParameters(subqb.getParameters());
    return await qb.getRawMany<{ projectId: string, evaluateIds: string }>();
  }

  async getInactiveProjectIdList({ projectIds, startTime, endTime }: { projectIds: string[], startTime: string, endTime: string }) {
    if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select(['p.project_id as projectId'])
      .from(ProjectActiveHistory, 'pah')
      .where(`pah.delete_flag = 'N'`)
      .andWhere('pah.is_active = 2')
      .andWhere(`pah.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`pah.time >= :startTime`, { startTime })
      .andWhere(`pah.time < :endTime`, { endTime })
    const res = await qb.getRawMany<{ projectId: string }>();
    return res.map(item => item.projectId);
  }

  async getDelayProjectIdList({ projectIds, startTime, endTime, isNodeDelayOverdue }: { projectIds: string[], startTime: string, endTime: string, isNodeDelayOverdue?: 'Y' | 'N' }) {
    if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select(['psm.project_id as projectId'])
      .from(ProjectEntity, 'p')
      .innerJoin(ProjectNodeEntity, 'pn', 'p.project_id = pn.project_id')
      .innerJoin(ProjectScheduleModifiedEntity, 'psm', 'pn.project_node_id = psm.project_node_id and psm.delete_flag = "N"')
      .where(`p.bind_project_node = 'Y'`)
      .andWhere(`psm.type = 'delay'`)
      .andWhere(`psm.project_id in (:...projectIds)`, { projectIds })
    if (Common.Flag.Y === isNodeDelayOverdue) {
      qb.andWhere(`pn.origin_deadline_time >= :startTime`, { startTime })
      qb.andWhere(`pn.origin_deadline_time < :endTime`, { endTime })
    } else {
      qb.andWhere(`pn.deadline_time >= :startTime`, { startTime })
      qb.andWhere(`pn.deadline_time < :endTime`, { endTime })
    }
    qb.groupBy('psm.project_id');
    const res = await qb.getRawMany<{ projectId: string }>();
    return res.map(item => item.projectId);
  }

  async getProjectIdsByEvaluateIds({ evaluateIds }: { evaluateIds: string[] }) {
    const qb = this.dataSource.createQueryBuilder()
    if (evaluateIds.length === 0) return [];
    // evaluateEntity join projectEntity evaluateEntity.target_id = projectEntity.project_id
    qb.select(['p.project_id'])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .where(`e.evaluate_id in (:...evaluateIds)`, { evaluateIds })
      .andWhere(`e.delete_flag = 'N'`)
      .andWhere(`p.delete_flag = 'N'`)
      .groupBy('p.project_id');
    const res = await qb.getRawMany();
    return res.map(item => item.project_id);
  }

  async getProjectIdsByProblemIds(problemIds: string[]) {
    const qb = this.dataSource.createQueryBuilder()
    if (problemIds.length === 0) return [];
    // evaluateEntity join projectEntity evaluateEntity.target_id = projectEntity.project_id
    qb.select(['p.project_id'])
      .from(ProjectProblemEntity, 'pp')
      .leftJoin(ProjectEntity, 'p', 'pp.project_id = p.project_id')
      .where(`pp.project_problem_id in (:...problemIds)`, { problemIds })
      .andWhere(`pp.delete_flag = 'N'`)
      .andWhere(`p.delete_flag = 'N'`)
      .groupBy('p.project_id');
    const res = await qb.getRawMany();
    return res.map(item => item.project_id);
  }

  async searchProjectEvaluateProjectList(params: Project.EvaluateSearchReq) {
    const { idList } = params;
    if (_.isEmpty(idList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
      ])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .leftJoin(PersonEntity, 'per', 'per.person_id = e.person_id')
      .where(`e.evaluate_id in (:...idList)`, { idList })
      .andWhere(`e.delete_flag = 'N'`)
      .andWhere(`p.delete_flag = 'N'`)
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) {
      qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    }
    if (params.stage) {
      qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    }
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    qb.groupBy('p.project_id');
    return await qb.getRawMany<{ projectId: string }>();
  }

  async searchProjectEvaluateList(params: Project.EvaluateSearchReq) {
    const { idList } = params;
    if (_.isEmpty(idList)) return { total: 0, items: [] };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.company_id as linkCompanyId',
        'e.evaluate_id as evaluateId',
        'p.project_id as projectId',
        'LEFT(e.stage_label, CHAR_LENGTH(e.stage_label) - 4) as stage',
        `ROUND(
          (COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3
          , 1 ) AS score`,//综合得分，保留一位小数
        'e.CONSTRUCT_VALUE as constructValue',
        'e.DESIGH_VALUE as desighValue',
        'e.COST_VALUE as costValue',
        'e.evaluate_desc as description',
        'e.evaluate_time as time',
        'per.nick_name as owner',
        'e.evaluate_img as evaluateImgIds'
      ])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .leftJoin(PersonEntity, 'per', 'per.person_id = e.person_id')
      .where(`e.evaluate_id in (:...idList)`, { idList })
      .andWhere(`e.delete_flag = 'N'`)
      .andWhere(`p.delete_flag = 'N'`)
    //所属公司
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.projectManagerId) {
      qb.andWhere(`p.project_manager = :projectManagerId`, { projectManagerId: params.projectManagerId });
    }
    if (params.stage) {
      qb.andWhere('e.stage_label = :stage', { stage: params.stage + "阶段评价" });
    }
    if (params.score) {
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
          COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
          COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) >= :scoreStart`, { scoreStart: params.score });
      qb.andWhere(`((COALESCE(CAST(NULLIF(e.CONSTRUCT_VALUE, '') AS DECIMAL(10,2)), 0) + 
            COALESCE(CAST(NULLIF(e.DESIGH_VALUE, '') AS DECIMAL(10,2)), 0) +
            COALESCE(CAST(NULLIF(e.COST_VALUE, '') AS DECIMAL(10,2)), 0)) / 3) < :scoreEnd`, { scoreEnd: Number(params.score) + 1 });
    }
    qb.groupBy('e.evaluate_id');
    qb.orderBy('e.EVALUATE_TIME', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    const res = await DaoUtil.getPageRes<{ linkCompanyId: string, evaluateId: string, projectId: string, stage: string, score: string, description: string, time: string, owner: string, evaluateImgIds: string, constructValue: string, desighValue: string, costValue: string }>(qb);
    return res;
  }

  async getProjectIdsByDeviceDepartmentId({ projectIds, deviceDepartmentId }: { projectIds: string[], deviceDepartmentId: string }) {
    if (_.isEmpty(projectIds) || !deviceDepartmentId) return [];
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
      .from(DeviceProjectInfoEntity, 'dpi').where('dpi.delete_flag = "N"')
      .andWhere('dpi.project_id in (:...projectIds)', { projectIds })
      .andWhere(qb => {
        const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      })
      .groupBy('dpi.project_id');
    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select('dpd.project_id AS projectId')
      .from(`(${subQuery.getQuery()})`, 'dpd') // 确保是最后一条记录
      .where('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId });
    qb.setParameters(subQuery.getParameters());
    const res = await qb.getRawMany<{ projectId: string }>();
    return res.map(item => item.projectId);
  }

  async searchEvaluateIdList(params: { projectIds: string[] }) {
    const { projectIds } = params;
    if (_.isEmpty(projectIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'e.evaluate_id as evaluateId',
      ])
      .from(EvaluateEntity, 'e')
      .leftJoin(ProjectEntity, 'p', 'e.target_id = p.project_id')
      .where(`p.project_id in (:...projectIds)`, { projectIds })
      .andWhere(`e.evaluate_type = '3'`)
      .andWhere(`e.delete_flag = 'N'`)
      .andWhere(`p.delete_flag = 'N'`)
      .groupBy('e.evaluate_id');
    const result = await qb.getRawMany<{ evaluateId: string }>();
    return result.map(item => item.evaluateId);
  }

  async getProjectAlbumListCount(params: Project.ProjectAlbumListDetailReq) {
    const { companyId, projectId, yqzDeviceId, photoTypes, eventType, generateSource, materialType, startTime, endTime } = params;
    if (!companyId || !projectId || _.isEmpty(photoTypes)) return 0;
    //直播剪影
    const liveSilhouetteQb = this.dataSource.getRepository(ProjectCameraLiveSilhouetteEntity)
      .createQueryBuilder('cls')
      .select([
        'cls.photo_id as mediaId',
      ])
      .where('cls.company_id = :companyId', { companyId })
      .andWhere('cls.project_id = :projectId', { projectId })
      .andWhere('cls.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N });
    if (yqzDeviceId) liveSilhouetteQb.andWhere('cls.yqz_device_id = :yqzDeviceId', { yqzDeviceId });
    if (generateSource) liveSilhouetteQb.andWhere('cls.generate_source = :generateSource', { generateSource });
    if (materialType) liveSilhouetteQb.andWhere('cls.material_type = :materialType', { materialType });
    if (startTime) liveSilhouetteQb.andWhere('cls.generate_time >= :clsStartTime', { clsStartTime: startTime });
    if (endTime) liveSilhouetteQb.andWhere('cls.generate_time <= :clsEndTime', { clsEndTime: endTime });

    //工地更新
    const projectUpdateQb = this.dataSource.getRepository(ProjectUpdateEntity)
      .createQueryBuilder('pu')
      .select([
        'pum.photo_id as mediaId',
      ])
      .leftJoin(ProjectUpdateMediaEntity, 'pum', 'pum.project_update_id = pu.project_update_id')
      .where('pu.company_id = :companyId', { companyId })
      .andWhere('pu.project_id = :projectId', { projectId })
      .andWhere('pu.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('pum.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('pu.project_update_type = "12"');
    if (startTime) projectUpdateQb.andWhere('pu.project_update_time >= :puStartTime', { puStartTime: startTime });
    if (endTime) projectUpdateQb.andWhere('pu.project_update_time <= :puEndTime', { puEndTime: endTime });
    projectUpdateQb.andWhere('pu.project_update_media_count > 0');

    //摄像头
    const cameraEventQb = this.dataSource.getRepository(MotiondetectEventEntity)
      .createQueryBuilder('e')
      .select([
        'e.id as mediaId',
      ])
      .where('e.company_id = :companyId', { companyId })
      .andWhere('e.project_id = :projectId', { projectId })
      .andWhere('e.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N });
    if (yqzDeviceId) cameraEventQb.andWhere('e.yqz_device_id = :yqzDeviceId', { yqzDeviceId });
    if (eventType) cameraEventQb.andWhere('e.event_type = :eventType', { eventType });
    if (startTime) cameraEventQb.andWhere('e.event_time >= :cameraStartTime', { cameraStartTime: startTime });
    if (endTime) cameraEventQb.andWhere('e.event_time <= :cameraEndTime', { cameraEndTime: endTime });

    const qbList = [];
    if (photoTypes.includes(Project.PhotoType.LIVE_IMAGE)) qbList.push(liveSilhouetteQb.getQuery());
    if (photoTypes.includes(Project.PhotoType.UPLOAD_IMAGE)) qbList.push(projectUpdateQb.getQuery());
    if (photoTypes.includes(Project.PhotoType.CAMERA_IMAGE)) qbList.push(cameraEventQb.getQuery());
    if (_.isEmpty(qbList)) return 0;
    const sql = qbList.join(' UNION ALL ');
    const qb = this.dataSource.createQueryBuilder()
      .select('COUNT(1) as total')
      .from(`(${sql})`, 'tt')
    if (photoTypes.includes(Project.PhotoType.LIVE_IMAGE)) qb.setParameters(liveSilhouetteQb.getParameters());
    if (photoTypes.includes(Project.PhotoType.UPLOAD_IMAGE)) qb.setParameters(projectUpdateQb.getParameters());
    if (photoTypes.includes(Project.PhotoType.CAMERA_IMAGE)) qb.setParameters(cameraEventQb.getParameters());
    const res = await qb.getRawOne<{ total: number }>();
    return res?.total || 0;
  }

  async getProjectAlbumListDetail(params: Project.ProjectAlbumListDetailReq) {
    const { companyId, projectId, yqzDeviceId, photoTypes, eventType, generateSource, materialType, startTime, endTime, pageNo = 1, pageSize = 10 } = params;
    if (!companyId || !projectId || _.isEmpty(photoTypes)) return [];
    //直播剪影
    const liveSilhouetteQb = this.dataSource.getRepository(ProjectCameraLiveSilhouetteEntity)
      .createQueryBuilder('cls')
      .select([
        '"1" as albumType',
        '"liveCut" as type',
        'DATE_FORMAT(cls.generate_time, "%Y-%m-%d") as publishDate',
        'DATE_FORMAT(cls.generate_time, "%Y-%m-%d %H:%i:%s") as publishTime',
        'cls.photo_id as mediaId',
        'cm.media_type as mediaType',
        'cm.oss_url as mediaUri',
        'IFNULL(if(cm.media_type = 2, cm.resource_url, cm.media_resource_url), cm.oss_url) as mediaResourceUri',
        'null as eventType'
      ])
      .leftJoin(CmsPhotoEntity, 'cm', 'cm.photo_id = cls.photo_id')
      .where('cls.company_id = :companyId', { companyId })
      .andWhere('cls.project_id = :projectId', { projectId })
      .andWhere('cls.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N });
    if (yqzDeviceId) liveSilhouetteQb.andWhere('cls.yqz_device_id = :yqzDeviceId', { yqzDeviceId });
    if (generateSource) liveSilhouetteQb.andWhere('cls.generate_source = :generateSource', { generateSource });
    if (materialType) liveSilhouetteQb.andWhere('cls.material_type = :materialType', { materialType });
    if (startTime) liveSilhouetteQb.andWhere('cls.generate_time >= :clsStartTime', { clsStartTime: startTime });
    if (endTime) liveSilhouetteQb.andWhere('cls.generate_time <= :clsEndTime', { clsEndTime: endTime });

    //工地更新
    const projectUpdateQb = this.dataSource.getRepository(ProjectUpdateEntity)
      .createQueryBuilder('pu')
      .select([
        '"2" as albumType',
        '"projectUpdate" as type',
        'DATE_FORMAT(pu.project_update_time, "%Y-%m-%d") as publishDate',
        'DATE_FORMAT(pu.project_update_time, "%Y-%m-%d %H:%i:%s") as publishTime',
        'pum.photo_id as mediaId',
        'cm.media_type as mediaType',
        'cm.oss_url as mediaUri',
        'IFNULL(if(cm.media_type = 2, cm.resource_url, cm.media_resource_url), cm.oss_url) as mediaResourceUri',
        'null as eventType'
      ])
      .leftJoin(ProjectUpdateMediaEntity, 'pum', 'pum.project_update_id = pu.project_update_id')
      .leftJoin(CmsPhotoEntity, 'cm', 'cm.photo_id = pum.photo_id')
      .where('pu.company_id = :companyId', { companyId })
      .andWhere('pu.project_id = :projectId', { projectId })
      .andWhere('pu.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('pum.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('pu.project_update_type = "12"');
    if (startTime) projectUpdateQb.andWhere('pu.project_update_time >= :puStartTime', { puStartTime: startTime });
    if (endTime) projectUpdateQb.andWhere('pu.project_update_time <= :puEndTime', { puEndTime: endTime });
    projectUpdateQb.andWhere('pu.project_update_media_count > 0');

    //摄像头
    const cameraEventQb = this.dataSource.getRepository(MotiondetectEventEntity)
      .createQueryBuilder('e')
      .select([
        '"3" albumType',
        '"motionDetection" as type',
        'DATE_FORMAT(e.event_time, "%Y-%m-%d") as publishDate',
        'DATE_FORMAT(e.event_time, "%Y-%m-%d %H:%i:%s") as publishTime',
        'e.id as mediaId',
        `"${YqzMediaType.IMAGE}" as mediaType`,
        'e.oss_photo_url as mediaUri',
        'e.oss_photo_url as mediaResourceUri',
        'e.event_type as eventType'
      ])
      .where('e.company_id = :companyId', { companyId })
      .andWhere('e.project_id = :projectId', { projectId })
      .andWhere('e.delete_flag = :deleteFlag', { deleteFlag: Common.Flag.N });
    if (yqzDeviceId) cameraEventQb.andWhere('e.yqz_device_id = :yqzDeviceId', { yqzDeviceId });
    if (eventType) cameraEventQb.andWhere('e.event_type = :eventType', { eventType });
    if (startTime) cameraEventQb.andWhere('e.event_time >= :cameraStartTime', { cameraStartTime: startTime });
    if (endTime) cameraEventQb.andWhere('e.event_time <= :cameraEndTime', { cameraEndTime: endTime });

    const qbList = [];
    if (photoTypes.includes(Project.PhotoType.LIVE_IMAGE)) qbList.push(liveSilhouetteQb.getQuery());
    if (photoTypes.includes(Project.PhotoType.UPLOAD_IMAGE)) qbList.push(projectUpdateQb.getQuery());
    if (photoTypes.includes(Project.PhotoType.CAMERA_IMAGE)) qbList.push(cameraEventQb.getQuery());
    if (_.isEmpty(qbList)) return [];

    const sql = qbList.join(' UNION ALL ');
    const qb = this.dataSource.createQueryBuilder()
      .select('*')
      .from(`(${sql})`, 'tt')
      .orderBy('tt.publishDate', 'DESC')
      .addOrderBy('tt.albumType', 'ASC')
      .addOrderBy('tt.publishTime', 'DESC')
      .addOrderBy('tt.mediaId', 'DESC');
    if (pageNo && pageSize) qb.offset((pageNo - 1) * pageSize).limit(pageSize);
    if (photoTypes.includes(Project.PhotoType.LIVE_IMAGE)) qb.setParameters(liveSilhouetteQb.getParameters());
    if (photoTypes.includes(Project.PhotoType.UPLOAD_IMAGE)) qb.setParameters(projectUpdateQb.getParameters());
    if (photoTypes.includes(Project.PhotoType.CAMERA_IMAGE)) qb.setParameters(cameraEventQb.getParameters());
    return await qb.getRawMany<{ albumType: string, type: string, publishDate: string, publishTime: string, mediaId: string, mediaType: string, mediaUri: string, mediaResourceUri: string, eventType: string }>();
  }

  async getProjectCustomKeyPosition({ companyId, projectIdList, roleIdList }: { companyId: string, projectIdList: string[], roleIdList: string[] }) {
    if (!companyId || _.isEmpty(projectIdList) || _.isEmpty(roleIdList)) return [];
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'm.manager_id managerId',
        'm.nick_name name',
        'p.project_id projectId'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectMemberEntity, 'pm', 'pm.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.company_id = p.COMPANY_ID and m.PERSON_ID = pm.PERSON_ID')
      .where('pm.DELETE_FLAG = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('m.DELETE_FLAG = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('p.company_id = :companyId', { companyId })
      .andWhere('p.project_id in (:...projectIdList)', { projectIdList })
      .andWhere('m.role_id in (:...roleIdList)', { roleIdList })
      .orderBy('pm.createTime', 'ASC').addOrderBy('pm.project_member_id', 'ASC')
      .having("1=1");
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'tt.projectId projectId',
        'tt.managerId managerId',
        'tt.name name'
      ])
      .from(`(${subQuery.getQuery()})`, 'tt')
      .groupBy('tt.projectId');
    qb.setParameters(subQuery.getParameters());
    return await qb.getRawMany<{ projectId: string, managerId: string, name: string }>();
  }

  async getProjectIdsByCustomKeyPosition({ companyId, projectIdList, customKeyPosition, roleIdList }: { companyId: string, projectIdList: string[], customKeyPosition: string, roleIdList: string[] }) {
    if (!companyId || _.isEmpty(projectIdList) || !customKeyPosition || _.isEmpty(roleIdList)) return [];
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'm.manager_id managerId',
        'p.project_id projectId'
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectMemberEntity, 'pm', 'pm.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.company_id = p.COMPANY_ID and m.PERSON_ID = pm.PERSON_ID')
      .where('pm.DELETE_FLAG = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('m.DELETE_FLAG = :deleteFlag', { deleteFlag: Common.Flag.N })
      .andWhere('p.company_id = :companyId', { companyId })
      .andWhere('p.project_id in (:...projectIdList)', { projectIdList })
      .andWhere('m.role_id in (:...roleIdList)', { roleIdList })
      .orderBy('pm.createTime', 'ASC').addOrderBy('pm.project_member_id', 'ASC')
      .having("1=1");
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'tt.projectId projectId',
        'tt.managerId managerId'
      ])
      .from(`(${subQuery.getQuery()})`, 'tt')
      .groupBy('tt.projectId')
      .having('tt.managerId = :customKeyPosition', { customKeyPosition })
    qb.setParameters(subQuery.getParameters());
    return await qb.getRawMany<{ projectId: string, managerId: string }>();
  }

}
