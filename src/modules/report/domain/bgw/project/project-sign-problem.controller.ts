import { Body, Controller, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { CheckFormatCsv, JwtUserGw, MyApiOkResponse, MyUser } from "@yqz/nest";
import { ProjectSignProblemService } from "./service/project-sign-problem.service";
import { ProjectSignProblem } from "./dto/project-sign-problem.dto";

/**
 * 工地看板 - 工地问题
 */
@ApiBearerAuth()
@Controller("project-sign/problem")
export class ProjectSignProblemController {
    constructor(
        private readonly projectSignProblemService: ProjectSignProblemService,
    ) { }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题统计' })
    @Post("statistics")
    @MyApiOkResponse(ProjectSignProblem.StatisticsRes)
    async projectProblemStatistics(@Body() body: ProjectSignProblem.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignProblemService.projectProblemStatistics(body);
    }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题来源分布' })
    @Post("source/group")
    @MyApiOkResponse(ProjectSignProblem.SourceGroupRes)
    async projectProblemSourceGroup(@Body() body: ProjectSignProblem.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignProblemService.projectProblemSourceGroup(body);
    }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题分类分布' })
    @Post("type/group")
    @MyApiOkResponse(ProjectSignProblem.TypeGroupRes)
    async projectProblemTypeGroup(@Body() body: ProjectSignProblem.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignProblemService.projectProblemTypeGroup(body);
    }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题状态分布' })
    @Post("status/group")
    @MyApiOkResponse(ProjectSignProblem.StatusGroupRes)
    async projectProblemStatusGroup(@Body() body: ProjectSignProblem.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignProblemService.projectProblemStatusGroup(body);
    }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题摄像头捕捉问题分布' })
    @Post("device/result/type/group")
    @MyApiOkResponse(ProjectSignProblem.DeviceResultTypeGroupRes)
    async projectProblemDeviceResultTypeGroup(@Body() body: ProjectSignProblem.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignProblemService.projectProblemDeviceResultTypeGroup(body);
    }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题列表' })
    @Post("list")
    @CheckFormatCsv(ProjectSignProblem.ListCsv)
    async projectProblemList(@Body() body: ProjectSignProblem.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignProblemService.projectProblemList(body);
    }

    @ApiTags("工地看板 - 工地问题")
    @ApiOperation({ summary: '工地看板问题列表筛选项' })
    @Post("list/options")
    async projectProblemListOptions() {
        return await this.projectSignProblemService.projectProblemListOptions();
    }

}