import { Body, Controller, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { CheckFormatCsv, JwtUserGw, MyApiOkResponse, MyUser } from "@yqz/nest";
import { ProjectSignNodeService } from "./service/project-sign-node.service";
import { ProjectSignNode } from "./dto/project-sign-node.dto";

/**
 * 工地看板 - 工地节点
 */
@ApiBearerAuth()
@Controller("project-sign/node")
export class ProjectSignNodeController {
    constructor(
        private readonly projectSignNodeService: ProjectSignNodeService,
    ) { }

    @ApiTags("工地看板 - 工地节点")
    @ApiOperation({ summary: '工地看板节点完成/未完成分布' })
    @Post("finish/group")
    @MyApiOkResponse(ProjectSignNode.FinishGroupRes)
    async projectNodeFinishGroup(@Body() body: ProjectSignNode.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignNodeService.projectNodeFinishGroup(body);
    }

    @ApiTags("工地看板 - 工地节点")
    @ApiOperation({ summary: '工地看板已完成节点逾期/未逾期分布' })
    @Post("finish/overdue/group")
    @MyApiOkResponse(ProjectSignNode.OverdueGroupRes)
    async projectNodeFinishOverdueGroup(@Body() body: ProjectSignNode.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignNodeService.projectNodeFinishOverdueGroup(body);
    }

    @ApiTags("工地看板 - 工地节点")
    @ApiOperation({ summary: '工地看板待完成节点逾期/未逾期分布' })
    @Post("unfinish/overdue/group")
    @MyApiOkResponse(ProjectSignNode.OverdueGroupRes)
    async projectNodeUnFinishOverdueGroup(@Body() body: ProjectSignNode.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignNodeService.projectNodeUnFinishOverdueGroup(body);
    }

    @ApiTags("工地看板 - 工地节点")
    @ApiOperation({ summary: '工地看板节点列表' })
    @Post("list")
    @CheckFormatCsv(ProjectSignNode.ListCsv)
    async projectNodeList(@Body() body: ProjectSignNode.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignNodeService.projectNodeList(body);
    }

    @ApiTags("工地看板 - 工地节点")
    @ApiOperation({ summary: '工地看板节点列表筛选项' })
    @Post("list/options")
    async projectNodeListOptions(@Body() body: ProjectSignNode.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId;
        return await this.projectSignNodeService.projectNodeListOptions(body);
    }

}