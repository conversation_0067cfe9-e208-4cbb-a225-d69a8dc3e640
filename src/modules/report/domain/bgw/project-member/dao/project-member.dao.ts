import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { DevTimeout } from "@yqz/nest";
import { ProjectMember } from "../dto/project.dto";
import { ProjectMemberEntity } from "@src/modules/report/entity/bgw/project-member.entity";
import { MemberRoleEntity } from "@src/modules/report/entity/bgw/member-role.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";

@Injectable()
export class ProjectMemberDao {
    private readonly logger = new MyLogger(ProjectMemberDao.name)
    constructor(
        private dataSource: DataSource
    ) { }

    @DevTimeout(500)
    async test() {
        // let res = await this.searchProject({companyId:"412",personId:"209373"});
        // console.log(res.length);
    }

    async searchProject(params: ProjectMember.Search): Promise<any[]> {
        const qb = this.dataSource.getRepository(ProjectMemberEntity)
            .createQueryBuilder('pm')
            .select([
                'pm.PROJECT_ID projectId'
            ])
            .leftJoin(MemberRoleEntity, 'mr', 'pm.PROJECT_MEMBER_ID = mr.PROJECT_MEMBER_ID')
            .leftJoin(ProjectEntity, 'pro', 'pm.PROJECT_ID = pro.PROJECT_ID')
            .leftJoin(ManagerEntity, 'm', 'pm.PERSON_ID = m.PERSON_ID and m.company_id = pro.company_id')
            .where('pm.delete_flag  = :delFlag', { delFlag: 'N' })
            .andWhere('mr.DELETE_FLAG = :delFlag', { delFlag: 'N' })
            .andWhere('mr.ROLE_ID != 3')
            .andWhere('pro.company_id = :companyId', { companyId: params.companyId })
            .andWhere('m.manager_id = :managerId', { managerId: params.managerId })
            .groupBy('pm.project_id');
        let result = await qb.getRawMany();
        return result;
    }

}