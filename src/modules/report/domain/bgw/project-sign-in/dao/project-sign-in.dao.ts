import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import * as _ from "lodash";
import { ProjectSignIn } from "../dto/project-sign-in.dto";
import { ProjectSignInEntity } from "@src/modules/report/entity/bgw/project-sign-in.entity";
import { DaoUtil, DevTimeout } from "@yqz/nest";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CorpReportSignIn } from "../../../corp/corp-report/dto/sign-in.dto";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { ProjectNoFacesignEventEntity } from "@src/modules/report/entity/camera/project-no-facesign-event.entity";

@Injectable()
export class ProjectSignInDao {
    private readonly logger = new MyLogger(ProjectSignInDao.name)
    constructor(
        private dataSource: DataSource
    ) { }


    async searchProjectSignInNum(params: ProjectSignIn.Search) {
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(DISTINCT(ps.manager_id)) AS personNum',
                'COUNT(DISTINCT(ps.project_id)) AS projectNum',
            ])
            .from(ProjectSignInEntity, 'ps')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = ps.project_id')
            .where('ps.delete_flag = "N" ')
            .andWhere('p.delete_flag = "N"')
            .andWhere('p.company_id = :companyId', { companyId: params.companyId })
        if (!_.isEmpty(params.projectIds))
            qb.andWhere('ps.project_id in (:...projectIds)', { projectIds: params.projectIds });
        qb.andWhere('ps.SIGN_IN_TIME between :startTime and :endTime', { startTime: params.startTime, endTime: params.endTime });
        return await qb.getRawOne();
    }

    async searchSignIn(params: { projectIds: string[], startTime?: string, endTime?: string }) {
        DaoUtil.checkEmptyParams(params, ProjectSignInDao.name + '.search', 'FailIfAllEmpty');
        const { projectIds, startTime, endTime } = params || {};
        const qb = this.dataSource
          .createQueryBuilder()
          .select()
          .from(ProjectSignInEntity, 'psi')
          .where('psi.delete_flag = "N" AND psi.type = "2" ')
          .andWhere('psi.project_id in (:...projectIds)', { projectIds })
        if (startTime) qb.andWhere('psi.sign_in_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('psi.sign_in_time < :endTime', { endTime });
        qb.groupBy("DATE_FORMAT( psi.SIGN_IN_TIME, '%Y%m%d' ),psi.PROJECT_ID,psi.manager_id ")
        const res = await DaoUtil.getPageRes<ProjectSignInEntity>(qb);
        res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
        return res;
    }

    async searchSignInProjectNum(params: { projectIds: string[], startTime?: string, endTime?: string }) {
        DaoUtil.checkEmptyParams(params, ProjectSignInDao.name + '.search', 'FailIfAllEmpty');
        const { projectIds, startTime, endTime } = params || {};
        const qb = this.dataSource
          .createQueryBuilder()
          .select([
            'project_id projectId',
            'count(project_id) num'
          ])
          .from(ProjectSignInEntity, 'psi')
          .where('psi.delete_flag = "N"  AND psi.type = "2" ')
          .andWhere('psi.project_id in (:...projectIds)', { projectIds })
        if (startTime) qb.andWhere('psi.sign_in_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('psi.sign_in_time < :endTime', { endTime });
        qb.groupBy('project_id')
        const res = await qb.getRawMany()
        return res;
    }

    async searchBiSignInProjectList(params: CorpReportSignIn.SignInProjectListReq){
        const m = this.dataSource.createQueryBuilder()
            .select([
                'project_id',
                'count(project_sign_in_id) signInNum',
                'MANAGER_ID'
            ])
            .from(subQuery => {
                return subQuery.select([
                    'project_id',
                    'project_sign_in_id',
                    'MANAGER_ID'
                ])
                .from(ProjectSignInEntity,'')
                .where('delete_flag = "N" and type="2"')
                .andWhere('project_sign_in_id in (:...signInIds)', { signInIds: params.signInIds })
                .groupBy("DATE_FORMAT( SIGN_IN_TIME, '%Y%m%d' ),PROJECT_ID,manager_id ")
            },'m')
            .groupBy('m.project_id,m.MANAGER_ID')
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'tt.project_id projectId',
                'sum(tt.signInNum) signInNum',
                'count(tt.MANAGER_ID) signInPersonNum',
                'p.COMPANY_ID linkCompanyId',
                'c.company_name linkCompanyName',
                'p.project_director projectManagerId'
            ])
            .from("("+m.getQuery()+")","tt")
            .leftJoin(ProjectEntity,'p','p.PROJECT_ID = tt.project_id')
            .leftJoin(CompanyEntity,'c','c.company_id = p.company_id')
            .leftJoin(CommunityEntity,'com','com.COMMUNITY_ID = p.COMMUNITY_ID')
        qb.where(' c.DELETE_FLAG = "N" ')    
        if(params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId",{projectManagerId: params.projectManagerId})
        if(params.linkCompanyId) qb.andWhere("p.company_id = :companyId",{companyId: params.linkCompanyId})
        if (!_.isEmpty(params.projectIdList)) qb.andWhere("p.project_id in (:...projectIdList)", { projectIdList: params.projectIdList });
        qb.groupBy('tt.project_id')
        if(params.signInTotalSort || +params.signInTotalSort) qb.orderBy('signInNum',params.signInTotalSort)
        else if(params.signInPersonSort || +params.signInPersonSort) qb.orderBy('signInPersonNum',params.signInPersonSort)
        else qb.orderBy('c.createTime','ASC').addOrderBy('p.createTime','DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        qb.setParameters(m.getParameters())
        const list = await qb.getRawMany()
        return list
    }

    async searchBiSignInProjectCount(params: CorpReportSignIn.SignInProjectListReq){
        const m = this.dataSource.createQueryBuilder()
            .select([
                'project_id',
                'count(project_sign_in_id) signInNum',
                'MANAGER_ID'
            ])
            .from(subQuery => {
                return subQuery.select([
                    'project_id',
                    'project_sign_in_id',
                    'MANAGER_ID'
                ])
                .from(ProjectSignInEntity,'')
                .where('delete_flag = "N" and type="2"')
                .andWhere('project_sign_in_id in (:...signInIds)', { signInIds: params.signInIds })
                .groupBy("DATE_FORMAT( SIGN_IN_TIME, '%Y%m%d' ),PROJECT_ID,manager_id ")
            },'m')
            .groupBy('m.project_id,m.MANAGER_ID')
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'tt.project_id projectId'
            ])
            .from("("+m.getQuery()+")","tt")
            .leftJoin(ProjectEntity,'p','p.PROJECT_ID = tt.project_id')
            .leftJoin(CompanyEntity,'c','c.company_id = p.company_id')
            .leftJoin(CommunityEntity,'com','com.COMMUNITY_ID = p.COMMUNITY_ID')
        qb.where(' c.DELETE_FLAG = "N" ')     
        if(params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId",{projectManagerId: params.projectManagerId})
        if(params.linkCompanyId) qb.andWhere("p.company_id = :companyId",{companyId: params.linkCompanyId})
        if (!_.isEmpty(params.projectIdList)) qb.andWhere("p.project_id in (:...projectIdList)", { projectIdList: params.projectIdList });
        qb.groupBy('tt.project_id')
        qb.setParameters(m.getParameters())
        const q = this.dataSource.createQueryBuilder().select('count(t.projectId) num').from("("+qb.getQuery()+")",'t')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()
        return count.num
    }

    async searchBiSignInPersonList(params: CorpReportSignIn.SignInPersonListReq){
        const m = this.dataSource.createQueryBuilder()
            .select([
                'MANAGER_ID',
                'count(MANAGER_ID) signInNum',
                'project_id'
            ])
            .from(subQuery => {
                return subQuery.select([
                    'MANAGER_ID',
                    'project_id'
                ])
                .from(ProjectSignInEntity,'')
                .where('delete_flag = "N" and type="2"')
                .andWhere('project_sign_in_id in (:...signInIds)', { signInIds: params.signInIds })
                .groupBy("DATE_FORMAT( SIGN_IN_TIME, '%Y%m%d' ),PROJECT_ID,manager_id ")
            },'m')
            .groupBy('MANAGER_ID,project_id')
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'tt.MANAGER_ID managerId',
                'sum(tt.signInNum) signInNum',
                'count(tt.MANAGER_ID) signInProjectNum',
                'm.COMPANY_ID linkCompanyId',
                'm.NICK_NAME nickName',
                'c.company_name linkCompanyName'
            ])
            .from(ProjectEntity,'p')
            .leftJoin("("+m.getQuery()+")","tt","tt.project_id = p.project_id")
            .leftJoin(ManagerEntity,'m','m.manager_id = tt.MANAGER_ID')
            .leftJoin(CompanyEntity,'c','c.COMPANY_ID = m.company_id')
        qb.where(' m.delete_flag = "N" and c.delete_flag = "N" ')
        if(params.managerId) qb.andWhere("tt.MANAGER_ID = :managerId",{managerId: params.managerId})
        if(params.linkCompanyId) qb.andWhere("c.company_id = :companyId",{companyId: params.linkCompanyId})
        qb.groupBy('tt.MANAGER_ID')
        if(params.signInTotalSort || +params.signInTotalSort) qb.orderBy('signInNum',params.signInTotalSort)
        else if(params.signInProjectSort || +params.signInProjectSort) qb.orderBy('signInProjectNum',params.signInProjectSort)
        else qb.orderBy('c.createTime','ASC').addOrderBy('m.createTime','DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        qb.setParameters(m.getParameters())
        const list = await qb.getRawMany()
        return list
    }

    async searchBiSignInPersonCount(params: CorpReportSignIn.SignInPersonListReq){
        const m = this.dataSource.createQueryBuilder()
            .select([
                'MANAGER_ID',
                'count(MANAGER_ID) signInNum',
                'project_id'
            ])
            .from(subQuery => {
                return subQuery.select([
                    'MANAGER_ID',
                    'project_id'
                ])
                .from(ProjectSignInEntity,'')
                .where('delete_flag = "N" and type="2"')
                .andWhere('project_sign_in_id in (:...signInIds)', { signInIds: params.signInIds })
                .groupBy("DATE_FORMAT( SIGN_IN_TIME, '%Y%m%d' ),PROJECT_ID,manager_id ")
            },'m')
            .groupBy('MANAGER_ID,project_id')
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'tt.MANAGER_ID managerId'
            ])
            .from(ProjectEntity,'p')
            .leftJoin("("+m.getQuery()+")","tt","tt.project_id = p.project_id")
            .leftJoin(ManagerEntity,'m','m.manager_id = tt.MANAGER_ID')
            .leftJoin(CompanyEntity,'c','c.COMPANY_ID = m.company_id')
        qb.where(' m.delete_flag = "N" and c.delete_flag = "N" ')
        if(params.managerId) qb.andWhere("tt.MANAGER_ID = :managerId",{managerId: params.managerId})
        if(params.linkCompanyId) qb.andWhere("c.company_id = :companyId",{companyId: params.linkCompanyId})
        qb.groupBy('tt.MANAGER_ID')
        qb.setParameters(m.getParameters())
        const q = this.dataSource.createQueryBuilder().select('count(t.managerId) num').from("("+qb.getQuery()+")",'t')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()
        return count.num
    }

    async searchBiSignInTotalList(params: CorpReportSignIn.SignInTotalListReq){
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'psi.project_id projectId',
                'date_format(psi.sign_in_time,"%Y-%m-%d %H:%i:%s") signInTime',
                'psi.sign_in_oss_url signInPhoto',
                'p.COMPANY_ID linkCompanyId',
                'psi.manager_id managerId',
                'm.NICK_NAME nickName',
                'per.front_face_photo faceOssUrl',
                'c.COMPANY_NAME linkCompanyName',
                'p.project_director projectManagerId'
            ])
            .from(ProjectSignInEntity,'psi')
            .leftJoin(ProjectEntity,'p','psi.project_id = p.PROJECT_ID')
            .leftJoin(ManagerEntity,'m','m.manager_id = psi.manager_id')
            .leftJoin(PersonEntity,'per','per.person_id = m.person_id')
            .leftJoin(CompanyEntity,'c','c.company_id = p.company_id')
            .leftJoin(CommunityEntity,'com','com.COMMUNITY_ID = p.COMMUNITY_ID')
            .where('psi.delete_flag = "N"   and per.DELETE_FLAG = "N"')
            .andWhere('c.DELETE_FLAG = "N" and com.DELETE_FLAG = "N"')
            .andWhere(' m.DELETE_FLAG = "N" and psi.type="2"')
            .andWhere('psi.project_sign_in_id in (:...signInIds)', { signInIds: params.signInIds })
        if(params.managerId) qb.andWhere("psi.manager_id = :managerId",{managerId: params.managerId})
        if(params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId",{projectManagerId: params.projectManagerId})
        if(params.linkCompanyId) qb.andWhere("p.company_id = :companyId",{companyId: params.linkCompanyId})
        if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList })
        qb.groupBy("DATE_FORMAT( psi.SIGN_IN_TIME, '%Y%m%d' ),psi.PROJECT_ID,psi.manager_id ")
        if(params.signInTimeSort || +params.signInTimeSort) qb.orderBy('psi.sign_in_time',params.signInTimeSort)
        else qb.orderBy('c.createTime','ASC').addOrderBy('psi.sign_in_time','DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        const list = await qb.getRawMany()
        return list
    }

    async searchBiSignInTotalListCount(params: CorpReportSignIn.SignInTotalListReq){
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'psi.project_sign_in_id signInId'
            ])
            .from(ProjectSignInEntity,'psi')
            .leftJoin(ProjectEntity,'p','psi.project_id = p.PROJECT_ID')
            .leftJoin(ManagerEntity,'m','m.manager_id = psi.manager_id')
            .leftJoin(PersonEntity,'per','per.person_id = m.person_id')
            .leftJoin(CompanyEntity,'c','c.company_id = p.company_id')
            .leftJoin(CommunityEntity,'com','com.COMMUNITY_ID = p.COMMUNITY_ID')
            .where('psi.delete_flag = "N"  and per.DELETE_FLAG = "N"')
            .andWhere('c.DELETE_FLAG = "N" and com.DELETE_FLAG = "N" ')
            .andWhere(' m.DELETE_FLAG = "N" and psi.type="2"')
            .andWhere('psi.project_sign_in_id in (:...signInIds)', { signInIds: params.signInIds })
        if(params.managerId) qb.andWhere("psi.manager_id = :managerId",{managerId: params.managerId})
        if(params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId",{projectManagerId: params.projectManagerId})
        if(params.linkCompanyId) qb.andWhere("p.company_id = :companyId",{companyId: params.linkCompanyId})
        if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList })
        qb.groupBy("DATE_FORMAT( psi.SIGN_IN_TIME, '%Y%m%d' ),psi.PROJECT_ID,psi.manager_id ")
        if(params.signInTimeSort || +params.signInTimeSort) qb.orderBy('psi.sign_in_time',params.signInTimeSort)
        else qb.orderBy('c.createTime','ASC').addOrderBy('psi.sign_in_time','DESC')
        const q = this.dataSource.createQueryBuilder().select('count(tt.signInId) num').from("("+qb.getQuery()+")",'tt')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()    
        return count.num
    }

    async searchBiSignInNoBodyProjectList(params: CorpReportSignIn.SignInNoBodyProjectListReq){
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'nf.company_id linkCompanyId',
                'nf.project_id projectId',
                'SUM(IFNULL(nf.duration_in_day,TIMESTAMPDIFF(day,nf.from_date, NOW()))) signInNoBodyProjectNum',
                'c.company_name linkCompanyName',
                'p.project_director projectManagerId'
            ])
            .from(ProjectNoFacesignEventEntity,'nf')
            .leftJoin(CompanyEntity,'c','c.company_id = nf.company_id')
            .leftJoin(ProjectEntity,'p','p.project_id = nf.project_id')
            .leftJoin(CommunityEntity,'com','com.COMMUNITY_ID = p.COMMUNITY_ID')
            .where('nf.DELETE_FLAG = "N" ')
            .andWhere(' c.DELETE_FLAG = "N" and com.DELETE_FLAG = "N" ')
            .andWhere('nf.project_id in (:...projectIds)', { projectIds: params.projectIds })
        if(params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId",{projectManagerId: params.projectManagerId})
        if(params.linkCompanyId) qb.andWhere("nf.company_id = :companyId",{companyId: params.linkCompanyId})
        qb.groupBy('nf.company_id,nf.project_id')
        if(params.signInNoBodySort || +params.signInNoBodySort) qb.orderBy('signInNoBodyProjectNum',params.signInNoBodySort)
        else qb.orderBy('c.createTime','ASC').addOrderBy('p.createTime','DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        const list = await qb.getRawMany()
        return list
    }

    async searchBiSignInNoBodyProjectCount(params: CorpReportSignIn.SignInNoBodyProjectListReq){
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'nf.project_id projectId'
            ])
            .from(ProjectNoFacesignEventEntity,'nf')
            .leftJoin(CompanyEntity,'c','c.company_id = nf.company_id')
            .leftJoin(ProjectEntity,'p','p.project_id = nf.project_id')
            .leftJoin(CommunityEntity,'com','com.COMMUNITY_ID = p.COMMUNITY_ID')
            .where('nf.DELETE_FLAG = "N" ')
            .andWhere(' c.DELETE_FLAG = "N" and com.DELETE_FLAG = "N" ')
            .andWhere('nf.project_id in (:...projectIds)', { projectIds: params.projectIds })
        if(params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId",{projectManagerId: params.projectManagerId})
        if(params.linkCompanyId) qb.andWhere("nf.company_id = :companyId",{companyId: params.linkCompanyId})
        qb.groupBy('nf.company_id,nf.project_id')
        const q = this.dataSource.createQueryBuilder().select('count(tt.projectId) num').from("("+qb.getQuery()+")",'tt')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()    
        return count.num
    }

}