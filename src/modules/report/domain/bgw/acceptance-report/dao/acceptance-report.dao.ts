import { Injectable } from "@nestjs/common";
import { AcceptanceReportEntity } from "@src/modules/report/entity/bgw/acceptance-report.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DeviceProjectInfoEntity } from "@src/modules/report/entity/camera/device-project-info.entity";
import { DevTimeout, MyLogger, Project } from "@yqz/nest";
import * as _ from "lodash";
import { Brackets, DataSource } from "typeorm";
import { AcceptanceReportDataReport } from "../../../corp/data-report/service/basic/dto/acceptance-report-data-report.dto";
import { AcceptanceReportCriteriaEntity } from "@src/modules/report/entity/bgw/acceptance-report-criteria.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { AcceptanceReportLogEntity } from "@src/modules/report/entity/bgw/acceptance-report-log.entity";
import { OwnerAcceptanceEntity } from "@src/modules/report/entity/bgw/owner-acceptance.entity";
import { AcceptanceApprovalRecordEntity } from "@src/modules/report/entity/bgw/acceptance-approval-record.entity";
import { AcceptanceReportDaoDto } from "../dto/acceptance-report.dao.dto";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { DaoUtil } from "@src/util/dao.util"
import { AcceptanceCriteriaEntity } from "@src/modules/report/entity/bgw/acceptance-criteria.entity";

@Injectable()
export class AcceptanceReportDao {
    private readonly logger = new MyLogger(AcceptanceReportDao.name);
    constructor(
        private readonly dataSource: DataSource
    ) { }

    @DevTimeout(500)
    private test() {
        // this.search({departmentId:'13'}).then(res=>{
        //   this.logger.log(res)
        // })
    }

    async countProjectAcceptance(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return { count: '0' };
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(DISTINCT p.project_id) count',
            ])
            .from(AcceptanceReportEntity, 'ar')
            .innerJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        return await qb.getRawOne<{ count: string }>();
    }

    async countAcceptanceGroupDepartment(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'count(1) as count',
                'p.department_id as departmentId',
                'dep.department_name as departmentName'
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .leftJoin(DepartmentEntity, 'dep', 'p.department_id = dep.department_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('p.department_id');
        return await qb.getRawMany<{ count: string, departmentId: string, departmentName: string }>();
    }

    async countAcceptance(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return { count: '0' };
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'count(1) as count'
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        return await qb.getRawOne<{ count: string }>();
    }

    async countAcceptanceGroupDeviceDepartment(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        // 子查询：获取每个 project_id 对应的最新记录
        const subQuery = this.dataSource.createQueryBuilder()
            .select([
                'dpi.project_id AS project_id',
                'dpi.department_id AS department_id'
            ])
            .from(DeviceProjectInfoEntity, 'dpi')
            .innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id')
            .where('dpi.delete_flag = "N"')
            .andWhere('ar.id in (:...ids)', { ids: params.idList });
        subQuery.andWhere(qb => {
            const subQuery = qb.subQuery()
                .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                .from(DeviceProjectInfoEntity, 'dpi2')
                .innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id')
                .where('dpi2.project_id = dpi.project_id')
                .andWhere('dpi2.delete_flag = "N"')
                .andWhere('ar.id in (:...ids)', { ids: params.idList });
            return `dpi.project_binding_time = (${subQuery.getQuery()})`;
        });
        subQuery.groupBy('dpi.project_id');
        // 主查询：关联子查询并完成统计
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(1) as count',
                'dpd.department_id as deviceDepartmentId',
                'dep.department_name as deviceDepartmentName'
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .leftJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            .leftJoin(DepartmentEntity, 'dep', 'dpd.department_id = dep.department_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('dpd.department_id');
        qb.setParameters(subQuery.getParameters());
        return await qb.getRawMany<{ count: string, deviceDepartmentId: string, deviceDepartmentName: string }>();
    }

    async countAcceptanceGroupReportStatus(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'count(1) as count',
                `case 
                    when status = 3 and approval_status = 'approved' then '${AcceptanceReportDataReport.Status.Pending}' 
                    when status = 3 and approval_status = 'rejected' then '${AcceptanceReportDataReport.Status.Rejected}'
                    when status = 3 and approval_status = 'in_approval' then '${AcceptanceReportDataReport.Status.InApproval}'
                    when status = 2 and approval_status = 'approved' then '${AcceptanceReportDataReport.Status.Refuse}'
                    when status = 1 and approval_status = 'approved' then '${AcceptanceReportDataReport.Status.Agree}'
                    when status = 2 then '${AcceptanceReportDataReport.Status.Refuse}'
                    when status = 1 then '${AcceptanceReportDataReport.Status.Agree}'
                    when status = 3 then '${AcceptanceReportDataReport.Status.Pending}'
                end as reportStatus`,
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('reportStatus');
        return await qb.getRawMany<{ count: string, reportStatus: AcceptanceReportDataReport.Status }>();
    }

    async countAcceptanceGroupReportCriteria(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'count(1) as count',
                'ac.id as acceptanceCriteriaId',
                'ac.name as acceptanceCriteriaName'
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .leftJoin(AcceptanceCriteriaEntity, 'ac', 'ac.id = ar.acceptance_criteria_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('ar.acceptance_criteria_id');
        return await qb.getRawMany<{ count: string, acceptanceCriteriaId: string, acceptanceCriteriaName: string }>();
    }

    async countAcceptanceGroupCompany(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'count(1) as count',
                'p.company_id as companyId',
                'c.company_name as companyName'
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('p.company_id');
        return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
    }

    async countAcceptanceGroupProjectManager(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'count(1) as count',
                `p.project_manager as projectManagerId`,
                `m.nick_name as projectManagerName`,
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('p.project_manager');
        return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
    }

    async searchList(params: AcceptanceReportDaoDto.Search) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return { total: 0, items: [] };
        //查询
        const qb = this.dataSource.createQueryBuilder()
            .select([
                `ar.id as id`,
                `ar.company_id as linkCompanyId`,
                'ar.project_id as projectId',
                `ar.acceptance_report_name as acceptanceReportName`,
                `case 
                when ar.status = 3 and ar.approval_status = 'approved' then '${AcceptanceReportDataReport.Status.Pending}' 
                when ar.status = 3 and ar.approval_status = 'rejected' then '${AcceptanceReportDataReport.Status.Rejected}'
                when ar.status = 3 and ar.approval_status = 'in_approval' then '${AcceptanceReportDataReport.Status.InApproval}'
                when ar.status = 2 and ar.approval_status = 'approved' then '${AcceptanceReportDataReport.Status.Refuse}'
                when ar.status = 1 and ar.approval_status = 'approved' then '${AcceptanceReportDataReport.Status.Agree}'
                when ar.status = 2 then '${AcceptanceReportDataReport.Status.Refuse}'
                when ar.status = 1 then '${AcceptanceReportDataReport.Status.Agree}'
                when ar.status = 3 then '${AcceptanceReportDataReport.Status.Pending}'
                end as acceptanceStatus`,
                `ar.inspectors as inspectors`,
                `date_format(ar.acceptance_date, '%Y-%m-%d') as acceptanceDate`,
            ])
            .from(AcceptanceReportEntity, 'ar')
            .leftJoin(ProjectEntity, 'p', 'ar.project_id = p.project_id')
            .where('ar.id in (:...ids)', { ids: params.idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('ar.company_id = :companyId', { companyId: params.linkCompanyId });
        //工地负责人
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //验收节点
        if (params.acceptanceCriteriaId) qb.andWhere('ar.acceptance_criteria_id = :acceptanceCriteriaId', { acceptanceCriteriaId: params.acceptanceCriteriaId });
        //验收状态
        if (params.acceptanceStatus) {
            switch (params.acceptanceStatus) {
                case AcceptanceReportDataReport.Status.Pending:
                    qb.andWhere('ar.status = 3 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Rejected:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "rejected"');
                    break;
                case AcceptanceReportDataReport.Status.InApproval:
                    qb.andWhere('ar.status = 3 and ar.approval_status = "in_approval"');
                    break;
                case AcceptanceReportDataReport.Status.Refuse:
                    qb.andWhere('ar.status = 2 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
                case AcceptanceReportDataReport.Status.Agree:
                    qb.andWhere('ar.status = 1 and (ar.approval_status = "approved" or ar.approval_status = "" or ar.approval_status is null)');
                    break;
            }
        }
        //工地地址
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder().select(['dpi.project_id AS project_id', 'dpi.department_id AS department_id'])
                .from(DeviceProjectInfoEntity, 'dpi').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi.project_id').where('dpi.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList })
                .andWhere(qb => {
                    const subQuery = qb.subQuery().select('MAX(dpi2.project_binding_time)', 'maxBindingTime').from(DeviceProjectInfoEntity, 'dpi2').innerJoin(AcceptanceReportEntity, 'ar', 'ar.project_id = dpi2.project_id').where('dpi2.project_id = dpi.project_id').andWhere('dpi2.delete_flag = "N"').andWhere('ar.id in (:...ids)', { ids: params.idList });
                    return `dpi.project_binding_time = (${subQuery.getQuery()})`;
                })
                .groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        //审核拒绝次数
        if (!_.isEmpty(params.rejectedCount)) {
            const rejectedCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id', 'count(1) as rejectedCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(AcceptanceApprovalRecordEntity, 'acceptanceApprovalRecord', 'acceptanceApprovalRecord.id = acceptanceReportLog.acceptance_approval_record_id and acceptanceApprovalRecord.approval_result = "rejected"')
                .where('acceptanceReportLog.operate_type = 11')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id')
            qb.leftJoin(`(${rejectedCountSql.getQuery()})`, 'rejectedCountSql', 'rejectedCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.rejectedCount.forEach(item => {
                    if (AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]) mqb.orWhere(`rejectedCountSql.rejectedCount ${AcceptanceReportDataReport.RejectedCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RejectedCount.None) mqb.orWhere(`rejectedCountSql.rejectedCount is null`);
                });
            }));
            qb.setParameters(rejectedCountSql.getParameters());
        }
        //业主拒绝次数
        if (!_.isEmpty(params.refuseCount)) {
            const refuseCountSql = this.dataSource.createQueryBuilder()
                .select(['acceptanceReportLog.acceptance_report_id as acceptance_report_id, count(1) as refuseCount'])
                .from(AcceptanceReportLogEntity, 'acceptanceReportLog')
                .innerJoin(OwnerAcceptanceEntity, 'ownerAcceptance', 'ownerAcceptance.id = acceptanceReportLog.owner_acceptance_id and ownerAcceptance.acceptance_result = 0')//业主拒绝
                .where('acceptanceReportLog.operate_type = 5')
                .andWhere('acceptanceReportLog.delete_flag = "N"')
                .andWhere('acceptanceReportLog.acceptance_report_id in (:...ids)', { ids: params.idList })
                .groupBy('acceptanceReportLog.acceptance_report_id');
            qb.leftJoin(`(${refuseCountSql.getQuery()})`, 'refuseCountSql', 'refuseCountSql.acceptance_report_id = ar.id');
            qb.andWhere(new Brackets(mqb => {
                params.refuseCount.forEach(item => {
                    if (AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]) mqb.orWhere(`refuseCountSql.refuseCount ${AcceptanceReportDataReport.RefuseCountSqlExpressMap[item]}`);
                    if (item === AcceptanceReportDataReport.RefuseCount.None) mqb.orWhere(`refuseCountSql.refuseCount is null`);
                });
            }));
            qb.setParameters(refuseCountSql.getParameters());
        }
        qb.groupBy('ar.id');
        qb.orderBy('ar.id', 'DESC');
        DaoUtil.processPageAndSort(qb, params)
        return await DaoUtil.getPageRes<{ id: string, linkCompanyId: string, projectId: string, acceptanceReportName: string, acceptanceStatus: AcceptanceReportDataReport.Status, inspectors: string, acceptanceDate: string, rejectedCount: number, rejectedReason: string, refuseCount: number, refuseReason: string }>(qb);
    }

    async searchRejectedInfoByIds(idList: string[]) {
        //idList为空直接返回
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select(['arl.acceptance_report_id as id', 'count(1) as rejectedCount', 'group_concat(aar.text SEPARATOR ";") as rejectedReason'])
            .from(AcceptanceReportLogEntity, 'arl')
            .innerJoin(AcceptanceApprovalRecordEntity, 'aar', 'aar.id = arl.acceptance_approval_record_id and aar.approval_result = "rejected"')
            .where('arl.operate_type = 11')
            .andWhere('arl.delete_flag = "N"')
            .andWhere('arl.acceptance_report_id in (:...ids)', { ids: idList })
            .groupBy('arl.acceptance_report_id');
        return await qb.getRawMany<{ id: string, rejectedCount: string, rejectedReason: string }>();
    }

    async searchRefuseInfoByIds(idList: string[]) {
        //idList为空直接返回
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select(['arl.acceptance_report_id as id', 'count(1) as refuseCount', 'group_concat(oa.refuse_text SEPARATOR ";") as refuseReason'])
            .from(AcceptanceReportLogEntity, 'arl')
            .innerJoin(OwnerAcceptanceEntity, 'oa', 'oa.id = arl.owner_acceptance_id and oa.acceptance_result = 0')//业主拒绝
            .where('arl.operate_type = 5')
            .andWhere('arl.delete_flag = "N"')
            .andWhere('arl.acceptance_report_id in (:...ids)', { ids: idList })
            .groupBy('arl.acceptance_report_id');
        return await qb.getRawMany<{ id: string, refuseCount: string, refuseReason: string }>();
    }

}