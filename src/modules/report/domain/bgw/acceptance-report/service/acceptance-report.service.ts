import { Injectable } from '@nestjs/common';
import { DevTimeout } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { AcceptanceReportDao } from '../dao/acceptance-report.dao';

@Injectable()
export class AcceptanceReportService {
    private readonly logger = new MyLogger(AcceptanceReportService.name)

    constructor(
        private acceptanceReportDao: AcceptanceReportDao,
    ) { }

    // @DevTimeout(1000)
    async test() {
        // const res = await this.searchCompanyInfoMap({ companyIds: ['65'] });
        // console.log("🚀 ~ CompanyService ~ test ~ res:", res)
    }

    async searchRejectedInfoMap({ idList }: { idList: string[] }) {
        if (_.isEmpty(idList)) return {};
        const rejectedInfo = await this.acceptanceReportDao.searchRejectedInfoByIds(idList);
        const result = {};
        rejectedInfo.map(res => {
            result[res.id] = { id: res.id, rejectedCount: res.rejectedCount, rejectedReason: res.rejectedReason };
        })
        return result;
    }

    async searchRefuseInfoMap({ idList }: { idList: string[] }) {
        if (_.isEmpty(idList)) return {};
        const refuseInfo = await this.acceptanceReportDao.searchRefuseInfoByIds(idList);
        const result = {};
        refuseInfo.map(res => {
            result[res.id] = { id: res.id, refuseCount: res.refuseCount, refuseReason: res.refuseReason };
        })
        return result;
    }

}
