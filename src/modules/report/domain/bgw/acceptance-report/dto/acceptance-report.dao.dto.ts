import { AcceptanceReportDataReport } from "../../../corp/data-report/service/basic/dto/acceptance-report-data-report.dto";

export namespace AcceptanceReportDaoDto {
    export class Search {
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;//工地负责人
        acceptanceCriteriaId?: string;//验收节点
        acceptanceStatus?: string;//报告状态
        projectAddress?: string;//工地地址
        rejectedCount?: AcceptanceReportDataReport.RejectedCount[];//审核不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        refuseCount?: AcceptanceReportDataReport.RefuseCount[];//业主不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        pageNo?: number;
        pageSize?: number;
    }
}