import { Injectable } from "@nestjs/common";
import { OrderManageEntity } from "@src/modules/report/entity/bgw/order-manage.entity";
import { DaoUtil } from "@src/util/dao.util";
import { Flag } from "@yqz/nest/lib/types/common.type";
import * as _ from "lodash";
import { DataSource, In } from "typeorm";

@Injectable()
export class OrderManageDao {

    constructor(
        private readonly dataSource: DataSource
    ) { }

    async searchList({ idList }: { idList: string[] }) {
        const qb = await this.dataSource.manager.find(OrderManageEntity, {
            where: {
                orderManageId: In(idList)
            }
        })
        return qb
    }

    async searchNoFissionNewOrderCount(params: { companyId: string, departmentIds: string[], from: string, to: string }): Promise<{ totalNoFissionNewOrder: string }> {
        const { companyId, departmentIds, from, to } = params
        const qb = this.dataSource.getRepository(OrderManageEntity)
            .createQueryBuilder('om')
            .select([
                "count( DISTINCT order_manage_id) totalNoFissionNewOrder"
            ])
            .where('company_id = :companyId ', { companyId })
            .andWhere("DELETE_FLAG = :del", { del: Flag.N })
        // .andWhere("(SALES_PERSON_ID is null or SALES_PERSON_ID = '')")
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            qb.andWhere("department_id in (:...departmentIds)", { departmentIds })
        }
        if (from && to) {
            qb.andWhere("order_time between :from and :to", { from, to })
        }
        return await qb.getRawOne()
    }


    async searchNoFissionNewOrderV2Count(params: { companyId: string, departmentIds: string[], from: string, to: string }): Promise<{ totalNoFissionNewOrder: string }> {
        const { companyId, departmentIds, from, to } = params
        const paramList = []
        let sql1 = `SELECT
        om.COMPANY_ID companyId,
        om.ORDER_MANAGE_ID orderManageId,
        om.order_time orderTime,
        dm.department_id departmentId 
    FROM
    ${DaoUtil.getDbName("bgw.order_manage")} om
    LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.company_id = om.company_id 
        AND om.SALES_PERSON_ID = m.person_id
    LEFT JOIN ${DaoUtil.getDbName("bgw.department_member")} dm ON dm.manager_id = m.manager_id
    INNER JOIN (
        SELECT
			department_member_id deptId,
			max( dm.createTime ) createTime,
			m.MANAGER_ID managerId 
		FROM
        ${DaoUtil.getDbName("bgw.department_member")} dm
			LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
		WHERE
			m.DELETE_FLAG = 'N' 
		GROUP BY
			m.MANAGER_ID UNION ALL
		SELECT
			department_member_id deptId,
			min( dm.createTime ) createTime,
			m.MANAGER_ID managerId 
		FROM
        ${DaoUtil.getDbName("bgw.department_member")} dm
			LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
		WHERE
			m.DELETE_FLAG = 'Y' 
		GROUP BY
			m.MANAGER_ID 
        ) t ON dm.manager_id = t.managerId 
        AND dm.createTime = t.createTime
        LEFT OUTER JOIN ${DaoUtil.getDbName("bgw.department_member")} AS dm2 ON dm.MANAGER_ID = dm2.MANAGER_ID 
        AND dm.createTime = dm2.createTime 
        AND dm.department_member_id > dm2.department_member_id 
    WHERE
        om.company_id = ?
        AND dm2.department_member_id IS NULL 
        AND om.DELETE_FLAG = 'N' 
        AND ( om.SALES_PERSON_ID IS NOT NULL OR om.SALES_PERSON_ID != '' ) `
        paramList.push(companyId)
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql1 += ` AND dm.department_id IN (?) `
            paramList.push(departmentIds)
        }
        if (from && to) {
            sql1 += ` AND om.order_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql2 = ` SELECT
        om.COMPANY_ID companyId,
        om.ORDER_MANAGE_ID orderManageId,
        om.order_time orderTime,
        p.department_id departmentId 
    FROM
    ${DaoUtil.getDbName("bgw.order_manage")} om
        LEFT JOIN ${DaoUtil.getDbName("bgw.project")} p ON p.project_id = om.TARGET
        LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = p.project_manager
        LEFT JOIN ${DaoUtil.getDbName("bgw.department_member")} dm ON dm.manager_id = m.manager_id
        INNER JOIN (
        SELECT
            department_member_id deptId,
            max( dm.createTime ) createTime,
            m.MANAGER_ID managerId 
        FROM
        ${DaoUtil.getDbName("bgw.department_member")} dm
            LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
        WHERE
            m.DELETE_FLAG = 'N' 
        GROUP BY
            m.MANAGER_ID UNION ALL
        SELECT
            department_member_id deptId,
            min( dm.createTime ) createTime,
            m.MANAGER_ID managerId 
        FROM
        ${DaoUtil.getDbName("bgw.department_member")} dm
            LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
        WHERE
            m.DELETE_FLAG = 'Y' 
        GROUP BY
            m.MANAGER_ID 
        ) t ON dm.manager_id = t.managerId 
        AND dm.createTime = t.createTime
        LEFT OUTER JOIN ${DaoUtil.getDbName("bgw.department_member")} AS dm2 ON dm.MANAGER_ID = dm2.MANAGER_ID 
        AND dm.createTime = dm2.createTime 
        AND dm.department_member_id > dm2.department_member_id 
    WHERE
        m.manager_id IS NOT NULL 
        AND dm2.department_member_id IS NULL 
        AND om.DELETE_FLAG = 'N' 
        AND om.COMPANY_ID = ?
        AND ( SALES_PERSON_ID IS NULL OR SALES_PERSON_ID = '' ) 
        AND service_name = '查看直播'`
        paramList.push(companyId)
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql2 += ` AND p.department_id IN (?) `
            paramList.push(departmentIds)
        }
        if (from && to) {
            sql2 += ` AND om.order_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql3 = ` SELECT
        om.COMPANY_ID companyId,
        om.ORDER_MANAGE_ID orderManageId,
        om.order_time orderTime,
        "" departmentId 
    FROM
    ${DaoUtil.getDbName("bgw.order_manage")} om
    WHERE
        om.DELETE_FLAG = 'N' 
        AND om.COMPANY_ID = ?
        AND ( SALES_PERSON_ID IS NULL OR SALES_PERSON_ID = '' ) 
        AND service_name != '查看直播' `
        paramList.push(companyId)
        if (from && to) {
            sql3 += ` AND om.order_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql = ` SELECT count( tt.orderManageId ) totalNoFissionNewOrder FROM ( ` + sql1 + `  UNION ALL ` + sql2 + ` UNION ALL ` + sql3 + ` ) tt `
        sql += ` WHERE tt.companyId = ?   `
        paramList.push(companyId)
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql += ` AND tt.departmentId IN (?) `
            paramList.push(departmentIds)
        }
        if (from && to) {
            sql += ` AND tt.orderTime BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }
        const res = await this.dataSource.query(sql, paramList)
        return res
    }
}