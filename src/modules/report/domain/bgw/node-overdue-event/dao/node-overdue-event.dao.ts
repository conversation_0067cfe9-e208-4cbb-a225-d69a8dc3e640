import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { ProjectNodeEntity } from "@src/modules/report/entity/bgw/project-node.entity";
import { ProjectScheduleModifiedEntity } from "@src/modules/report/entity/bgw/project-schedule-modified.entity";

@Injectable()
export class NodeOverdueEventDao {
    private readonly logger = new MyLogger(NodeOverdueEventDao.name)
    constructor(
        private dataSource: DataSource
    ) { }


    // @DevTimeout(500)
    async test() {
        // this.searchDeviceList({yqzDeviceIds:[
        // ]}).then(res => {
        //   this.logger.log(res)
        // })
        // const res1 = await this.searchOverdueProjectNodeListByOverdueTimeV2({ projectIds: ['344293'], startTime: '2023-01-01', endTime: '2025-11-15', delayOverdue: 'Y', overdueTime: '1' });
        // console.log("🚀 ~ NodeOverdueEventDao ~ test ~ res:", res1)
        // const res2 = await this.searchOverdueProjectListByOverdueTime({ projectIds: ['344293'], startTime: '2023-01-01', endTime: '2025-11-15', overdueTime: '1' });
        // console.log("🚀 ~ NodeOverdueEventDao ~ test ~ res:", res2.length)
        // console.log(_.difference(res1, res2))
        // console.log(_.difference(res2, res1))
    }

    // /**
    //  * 查询时间范围内逾期工地（工地所有节点）
    //  * @param params 
    //  * @returns 
    //  */
    // async searchOverdueProjectListV2({ projectIds, startTime, endTime, delayOverdue = 'Y' }: { projectIds: string[], startTime: string, endTime: string, delayOverdue: 'Y' | 'N' }) {
    //     if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
    //     const subqb1 = this.dataSource.createQueryBuilder()
    //         .select([
    //             'psm.project_node_id as project_node_id',
    //             'min(psm.id) as id'
    //         ])
    //         .from(ProjectScheduleModifiedEntity, 'psm')
    //         .innerJoin(ProjectNodeEntity, 'pn', 'psm.project_node_id = pn.project_node_id')
    //         .where('pn.project_id in (:...projectIds)', { projectIds: projectIds })
    //         .andWhere('psm.delete_flag = "N"')
    //         .groupBy('psm.project_node_id');

    //     const subqb2 = this.dataSource.createQueryBuilder()
    //         .select([
    //             'pn.project_node_id as project_node_id',
    //             'p.project_id as project_id',
    //             `DATE(IFNULL(pn.finish_time, now())) as finish_time`,
    //             //delayOverdue 为 Y 拿的是调整之前的时间， 为N拿的是调整之后的时间
    //             delayOverdue === 'N' ? 'DATE(pn.deadline_time) as deadline_time' : `DATE(IFNULL(psm2.from_end_date, pn.deadline_time)) as deadline_time`
    //         ])
    //         .from(ProjectEntity, 'p')
    //         .innerJoin(ProjectNodeEntity, 'pn', 'p.project_id = pn.project_id and pn.delete_flag = "N"')
    //         .leftJoin(`(${subqb1.getQuery()})`, 'psm', `pn.project_node_id = psm.project_node_id`)
    //         .leftJoin(ProjectScheduleModifiedEntity, 'psm2', 'psm.id = psm2.id')
    //         .where('p.project_id in (:...projectIds)', { projectIds: projectIds })
    //         .andWhere("p.bind_project_node = 'Y'");
    //     if (delayOverdue === 'N') {
    //         subqb2.andWhere('pn.deadline_time >= :startTime', { startTime: startTime })
    //             .andWhere('pn.deadline_time < :endTime', { endTime: endTime })
    //     }
    //     if (delayOverdue === 'Y') {
    //         subqb2.having(`deadline_time >= :startTime`, { startTime: startTime })
    //             .andHaving(`deadline_time < :endTime`, { endTime: endTime });
    //     }
    //     subqb2.setParameters(subqb1.getParameters());
    //     const qb = this.dataSource.createQueryBuilder()
    //         .select([
    //             'n.project_id as projectId',
    //             'MAX(DATEDIFF(n.finish_time, n.deadline_time)) AS overdueTime'//最大逾期天数
    //         ])
    //         .from(`(${subqb2.getQuery()})`, 'n')
    //         .groupBy('n.project_id')
    //         .setParameters(subqb2.getParameters());
    //     return await qb.getRawMany<{ projectId: string, overdueTime: number }>();
    // }

    // /**
    //  * 查询时间范围内逾期工地（工地当前节点）
    //  * @param param0 
    //  * @returns 
    //  */
    // async searchOverdueProjectIdListByOverdueTimeV2({ projectIds, startTime, endTime, delayOverdue = 'Y', overdueTime }: { projectIds: string[], startTime: string, endTime: string, delayOverdue: 'Y' | 'N', overdueTime: string }) {
    //     if (_.isEmpty(projectIds) || !startTime || !endTime || !overdueTime) return [];
    //     const subqb1 = this.dataSource.createQueryBuilder()
    //         .select([
    //             'psm.project_node_id as project_node_id',
    //             'min(psm.id) as id'
    //         ])
    //         .from(ProjectScheduleModifiedEntity, 'psm')
    //         .innerJoin(ProjectNodeEntity, 'pn', 'psm.project_node_id = pn.project_node_id')
    //         .where('pn.project_id in (:...projectIds)', { projectIds: projectIds })
    //         .andWhere('psm.delete_flag = "N"')
    //         .groupBy('psm.project_node_id');
    //     const subqb2 = this.dataSource.createQueryBuilder()
    //         .select([
    //             'pn.project_node_id as project_node_id',
    //             'p.project_id as project_id',
    //             `DATE(IFNULL(pn.finish_time, now())) as finish_time`,
    //             //delayOverdue 为 Y 拿的是调整之前的时间， 为N拿的是调整之后的时间
    //             delayOverdue === 'N' ? 'DATE(pn.deadline_time) as deadline_time' : `DATE(IFNULL(psm2.from_end_date, pn.deadline_time)) as deadline_time`
    //         ])
    //         .from(ProjectEntity, 'p')
    //         .innerJoin(ProjectNodeEntity, 'pn', 'p.current_project_node = pn.project_node_id and pn.delete_flag = "N"')
    //         .leftJoin(`(${subqb1.getQuery()})`, 'psm', `pn.project_node_id = psm.project_node_id`)
    //         .leftJoin(ProjectScheduleModifiedEntity, 'psm2', 'psm.id = psm2.id')
    //         .where('p.project_id in (:...projectIds)', { projectIds: projectIds })
    //         .andWhere("p.bind_project_node = 'Y'");
    //     if (delayOverdue === 'N') {
    //         subqb2.andWhere('pn.deadline_time >= :startTime', { startTime: startTime })
    //             .andWhere('pn.deadline_time < :endTime', { endTime: endTime });
    //     }
    //     if (delayOverdue === 'Y') {
    //         subqb2.having(`deadline_time >= :startTime`, { startTime: startTime })
    //             .andHaving(`deadline_time < :endTime`, { endTime: endTime });
    //     }
    //     subqb2.setParameters(subqb1.getParameters());
    //     const qb = this.dataSource.createQueryBuilder()
    //         .select([
    //             'n.project_id as projectId',
    //         ])
    //         .from(`(${subqb2.getQuery()})`, 'n')
    //         .where('DATEDIFF(n.finish_time, n.deadline_time) >= :overdueTime', { overdueTime: overdueTime })
    //         .groupBy('n.project_id')
    //         .setParameters(subqb2.getParameters());
    //     const res = await qb.getRawMany<{ projectId: string }>();
    //     return res.map(item => item.projectId);
    // }

    /**
     * 查询时间范围内逾期节点
     * @param param0 
     * @returns 
     */
    async searchOverdueProjectNodeIdListByOverdueTimeV2({ projectIds, startTime, endTime, delayOverdue = 'Y', overdueTime }: { projectIds: string[], startTime: string, endTime: string, delayOverdue: 'Y' | 'N', overdueTime: string }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime || !overdueTime) return [];
        const subqb1 = this.dataSource.createQueryBuilder()
            .select([
                'psm.project_node_id as project_node_id',
                'min(psm.id) as id'
            ])
            .from(ProjectScheduleModifiedEntity, 'psm')
            .innerJoin(ProjectNodeEntity, 'pn', 'psm.project_node_id = pn.project_node_id')
            .where('pn.project_id in (:...projectIds)', { projectIds: projectIds })
            .andWhere('psm.delete_flag = "N"')
            .groupBy('psm.project_node_id')

        const subqb2 = this.dataSource.createQueryBuilder()
            .select([
                'pn.project_node_id as project_node_id',
                'p.project_id as project_id',
                `DATE(IFNULL(pn.finish_time, now())) as finish_time`,
                //delayOverdue 为 Y 拿的是调整之前的时间， 为N拿的是调整之后的时间
                delayOverdue === 'N' ? 'DATE(pn.deadline_time) as deadline_time' : `DATE(pn.origin_deadline_time) as deadline_time`
            ])
            .from(ProjectNodeEntity, 'pn')
            .innerJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
            .leftJoin(`(${subqb1.getQuery()})`, 'psm', `pn.project_node_id = psm.project_node_id`)
            .where('p.project_id in (:...projectIds)', { projectIds: projectIds })
            .andWhere('p.delete_flag = "N"')
            .andWhere('pn.delete_flag = :deleteFlag', { deleteFlag: 'N' })
            .andWhere("p.bind_project_node = 'Y'");
        if (delayOverdue === 'N') {
            subqb2.andWhere('pn.deadline_time >= :startTime', { startTime: startTime })
                .andWhere('pn.deadline_time < :endTime', { endTime: endTime });
        }
        if (delayOverdue === 'Y') {
            subqb2.andWhere('pn.origin_deadline_time >= :startTime', { startTime: startTime })
                .andWhere('pn.origin_deadline_time < :endTime', { endTime: endTime });
        }
        subqb2.setParameters(subqb1.getParameters());
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'n.project_node_id as projectNodeId',
                'n.project_id as projectId'
            ])
            .from(`(${subqb2.getQuery()})`, 'n')
            .where('DATEDIFF(n.finish_time, n.deadline_time) >= :overdueTime', { overdueTime: overdueTime })
            .setParameters(subqb2.getParameters());
        return await qb.getRawMany<{ projectNodeId: string, projectId: string }>();
    }

    async searchProjectOverdueInfo({ projectIds, startTime, endTime, delayOverdue = 'Y', overdueTime }: { projectIds: string[], startTime: string, endTime: string, delayOverdue: 'Y' | 'N', overdueTime: string }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime || !overdueTime) return [];
        const subqb1 = this.dataSource.createQueryBuilder()
            .select([
                'psm.project_node_id as project_node_id',
                'min(psm.id) as id'
            ])
            .from(ProjectScheduleModifiedEntity, 'psm')
            .innerJoin(ProjectNodeEntity, 'pn', 'psm.project_node_id = pn.project_node_id')
            .where('pn.project_id in (:...projectIds)', { projectIds: projectIds })
            .andWhere('psm.delete_flag = "N"')
            .groupBy('psm.project_node_id')

        const subqb2 = this.dataSource.createQueryBuilder()
            .select([
                'pn.project_node_id as project_node_id',
                'p.project_id as project_id',
                `DATE(IFNULL(pn.finish_time, now())) as finish_time`,
                //delayOverdue 为 Y 拿的是调整之前的时间， 为N拿的是调整之后的时间
                delayOverdue === 'N' ? 'DATE(pn.deadline_time) as deadline_time' : `DATE(pn.origin_deadline_time) as deadline_time`
            ])
            .from(ProjectNodeEntity, 'pn')
            .innerJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
            .leftJoin(`(${subqb1.getQuery()})`, 'psm', `pn.project_node_id = psm.project_node_id`)
            .where('p.project_id in (:...projectIds)', { projectIds: projectIds })
            .andWhere('pn.delete_flag = :deleteFlag', { deleteFlag: 'N' })
            .andWhere("p.bind_project_node = 'Y'");
        if (delayOverdue === 'N') {
            subqb2.andWhere('pn.deadline_time >= :startTime', { startTime: startTime })
                .andWhere('pn.deadline_time < :endTime', { endTime: endTime });
        }
        if (delayOverdue === 'Y') {
            subqb2.andWhere('pn.origin_deadline_time >= :startTime', { startTime: startTime })
                .andWhere('pn.origin_deadline_time < :endTime', { endTime: endTime });
        }
        subqb2.setParameters(subqb1.getParameters());
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'n.project_id as projectId',
                'MAX(DATEDIFF(n.finish_time, n.deadline_time)) as overdueTime'
            ])
            .from(`(${subqb2.getQuery()})`, 'n')
            .where('DATEDIFF(n.finish_time, n.deadline_time) >= :overdueTime', { overdueTime: overdueTime })
            .groupBy('n.project_id')
            .setParameters(subqb2.getParameters());
        return await qb.getRawMany<{ projectId: string, overdueTime: string }>();
    }

    /**
     * 查询时间范围内逾期节点信息
     * @param param0 
     * @returns 
     */
    async searchNodeOverdueInfo({ projectNodeIds, startTime, endTime, delayOverdue = 'Y', overdueTime }: { projectNodeIds: string[], startTime: string, endTime: string, delayOverdue: 'Y' | 'N', overdueTime: string }) {
        if (_.isEmpty(projectNodeIds) || !startTime || !endTime || !overdueTime) return [];
        const subqb1 = this.dataSource.createQueryBuilder()
            .select([
                'psm.project_node_id as project_node_id',
                'min(psm.id) as id'
            ])
            .from(ProjectScheduleModifiedEntity, 'psm')
            .where('psm.project_node_id in (:...projectNodeIds)', { projectNodeIds: projectNodeIds })
            .andWhere('psm.delete_flag = "N"')
            .groupBy('psm.project_node_id')

        const subqb2 = this.dataSource.createQueryBuilder()
            .select([
                'pn.project_node_id as project_node_id',
                'p.project_id as project_id',
                `DATE(IFNULL(pn.finish_time, now())) as finish_time`,
                //delayOverdue 为 Y 拿的是调整之前的时间， 为N拿的是调整之后的时间
                delayOverdue === 'N' ? 'DATE(pn.deadline_time) as deadline_time' : `DATE(pn.origin_deadline_time) as deadline_time`
            ])
            .from(ProjectNodeEntity, 'pn')
            .innerJoin(ProjectEntity, 'p', 'p.project_id = pn.project_id')
            .leftJoin(`(${subqb1.getQuery()})`, 'psm', `pn.project_node_id = psm.project_node_id`)
            .where('pn.project_node_id in (:...projectNodeIds)', { projectNodeIds: projectNodeIds })
            .andWhere('pn.delete_flag = :deleteFlag', { deleteFlag: 'N' })
            .andWhere("p.bind_project_node = 'Y'");
        if (delayOverdue === 'N') {
            subqb2.andWhere('pn.deadline_time >= :startTime', { startTime: startTime })
                .andWhere('pn.deadline_time < :endTime', { endTime: endTime });
        }
        if (delayOverdue === 'Y') {
            subqb2.andWhere('pn.origin_deadline_time >= :startTime', { startTime: startTime })
                .andWhere('pn.origin_deadline_time < :endTime', { endTime: endTime });
        }
        subqb2.setParameters(subqb1.getParameters());
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'n.project_node_id as projectNodeId',
                'DATEDIFF(n.finish_time, n.deadline_time) as overdueTime'
            ])
            .from(`(${subqb2.getQuery()})`, 'n')
            .where('DATEDIFF(n.finish_time, n.deadline_time) >= :overdueTime', { overdueTime: overdueTime })
            .groupBy('n.project_node_id')
            .setParameters(subqb2.getParameters());
        return await qb.getRawMany<{ projectNodeId: string, overdueTime: string }>();
    }

    // /**
    //  * 查询时间范围内逾期工地
    //  * @param params 
    //  * @returns 
    //  */
    // async searchOverdueProjectList(params: { projectIds: string[], startTime: string, endTime: string }) {
    //     if (_.isEmpty(params.projectIds) || !params.startTime || !params.endTime) return [];
    //     const qb = this.dataSource.createQueryBuilder()
    //         .select([
    //             'p.project_id as projectId',
    //             'SUM( ' +
    //             'CASE ' +
    //             "WHEN noe.from_date < '" + params.endTime + "' AND noe.to_date is NULL THEN DATEDIFF('" + params.endTime + "',noe.from_date) " +
    //             "WHEN noe.to_date >= '" + params.startTime + "' AND noe.to_date < '" + params.endTime + "'  THEN noe.duration_in_day " +
    //             "WHEN noe.from_date < '" + params.endTime + "' AND noe.to_date >= '" + params.endTime + "' THEN DATEDIFF('" + params.endTime + "',noe.from_date) " +
    //             "END) AS overdueTime"
    //         ])
    //         .from(ProjectEntity, 'p')
    //         .leftJoin(ProjectNodeEntity, 'pn', 'p.project_id = pn.project_id and p.current_project_node = pn.project_node_id and pn.delete_flag = "N"')
    //         .leftJoin(NodeOverdueEventEntity, 'noe', 'noe.project_node_id = pn.project_node_id and noe.delete_flag = "N"')
    //         .where('p.project_id in (:...projectIds)', { projectIds: params.projectIds })
    //         // qb.andWhere("((noe.to_date >= :startTime and noe.to_date < :endTime) or (noe.to_date is null and noe.from_date < :endTime))", { startTime: params.startTime, endTime: params.endTime });
    //         .groupBy('p.project_id')
    //     return await qb.getRawMany<{ projectId: string, overdueTime: number }>();
    // }

    // async searchOverdueProjectListByOverdueTime(params: { projectIds: string[], startTime: string, endTime: string, overdueTime: string }) {
    //     if (_.isEmpty(params.projectIds) || !params.startTime || !params.endTime) return [];
    //     const qb = this.dataSource.createQueryBuilder()
    //         .select([
    //             'p.project_id as projectId',
    //             'SUM( ' +
    //             'CASE ' +
    //             "WHEN noe.from_date < '" + params.endTime + "' AND noe.to_date is NULL THEN DATEDIFF('" + params.endTime + "',noe.from_date) " +
    //             "WHEN noe.to_date >= '" + params.startTime + "' AND noe.to_date < '" + params.endTime + "'  THEN noe.duration_in_day " +
    //             "WHEN noe.from_date < '" + params.endTime + "' AND noe.to_date >= '" + params.endTime + "' THEN DATEDIFF('" + params.endTime + "',noe.from_date) " +
    //             "END) AS overdueTime"
    //         ])
    //         .from(ProjectEntity, 'p')
    //         .leftJoin(ProjectNodeEntity, 'pn', 'p.project_id = pn.project_id and p.current_project_node = pn.project_node_id and pn.delete_flag = "N"')
    //         .leftJoin(NodeOverdueEventEntity, 'noe', 'noe.project_node_id = pn.project_node_id and noe.delete_flag = "N"')
    //         .where('p.project_id in (:...projectIds)', { projectIds: params.projectIds })
    //         // qb.andWhere("((noe.to_date >= :startTime and noe.to_date < :endTime) or (noe.to_date is null and noe.from_date < :endTime))", { startTime: params.startTime, endTime: params.endTime });
    //         .groupBy('p.project_id')
    //         .having('overdueTime >= :overdueTime', { overdueTime: params.overdueTime })
    //     const res = await qb.getRawMany();
    //     return res.map(item => item.projectId);
    // }

}