import { PropertyCsv } from "@yqz/nest/lib/nestjs/utils/types/util.type";

export namespace Marketing {

  export class Analytics {
    format?: 'csv';
    companyId?: string;
    personId?: string;
    departmentId?: string;
    roleId?: string;
    managerId?: string;
    operateManagerId?: string;
    departmentIds?: string[];
    managerIds?: string[];
    actionType?: string;
    orderInterest?: string
    from?: string;
    to?: string;
    pageNo?: number;
    pageSize?: number;
  }

  export class AnalyticsTotal {
    totalPosition: string;//岗位数
    totalManager: string;//员工数
    totalShareDegree: string;//分享次数
    totalNewCustomer: string;//新客户数
    totalNewOrder: string;//新预约数
  }

  export class AnalyticsPosition {
    roleId: string;//岗位id
    roleName: string;//岗位名
    totalManager: string;//员工数
    totalShareDegree: string;//分享次数
    totalNewCustomer: string;//新客户数
    totalNewOrder: string;//新预约数
    credits: string;//营销积分
  }

  export class AnalyticsPerson {
    managerId: string;//员工id
    nickName: string;//员工昵称
    totalShareDegree: string;//分享次数
    totalNewCustomer: string;//新客户数
    totalNewOrder: string;//新预约数
    credits: string;//营销积分
  }

  export class AnalyticsDataDetail {
    departmentId: string;//部门id
    departmentName: string;//部门名
    roleId: string;//岗位id
    roleName: string;//岗位名
    managerId: string;//员工id
    nickName: string;//员工昵称
    actionType: string;//操作行为 created、deleted、newUser、newReservation、shared
    targetType: string;//操作对象 
    targetId?: string;
    id?: string;
    orderManageId?: string;
    actionContent: string;//操作具体内容
    type?: string;//预约来源
    actionTime: string;//操作时间
    credits: number;//获得积分
  }

  export const ActionType = {
    newUser: {value: "newUser",name:"触达新客户"},
    shared: {value: "shared",name:"分享内容"},
    newReservation: {value: "newReservation",name:"获得新预约"},
  }

  export const csvPosition: PropertyCsv[] = [
    {
      key: "roleName",
      value: "岗位名称"
    }, {
      key: "totalManager",
      value: "员工数"
    }, {
      key: "totalShareDegree",
      value: "分享次数"
    }, {
      key: "totalNewCustomer",
      value: "裂变人数"
    }, {
      key: "totalNewOrder",
      value: "新预约数"
    }, {
      key: "credits",
      value: "营销积分"
    }
  ]

  export const csvPerson: PropertyCsv[] = [
    {
      key: "nickName",
      value: "员工昵称"
    }, {
      key: "totalShareDegree",
      value: "分享次数"
    }, {
      key: "totalNewCustomer",
      value: "裂变人数"
    }, {
      key: "totalNewOrder",
      value: "新预约数"
    }, {
      key: "credits",
      value: "营销积分"
    }
  ]

  export const csvList: PropertyCsv[] = [
    {
      key: "departmentName",
      value: "部门"
    }, {
      key: "roleName",
      value: "岗位"
    }, {
      key: "nickName",
      value: "员工昵称"
    }, {
      key: "targetType",
      value: "操作行为"
    }, {
      key: "actionContent",
      value: "操作内容"
    }, {
      key: "actionTime",
      value: "操作时间"
    }, {
      key: "credits",
      value: "获得积分"
    }
  ]

}