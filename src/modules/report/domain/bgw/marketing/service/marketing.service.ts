import { Injectable } from "@nestjs/common";
import { BingoHomeApi, AgileApi, CsvUtil, DevTimeout, YqzException } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { OrderManageDao } from "../../order/dao/order-manage.dao";
import { SalesCustomerDao } from "../../sales-customer/dao/sales-customer.dao";
import { MarketingDao } from "../dao/marketing.dao";
import { Marketing } from "../types/marketing.type";
import { DepartmentDao } from "../../department/dao/department.dao";
import { RoleDao } from "../../role/dao/role.dao";
import { ManagerCreditDao } from "../dao/manager-credit.dao";
import { CommonParameterDao } from "../../common/common-parameter.dao";

@Injectable()
export class MarketingService {
    private readonly logger = new MyLogger(MarketingService.name);

    constructor(
        private readonly marketingDao: MarketingDao,
        private readonly salesCustomerDao: SalesCustomerDao,
        private readonly orderManageDao: OrderManageDao,
        private readonly departmentDao: DepartmentDao,
        private readonly roleDao: RoleDao,
        private readonly managerCreditDao: ManagerCreditDao,
        private readonly commonParameterDao: CommonParameterDao,
    ) { }

    // @DevTimeout(500)
    test() {
    }

    /**
     * 营销数据报表统计
     * @param params 
     */
    async marketingAnalyticsTotal(params: Marketing.Analytics): Promise<Marketing.AnalyticsTotal> {
        const { companyId, operateManagerId } = params;
        if (!companyId || !operateManagerId) throw new YqzException("必传参数不能为空");
        //筛选部门
        const body = await AgileApi.bodyParse(params)
        if (!body?.departmentIds || _.isEmpty(body.departmentIds)) {
            return {
                totalPosition: "0",
                totalManager: "0",
                totalShareDegree: "0",
                totalNewCustomer: "0",
                totalNewOrder: "0",
            }
        }
        //查询公司根部门
        const { items: [root] } = await this.departmentDao.searchDept({ companyId, level: 0 })
        //如果为根部门，则查询全部数据
        if(root?.departmentId && body.departmentIds.includes(root.departmentId)){
            body.departmentIds.push("*")
        }
        //查询数据
        const result: Marketing.AnalyticsTotal = await this.marketingDao.marketingAnalyticsTotal(body)
        //查询客户预约数
        const noFissionNewOrder = await this.orderManageDao.searchNoFissionNewOrderV2Count({...body});
        const totalNoFissionNewOrder = noFissionNewOrder[0]?.totalNoFissionNewOrder ? Number(noFissionNewOrder[0]?.totalNoFissionNewOrder) : 0;
        result.totalNewOrder = String(totalNoFissionNewOrder);
        return result;
    }

    /**
     * 营销数据报表岗位分布
     * @param params 
     * @returns 
     */
    async marketingAnalyticsPosition(params: Marketing.Analytics) {
        const { companyId, operateManagerId } = params;
        if (!companyId || !operateManagerId) throw new YqzException("必传参数不能为空");
        //筛选部门
        const body = await AgileApi.bodyParse(params)
        if (!body?.departmentIds || _.isEmpty(body.departmentIds)) {
            return { total: 0, items: [] }
        }
        // const result = await this.marketingDao.marketingAnalyticsPosition(body)
        const result = await this.marketingDao.marketingAnalyticsV2Position(body)
        //返回csv格式
        if ('csv' == params.format) {
            const csv = CsvUtil.mapObjectPropertiesToChinese(result, Marketing.csvPosition);
            return CsvUtil.convert2Csv(csv);
        }
        return { items: result };
    }

    /**
     * 营销数据报表员工分布
     * @param params 
     * @returns 
     */
    async marketingAnalyticsPerson(params: Marketing.Analytics) {
        const { companyId, operateManagerId } = params;
        if (!companyId || !operateManagerId) throw new YqzException("必传参数不能为空");
        //筛选部门
        const body = await AgileApi.bodyParse(params)
        if (!body?.departmentIds || _.isEmpty(body.departmentIds)) {
            return { total: 0, items: [] }
        }
        // const result = await this.marketingDao.marketingAnalyticsPerson(body)
        const result = await this.marketingDao.marketingAnalyticsV2Person(body)
        //返回csv格式
        if ('csv' == params.format) {
            const csv = CsvUtil.mapObjectPropertiesToChinese(result, Marketing.csvPerson);
            return CsvUtil.convert2Csv(csv);
        }
        return { items: result };
    }

    /**
     * 营销数据报表数据明细
     * @param params 
     * @returns 
     */
    async marketingAnalyticsDataDetail(params: Marketing.Analytics) {
        const { companyId, operateManagerId } = params;
        if (!companyId || !operateManagerId) throw new YqzException("必传参数不能为空");

        //筛选部门
        const body = await AgileApi.bodyParse(params)
        if (!body?.departmentIds || _.isEmpty(body.departmentIds)) {
            return { total: 0, items: [] }
        }
        //筛选客户兴趣
        if(params.orderInterest) body.orderInterest = params.orderInterest
        //查询公司根部门
        const { items: [root] } = await this.departmentDao.searchDept({ companyId, level: 0 })
        //如果为根部门，则查询全部数据
        if(root?.departmentId && body.departmentIds.includes(root.departmentId)){
            body.departmentIds.push("*")
        }
        const result = await this.marketingDao.marketingAnalyticsDataV2Detail(body);

        const customerIds = []
        const roleIds = []
        const deptIds = []
        const orderManageIds = []
        const types = []
        const target = result.items.map(res => {
            if(res.roleId) roleIds.push(res.roleId)
            if(res.departmentId) deptIds.push(res.departmentId)
            if (res.actionType == Marketing.ActionType.newUser.value)
                customerIds.push(res.targetId)
            if (res.actionType == Marketing.ActionType.newReservation.value && res.orderManageId)
                orderManageIds.push(res.orderManageId)
            if(res.type) types.push(res.type)
            return {
                creditId: res.id,
                targetType: res.targetType,
                targetId: res.targetId,
                shared: res.actionType === Marketing.ActionType.shared.value
            };
        });

        //操作内容
        let customerMap = {}
        if (!_.isEmpty(customerIds)) {
            const customerList = await this.salesCustomerDao.joinSearchList({ ids: customerIds })
            customerMap = _.keyBy(customerList, 'id')
        }

        //岗位
        let roleMap = {}
        if (!_.isEmpty(roleIds)) {
            const roleList = await this.roleDao.searchList({ids: roleIds})
            roleMap = _.keyBy(roleList, 'companyMemberRoleId')
        }
        //部门名
        let deptMap = {}
        if (!_.isEmpty(deptIds)) {
            const deptList = await this.departmentDao.searchDeptInfoList({ deptIds });//(不校验删除)
            deptMap = _.keyBy(deptList, 'departmentId')
        }

        //积分查询
        let creditMap = {}
        if (!_.isEmpty(orderManageIds)) {
            const creditList = await this.managerCreditDao.searchOrderCreditList(companyId,Marketing.ActionType.newReservation.value,orderManageIds)
            creditMap = _.keyBy(creditList, 'targetId')
        }

        //客户来源查询
        let typeMap = {}
        if (!_.isEmpty(types)) {
            const targetType = await this.commonParameterDao.search('TARGET_TYPE')
            typeMap = _.keyBy(targetType, 'parameterCode')
        }

        //操作内容
        let targetMap = {}
        if(!_.isEmpty(target)) {
            targetMap = await BingoHomeApi.getTargetContent(target);
        }

        result.items.map(item => {
            item.targetType = Marketing.ActionType[item.actionType]?.name
            if (item.actionType == Marketing.ActionType.shared.value) {
                if(targetMap[item.id]) {
                    const target = targetMap[item.id].match(/\[(.*?)\](.*)/)
                    item.actionContent = `分享${target[1]}: ${target[2]}`
                }
            }

            if (item.actionType == Marketing.ActionType.newUser.value) {
                item.actionContent = customerMap[item.targetId] && customerMap[item.targetId]?.nickName ? "昵称: " + customerMap[item.targetId]?.nickName : ""
            }
            if (item.actionType == Marketing.ActionType.newReservation.value) {
                const type = typeMap[item.type]?.parameterName || ""
                if(type && item?.actionContent) {
                    item.actionContent = "客户来源：" + type + "/" + item.actionContent
                }
                if(!type && item?.actionContent) {
                    item.actionContent = "客户来源：" + item.actionContent
                }
                if(type && !item?.actionContent) {
                    item.actionContent = "客户来源：" + type
                }
                item.credits = creditMap[item?.orderManageId]?creditMap[item?.orderManageId]?.credit:item.credits
            }
            item.roleName = roleMap[item.roleId] && roleMap[item.roleId]?.roleName ? roleMap[item.roleId]?.roleName : ""
            item.departmentName = deptMap[item.departmentId] && deptMap[item.departmentId]?.departmentName ? deptMap[item.departmentId]?.departmentName : ""
            delete item.id
            delete item.targetId
        })
        //返回csv格式
        if ('csv' == params.format) {
            const csv = CsvUtil.mapObjectPropertiesToChinese(result.items, Marketing.csvList);
            return CsvUtil.convert2Csv(csv);
        }
        return result;
    }

    async marketingAnalyticsOptions(companyId: string) {
        if (!companyId) throw new YqzException("必传参数不能为空");
        const managerList = await this.marketingDao.marketingAnalyticsManagerList(companyId)
        const roleList = await this.marketingDao.marketingAnalyticsRoleList(companyId)
        const actionTypes = Object.values(Marketing.ActionType)
        const orderInterestList = await this.marketingDao.marketingAnalyticsOrderInterestList(companyId)
        const orderInterests = orderInterestList.filter(item => item?.targetName).map(item => {
            return {
                value: item?.targetName,
                name: item?.targetName
            }
        })
        return {
            managerList,
            roleList,
            actionTypes,
            orderInterests
        }
    }

}