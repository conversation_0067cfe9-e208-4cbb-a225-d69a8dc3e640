import { Body, Controller, Post, UseInterceptors } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { MyUser, TransformInterceptor, MyLogger, JwtUserGw, MyApiOkArrayResponse, MyApiOkResponse, MyCache } from '@yqz/nest';
import { MarketingService } from "./service/marketing.service";
import { Marketing } from "./types/marketing.type";

@ApiTags("marketing")
@Controller("marketing")
@ApiBearerAuth()
// @UseInterceptors(TransformInterceptor)
export class MarketingController {
    private readonly logger = new MyLogger(MarketingController.name);

    constructor(
        private readonly marketingService: MarketingService
    ) { }

    @ApiTags("analytics")
    @ApiOperation({ summary: '营销数据报表统计' })
    @MyApiOkResponse(Marketing.AnalyticsTotal)
    @Post("analytics/total")
    async marketingAnalyticsTotal(@Body() body: Marketing.Analytics, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        body.operateManagerId = user?.targetCompany?.managerId || body?.operateManagerId;
        return await this.marketingService.marketingAnalyticsTotal(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '营销数据报表岗位分布' })
    @MyApiOkArrayResponse(Marketing.AnalyticsPosition)
    @Post("analytics/position")
    async marketingAnalyticsPosition(@Body() body: Marketing.Analytics, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        body.operateManagerId = user?.targetCompany?.managerId || body?.operateManagerId;
        return await this.marketingService.marketingAnalyticsPosition(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '营销数据报表员工分布' })
    @MyApiOkArrayResponse(Marketing.AnalyticsPerson)
    @Post("analytics/person")
    async marketingAnalyticsPerson(@Body() body: Marketing.Analytics, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        body.operateManagerId = user?.targetCompany?.managerId || body?.operateManagerId;
        return await this.marketingService.marketingAnalyticsPerson(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '营销数据报表数据明细' })
    @MyApiOkArrayResponse(Marketing.AnalyticsDataDetail)
    @Post("analytics/data/detail")
    async marketingAnalyticsDataDetail(@Body() body: Marketing.Analytics, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        body.operateManagerId = user?.targetCompany?.managerId || body?.operateManagerId;
        // return await this.marketingService.marketingAnalyticsDataDetail(body);
        return await this._marketingAnalyticsDataDetail(body);
    }

    // @MyCache({cacheKey:'marketing:analytics:data:detail:v1',ttl: 10 * 60,exclude: ["operateManagerId"]})
    async _marketingAnalyticsDataDetail(body: Marketing.Analytics) {
        return await this.marketingService.marketingAnalyticsDataDetail(body);
    }

    @Post("analytics/options")
    async marketingAnalyticsOptions(@MyUser() user: JwtUserGw) {
        const companyId = user?.targetCompany?.companyId
        return await this.marketingService.marketingAnalyticsOptions(companyId)
    }

}