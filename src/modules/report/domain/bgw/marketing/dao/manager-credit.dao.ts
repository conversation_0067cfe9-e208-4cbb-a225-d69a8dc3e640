import { Injectable } from "@nestjs/common";
import { ManagerCreditEntity } from "@src/modules/report/entity/credit/manager-credit.entity";
import { DeleteFlag } from "@src/types/delete-flag.enum";
import { DataSource, In } from "typeorm";

@Injectable()
export class ManagerCreditDao {

    constructor(
        private readonly dataSource: DataSource
    ) { }

    async searchOrderCreditList(companyId: string, actionType: string, orderManageIds: string[]) {
        const qb = await this.dataSource.manager.find(ManagerCreditEntity, {
            where: {
                companyId: companyId,
                actionType: actionType,
                targetId: In(orderManageIds),
                deleteFlag: DeleteFlag.N
            }
        })
        return qb
    }

}