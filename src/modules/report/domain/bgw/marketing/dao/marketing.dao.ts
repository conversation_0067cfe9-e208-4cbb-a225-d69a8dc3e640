import { Injectable } from "@nestjs/common";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { ManagerCreditEntity } from "@src/modules/report/entity/credit/manager-credit.entity";
import * as _ from "lodash";
import { DataSource } from "typeorm";
import { Marketing } from "../types/marketing.type";
import { OrderManageEntity } from "@src/modules/report/entity/bgw/order-manage.entity";
import { DaoUtil } from "@src/util/dao.util";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";

@Injectable()
export class MarketingDao {

    constructor(
        private readonly dataSource: DataSource
    ) { }

    async marketingAnalyticsTotal(params: Marketing.Analytics) {
        const { companyId, departmentIds, from, to } = params
        const qb = this.dataSource.getRepository(ManagerCreditEntity)
            .createQueryBuilder('mc')
            .select([
                "count(DISTINCT m.role_id) totalPosition",
                "count(DISTINCT mc.manager_id) totalManager",
                "ifnull(SUM(CASE WHEN mc.action_type = 'newUser' THEN 1 ELSE 0 END),0) totalNewCustomer",
                "ifnull(SUM(CASE WHEN mc.action_type = 'shared' THEN 1 ELSE 0 END),0) totalShareDegree",
                "ifnull(SUM(CASE WHEN mc.action_type = 'newReservation' THEN 1 ELSE 0 END),0) totalNewOrder"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .where('mc.company_id = :companyId ', { companyId })
            .andWhere("mc.DELETE_FLAG = 'N'  and mc.action_type in ('newUser','shared','newReservation')")
        if (!_.isEmpty(departmentIds)) {
            qb.andWhere("mc.department_id in (:...departmentIds)", { departmentIds })
        }
        if (from && to) {
            qb.andWhere("mc.action_time between :from and :to", { from, to })
        }
        return await qb.getRawOne()
    }

    async marketingAnalyticsPosition(params: Marketing.Analytics): Promise<Marketing.AnalyticsPosition[]> {
        const { companyId, departmentIds, from, to } = params
        const qb = this.dataSource.getRepository(ManagerCreditEntity)
            .createQueryBuilder('mc')
            .select([
                "m.role_id roleId",
                "mr.ROLE_NAME roleName",
                "count(DISTINCT mc.manager_id) totalManager",
                "SUM(CASE WHEN mc.action_type = 'newUser' THEN 1 ELSE 0 END) totalNewCustomer",
                "SUM(CASE WHEN mc.action_type = 'shared' THEN 1 ELSE 0 END) totalShareDegree",
                "SUM(CASE WHEN mc.action_type = 'newReservation' THEN 1 ELSE 0 END) totalNewOrder",
                "sum(mc.credit) credits"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('mc.company_id = :companyId ', { companyId })
            .andWhere("mc.DELETE_FLAG = 'N'  and mc.action_type in ('newUser','shared','newReservation')")
        if (!_.isEmpty(departmentIds)) {
            qb.andWhere("mc.department_id in (:...departmentIds)", { departmentIds })
        }
        if (from && to) {
            qb.andWhere("mc.action_time between :from and :to", { from, to })
        }
        qb.groupBy("m.role_id")
        const sort = this.dataSource.createQueryBuilder()
            .from("(" + qb.getQuery() + ")", 'tt')
            .orderBy("tt.totalShareDegree", "DESC")
            .addOrderBy("tt.totalNewCustomer", "DESC")
            .addOrderBy("tt.totalNewOrder", "DESC")
            .addOrderBy("tt.roleId", "DESC")
        sort.setParameters(qb.getParameters())
        return await sort.getRawMany()
    }

    async marketingAnalyticsV2Position(params: Marketing.Analytics): Promise<Marketing.AnalyticsPosition[]> {
        const { companyId, departmentIds, from, to } = params
        const sql1 = this.dataSource.createQueryBuilder()
            .select([
                "mc.manager_id managerId",
                "m.ROLE_ID roleId",
                "mr.ROLE_NAME roleName",
                "mc.action_type actionType",
                "mc.department_id deptId",
                "mc.credit credit"
            ])
            .from(ManagerCreditEntity, 'mc')
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('mc.company_id  = :companyId', { companyId })
            .andWhere("mc.DELETE_FLAG = 'N'  and mc.action_type in ('newUser','shared')")
        if (!_.isEmpty(departmentIds)) {
            sql1.andWhere("mc.department_id in (:...departmentIds)", { departmentIds })
        }
        if (from && to) {
            sql1.andWhere("mc.action_time between :from and :to", { from, to })
        }
        const sql2 = this.dataSource.createQueryBuilder()
            .select([
                "m.manager_id managerId",
                "m.ROLE_ID roleId",
                "mr.ROLE_NAME roleName",
                "'newReservation' actionType",
                "(select department_id from department_member where manager_id = m.MANAGER_ID order by createTime desc limit 1) deptId",
                "0 credit"])
            .from(OrderManageEntity, 'oc')
            .leftJoin(ManagerEntity, 'm', 'm.company_id = oc.company_id and m.person_id = oc.SALES_PERSON_ID')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('m.company_id = :companyId')
            .andWhere("oc.delete_flag = 'N'")
            .andWhere("oc.SALES_PERSON_ID is not null")

        if (from && to) {
            sql2.andWhere("oc.order_time between :from and :to", { from, to })
        }
        const sql3 = this.dataSource.createQueryBuilder()
            .select([
                "m.manager_id managerId",
                "m.ROLE_ID roleId",
                "mr.ROLE_NAME roleName",
                "'newReservation' actionType",
                "p.department_id deptId",
                "0 credit"])
            .from(OrderManageEntity, 'oc')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = oc.TARGET')
            .leftJoin(ManagerEntity, 'm', 'm.MANAGER_ID = p.project_manager')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('m.company_id = :companyId')
            .andWhere("oc.delete_flag = 'N'")
            .andWhere("oc.SALES_PERSON_ID is null")
            .andWhere("oc.service_name = '查看直播'")
        if (from && to) {
            sql3.andWhere("oc.order_time between :from and :to", { from, to })
        }
        const sql4 = sql1.getQuery() + ` union all ` + sql2.getQuery() + ` union all ` + sql3.getQuery()
        const qb = this.dataSource.createQueryBuilder()
            .select([
                "tt.roleId roleId",
                "tt.roleName",
                "count( DISTINCT tt.managerId ) totalManager",
                "SUM( CASE WHEN tt.actionType = 'newUser' THEN 1 ELSE 0 END ) totalNewCustomer",
                "SUM( CASE WHEN tt.actionType = 'shared' THEN 1 ELSE 0 END ) totalShareDegree",
                "SUM( CASE WHEN tt.actionType = 'newReservation' THEN 1 ELSE 0 END ) totalNewOrder",
                "sum( tt.credit ) credits"
            ])
            .from("(" + sql4 + ")", 'tt')
        if (!_.isEmpty(departmentIds)) {
            qb.where("tt.deptId in (:departmentIds)", { departmentIds })
        }
        qb.groupBy("tt.roleId")
            .having("1")
        qb.setParameter('companyId', companyId)
        if (!_.isEmpty(departmentIds)) {
            qb.setParameter('departmentIds', departmentIds)
        }
        if (from && to) {
            qb.setParameter('from', from)
            qb.setParameter('to', to)
        }
        const sort = this.dataSource.createQueryBuilder()
            .from("(" + qb.getQuery() + ")", 't')
            .orderBy("t.totalShareDegree", "DESC")
            .addOrderBy("t.totalNewCustomer", "DESC")
            .addOrderBy("t.totalNewOrder", "DESC")
            .addOrderBy("t.roleId", "DESC")
        sort.setParameters(qb.getParameters())
        return await sort.getRawMany()
    }

    async marketingAnalyticsPerson(params: Marketing.Analytics): Promise<Marketing.AnalyticsPerson[]> {
        const { companyId, roleId, from, to, departmentIds } = params
        const qb = this.dataSource.getRepository(ManagerCreditEntity)
            .createQueryBuilder('mc')
            .select([
                "mc.manager_id managerId",
                "m.nick_name nickName",
                "SUM(CASE WHEN mc.action_type = 'newUser' THEN 1 ELSE 0 END) totalNewCustomer",
                "SUM(CASE WHEN mc.action_type = 'shared' THEN 1 ELSE 0 END) totalShareDegree",
                "SUM(CASE WHEN mc.action_type = 'newReservation' THEN 1 ELSE 0 END) totalNewOrder",
                "sum(mc.credit) credits"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .where('mc.company_id = :companyId ', { companyId })
            .andWhere("mc.DELETE_FLAG = 'N'  and mc.action_type in ('newUser','shared','newReservation')")
        if (roleId) {
            qb.andWhere("m.ROLE_ID = :roleId", { roleId })
        }
        if (!_.isEmpty(departmentIds)) {
            qb.andWhere("mc.department_id in (:...departmentIds)", { departmentIds })
        }
        if (from && to) {
            qb.andWhere("mc.action_time between :from and :to", { from, to })
        }
        qb.groupBy("mc.manager_id")
        const sort = this.dataSource.createQueryBuilder()
            .from("(" + qb.getQuery() + ")", 'tt')
            .orderBy("tt.totalShareDegree", "DESC")
            .addOrderBy("tt.totalNewCustomer", "DESC")
            .addOrderBy("tt.totalNewOrder", "DESC")
            .addOrderBy("tt.managerId", "DESC")
        sort.setParameters(qb.getParameters())
        return await sort.getRawMany()
    }

    async marketingAnalyticsV2Person(params: Marketing.Analytics): Promise<Marketing.AnalyticsPosition[]> {
        const { companyId, roleId, from, to, departmentIds } = params

        const sql1 = this.dataSource.createQueryBuilder()
            .select([
                "mc.manager_id managerId",
                "m.nick_name nickName",
                "mc.action_type actionType",
                "mc.department_id deptId",
                "mc.credit credit"
            ])
            .from(ManagerCreditEntity, 'mc')
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .where('mc.company_id  = :companyId', { companyId })
            .andWhere("mc.DELETE_FLAG = 'N'  and mc.action_type in ('newUser','shared')")
        if (roleId) {
            sql1.andWhere("m.ROLE_ID = :roleId", { roleId })
        }
        if (!_.isEmpty(departmentIds)) {
            sql1.andWhere("mc.department_id in (:...departmentIds)", { departmentIds })
        }
        if (from && to) {
            sql1.andWhere("mc.action_time between :from and :to", { from, to })
        }
        const sql2 = this.dataSource.createQueryBuilder()
            .select([
                "m.manager_id managerId",
                "m.nick_name nickName",
                "'newReservation' actionType",
                "(select department_id from department_member where manager_id = m.MANAGER_ID order by createTime asc limit 1) detpId",
                "0 credit"])
            .from(OrderManageEntity, 'oc')
            .leftJoin(ManagerEntity, 'm', 'm.company_id = oc.company_id and m.person_id = oc.SALES_PERSON_ID')
            .where('m.company_id = :companyId')
            .andWhere("oc.delete_flag = 'N'")
            .andWhere("oc.SALES_PERSON_ID is not null")
        if (roleId) {
            sql2.andWhere("m.ROLE_ID = :roleId", { roleId })
        }
        if (from && to) {
            sql2.andWhere("oc.order_time between :from and :to", { from, to })
        }
        const sql3 = this.dataSource.createQueryBuilder()
            .select([
                "m.manager_id managerId",
                "m.nick_name nickName",
                "'newReservation' actionType",
                "p.department_id deptId",
                "0 credit"])
            .from(OrderManageEntity, 'oc')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = oc.TARGET')
            .leftJoin(ManagerEntity, 'm', 'm.MANAGER_ID = p.project_manager')
            .where('m.company_id = :companyId')
            .andWhere("oc.delete_flag = 'N'")
            .andWhere("oc.SALES_PERSON_ID is null")
            .andWhere("oc.service_name = '查看直播'")
        if (roleId) {
            sql3.andWhere("m.ROLE_ID = :roleId", { roleId })
        }
        if (from && to) {
            sql3.andWhere("oc.order_time between :from and :to", { from, to })
        }
        const sql4 = sql1.getQuery() + ` union all ` + sql2.getQuery() + ` union all ` + sql3.getQuery()
        const qb = this.dataSource.createQueryBuilder()
            .select([
                "tt.managerId",
                "tt.nickName",
                "SUM(CASE WHEN tt.actionType = 'newUser' THEN 1 ELSE 0 END) totalNewCustomer",
                "SUM(CASE WHEN tt.actionType = 'shared' THEN 1 ELSE 0 END) totalShareDegree",
                "SUM(CASE WHEN tt.actionType = 'newReservation' THEN 1 ELSE 0 END) totalNewOrder",
                "sum(tt.credit) credits"
            ])
            .from("(" + sql4 + ")", 'tt')
        if (!_.isEmpty(departmentIds)) {
            qb.where("tt.deptId in (:departmentIds)", { departmentIds })
        }
        qb.groupBy("tt.managerId")
            .having("1")
        qb.setParameter('companyId', companyId)
        if (roleId) {
            qb.setParameter('roleId', roleId)
        }
        if (!_.isEmpty(departmentIds)) {
            qb.setParameter('departmentIds', departmentIds)
        }
        if (from && to) {
            qb.setParameter('from', from)
            qb.setParameter('to', to)
        }
        const sort = this.dataSource.createQueryBuilder()
            .from("(" + qb.getQuery() + ")", 't')
            .orderBy("t.totalShareDegree", "DESC")
            .addOrderBy("t.totalNewCustomer", "DESC")
            .addOrderBy("t.totalNewOrder", "DESC")
            .addOrderBy("t.managerId", "DESC")
        sort.setParameters(qb.getParameters())
        return await sort.getRawMany()
    }

    async marketingAnalyticsDataDetail(params: Marketing.Analytics): Promise<{ total: number, items: Marketing.AnalyticsDataDetail[] }> {
        const { companyId, roleId, managerId, departmentIds, actionType, from, to } = params
        const qb = this.dataSource.getRepository(ManagerCreditEntity)
            .createQueryBuilder('mc')
            .select([
                "m.role_id roleId",
                "mr.ROLE_NAME roleName",
                "mc.manager_id managerId",
                "mc.department_id departmentId",
                "d.department_name departmentName",
                "m.nick_name nickName",
                "mc.action_type actionType",
                "mc.target_type targetType",
                "mc.target_id targetId",
                "mc.manager_credit_id id",
                "date_format(mc.action_time,'%Y-%m-%d %H:%i:%s') actionTime",
                "mc.credit credits"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .leftJoin(DepartmentEntity, 'd', 'd.department_id = mc.department_id')
            .where('mc.company_id = :companyId ', { companyId })
            .andWhere("mc.DELETE_FLAG = 'N' and credit not in (0) and action_type in ('newUser','shared','newReservation')")
        if (managerId) {
            qb.andWhere("mc.manager_id = :managerId", { managerId })
        }
        if (!_.isEmpty(departmentIds)) {
            qb.andWhere("mc.department_id in (:...departmentIds)", { departmentIds })
        }
        if (roleId) {
            qb.andWhere("m.ROLE_ID = :roleId", { roleId })
        }
        if (actionType) {
            qb.andWhere("mc.action_type = :actionType", { actionType })
        }
        if (from && to) {
            qb.andWhere("mc.action_time between :from and :to", { from, to })
        }
        qb.orderBy("mc.action_time", 'DESC')
        DaoUtil.processPageAndSort(qb, params)
        return await DaoUtil.getPageRes(qb)
    }


    async marketingAnalyticsDataV2Detail(params: Marketing.Analytics): Promise<{ total: number, items: Marketing.AnalyticsDataDetail[] }> {
        const { companyId, roleId, managerId, departmentIds, actionType, orderInterest, from, to, pageNo, pageSize } = params
        const paramList = []
        let sql1 = `SELECT
                om.COMPANY_ID companyId,
                om.ORDER_MANAGE_ID orderManageId,
                om.order_time actionTime,
                m.role_id roleId,
                m.manager_id managerId,
                m.NICK_NAME nickName,
                dm.department_id departmentId,
                "newReservation" actionType,
                target_name actionContent,
                om.target_type type,
                "" targetType,
                "" targetId,
                "" id,
                "" credits
            FROM
                ${DaoUtil.getDbName("bgw.order_manage")} om
                LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.company_id = om.company_id 
                AND om.SALES_PERSON_ID = m.person_id
                LEFT JOIN ${DaoUtil.getDbName("bgw.department_member")} dm ON dm.manager_id = m.manager_id
                INNER JOIN (
                SELECT
                    department_member_id deptId,
                    max( dm.createTime ) createTime,
                    m.MANAGER_ID managerId 
                FROM
                ${DaoUtil.getDbName("bgw.department_member")} dm
                LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
                WHERE
                    m.DELETE_FLAG = 'N' 
                and m.company_id = ${companyId}
                GROUP BY
                    m.MANAGER_ID UNION ALL
                SELECT
                    department_member_id deptId,
                    min( dm.createTime ) createTime,
                    m.MANAGER_ID managerId 
                FROM
                ${DaoUtil.getDbName("bgw.department_member")} dm
                LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
                WHERE
                    m.DELETE_FLAG = 'Y' 
                and m.company_id = ${companyId}
                GROUP BY
                    m.MANAGER_ID 
                ) t ON dm.manager_id = t.managerId 
                AND dm.createTime = t.createTime
                LEFT OUTER JOIN ${DaoUtil.getDbName("bgw.department_member")} AS dm2 ON dm.MANAGER_ID = dm2.MANAGER_ID 
                AND dm.createTime = dm2.createTime 
                AND dm.department_member_id > dm2.department_member_id
            WHERE
                m.manager_id IS NOT NULL 
                AND dm2.department_member_id IS NULL 
                AND om.DELETE_FLAG = 'N' 
                AND om.COMPANY_ID = ? 
                AND ( om.SALES_PERSON_ID IS NOT NULL OR om.SALES_PERSON_ID != '' ) `
        paramList.push(companyId)
        if (managerId) {
            sql1 += ` AND m.manager_id = ? `
            paramList.push(managerId)
        }
        // if (!_.isEmpty(departmentIds)) {
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql1 += ` AND dm.department_id IN (?) `
            paramList.push(departmentIds)
        }
        if (roleId) {
            sql1 += ` AND m.ROLE_ID = ? `
            paramList.push(roleId)
        }
        if (from && to) {
            sql1 += ` AND om.order_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql2 = `SELECT
                om.COMPANY_ID companyId,
                om.ORDER_MANAGE_ID orderManageId,
                om.order_time actionTime,
                m.role_id roleId,
                m.manager_id managerId,
                m.NICK_NAME nickName,
                p.department_id departmentId,
                "newReservation" actionType,
                target_name actionContent,
                om.target_type type,
                "" targetType,
                "" targetId,
                "" id,
                "" credits
            FROM
            ${DaoUtil.getDbName("bgw.order_manage")} om
            LEFT JOIN ${DaoUtil.getDbName("bgw.project")} p ON p.project_id = om.TARGET
            LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = p.project_manager
            LEFT JOIN ${DaoUtil.getDbName("bgw.department_member")} dm ON dm.manager_id = m.manager_id
            INNER JOIN (
                SELECT
                    department_member_id deptId,
                    max( dm.createTime ) createTime,
                    m.MANAGER_ID managerId 
                FROM
                ${DaoUtil.getDbName("bgw.department_member")} dm
                LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
                WHERE
                    m.DELETE_FLAG = 'N' 
                and m.company_id = ${companyId}
                GROUP BY
                    m.MANAGER_ID UNION ALL
                SELECT
                    department_member_id deptId,
                    min( dm.createTime ) createTime,
                    m.MANAGER_ID managerId 
                FROM
                ${DaoUtil.getDbName("bgw.department_member")} dm
                LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = dm.manager_id 
                WHERE
                    m.DELETE_FLAG = 'Y' 
                and m.company_id = ${companyId}
                GROUP BY
                    m.MANAGER_ID 
                ) t ON dm.manager_id = t.managerId 
                AND dm.createTime = t.createTime
                LEFT OUTER JOIN ${DaoUtil.getDbName("bgw.department_member")} AS dm2 ON dm.MANAGER_ID = dm2.MANAGER_ID 
                AND dm.createTime = dm2.createTime 
                AND dm.department_member_id > dm2.department_member_id 
            WHERE
                m.manager_id IS NOT NULL 
                AND dm2.department_member_id IS NULL 
                AND om.DELETE_FLAG = 'N' 
                AND om.COMPANY_ID = ? 
                AND ( SALES_PERSON_ID IS NULL OR SALES_PERSON_ID = '' ) 
                AND service_name = '查看直播' `
        paramList.push(companyId)
        if (managerId) {
            sql2 += ` AND m.manager_id = ? `
            paramList.push(managerId)
        }
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql2 += ` AND p.department_id IN (?) `
            paramList.push(departmentIds)
        }
        if (roleId) {
            sql2 += ` AND m.ROLE_ID = ? `
            paramList.push(roleId)
        }
        if (from && to) {
            sql2 += ` AND om.order_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql3 = `SELECT
            om.COMPANY_ID companyId,
            om.ORDER_MANAGE_ID orderManageId,
                om.order_time actionTime,
                "" roleId,
                "" managerId,
                "" nickName,
                "" departmentId,
                "newReservation" actionType,
                target_name actionContent,
                om.target_type type,
                "" targetType,
                "" targetId,
                "" id,
                "" credits
            FROM
            ${DaoUtil.getDbName("bgw.order_manage")} om 
            WHERE
                om.DELETE_FLAG = 'N' 
                AND om.COMPANY_ID = ? 
                AND ( SALES_PERSON_ID IS NULL OR SALES_PERSON_ID = '' ) 
                AND service_name != '查看直播' `
        paramList.push(companyId)
        if (from && to) {
            sql3 += ` AND om.order_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql4 = `SELECT
                mc.COMPANY_ID companyId,
                "" orderManageId,
                date_format( mc.action_time, '%Y-%m-%d %H:%i:%s' ) actionTime,
                m.role_id roleId,
                mc.manager_id managerId,
                m.nick_name nickName,
                mc.department_id departmentId,
                mc.action_type actionType,
                "" actionContent,
                "" type,
                mc.target_type targetType,
                mc.target_id targetId,
                    mc.manager_credit_id id,
                mc.credit credits
            FROM
            ${DaoUtil.getDbName("credit.manager_credit")} mc
            LEFT JOIN ${DaoUtil.getDbName("bgw.manager")} m ON m.manager_id = mc.manager_id 
            WHERE
                mc.company_id = ?
                AND mc.DELETE_FLAG = 'N' 
                AND credit NOT IN ( 0 ) 
                AND action_type IN ( 'newUser', 'shared' ) `
        paramList.push(companyId)
        if (managerId) {
            sql4 += ` AND mc.manager_id = ? `
            paramList.push(managerId)
        }
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql4 += ` AND mc.department_id IN (?) `
            paramList.push(departmentIds)
        }
        if (roleId) {
            sql4 += ` AND m.ROLE_ID = ? `
            paramList.push(roleId)
        }
        if (actionType) {
            sql4 += ` AND mc.action_type = ? `
            paramList.push(actionType)
        }
        if (from && to) {
            sql4 += ` AND mc.action_time BETWEEN ? AND ? `
            paramList.push(from)
            paramList.push(to)
        }

        let sql = `SELECT *  FROM ( ` + sql1 + ` UNION ALL ` + sql2 + ` UNION ALL ` + sql3 + ` UNION ALL ` + sql4 + `) tt`
        sql += ` where companyId = ? `
        paramList.push(companyId)
        if (actionType) {
            sql += ` and  actionType = ? `
            paramList.push(actionType)
        }
        if (managerId) {
            sql += ` AND managerId = ? `
            paramList.push(managerId)
        }
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            sql += ` AND departmentId IN (?) `
            paramList.push(departmentIds)
        }
        if (roleId) {
            sql += ` AND roleId = ? `
            paramList.push(roleId)
        }
        if (orderInterest) {
            sql += ` and  actionType = ? `
            sql += ` and  actionContent = ? `
            paramList.push(Marketing.ActionType.newReservation.value)
            paramList.push(orderInterest)
        }
        sql += ` ORDER BY actionTime DESC`
        if (pageNo && pageSize) {
            sql += ` LIMIT ? OFFSET ? `
            paramList.push(pageSize)
            paramList.push((pageNo - 1) * pageSize)
        }

        let countSql = `SELECT count(1) as count FROM ( ` + sql1 + ` UNION ALL ` + sql2 + ` UNION ALL ` + sql3 + ` UNION ALL ` + sql4 + `) tt`
        countSql += ` where companyId = ? `
        paramList.push(companyId)
        if (actionType) {
            countSql += ` and  actionType = ? `
            paramList.push(actionType)
        }
        if (managerId) {
            countSql += ` AND managerId = ? `
            paramList.push(managerId)
        }
        if (!_.isEmpty(departmentIds) && !departmentIds.includes('*')) {
            countSql += ` AND departmentId IN (?) `
            paramList.push(departmentIds)
        }
        if (roleId) {
            countSql += ` AND roleId = ? `
            paramList.push(roleId)
        }
        if (orderInterest) {
            countSql += ` and  actionType = ? `
            countSql += ` and  actionContent = ? `
            paramList.push(Marketing.ActionType.newReservation.value)
            paramList.push(orderInterest)
        }

        const total = Number((await this.dataSource.query(countSql, paramList))[0]?.count)
        const items = await this.dataSource.query(sql, paramList)

        return { total, items }
    }

    async marketingAnalyticsManagerList(companyId: string) {
        const qb = this.dataSource.getRepository(ManagerCreditEntity)
            .createQueryBuilder('mc')
            .select([
                "m.manager_id value",
                "m.nick_name name"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .where("mc.company_id = :companyId", { companyId })
            .groupBy("mc.manager_id")
        return await qb.getRawMany()
    }

    async marketingAnalyticsOrderInterestList(companyId: string) {
        const qb = this.dataSource.getRepository(OrderManageEntity)
            .createQueryBuilder('')
            .select([
                "target_name targetName"
            ])
            .where("company_id = :companyId", { companyId })
            .andWhere("(service_name is not null or service_name != '')")
            .andWhere("delete_flag = 'N'")
            .groupBy("service_name")
        return await qb.getRawMany()
    }

    async marketingAnalyticsRoleList(companyId: string) {
        const qb = this.dataSource.getRepository(ManagerCreditEntity)
            .createQueryBuilder('mc')
            .select([
                "m.role_id value",
                "mr.role_name name"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = mc.manager_id')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where("mc.company_id = :companyId", { companyId })
            .groupBy("m.role_id")
        return await qb.getRawMany()
    }
}