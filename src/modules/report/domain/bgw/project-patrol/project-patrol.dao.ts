import { ConsoleLogger, Injectable } from "@nestjs/common";
import { CmsPhotoEntity } from "@src/modules/report/entity/bgw/cms-photo.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { ProjectPatrolEntity } from "@src/modules/report/entity/bgw/project-patrol.entity";
import { ProjectProblemEntity } from "@src/modules/report/entity/bgw/project-problem.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import * as _ from "lodash";
import { DaoUtil } from "@yqz/nest";
import { DataSource } from "typeorm";
import { CorpReportPatrol } from "../../corp/corp-report/dto/patrol.dto";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";

@Injectable()
export class ProjectPatrolDao {
    constructor(
        private dataSource: DataSource
    ) { }

    async searchProjectPatrol(params: { projectIds: string[], startTime?: string, endTime?: string }) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { projectIds, startTime, endTime } = params || {};
        const qb = this.dataSource
            .createQueryBuilder()
            .select()
            .from(ProjectPatrolEntity, 'pt')
            .where('pt.delete_flag = "N"')
            .andWhere('pt.project_id in (:...projectIds)', { projectIds })
        if (startTime) qb.andWhere('pt.patrol_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pt.patrol_time < :endTime', { endTime });
        const res = await DaoUtil.getPageRes<ProjectPatrolEntity>(qb);
        res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
        return res;
    }

    async searchPatrolProjectNum(params: { projectIds: string[], startTime?: string, endTime?: string }) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { projectIds, startTime, endTime } = params || {};
        const qb = this.dataSource
            .createQueryBuilder()
            .select([
                'project_id projectId',
                'count(project_id) num'
            ])
            .from(ProjectPatrolEntity, 'pt')
            .where('pt.delete_flag = "N"')
            .andWhere('pt.project_id in (:...projectIds)', { projectIds })
        if (startTime) qb.andWhere('pt.patrol_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pt.patrol_time < :endTime', { endTime });
        qb.groupBy('project_id')
        const res = await qb.getRawMany()
        return res;
    }

    async searchPatrolProjectList(params: CorpReportPatrol.PatrolWisdomProjectListReq) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { projectIdList, projectManagerId, linkCompanyId, projectAddress, patrolIdList, problemIdList } = params || {}
        const e = this.dataSource.createQueryBuilder()
            .select('tt.project_id,count(tt.manager_id) patrolPerson')
            .from(sub => {
                return sub
                    .select('p.project_id,pt.manager_id')
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectPatrolEntity, 'pt', 'pt.project_id = p.project_id')
                    .where(' pt.delete_flag = "N"')
                    .andWhere('pt.project_patrol_id in (:...patrolIdList)', { patrolIdList })
                    .groupBy('p.project_id,pt.manager_id')
            }, 'tt').groupBy('tt.project_id')
        const m = this.dataSource.createQueryBuilder()
            .select('ppn.project_id,ppn.problemNum,ROUND((IFNULL(ppt.problemNum,0)/ppn.problemNum)*100) dealProblemRate')
            .from(sub => {
                return sub
                    .select('p.project_id,count(pp.project_problem_id) problemNum')
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.project_id')
                    .where(' pp.delete_flag = "N"')
                    .andWhere('pp.problem_status != 0')
                    .andWhere('pp.problem_status != 4')
                    .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
                    .andWhere('pp.problem_source = 4')
                    .groupBy('p.project_id')
            }, 'ppn')
            .leftJoin(sub => {
                return sub
                    .select('p.project_id,count(pp.project_problem_id) problemNum')
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.project_id')
                    .where(' pp.delete_flag = "N" and pp.problem_status = 3')
                    .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
                    .andWhere('pp.problem_source = 4')
                    .andWhere('pp.problem_solved_time >= :startTime and pp.problem_solved_time < :endTime',{startTime: params.startTime,endTime: params.endTime})
                    .groupBy('p.project_id')
            }, 'ppt', 'ppt.project_id = ppn.project_id')
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'p.PROJECT_ID projectId',
                'p.project_director projectManagerId',
                'p.COMPANY_ID linkCompanyId',
                'c.company_name linkCompanyName'
            ])
            if (!_.isEmpty(params.patrolIdList)) {
                qb.addSelect([
                    'IFNULL(pn.patrolNum,0) patrolNum',
                    'IFNULL(ptp.patrolPerson,0) patrolPersonNum',
                ])
            }
            if (!_.isEmpty(params.problemIdList)) {
                qb.addSelect([
                    'concat(IFNULL(pp.dealProblemRate,0),"%") problemDealRate',
                    'IFNULL(pp.problemNum,0) problemNum',
                    'IFNULL(ppa.dealAvgTime,0) problemAvgDealTime',
                ])
            }
            qb.from(ProjectEntity, 'p')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .leftJoin(CommunityEntity, 'com', 'com.community_id = p.COMMUNITY_ID')
        if (!_.isEmpty(params.patrolIdList)) {
            qb.leftJoin(subQuery => {
                return subQuery
                    .select([
                        'p.project_id',
                        'count(pt.project_patrol_id) patrolNum'
                    ])
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectPatrolEntity, 'pt', 'pt.project_id = p.project_id')
                    .where(' pt.delete_flag = "N"')
                    .andWhere('pt.project_patrol_id in (:...patrolIdList)', { patrolIdList })
                    .groupBy('p.project_id')
            }, "pn", 'p.PROJECT_ID = pn.project_id')
                .leftJoin("(" + e.getQuery() + ")", 'ptp', 'p.PROJECT_ID = ptp.project_id')
        }
        if (!_.isEmpty(params.problemIdList)) {
            qb.leftJoin("(" + m.getQuery() + ")", 'pp', 'p.PROJECT_ID = pp.project_id')
                .leftJoin(subQuery => {
                    return subQuery
                        .select('p.project_id,Round(AVG(TIMESTAMPDIFF(minute,pp.problem_time,pp.problem_solved_time))/60,1) dealAvgTime')
                        .from(ProjectEntity, 'p')
                        .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.project_id')
                        .where(' pp.delete_flag = "N" and pp.problem_status = 3')
                        .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
                        .andWhere('pp.problem_source = 4')
                        .andWhere('pp.problem_solved_time >= :startTime and pp.problem_solved_time < :endTime',{startTime: params.startTime,endTime: params.endTime})
                        .groupBy('p.project_id')
                }, 'ppa', 'p.PROJECT_ID = ppa.project_id')
        }
        qb.where("c.DELETE_FLAG = 'N' and com.DELETE_FLAG = 'N' ")
            .andWhere('p.project_id in (:...projectIdList)', { projectIdList })
        if (params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId", { projectManagerId })
        if (params.linkCompanyId) qb.andWhere("p.company_id = :linkCompanyId", { linkCompanyId })
        if (params.problemIdList.length>0&&+params.findProblemCount)qb.andWhere("problemNum >= :problemNum", { problemNum: params.findProblemCount })
        if (params.patrolIdList.length>0&&+params.patrolCount)qb.andWhere("patrolNum >= :patrolNum", { patrolNum: params.patrolCount })
        if (params.patrolIdList.length>0&&(params.patrolTotalSort || +params.patrolTotalSort)) qb.orderBy('patrolNum', params.patrolTotalSort)
        else if (params.patrolIdList.length>0&&(params.patrolPersonSort || +params.patrolPersonSort)) qb.orderBy('patrolPersonNum', params.patrolPersonSort)
        else if (params.problemIdList.length>0&&(params.patrolProblemSort || +params.patrolProblemSort)) qb.orderBy('problemNum', params.patrolProblemSort)
        else if (params.problemIdList.length>0&&(params.problemAvgDealTimeSort || +params.problemAvgDealTimeSort)) qb.orderBy('problemAvgDealTime', params.problemAvgDealTimeSort)
        else if (params.problemIdList.length>0&&(params.problemDealSort || +params.problemDealSort)) qb.orderBy('IFNULL(pp.dealProblemRate,0)', params.problemDealSort)
        else qb.orderBy('c.createTime', 'ASC').addOrderBy('p.createTime', 'DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        const list = await qb.getRawMany()
        return list
    }

    async searchPatrolProjectCount(params: CorpReportPatrol.PatrolWisdomProjectListReq) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { projectIdList, projectManagerId, linkCompanyId, projectAddress, patrolIdList, problemIdList } = params || {}
        const e = this.dataSource.createQueryBuilder()
            .select('tt.project_id,count(tt.manager_id) patrolPerson')
            .from(sub => {
                return sub
                    .select('p.project_id,pt.manager_id')
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectPatrolEntity, 'pt', 'pt.project_id = p.project_id')
                    .where(' pt.delete_flag = "N"')
                    .andWhere('pt.project_patrol_id in (:...patrolIdList)', { patrolIdList })
                    .groupBy('p.project_id,pt.manager_id')
            }, 'tt').groupBy('tt.project_id')
        const m = this.dataSource.createQueryBuilder()
            .select('ppn.project_id,ppn.problemNum,ROUND((IFNULL(ppt.problemNum,0)/ppn.problemNum)*100) dealProblemRate')
            .from(sub => {
                return sub
                    .select('p.project_id,count(pp.project_problem_id) problemNum')
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.project_id')
                    .where(' pp.delete_flag = "N"')
                    .andWhere('pp.problem_status != 0')
                    .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
                    .groupBy('p.project_id')
            }, 'ppn')
            .leftJoin(sub => {
                return sub
                    .select('p.project_id,count(pp.project_problem_id) problemNum')
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.project_id')
                    .where(' pp.delete_flag = "N" and pp.problem_status = 3')
                    .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
                    .groupBy('p.project_id')
            }, 'ppt', 'ppt.project_id = ppn.project_id')
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'p.PROJECT_ID projectId'
            ])
            .from(ProjectEntity, 'p')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .leftJoin(CommunityEntity, 'com', 'com.community_id = p.COMMUNITY_ID')
        if (!_.isEmpty(params.patrolIdList)) {
            qb.leftJoin(subQuery => {
                return subQuery
                    .select([
                        'p.project_id',
                        'count(pt.project_patrol_id) patrolNum'
                    ])
                    .from(ProjectEntity, 'p')
                    .leftJoin(ProjectPatrolEntity, 'pt', 'pt.project_id = p.project_id')
                    .where(' pt.delete_flag = "N"')
                    .andWhere('pt.project_patrol_id in (:...patrolIdList)', { patrolIdList })
                    .groupBy('p.project_id')
            }, "pn", 'p.PROJECT_ID = pn.project_id')
                .leftJoin("(" + e.getQuery() + ")", 'ptp', 'p.PROJECT_ID = ptp.project_id')
        }
        if (!_.isEmpty(params.problemIdList)) {
            qb.leftJoin("(" + m.getQuery() + ")", 'pp', 'p.PROJECT_ID = pp.project_id')
                .leftJoin(subQuery => {
                    return subQuery
                        .select('p.project_id,Round(AVG(TIMESTAMPDIFF(hour,pp.problem_time,pp.problem_solved_time)),1) dealAvgTime')
                        .from(ProjectEntity, 'p')
                        .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.project_id')
                        .where(' pp.delete_flag = "N" and pp.problem_status = 3')
                        .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
                        .groupBy('p.project_id')
                }, 'ppa', 'p.PROJECT_ID = ppa.project_id')
        }
        qb.where("c.DELETE_FLAG = 'N' and com.DELETE_FLAG = 'N' ")
            .andWhere('p.project_id in (:...projectIdList)', { projectIdList })
        if (params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId", { projectManagerId })
        if (params.linkCompanyId) qb.andWhere("p.company_id = :linkCompanyId", { linkCompanyId })
        if (params.problemIdList.length>0&&+params.findProblemCount) qb.andWhere("problemNum >= :problemNum", { problemNum: params.findProblemCount })
        if (params.patrolIdList.length>0&&+params.patrolCount) qb.andWhere("patrolNum >= :patrolNum", { patrolNum: params.patrolCount })
        const q = this.dataSource.createQueryBuilder().select('count(tt.projectId) num').from("(" + qb.getQuery() + ")", 'tt')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()
        return count.num
    }

    async searchPatrolTotalList(params: CorpReportPatrol.PatrolTotalListReq) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { patrolIdList, managerId, projectManagerId, linkCompanyId, projectAddress, projectIdList } = params || {};
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pt.manager_id managerId',
                'DATE_FORMAT(pt.patrol_time,"%Y-%m-%d %H:%i:%s") patrolTime',
                'p.project_director projectManagerId',
                'p.project_id projectId',
                'p.company_id linkCompanyId',
                'c.company_name linkCompanyName',
                'mg.nick_name nickName',
            ])
            .from(ProjectEntity, 'p')
            .leftJoin(ProjectPatrolEntity, 'pt', 'pt.project_id = p.project_id')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .leftJoin(ManagerEntity, 'mg', 'mg.manager_id = pt.manager_id')
            .leftJoin(CommunityEntity, 'com', 'com.community_id = p.community_id ')
            .where("pt.delete_flag = 'N' and c.DELETE_FLAG = 'N' ")
            .andWhere("p.DELETE_FLAG = 'N' and mg.DELETE_FLAG = 'N' and com.DELETE_FLAG = 'N'")
            .andWhere('pt.project_patrol_id in (:...patrolIdList)', { patrolIdList })
        if (params.managerId) qb.andWhere("pt.manager_id = :managerId", { managerId })
        if (params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId", { projectManagerId })
        if (params.linkCompanyId) qb.andWhere("p.company_id = :linkCompanyId", { linkCompanyId })
        if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList })
        if (params.patrolTimeSort || +params.patrolTimeSort) qb.orderBy('pt.patrol_time', params.patrolTimeSort)
        else qb.orderBy('c.createTime', 'ASC').addOrderBy('pt.patrol_time', 'DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        const list = await qb.getRawMany()
        return list
    }


    async searchPatrolTotalCount(params: CorpReportPatrol.PatrolTotalListReq) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { patrolIdList, managerId, projectManagerId, linkCompanyId, projectIdList } = params || {};
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pt.project_patrol_id patrolId'
            ])
            .from(ProjectEntity, 'p')
            .leftJoin(ProjectPatrolEntity, 'pt', 'pt.project_id = p.project_id')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .leftJoin(ManagerEntity, 'mg', 'mg.manager_id = pt.manager_id')
            .leftJoin(CommunityEntity, 'com', 'com.community_id = p.community_id ')
            .where("pt.delete_flag = 'N' and c.DELETE_FLAG = 'N' ")
            .andWhere("p.DELETE_FLAG = 'N' and mg.DELETE_FLAG = 'N' and com.DELETE_FLAG = 'N'")
            .andWhere('pt.project_patrol_id in (:...patrolIdList)', { patrolIdList })
        if (params.managerId) qb.andWhere("pt.manager_id = :managerId", { managerId })
        if (params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId", { projectManagerId })
        if (params.linkCompanyId) qb.andWhere("p.company_id = :linkCompanyId", { linkCompanyId })
        if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList })
        const q = this.dataSource.createQueryBuilder().select('count(tt.patrolId) num').from("(" + qb.getQuery() + ")", 'tt')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()
        return count.num
    }


    async searchPatrolProblemList(params: CorpReportPatrol.PatrolProblemNumListReq) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { problemIdList, reportId, projectManagerId, linkCompanyId, projectIdList, problemStatus, problemType } = params || {}
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'p.PROJECT_ID projectId',
                'pp.problem_img problemPhoto',
                'pp.problem_status problemStatus',
                'pp.problem_type problemType',
                // 'pp.problem_record_person reportId',
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, pp.problem_record_owner, m.person_id) as reportId",
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, owner.nick_name, m.nick_name) as reportName",
                'date_format(pp.problem_time,"%Y-%m-%d") reportDate',
                'date_format(pp.problem_solved_time,"%Y-%m-%d") solvedDate',
                'pp.problem_solved_img solvedPhoto',
                'pp.problem_description problemDescription',
                'pp.problem_solved_description solvedDescription',
                'p.project_director projectManagerId',
                'p.company_id linkCompanyId',
                'c.company_name linkCompanyName'
            ])
            .from(ProjectEntity, 'p')
            .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.PROJECT_ID')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .leftJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
            .leftJoin(ManagerEntity, 'm', 'pp.problem_record_person = m.manager_id')
            .leftJoin(PersonEntity, 'owner', 'owner.person_id = pp.problem_record_owner')
            .where("pp.delete_flag = 'N' and c.DELETE_FLAG = 'N'")
            .andWhere("p.DELETE_FLAG = 'N' and com.DELETE_FLAG = 'N'")
            .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
        if (params.problemStatus) qb.andWhere("pp.problem_status = :problemStatus", { problemStatus })
        if (params.problemType) qb.andWhere("pp.problem_type = :problemType", { problemType })
        if (params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId", { projectManagerId })
        if (params.linkCompanyId) qb.andWhere("p.company_id = :linkCompanyId", { linkCompanyId })
        if (!_.isEmpty(projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList })
        if (params.reportId) qb.andWhere("(CASE WHEN pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null THEN pp.problem_record_owner = :reportId ELSE m.person_id = :reportId END)", { reportId });
        qb.andWhere('pp.problem_source = 4')
        qb.orderBy('c.createTime', 'ASC').addOrderBy('pp.createTime', 'DESC')
        if (params.pageSize) qb.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize)
        const list = await qb.getRawMany()
        return list
    }

    async searchPatrolProblemCount(params: CorpReportPatrol.PatrolProblemNumListReq) {
        DaoUtil.checkEmptyParams(params, ProjectPatrolDao.name + '.search', 'FailIfAllEmpty');
        const { problemIdList, reportId, projectManagerId, linkCompanyId, projectIdList, problemStatus, problemType } = params || {}
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.project_problem_id problemId'
            ])
            .from(ProjectEntity, 'p')
            .leftJoin(ProjectProblemEntity, 'pp', 'pp.project_id = p.PROJECT_ID')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
            .leftJoin(CommunityEntity, 'com', 'com.community_id = p.community_id')
            .leftJoin(ManagerEntity, 'm', 'pp.problem_record_person = m.manager_id')
            .where("pp.delete_flag = 'N' and c.DELETE_FLAG = 'N'")
            .andWhere("p.DELETE_FLAG = 'N' and com.DELETE_FLAG = 'N'")
            .andWhere('pp.project_problem_id in (:...problemIdList)', { problemIdList })
        if (params.problemStatus) qb.andWhere("pp.problem_status = :problemStatus", { problemStatus })
        if (params.problemType) qb.andWhere("pp.problem_type = :problemType", { problemType })
        if (params.projectManagerId) qb.andWhere("p.project_director = :projectManagerId", { projectManagerId })
        if (params.linkCompanyId) qb.andWhere("p.company_id = :linkCompanyId", { linkCompanyId })
        if (!_.isEmpty(projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList })
        if (params.reportId) qb.andWhere("(CASE WHEN pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null THEN pp.problem_record_owner = :reportId ELSE m.person_id = :reportId END)", { reportId });
        qb.andWhere('pp.problem_source = 4')
        const q = this.dataSource.createQueryBuilder().select('count(tt.problemId) num').from("(" + qb.getQuery() + ")", 'tt')
        q.setParameters(qb.getParameters())
        const count = await q.getRawOne()
        return count.num
    }
}