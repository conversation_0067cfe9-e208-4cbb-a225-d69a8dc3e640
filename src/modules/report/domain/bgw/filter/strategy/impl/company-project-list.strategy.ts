import { Filter } from "../../types/filter.type";
import { ProjectDao } from "../../../project/dao/project.dao";

/**
 * 查询公司下所有工地
 */
export class CompanyIdToProjectIdsStrategy implements IInitialListStrategy<string,Filter.InitialReq> {
    constructor(
        private readonly projectDao: ProjectDao,
    ) { }

    async getInitialList(request: Filter.InitialReq): Promise<string[]> {
        return await this.projectDao.getProjectIdsByCompany({companyId: request.companyId})
    }
}