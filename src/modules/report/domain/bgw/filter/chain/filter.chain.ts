import * as _ from 'lodash';

export class Filter<PERSON>hain<T, Request, <PERSON><PERSON> extends IFilterHandler<T, Request>> {

    private firstHandler: IFilterHandler<T, Request> | null = null;
    private lastHandler: <PERSON>ilterHandler<T, Request> | null = null;
    private initialListStrategy: IInitialListStrategy<T, Request> | null = null;

    constructor(initialListStrategy?: IInitialListStrategy<T, Request>) {
        if (initialListStrategy) {
            this.initialListStrategy = initialListStrategy;
        }
    }

    public addHandler(handler: Handler): FilterChain<T, Request, Handler> {
        if (this.firstHandler === null) {
            this.firstHandler = handler;
            this.lastHandler = handler;
        } else {
            this.lastHandler.setNext(handler);
            this.lastHandler = handler;
        }
        return this;
    }

    public async handle(request: Request, list?: T[], params?: Request): Promise<T[]> {
        // 初始列表
        let initialList: T[] = [];
        // 全部列表
        const allList = []
        allList.push("*")
        // 默认列表
        const defList = _.isEmpty(initialList)? allList : initialList;

        if (!_.isEmpty(list)) {
            initialList = list
        } else if (this.initialListStrategy) {
            // 如果不传list，则获取默认列表
            initialList = await this.initialListStrategy.getInitialList(params);
        }

        // 没有handler，直接返回初始列表
        if (this.firstHandler === null) {
            return defList;
        }

        // 是否有过滤器执行过, 有则返回默认列表
        request['filterExecuted'] = false;
        
        const result = await this.firstHandler.handle(request, initialList);
        if (!request['filterExecuted']) {
            return defList;
        }
        return result;
    }

}