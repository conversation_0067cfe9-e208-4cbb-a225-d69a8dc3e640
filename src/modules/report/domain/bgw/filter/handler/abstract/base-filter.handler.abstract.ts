import * as _ from 'lodash';

export abstract class BaseFilterHandler<T, Request> implements <PERSON>ilterHandler<T, Request> {
    private nextHandler: IFilterHandler<T, Request> | null = null;

    public setNext(handler: <PERSON>ilterHandler<T, Request>): <PERSON>ilterHandler<T, Request> {
        this.nextHandler = handler;
        return handler;
    }

    public async handle(request: Request & {filterExecuted?: boolean},list?: T[]): Promise<T[]> {
        // 初始化filterExecuted为false，如果该字段不存在
        if (request.filterExecuted === undefined) {
            request.filterExecuted = false;
        }
        
        let newList = list;
        
        // 是否执行当前处理器
        const checkFlag = this.check(request, list);

        // 首先检查是否应该处理这个请求，为空查全部
        if (checkFlag) {
            request.filterExecuted = true;
            newList = await this.process(request, list);
        }
        
        // 如果存在下一个处理器，则递归调用其handle方法
        if (this.nextHandler != null) {
            return this.nextHandler.handle(request, newList);
        } 
        
        return newList;
    }

    protected abstract process(request: Request, list?: T[]): Promise<T[]>;
    protected abstract check(request: Request, list?: T[]): boolean;
}