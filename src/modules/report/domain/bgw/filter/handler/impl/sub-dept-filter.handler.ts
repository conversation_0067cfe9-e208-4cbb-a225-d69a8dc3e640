import { DeptFilter } from "../abstract/dept-filter.handler.abstract";
import { DepartmentDao } from "../../../department/dao/department.dao";
import * as _ from "lodash";
import { BaseFilterReq } from "../../types/filter.type";
import { Injectable } from "@nestjs/common";

@Injectable()
export class SubDeptFilter extends DeptFilter<string, BaseFilterReq> {

    constructor( 
        private readonly deptDao: DepartmentDao,
     ) {
        super();
    }

    protected check(request: BaseFilterReq): boolean {
        return _.isString(request['departmentId']) &&
        (_.isString(request['companyId']) || _.isNumber(request['companyId']));
    }

    protected async process(request: BaseFilterReq): Promise<string[]> {
        const companyId: string = request['companyId'];
        const departmentId: string = request['departmentId'];
        const subDepts = await this.deptDao.getSubByDepartmentId({ companyId, departmentId });
        const departmentIds = subDepts.map(res => res.departmentId);
        if (!departmentIds.includes(departmentId)) departmentIds.push(departmentId);
        return departmentIds;
    };
}