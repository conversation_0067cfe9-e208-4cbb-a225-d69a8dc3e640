import * as _ from "lodash";
import { ProjectFilter } from "../abstract/project-filter.handle.abstract";
import { AgileApi, YqzException } from "@yqz/nest";
import { BaseFilterReq } from "../../types/filter.type";
import { Injectable } from "@nestjs/common";

@Injectable()
export class ScopeProjectFilter extends ProjectFilter<string, BaseFilterReq> {

    protected check(request: BaseFilterReq): boolean {
        return _.isString(request['scope']) &&
        (_.isString(request['companyId']) || _.isNumber(request['companyId']));
    }

    protected async process(request: BaseFilterReq, list?: string[]): Promise<string[]> {
        const scope = request['scope'];
        const companyId = request['companyId'];
        const personId = request['personId'];
        if ('all' != scope && !personId) throw new YqzException("范围筛选personId不能为空");
        const res = await Agile<PERSON>pi.getScopeProjectList({companyId, scope, personId, projectIds: list})
        return res
    };
}