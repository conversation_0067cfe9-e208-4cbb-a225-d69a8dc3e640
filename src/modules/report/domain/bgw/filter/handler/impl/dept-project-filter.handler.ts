import * as _ from "lodash";
import { ProjectDao } from "../../../project/dao/project.dao";
import { BaseFilterReq } from "../../types/filter.type";
import { ProjectFilter } from "../abstract/project-filter.handle.abstract";
import { Injectable } from "@nestjs/common";

@Injectable()
export class DeptProjectFilter extends ProjectFilter<string, BaseFilterReq> {

    constructor( 
        private readonly projectDao: ProjectDao,
     ) {
        super();
    }

    protected check(request: BaseFilterReq): boolean {
        return (_.isString(request['departmentId'])&&request['departmentId']!=="") &&
            (_.isString(request['companyId']) || _.isNumber(request['companyId']));
    }

    protected async process(request: BaseFilterReq): Promise<string[]> {
        const companyId: string = request['companyId'];
        const departmentId: string = request['departmentId'];
        const projectIds = await this.projectDao.getProjectIdsBySubDept({companyId, departmentId})
        return projectIds;
    };
}