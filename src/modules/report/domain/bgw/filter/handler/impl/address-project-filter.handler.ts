import * as _ from "lodash";
import { ProjectFilter } from "../abstract/project-filter.handle.abstract";
import { ProjectService } from "../../../project/service/project.service";
import { BaseFilterReq } from "../../types/filter.type";
import { Injectable } from "@nestjs/common";

@Injectable()
export class ProjectAddressFilter extends ProjectFilter<string, BaseFilterReq> {

    constructor(
        private readonly projectService: ProjectService
    ) {
        super();
    }

    protected check(request: BaseFilterReq, list?: string[]): boolean {
        return (_.isString(request['projectAddress'])&&request['projectAddress']!=="") &&
            (_.isString(request['companyId']) || _.isNumber(request['companyId']));
    }

    protected async process(request: BaseFilterReq, list?: string[]): Promise<string[]> {
        const projectAddress = request['projectAddress'];
        const companyId = request['companyId'];
        const projectIdList = await this.projectService.getProjectIdListByAddress({ projectAddress, companyId, projectIds: list });
        return projectIdList
    };
}