import { Body, Controller, Post } from "@nestjs/common";
import { ProjectReportEtl } from "../../etl-task/project-report/etl";

/**
 * 工地日报
 */
@Controller("project/report")
export class ProjectReportController {
  constructor(
    private readonly projectReportEtl: ProjectReportEtl,
  ) { }

  @Post("generate/company/list")
  async generateProjectReportByCompanyList(@Body() query: { date: string, companyIdList?: string[] }) {
    const res = await this.projectReportEtl.myEtlByCompanyList({
      companyIdList: query?.companyIdList,
      date: new Date(query.date)
    });
    return null;
  }

  @Post("generate/company")
  async generateProjectReportByCompany(@Body() query: { date: string, companyId: string, managerId?: string }) {
    const res = await this.projectReportEtl.myEtlByCompany({
      companyId: query?.companyId,
      managerId: query?.managerId,
      date: new Date(query.date)
    });
    return null;
  }

}