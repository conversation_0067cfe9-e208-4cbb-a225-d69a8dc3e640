import { ApiProperty } from "@nestjs/swagger";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { ProjectReportEntity } from "@src/modules/report/entity/bgw/project-report.entity";

export namespace ProjectReport {

    export class Search {
        @ApiProperty()
        companyId: string;
        @ApiProperty()
        managerId?: string;
        @ApiProperty()//可查看project范围
        projectIds?: string[];
        @ApiProperty()
        startTime?: string;
        @ApiProperty()
        endTime?: string;
        @ApiProperty()//是否是设备管理员
        isDeviceAdmin?: string;
        @ApiProperty()//是否是公司管理员
        isCompanyAdmin?: string;
        @ApiProperty()//公司设备数
        companyDeviceNum?: number;
        @ApiProperty()
        day?: string;//日报日期
    }

    export class Save {
        @ApiProperty()
        companyId: string;
        @ApiProperty()
        managerId: string;
        @ApiProperty()
        reportContent: string;
        @ApiProperty()
        reportDate: Date;
        @ApiProperty()
        expCount: number;
    }

    export class ProjectReportMsg {
        reportDate: string;
        reportList: {
            companyId: string;
            personId: string; 
            expCount: number;
        }[];
    }

    export type JoinEntity = ProjectReportEntity & {
        manager: ManagerEntity
    }
}