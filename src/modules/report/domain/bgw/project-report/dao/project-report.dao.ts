import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource, QueryBuilder, SelectQueryBuilder } from "typeorm";
import { ProjectReportEntity } from "@src/modules/report/entity/bgw/project-report.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { ProjectReport } from "../dto/project-report.dto";
import { DevTimeout } from "@yqz/nest";
@Injectable()
export class ProjectReportDao {
    private readonly logger = new MyLogger(ProjectReportDao.name)
    constructor(
        private dataSource: DataSource
    ) { }

    @DevTimeout(500)
    test() {

    }

    async save(params: ProjectReportEntity) {
        await this.dataSource.manager
            .save(ProjectReportEntity, params);
        return params.id;
    }

    async searchListByDate(date: string) {
        const qb = this.dataSource
          .getRepository(ProjectReportEntity)
          .createQueryBuilder('r')
          .select([
            'r.company_id companyId',
            'm.person_id personId',
            'r.report_content reportContent'
          ])
          .leftJoin(ManagerEntity, 'm', 'm.manager_id = r.manager_id')
          .where('r.delete_flag = "N" and m.delete_flag="N"')
          .andWhere('r.report_date = :reportDate', { reportDate: date });
        const res = await qb.getRawMany();
        return res
      }

}