import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DeviceDao } from "@src/modules/report/domain/camera/device/dao/device.dao";
import { Device } from "@src/modules/report/domain/camera/device/types/device.type";
import { ProjectDetectionDao } from "@src/modules/report/domain/camera/project-detection/dao/detection.dao";
import { Detection } from "@src/modules/report/domain/camera/project-detection/dto/detection.dto";
import { ProjectInactiveRecordDao } from "@src/modules/report/domain/camera/project-inactive-record/dao/project-inactive-record.dao";
import { ProjectReportEntity } from "@src/modules/report/entity/bgw/project-report.entity";
import { AgileApi, DevTimeout } from "@yqz/nest";
import * as _ from "lodash";
import { ProjectMemberDao } from "../../../project-member/dao/project-member.dao";
import { ProjectProblemDao } from "../../../project-problem/dao/project-problem.dao";
import { ProjectProblem } from "../../../project-problem/dto/project-problem.dto";
import { ProjectSignInDao } from "../../../project-sign-in/dao/project-sign-in.dao";
import { ProjectSignIn } from "../../../project-sign-in/dto/project-sign-in.dto";
import { ProjectDao } from "../../../project/dao/project.dao";
import { ProjectReportDao } from "../../dao/project-report.dao";
import { ProjectReport } from "../../dto/project-report.dto";
import { ManagerDao } from "../../../manager/dao/manager.dao";
import { DepartmentMemberDao } from "../../../department/dao/department-member.dao";
import { ProjectNodeDao } from "../../../project/dao/project-node.dao";

@Injectable()
export class ProjectReportBasicsService {
  private readonly logger = new MyLogger(ProjectReportBasicsService.name)
  constructor(
    private readonly deviceDao: DeviceDao,
    private readonly projectDao: ProjectDao,
    private readonly projectNodeDao: ProjectNodeDao,
    private readonly projectDetectionDao: ProjectDetectionDao,
    private readonly projectProblemDao: ProjectProblemDao,
    private readonly projectReportDao: ProjectReportDao,
    private readonly projectSignInDao: ProjectSignInDao,
    private readonly projectInactiveRecordDao: ProjectInactiveRecordDao,
    private readonly projectMemberDao: ProjectMemberDao,
    private readonly managerDao: ManagerDao,
    private readonly departmentMemberDao: DepartmentMemberDao
  ) { }

  @DevTimeout(500)
  private async test() {
    // let res = await this.searchProjectInactive({
    //   companyId: '65', projectIds: [], day: "2022-11-28", searchProAll: true,
    //   startTime: "2022-11-28",
    //   endTime: "2022-11-28"
    // });
    // console.log(res)
    // const proOverdue = await this.projectDao.searchProjectOverdueNum({ companyId: '65', projectIds: [] });
    // console.log(proOverdue.overdueNum)
    // const proShutdown = await this.projectInactiveRecordDao.searchProjectInactiveNum({ companyId: '65', projectIds: [], day: '2022-11-28' })
    // console.log(proShutdown)
  }

  /**
   * 设备情况（仅设备管理员展示该模块）
   * @param params 
   * @returns 
   */
  async save(params: ProjectReport.Save) {
    let entity = new ProjectReportEntity();
    entity.companyId = params.companyId;
    entity.managerId = params.managerId;
    entity.reportContent = params.reportContent;
    entity.reportDate = params.reportDate;
    entity.expCount = params.expCount
    entity.createTime = new Date();
    let id = await this.projectReportDao.save(entity);
    return id;
  }

  /**
   * 查询工地范围
   * @param params 
   */
  async searchProjectScope(params: { managerId: string }) {
    const projectIds = await AgileApi.getUserScopeProjects(params.managerId);
    return projectIds.projectIdList;
  }

  /**
   * 查询manager范围
   * @param params 
   */
  async searchManagerScope(params: { managerId: string }) {
    let managerIds = [];
    //查询可视管理的manager(设备领用manager)
    const scopeManagers = await AgileApi.getUserScopeManagers(params.managerId);
    managerIds = scopeManagers.map(res => res.managerId);
    return managerIds;
  }


  /**
   * 设备情况（公司有设备，且仅设备管理员展示该模块）
   * @param params 
   * @returns 
   */
  async searchDeviceTotal(params: Device.SearchSituation) {
    let cameraSituation = await this.deviceDao.searchCameraSituationNum({ companyId: params.companyId, startTime: params.startTime, endTime: params.endTime });
    return {
      devTotalNum: Number(cameraSituation.totalNum),
      devTotalStockIdleNum: Number(cameraSituation.stockIdleNum),
      devTotalOutStockIdleNum: Number(cameraSituation.outStockIdleNum),
      devTotalOfflineNum: Number(cameraSituation.offlineNum)
    }
  }

  /**
   * 工地概况
   * @param params 
   * @returns 
   */
  async searchProjectConstruction(params: { companyId: string, projectIds: string[], managerId: string, isCompanyAdmin: string, isDeviceAdmin: string, startTime: string, endTime: string }) {
    const {startTime, endTime} = params
    const result = { proConstructionNum: 0, devReceiveNum: 0, devIdleNum: 0, devOfflineNum: 0, proCreateNum: 0 };
    //在建工地
    const manager = await this.managerDao.find(params.managerId);
    const deptList = await this.departmentMemberDao.search({ managerId: params.managerId });
    const deptHeadList = deptList.filter(res => res.role && res.role.includes('department_head'));
    if ('1' == manager.MANAGER_TYPE || 'Y' == manager.content_admin) {//全部 - 公司管理员/内容管理员
      const projects = await this.projectDao.searchProject({ companyId: params.companyId, projectStatus: 'no_finish' });
      result.proConstructionNum = _.uniq(projects.map(e => e.PROJECT_ID)).length;
      //新增工地数
      const createProject = await this.projectDao.searchProject({ companyId: params.companyId, startTime, endTime });
      result.proCreateNum = _.uniq(createProject.map(e => e.PROJECT_ID)).length;
    } else if (deptHeadList.length > 0 || 'Y' == manager.device_admin) {//我管理的 - 部门负责人/摄像头管理员
      if (!_.isEmpty(params.projectIds)) {
        if (1 == params.projectIds.length && params.projectIds.includes("*")) params.projectIds = [];//['*']表示查全部工地
        const projects = await this.projectDao.searchProject({ companyId: params.companyId, projectStatus: 'no_finish', projectIds: params.projectIds });
        result.proConstructionNum = _.uniq(projects.map(e => e.PROJECT_ID)).length;
        //新增工地数
        const createProject = await this.projectDao.searchProject({ companyId: params.companyId, startTime, endTime, projectIds: params.projectIds });
        result.proCreateNum = _.uniq(createProject.map(e => e.PROJECT_ID)).length;
      }
    } else {//我参与的
      const res = await this.projectMemberDao.searchProject({ companyId: params.companyId, managerId: params.managerId });
      const projectIds = res.map(res => res.projectId);
      if (!_.isEmpty(projectIds)) {
        const projects = await this.projectDao.searchProject({ companyId: params.companyId, projectStatus: 'no_finish', projectIds: projectIds });
        result.proConstructionNum = _.uniq(projects.map(e => e.PROJECT_ID)).length;
        //新增工地数
        const createProject = await this.projectDao.searchProject({ companyId: params.companyId, startTime, endTime, projectIds: projectIds });
        result.proCreateNum = _.uniq(createProject.map(e => e.PROJECT_ID)).length;
      }
    }

    //设备情况（公司管理员、设备管理员可看全部、部门管理员看自己以及手下的、普通成员看自己的）
    if ('Y' == params.isCompanyAdmin || 'Y' == params.isDeviceAdmin) {
      let cameraSituation = await this.deviceDao.searchCameraSituationNum({ companyId: params.companyId, startTime: params.startTime, endTime: params.endTime });
      result.devReceiveNum = Number(cameraSituation.receiveNum);
      result.devIdleNum = Number(cameraSituation.outStockIdleNum);
      result.devOfflineNum = Number(cameraSituation.offlineNum);
    } else {
      const managerScope = await this.searchManagerScope({ managerId: params.managerId });
      const managerIds = managerScope.map(res => res.managerId)
      if (!_.isEmpty(managerIds)) {
        let cameraSituation = await this.deviceDao.searchCameraSituationNum({ companyId: params.companyId, managerIds: managerIds, startTime: params.startTime, endTime: params.endTime });
        result.devReceiveNum = Number(cameraSituation.receiveNum);
        result.devIdleNum = Number(cameraSituation.outStockIdleNum);
        result.devOfflineNum = Number(cameraSituation.offlineNum);
      }
    }
    return result;
  }

  /**
   * 工地施工进度
   * @param params 
   */
  async searchProjectInactive(params: { companyId: string, projectIds: string[], day: string, startTime: string, endTime: string }) {
    let result = { proShutdownNum: 0, proShutdownNum15d: 0, proShutdownNum7to15: 0, proOverdueNum: 0, newNodeOverdueNum: 0, nodeOverdueNum: 0 };
    if (!_.isEmpty(params.projectIds)) {
      if (1 == params.projectIds.length && params.projectIds.includes("*")) params.projectIds = [];//['*']表示查全部工地
      //查询工地停工施工（绑定了设备）
      let bindPro = await this.deviceDao.searchBindDeviceProject({ companyId: params.companyId });
      let proShutIds = bindPro.map(res => res.projectId);
      if (!_.isEmpty(params.projectIds))
        proShutIds = _.intersection(proShutIds, params.projectIds);
      if (_.isEmpty(proShutIds)) return result;
      const proShutdown = await this.projectInactiveRecordDao.searchProjectInactiveNum({ companyId: params.companyId, projectIds: proShutIds, day: params.day });
      result.proShutdownNum = Number(proShutdown.shutdownNum);//无人施工工地数
      result.proShutdownNum15d = Number(proShutdown.shutdownNum15d);//超过15天无人施工工地数
      result.proShutdownNum7to15 = Number(proShutdown.shutdownNum7to15);//8-15天无人施工工地数
      //查询是否开启节点延期配置(不需要了，暂不开启)
      // const matterSet = await AgileApi.searchMatterSet({companyId: params.companyId});
      // const isNodeDelayOverdue = matterSet?.isNodeDelayOverdue;
      const isNodeDelayOverdue = 'N';
      //查询逾期工地
      const proOverdue = await this.projectDao.searchProjectOverdueNum({ companyId: params.companyId, projectIds: params.projectIds, projectStatus: 'no_finish', isNodeDelayOverdue });
      result.proOverdueNum = Number(proOverdue.overdueNum);//总计x个工地预期
      //查询昨日节点逾期总数
      const newNodeOverdue = await this.projectNodeDao.searchNodeOverdueNum({ companyId: params.companyId, projectIds: params.projectIds, projectStatus: 'no_finish', isNodeDelayOverdue, startTime: params.startTime, endTime: params.endTime  });
      result.newNodeOverdueNum = Number(newNodeOverdue.nodeOverdueNum);
      //查询节点逾期总数
      const nodeOverdue = await this.projectNodeDao.searchNodeOverdueNum({ companyId: params.companyId, projectIds: params.projectIds, projectStatus: 'no_finish', isNodeDelayOverdue });
      result.nodeOverdueNum = Number(nodeOverdue.nodeOverdueNum);
    }
    return result;
  }

  /**
   * 工地AI监理
   * @param params 
   */
  async searchProjectDetection(params: Detection.Search) {
    const result: Detection.Response = new Detection.Response();
    if (!_.isEmpty(params.projectIds)) {
      if (1 == params.projectIds.length && params.projectIds.includes("*")) params.projectIds = [];//['*']表示查全部工地

      const proDetection = await this.projectDetectionDao.searchProjectSecurityDetectionNum({ companyId: params.companyId, projectIds: params.projectIds, startTime: params.startTime, endTime: params.endTime });
      result.detSmokeNum = Number(proDetection?.smokeNum || 0);//抽烟
      result.detNoUniformNum = Number(proDetection?.noUniformNum || 0);//未穿工服
      result.detSlipperNum = Number(proDetection?.slipperNum || 0);//拖鞋
      result.detSafetyHelmetNum = Number(proDetection?.safetyHelmetNum || 0);//安全帽
      result.detClimbWorkNum = Number(proDetection?.climbWorkNum || 0);//登高
      result.detFireNum = Number(proDetection?.fireNum || 0);//动火
      result.detElectroMobileNum = Number(proDetection?.electroMobileNum || 0);//电瓶车
      result.detAngleGrinderNum = Number(proDetection?.angleGrinderNum || 0);//角磨机
      result.detCuttingMachineNum = Number(proDetection?.cuttingMachineNum || 0);//切割机
      result.detFireExtinguisherNum = Number(proDetection?.fireExtinguisherNum || 0);//灭火器
      result.detTemporaryElectricityNum = Number(proDetection?.temporaryElectricityNum || 0);//临时用电
      result.detSafetyMeshNum = Number(proDetection?.safetyMeshNum || 0);//脚手架未装防护网
      result.detSafetyHoleNum = Number(proDetection?.safetyHoleNum || 0);//临边洞口无防护
      result.detSafetyStairsNum = Number(proDetection?.safetyStairsNum || 0);//楼梯无防护
      const tidinessDetection = await this.projectDetectionDao.searchProjectTidinessDetectionNum({ companyId: params.companyId, projectIds: params.projectIds, startTime: params.startTime, endTime: params.endTime });
      result.detTidinessNum = Number(tidinessDetection?.tidinessNum || 0);//整洁度
    }
    return result;
  }

  /**
   * 施工问题
   * @param params 
   */
  async searchProjectProblem(params: ProjectProblem.Search) {
    let result = { probProjectNum: 0, probNum: 0, probUnsolvedNum: 0, probUnSolvedDeadlineNum: 0, probTodaySolvedDeadlineNum: 0 };
    if (!_.isEmpty(params.projectIds)) {
      if (1 == params.projectIds.length && params.projectIds.includes("*")) params.projectIds = [];//['*']表示查全部工地
      const proProblem = await this.projectProblemDao.searchProjectProblemNum({ companyId: params.companyId, projectIds: params.projectIds, startTime: params.startTime, endTime: params.endTime });
      result.probProjectNum = Number(proProblem.projectNum);
      result.probNum = Number(proProblem.problemNum);
      result.probUnsolvedNum = Number(proProblem.unsolvedNum);
      result.probUnSolvedDeadlineNum = Number(proProblem.probUnSolvedDeadlineNum);
      result.probTodaySolvedDeadlineNum = Number(proProblem.probTodaySolvedDeadlineNum);
    }
    return result;
  }

  /**
   * 员工签到
   * @param params 
   */
  async searchSignIn(params: ProjectSignIn.Search) {
    let result = { signInPersonNum: 0, signInProjectNum: 0 };
    if (!_.isEmpty(params.projectIds)) {
      if (1 == params.projectIds.length && params.projectIds.includes("*")) params.projectIds = [];//['*']表示查全部工地
      const proSignIn = await this.projectSignInDao.searchProjectSignInNum({ companyId: params.companyId, projectIds: params.projectIds, startTime: params.startTime, endTime: params.endTime });
      result.signInPersonNum = Number(proSignIn.personNum);
      result.signInProjectNum = Number(proSignIn.projectNum);
    }
    return result;
  }

}