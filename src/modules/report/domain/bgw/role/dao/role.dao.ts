import { Injectable } from "@nestjs/common";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { DaoUtil } from "@yqz/nest";
import { Flag } from "@yqz/nest/lib/types/common.type";
import * as _ from "lodash";
import { DataSource, In } from "typeorm";

@Injectable()
export class RoleDao {

    constructor(
        private readonly dataSource: DataSource
    ) { }

    async searchList({ ids, names, companyIds }: { companyIds?: string[], ids?: string[], names?: string[] }) {
        const where = {}
        if (!_.isEmpty(ids)) {
            where['companyMemberRoleId'] = In(ids)
        }
        if (!_.isEmpty(companyIds)) {
            where['companyId'] = In(companyIds)
        }
        if (!_.isEmpty(names)) {
            where['roleName'] = In(names)
        }
        DaoUtil.checkEmptyParams(where, RoleDao.name + ".searchList", 'FailIfAllEmpty')
        where['deleteFlag'] = Flag.N
        const qb = await this.dataSource.manager.find(CompanyMemberRoleEntity, {
            where: where
        })
        return qb
    }

    async searchByIds({ roleIds }: { roleIds: string[] }) {
        if (_.isEmpty(roleIds)) return []
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'cmre.company_member_role_id as roleId',
                'cmre.role_name as roleName',
            ])
            .from(CompanyMemberRoleEntity, 'cmre')
            .where('cmre.company_member_role_id in (:...roleIds)', { roleIds })
        return await qb.getRawMany<{ roleId: string, roleName: string }>()
    }
}