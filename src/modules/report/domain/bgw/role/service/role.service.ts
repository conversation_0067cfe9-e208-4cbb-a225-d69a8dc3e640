import { Injectable } from '@nestjs/common';
import { DevTimeout } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { RoleDao } from '../dao/role.dao';

@Injectable()
export class RoleService {
    private readonly logger = new MyLogger(RoleService.name)

    constructor(
        private roleDao: RoleDao,
    ) { }

    // @DevTimeout(1000)
    async test() {
        // const res = await this.searchRoleInfoMap({ roleIds: ['144771'] });
        // console.log("🚀 ~ RoleService ~ test ~ res:", res)
    }

    async searchRoleInfoMap({ roleIds }: { roleIds: string[] }) {
        if (_.isEmpty(roleIds)) return {};
        const roleInfos = await this.roleDao.searchList({ ids: roleIds });
        const result = {};
        roleInfos.map(res => {
            result[res.companyMemberRoleId] = { roleId: res.companyMemberRoleId, roleName: res.roleName };
        })
        return result;
    }

}
