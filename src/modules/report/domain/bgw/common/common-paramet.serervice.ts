import { Injectable } from '@nestjs/common';
import { DevTimeout } from '@yqz/nest';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { CommonParameterDao } from './common-parameter.dao';

@Injectable()
export class CommonParameterService {
    private readonly logger = new MyLogger(CommonParameterService.name)

    constructor(
        private readonly commonParameterDao: CommonParameterDao,
    ) { }

    // @DevTimeout(1000)
    async test() {
        // const res = await this.searchCompanyInfoMap({ companyIds: ['65'] });
        // console.log("🚀 ~ CompanyService ~ test ~ res:", res)
    }

    async searchCommonInfoMap({ parameterType }: { parameterType: string }) {
        if (_.isEmpty(parameterType)) return {};
        const infos = await this.commonParameterDao.search(parameterType);
        const result = {};
        infos.map(res => {
            result[res.parameterCode] = { parameterCode: res.parameterCode, parameterName: res.parameterName };
        })
        return result;
    }

}
