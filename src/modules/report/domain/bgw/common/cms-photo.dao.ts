import { Injectable } from "@nestjs/common";
import { CmsPhotoEntity } from "@src/modules/report/entity/bgw/cms-photo.entity";
import { YqzException } from "@yqz/nest";
import { DeleteFlag } from "@yqz/nest/lib/types/delete-flag.enum";
import { DataSource } from "typeorm";
import * as _ from "lodash";

@Injectable()
export class CmsPhotoDao {
    constructor(
        private dataSource: DataSource
    ) { }

    async search(photoId: string) {
        if (!photoId) return null;
        return await this.dataSource.manager.findOne(CmsPhotoEntity, {
            where: {
                deleteFlag: DeleteFlag.N,
                photoId: photoId
            }
        })
    }

    async searchList({ photoIdList }: { photoIdList: string[] }): Promise<CmsPhotoEntity[]> {
        if (_.isEmpty(photoIdList)) throw new YqzException([CmsPhotoDao.name, 'missing required params'].join(": "))
        const queryBuilder = this.dataSource.getRepository(CmsPhotoEntity)
            .createQueryBuilder('c')
            .where('c.delete_flag = "N"')
            .andWhere('c.photo_id in (:...photoIdList)', { photoIdList })
        return await queryBuilder.getMany();
    }
}