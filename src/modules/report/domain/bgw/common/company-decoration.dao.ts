import { Injectable } from "@nestjs/common";
import { CompanyDecorationEntity } from "@src/modules/report/entity/bgw/company-decoration.entity";
import { DeleteFlag } from "@src/types/delete-flag.enum";
import { DataSource } from "typeorm";

@Injectable()
export class CompanyDecorationDao {
    constructor(
        private dataSource: DataSource
    ){}
    
    async search(companyId: string){
        return await this.dataSource.manager.findOne(CompanyDecorationEntity,{
            where: {
                deleteFlag: DeleteFlag.N,
                companyId: companyId
            }
        })
    }

    async update(entity: CompanyDecorationEntity){
        entity.updateTime = new Date()
        const res = await this.dataSource
            .manager
            .update(CompanyDecorationEntity, {companyId: entity.companyId}, entity)
        return res.affected===1
    }
}