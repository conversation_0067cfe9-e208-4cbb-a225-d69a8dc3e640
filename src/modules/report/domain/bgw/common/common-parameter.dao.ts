import { Injectable } from "@nestjs/common";
import { CommonParameterEntity } from "@src/modules/report/entity/bgw/common-parameter.entity";
import { DeleteFlag } from "@src/types/delete-flag.enum";
import { DataSource } from "typeorm";

@Injectable()
export class CommonParameterDao {
    constructor(
        private dataSource: DataSource
    ){}
    
    async search(parameterType: string){
        return await this.dataSource.manager.find(CommonParameterEntity,{
            where: {
                deleteFlag: DeleteFlag.N,
                parameterType: parameterType
            }
        })
    }
}