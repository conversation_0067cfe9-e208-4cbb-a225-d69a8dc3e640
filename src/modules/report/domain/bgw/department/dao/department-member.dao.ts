import { Injectable } from "@nestjs/common";
import { DepartmentMemberEntity } from "@src/modules/report/entity/bgw/department-member.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { DaoUtil } from "@src/util/dao.util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import { DataSource } from "typeorm";

@Injectable()
export class DepartmentMemberDao {
  private readonly logger = new MyLogger(DepartmentMemberDao.name);
  constructor(
    private readonly datasource:DataSource
  ){}

  @DevTimeout(500)
  private test(){
    // this.search({departmentId:'13'}).then(res=>{
    //   this.logger.log(res)
    // })
  }

  async search(params: { departmentId?: string, companyId?: string, managerId?: string }) {
    DaoUtil.checkEmptyParams(params, DepartmentMemberDao.name + ".search", 'FailIfAllEmpty')
    const { departmentId, companyId, managerId } = params;
    const qb = this.datasource
      .createQueryBuilder()
      .select(["dm.*", 'm.person_id'])
      .from(DepartmentMemberEntity, 'dm')
      .leftJoin(DepartmentEntity, 'dp', 'dm.department_id = dp.department_id')
      .leftJoin(ManagerEntity, 'm', 'dm.manager_id = m.manager_id')
      .where('dm.delete_flag = :delFlag', { delFlag: 'N' })
    if (managerId) qb.andWhere('dm.MANAGER_ID = :managerId', { managerId })
    if (departmentId) qb.andWhere('dm.department_id = :departmentId', { departmentId })
    if (companyId) qb.andWhere('dp.company_id = :companyId', { companyId })
    qb.andWhere('dm.manager_id is not null');
    const entities = await qb.getRawMany();
    return entities.map(e => DaoUtil.convertRaw2Dto<{ managerId: string, personId: string, role: string }>(e))
  }

  async searchDeptInfoList(params:{companyId?:string,companyIds?:string[],departmentIds?:string[],managerIds?:string[]}) {
    DaoUtil.checkEmptyParams(params, DepartmentMemberDao.name+".search", 'FailIfAllEmpty')
    const { departmentIds, companyId, managerIds, companyIds } = params;
    const qb = this.datasource
      .createQueryBuilder()
      .select([
        'dp.department_id deptId',
        'dp.department_name deptName',
        'dm.manager_id managerId'
      ])
      .from(DepartmentMemberEntity, 'dm')
      .leftJoin(DepartmentEntity, 'dp', 'dm.department_id = dp.department_id')
      .where('1=1')
    if (!_.isEmpty(managerIds))
      qb.andWhere('dm.MANAGER_ID in (:...managerIds)', { managerIds })
    if (!_.isEmpty(departmentIds))
      qb.andWhere('dm.department_id in (:...departmentIds)', { departmentIds })
    if (companyId)
      qb.andWhere('dp.company_id = :companyId', { companyId })
    if (!_.isEmpty(companyIds))
      qb.andWhere('dp.company_id in (:...companyIds)', { companyIds })
    const entities = await qb.getRawMany()
    return entities
  }

}