import { Injectable } from "@nestjs/common";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DataSource, In, Like } from "typeorm";
import { BaseReportReq } from "@src/types/report-req.type";
import { DaoUtil } from "@src/util/dao.util";
import * as _ from "lodash";
import { DimDepartmentEntity } from "@src/modules/report/entity/etl/dim-department.entity";
import { DimDepartmentDataEntity } from "@src/modules/report/entity/etl/dim-department-data.entity";
import { generateDateList } from "@src/util/datetime.util";
import { CustomChartType } from "../../../corp/custom-charts/type/custom-chart.type";
import { DeleteFlag } from "@src/types/delete-flag.enum";

@Injectable()
export class DepartmentDao {
  private readonly logger = new MyLogger(DepartmentDao.name)
  constructor(
    private readonly datasource: DataSource
  ) { }

  @DevTimeout(500)
  private test() {
    // this.searchCompanyIdList({hasDivisionDept:false}).then(res=>{
    //   this.logger.log(res)
    // })
    // this.searchDept({companyId:'65', sortBy:[{field:'level',order:'descending'}]}).then(res=>{
    //   this.logger.log(res)
    // })
    // this.get('13').then(res=>{
    //   this.logger.log(res)
    // })
    // this.searchCompanyIdList({hasDivisionDept:false}).then(res=>{
    //   this.logger.log(res)
    //   this.logger.log(res.includes('*********'))
    // })
  }

  async get(departmentId: string) {
    const { items: [entity] } = await this.searchDept({ departmentId })
    return entity
  }

  async getRootDept(companyId: string) {
    return await this.datasource.getRepository(DepartmentEntity).findOne({ where: { companyId, level: 0, delete_flag: DeleteFlag.N } });
  }

  async searchEtl(params: { departmentIdList: string[], startTime: string, endTime: string }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.search', 'FailIfAnyEmpty');
    const { departmentIdList, startTime, endTime } = params;
    //时间范围转换列表
    const dataList = generateDateList({ startTime, endTime });
    const qb = this.datasource
      .getRepository(DimDepartmentEntity)
      .createQueryBuilder()
      .select([
        'date as date',
        'department_id as departmentId',
        'child_department_id_list as childDepartmentIdList',
        'direct_child_department_id_list as directChildDepartmentIdList',
        'project_id_list as projectIdList'
      ])
      .where('department_id in (:...departmentIdList)', { departmentIdList })
      .andWhere('date in (:...dataList)', { dataList })
    return await qb.getRawMany<{ date: string, departmentId: string, childDepartmentIdList: string, directChildDepartmentIdList: string }>();
  }

  async searchEtlDataList(params: { date: string, departmentIdList: string[], selectedFields: CustomChartType.EtlDataKey[] }) {
    const { departmentIdList, date } = params;
    const select = [];
    params.selectedFields.forEach(field => {
      select.push(`${CustomChartType.EtlDataSqlKey[field]} as ${field}`);
    });
    if (_.isEmpty(select)) return [];
    const qb = this.datasource.getRepository(DimDepartmentDataEntity)
      .createQueryBuilder()
      .select(select)
      .where('department_id in (:...departmentIdList)', { departmentIdList })
      .andWhere('date = :date', { date });
    return await qb.getRawMany();
  }

  async searchDeptInfoList({ companyId, companyIds, deptId, deptIds, dir, includeDir, excludeDel }: { companyId?: string, companyIds?: string[], deptId?: string, deptIds?: string[], dir?: string, includeDir?: string, excludeDel?: boolean }) {
    // 使用 QueryBuilder 构建查询
    DaoUtil.checkEmptyParams({ deptId, deptIds, includeDir }, 'searchList', 'FailIfAllEmpty')
    const qb = this.datasource.getRepository(DepartmentEntity).createQueryBuilder('dp')
    qb.select([
      'dp.department_id departmentId',
      'dp.department_name departmentName',
      'dp.company_id companyId',
      'dp.link_company_id linkCompanyId',
      'dp.type type',
      'dp.dir dir'
    ])
    if (excludeDel) qb.andWhere('dp.delete_flag = :delFlag', { delFlag: 'N' });//未删除的 true，所有的 false或不传
    if (companyId) qb.andWhere('dp.company_id = :companyId', { companyId });
    if (!_.isEmpty(companyIds)) qb.andWhere('dp.company_id in (:...companyIds)', { companyIds });
    if (deptId) qb.andWhere('dp.department_id = :deptId', { deptId });
    if (!_.isEmpty(deptIds)) qb.andWhere('dp.department_id IN (:...deptIds)', { deptIds });
    if (includeDir) qb.andWhere('FIND_IN_SET(:includeDir, dp.dir)', { includeDir });
    if (dir) qb.andWhere('dp.dir = :dir', { dir });
    return await qb.getRawMany<{ departmentId: string, departmentName: string, companyId: string, linkCompanyId: string, type: string, dir: string }>();
  }

  async searchDept(params: { departmentId?: string, companyId?: string, dir?: string, level?: number, includeDir?: string } & BaseReportReq<{ level: string }>) {
    DaoUtil.checkEmptyParams(params, 'searchDept', 'FailIfAllEmpty')
    const { companyId, dir, departmentId, level, includeDir } = params || {}
    const qb = this.datasource
      .createQueryBuilder()
      .select()
      .from(DepartmentEntity, 'dp')
      .where('dp.delete_flag = :delFlag', { delFlag: 'N' });
    if (companyId) qb.andWhere('dp.company_id = :companyId', { companyId });
    if (dir) qb.andWhere('dp.dir = :dir', { dir });
    if (includeDir) qb.andWhere('find_in_set(:includeDir, dp.dir)', { includeDir });
    if (departmentId) qb.andWhere('dp.department_id = :departmentId', { departmentId });
    if ((typeof level) === 'number') qb.andWhere('dp.level = :level', { level });

    DaoUtil.processPageAndSort(qb, params)
    const res = await DaoUtil.getPageRes<DepartmentEntity>(qb)
    res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e))
    return res
  }

  async getSubByDepartmentId(params: { companyId?: string, departmentId: string }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.getSubByDepartmentId', 'FailIfAllEmpty');
    const { companyId, departmentId } = params;
    const qb = this.datasource.createQueryBuilder()
      .select([
        'd.department_id departmentId'
      ])
      .from(DepartmentEntity, 'd')
      .where("d.DELETE_FLAG = 'N'")
    if (companyId) qb.andWhere('d.company_id = :companyId', { companyId });
    qb.andWhere("(d.department_id = :departmentId OR FIND_IN_SET(:departmentId, d.dir))", { departmentId });
    return await qb.getRawMany<{ departmentId: string }>();
  }

  async searchCompanyByDept({ deptIds }: { deptIds: string[] }) {
    // 使用 QueryBuilder 构建查询
    DaoUtil.checkEmptyParams({ deptIds }, 'searchCompanyByDept', 'FailIfAllEmpty')
    const qb = this.datasource.getRepository(DepartmentEntity).createQueryBuilder('dp')
    qb.select([
      'dp.company_id companyId',
    ]).andWhere('dp.department_id IN (:...deptIds)', { deptIds })
      .groupBy('dp.company_id');
    return await qb.getRawMany<{ companyId: string }>();
  }

  async getDepartmentInfo(params: { departmentId: string }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.getDepartmentInfo', 'FailIfAnyEmpty');
    const { departmentId } = params;
    const qb = this.datasource.createQueryBuilder()
      .select([
        'd.department_id departmentId',
        'd.department_name departmentName',
        'd.link_company_id linkCompanyId',
        'd.type type',
        'dp.department_id linkDepartmentId'
      ])
      .from(DepartmentEntity, 'd')
      .leftJoin(DepartmentEntity, 'dp', 'd.link_company_id = dp.company_id and d.type = "division" and dp.level = 0 and dp.delete_flag = "N"')
      .where("d.department_id = :departmentId", { departmentId });
    return await qb.getRawOne<{ departmentId: string, linkCompanyId: string, type: string, departmentName: string, linkDepartmentId: string }>();
  }

  async getDepartmentInfoList(params: { departmentIds: string[] }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.getDepartmentInfoList', 'FailIfAnyEmpty');
    const { departmentIds } = params;
    const qb = this.datasource.createQueryBuilder()
      .select([
        'd.department_id departmentId',
        'd.department_name departmentName',
        'dp.department_id linkDepartmentId'
      ])
      .from(DepartmentEntity, 'd')
      .leftJoin(DepartmentEntity, 'dp', 'd.link_company_id = dp.company_id and d.type = "division" and dp.level = 0 and dp.delete_flag = "N"')
      .where("d.department_id in (:...departmentIds)", { departmentIds });
    return await qb.getRawMany<{ departmentId: string, departmentName: string, linkDepartmentId: string }>();
  }

  async searchLinkCompanyDeptId(params: { departmentIds: string[] }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.getDepartmentName', 'FailIfAnyEmpty');
    const { departmentIds } = params;
    const qb = this.datasource.createQueryBuilder()
      .select([
        'dp.department_id linkDepartmentId'
      ])
      .from(DepartmentEntity, 'd')
      .innerJoin(DepartmentEntity, 'dp', 'd.link_company_id = dp.company_id and d.type = "division" and dp.level = 0 and dp.delete_flag = "N"')
      .where("d.department_id in (:...departmentIds)", { departmentIds })
      .andWhere('d.type = "division"')
      .andWhere('d.link_company_id is not null')
      .groupBy('dp.department_id');
    return await qb.getRawMany<{ linkDepartmentId: string }>();
  }


  async getDirectChildDepartmentList(params: { departmentId: string }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.getDirectChildDepartmentIdList', 'FailIfAnyEmpty');
    const { departmentId } = params;
    const qb = this.datasource.createQueryBuilder()
      .select([
        'd.department_id departmentId',
        'd.link_company_id linkCompanyId',
        'd.type type',
      ])
      .from(DepartmentEntity, 'd')
      .where("d.DELETE_FLAG = 'N'")
      .andWhere("d.parent_department_id = :departmentId", { departmentId });
    return await qb.getRawMany<{ departmentId: string, linkCompanyId: string, type: string }>();
  }

  async getRootDepartment(params: { companyId: string }) {
    DaoUtil.checkEmptyParams(params, DepartmentDao.name + '.getRootDepartment', 'FailIfAnyEmpty');
    const { companyId } = params;
    const qb = this.datasource.createQueryBuilder()
      .select([
        'd.department_id departmentId',
      ])
      .from(DepartmentEntity, 'd')
      .where("d.DELETE_FLAG = 'N'")
      .andWhere("d.company_id = :companyId", { companyId })
      .andWhere("d.level = 0");
    return await qb.getRawOne<{ departmentId: string }>();
  }

}