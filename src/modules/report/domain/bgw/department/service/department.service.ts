import { Injectable } from "@nestjs/common";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DepartmentDao } from "../dao/department.dao";

@Injectable()
export class DepartmentService {
  private readonly logger = new MyLogger(DepartmentService.name)
  constructor(
    private readonly departmentDao: DepartmentDao
  ) { }

  @DevTimeout(500)
  test() {
    // this.getDirectChildDepts({departmentId:'13',includingSelf:true}).then(res=>{
    //   this.logger.log(res.length)
    // })
    // this.getRootDept('65').then(res=>{
    //   this.logger.log(res)
    // })
  }


  /**
   * 获取直接子部门列表
   * @param departmentId 父部门的id
   * @param includingSelf 是否包含父部门本省
   * @returns 
   */
  async getDirectChildDepts({ departmentId, includingSelf }: { departmentId: string, includingSelf?: boolean }) {
    /** 获取 此部门的 父级部门 */
    let parent = await this.departmentDao.get(departmentId)
    if (parent.type === 'division' && parent.linkCompanyId) parent = await this.getRootDept(parent.linkCompanyId);

    const parentPath = parent.dir ? [parent.dir, parent.departmentId].join(',') : parent.departmentId;
    const { items: children } = await this.departmentDao.searchDept({ companyId: parent.companyId, dir: parentPath })
    return includingSelf ? [parent, ...children] : children
  }

  async getRootDept(companyId: string) {
    /** 从对象中提取 items 属性，并将其值赋给变量 root */
    return await this.departmentDao.getRootDept(companyId);
  }
}