import { Injectable } from "@nestjs/common";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { SalesCustomerEntity } from "@src/modules/report/entity/bgw/sales-customer.entity";
import { DataSource } from "typeorm";

@Injectable()
export class SalesCustomerDao {

    constructor(
        private readonly dataSource: DataSource
    ){ }

    async joinSearchList({ids}:{ids: string[]}) {
        const qb = this.dataSource.getRepository(SalesCustomerEntity)
                .createQueryBuilder('sc')
                .select([
                    'sc.SALES_CUSTOMER_ID id',
                    'p.person_id personId',
                    'p.nick_name nickName'
                ])
                .leftJoinAndMapOne('sc.person',PersonEntity,'p','p.person_id = sc.customer_person_id')
                .where('sc.sales_customer_id in (:ids)',{ids})
        return await qb.getRawMany()        
    }
}