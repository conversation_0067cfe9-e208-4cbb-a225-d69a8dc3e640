import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import { ProjectProblem } from "../dto/project-problem.dto";
import { ProjectProblemEntity } from "@src/modules/report/entity/bgw/project-problem.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import * as _ from "lodash";
import { DaoUtil, DevTimeout } from "@yqz/nest";
import { ProblemSolvedEntity } from "@src/modules/report/entity/bgw/problem-solved.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { DeviceProjectInfoEntity } from "@src/modules/report/entity/camera/device-project-info.entity";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { endOfDay, startOfDay } from "date-fns";
import { ProjectProblemDataReport } from "../../../corp/data-report/service/basic/dto/project-problem-data-report.dto";
import { CommonParameterEntity } from "@src/modules/report/entity/bgw/common-parameter.entity";
import { DataReportModuleFilter } from "../../../corp/data-report/dto/data-report-module-filter.dto";

export namespace ProjectProblemDaoDto {
    export class Search {
        idList: string[];
        pageNo?: number;
        pageSize?: number;
        linkCompanyId?: string;
        projectAddress?: string;//工地地址
        deviceDepartmentId?: string;//服务厂商-设备部门id
        decorationModel?: string;//装修方式
        problemStatus?: string;//问题状态
        problemOverdueStatus?: string;//问题逾期状态
        problemType?: string;//问题类型
        problemSource?: string;//问题来源
        problemTimeFrom?: string;//问题发生时间开始
        problemTimeTo?: string;//问题发生时间结束
        problemTime?: DataReportModuleFilter.DateRangeFilterValue;
        problemDeadlineFrom?: string;//问题截止时间开始
        problemDeadlineTo?: string;//问题截止时间结束
        problemDeadline?: DataReportModuleFilter.DateRangeFilterValue;
        problemSolvedTimeFrom?: string;//问题解决时间开始
        problemSolvedTimeTo?: string;//问题解决时间结束
        problemSolvedTime?: DataReportModuleFilter.DateRangeFilterValue;
        overdueStatus?: string;//问题超时状态
        projectManagerId?: string;//工地负责人
    }
}

@Injectable()
export class ProjectProblemDao {
    private readonly logger = new MyLogger(ProjectProblemDao.name)
    constructor(
        private dataSource: DataSource
    ) { }

    @DevTimeout(500)
    test() {

    }

    async searchProjectProblemNum(params: ProjectProblem.Search) {
        const prqb = this.dataSource.createQueryBuilder()
            .select('IFNULL(SUM(pr.problem_status = "1" or pr.problem_status = "2"),0)')
            .from(ProjectProblemEntity, 'pr')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pr.project_id')
            .where('pr.delete_flag = "N"')
            .andWhere('p.delete_flag = "N"')
            .andWhere('p.company_id = :companyId', { companyId: params.companyId })
            .andWhere('pr.problem_status != "0"')
            .andWhere('pr.problem_status != "4"')
        if (!_.isEmpty(params.projectIds))
            prqb.andWhere('pr.project_id in (:...projectIds)', { projectIds: params.projectIds });

        const dlUnsolvedNum = this.dataSource.createQueryBuilder()
            .select('IFNULL(SUM(CASE WHEN ps.id IS NULL and pr.solve_deadline_hours IS NOT NULL AND pr.solve_deadline_hours <= TIMESTAMPDIFF(hour, pr.problem_time, IFNULL(ps.time, NOW())) THEN 1 ELSE 0 END), 0)')
            .from(ProjectProblemEntity, 'pr')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pr.project_id')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pr.problem_solved_id')
            .where('pr.delete_flag = "N"')
            .andWhere('p.delete_flag = "N"')
            .andWhere('p.company_id = :companyId', { companyId: params.companyId })
            .andWhere('pr.problem_status in ("1", "2")')
        if (!_.isEmpty(params.projectIds))
            dlUnsolvedNum.andWhere('pr.project_id in (:...projectIds)', { projectIds: params.projectIds });

        const dltSolvedNum = this.dataSource.createQueryBuilder()
            .select('IFNULL(SUM(CASE WHEN ps.id IS NOT NULL and pr.solve_deadline_hours IS NOT NULL AND pr.solve_deadline_hours <= TIMESTAMPDIFF(hour, pr.problem_time, IFNULL(ps.time, NOW())) AND DATE(ps.time) = CURDATE() THEN 1 ELSE 0 END),0)')
            .from(ProjectProblemEntity, 'pr')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pr.project_id')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pr.problem_solved_id')
            .where('pr.delete_flag = "N"')
            .andWhere('p.delete_flag = "N"')
            .andWhere('p.company_id = :companyId', { companyId: params.companyId })
            .andWhere('pr.problem_status = "3"')
        if (!_.isEmpty(params.projectIds))
            dltSolvedNum.andWhere('pr.project_id in (:...projectIds)', { projectIds: params.projectIds });
        dltSolvedNum.andWhere('pr.problem_time between :startTime and :endTime', { startTime: params.startTime, endTime: params.endTime });

        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(DISTINCT(pp.project_id)) AS projectNum',
                'COUNT(pp.project_problem_id) AS problemNum',
                `(${prqb.getQuery()}) AS unsolvedNum`,
                `(${dlUnsolvedNum.getQuery()}) AS probUnSolvedDeadlineNum`,
                `(${dltSolvedNum.getQuery()}) AS probTodaySolvedDeadlineNum`
            ])
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('p.delete_flag = "N"')
            .andWhere('p.company_id = :companyId', { companyId: params.companyId })
            .andWhere('pp.problem_status != "0"')
            .andWhere('pp.problem_status != "4"')
        if (!_.isEmpty(params.projectIds))
            qb.andWhere('pp.project_id in (:...projectIds)', { projectIds: params.projectIds });
        qb.andWhere('pp.problem_time between :startTime and :endTime', { startTime: params.startTime, endTime: params.endTime });
        qb.setParameters(prqb.getParameters());
        qb.setParameters(dlUnsolvedNum.getParameters());
        qb.setParameters(dltSolvedNum.getParameters());
        return await qb.getRawOne();
    }

    async searchProjectProblem(params: { projectIds: string[], projectSource?: string, startTime?: string, endTime?: string, status?: string }) {
        DaoUtil.checkEmptyParams(params, ProjectProblemDao.name + '.search', 'FailIfAllEmpty');
        const { projectIds, projectSource, startTime, endTime, status } = params || {};
        const qb = this.dataSource
            .createQueryBuilder()
            .select()
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_id in (:...projectIds)', { projectIds })
        if (status) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: status })
        if (startTime) qb.andWhere('pp.problem_time >= :startTime', { startTime });
        if (endTime) qb.andWhere('pp.problem_time < :endTime', { endTime });
        if (status == '3') {
            qb.andWhere('ps.time >= :startTime', { startTime });
            qb.andWhere('ps.time < :endTime', { endTime });
        }
        if (projectSource == 'projectPatrol') qb.andWhere('pp.problem_source = 4');
        const res = await DaoUtil.getPageRes<ProjectProblemEntity>(qb);
        res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
        return res;
    }
    async countDepartmentGroupType(params: ProjectProblemDaoDto.Search) {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select('p.department_id departmentId')
            .addSelect('d.department_name departmentName')
            .addSelect('count(pp.project_problem_id) count')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .leftJoin(DepartmentEntity, 'd', 'd.department_id = p.department_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id in (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('p.department_id')
        const res = await qb.getRawMany<{ departmentId: string, departmentName: string, count: string }>()
        return res;
    }
    async countDeviceDepartmentGroupType(params: ProjectProblemDaoDto.Search) {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];
        // 子查询：获取每个 project_id 对应的最新记录
        const subQuery = this.dataSource.createQueryBuilder()
            .select([
                'dpi.project_id AS project_id',
                'dpi.department_id AS department_id'
            ])
            .from(DeviceProjectInfoEntity, 'dpi')
            .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
            .where('dpi.delete_flag = "N"')
            .andWhere('pp.project_problem_id in (:...ids)', { ids: params.idList });
        subQuery.andWhere(qb => {
            const subQuery = qb.subQuery()
                .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                .from(DeviceProjectInfoEntity, 'dpi2')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi2.project_id')
                .where('dpi2.project_id = dpi.project_id')
                .andWhere('dpi2.delete_flag = "N"')
                .andWhere('pp.project_problem_id in (:...ids)', { ids: params.idList });
            return `dpi.project_binding_time = (${subQuery.getQuery()})`;
        });
        subQuery.groupBy('dpi.project_id');
        // 主查询：关联子查询并完成统计
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(1) as count',
                'dpd.department_id as deviceDepartmentId',
                'dep.department_name as deviceDepartmentName'
            ])
            .from(ProjectProblemEntity, 'pp')
            .innerJoin(ProjectEntity, 'p', 'pp.project_id = p.project_id')
            .leftJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            .leftJoin(DepartmentEntity, 'dep', 'dpd.department_id = dep.department_id')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('pp.project_problem_id in (:...ids)', { ids: params.idList })
            .groupBy('dpd.department_id')
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
        qb.setParameters(subQuery.getParameters());
        return await qb.getRawMany<{ count: string, deviceDepartmentId: string, deviceDepartmentName: string }>();
    }

    async countProjectProblem(params: ProjectProblemDaoDto.Search) {
        const { idList } = params;
        if (_.isEmpty(idList)) return { count: '0' };
        const qb = this.dataSource.createQueryBuilder()
            .select(['count(1) as count'])
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id in (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        return await qb.getRawOne<{ count: string }>();
    }

    /**
     * 统计问题状态分布
     * @param params 
     * @returns 
     */
    async countStatusGroupType(params: ProjectProblemDaoDto.Search): Promise<{ problemStatus: string, count: string }[]> {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];

        const qb = this.dataSource.createQueryBuilder()
            .select('pp.problem_status', 'problemStatus')
            .addSelect('COUNT(pp.project_problem_id)', 'count')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('pp.problem_status');
        const res = await qb.getRawMany();
        return res;
    }

    /**
     * 统计装修方式分布
     * @param params 
     * @returns 
     */
    async countDecorationModelGroupType(params: ProjectProblemDaoDto.Search): Promise<{ decorationModelName: string, decorationModel: string, count: string }[]> {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .addSelect('p.contract', 'decorationModel')
            .addSelect('COUNT(pp.project_problem_id)', 'count')
            .addSelect('cp.parameter_name', 'decorationModelName')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .leftJoin(CommonParameterEntity, 'cp', 'cp.parameter_type = "CONTRACT" and cp.parameter_code = p.contract')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('p.contract');
        const res = await qb.getRawMany();
        return res;
    }

    /**
     * 统计问题分类分布
     * @param params 
     * @returns 
     */
    async countTypeGroupType(params: ProjectProblemDaoDto.Search): Promise<{ problemType: string, count: string }[]> {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];

        const qb = this.dataSource.createQueryBuilder()
            .select('pp.problem_type', 'problemType')
            .addSelect('COUNT(pp.project_problem_id)', 'count')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('pp.problem_type');
        const res = await qb.getRawMany();
        return res;
    }

    /**
     * 统计问题来源分布
     * @param params 
     * @returns 
     */
    async countSourceGroupType(params: ProjectProblemDaoDto.Search): Promise<{ problemSource: string, count: string }[]> {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];

        const qb = this.dataSource.createQueryBuilder()
            .select('pp.problem_source', 'problemSource')
            .addSelect('COUNT(pp.project_problem_id)', 'count')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('pp.problem_source');
        const res = await qb.getRawMany();
        return res;
    }

    async countProjectManagerGroupType(params: ProjectProblemDaoDto.Search): Promise<{ projectManagerId: string, projectManagerName: string, count: string }[]> {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select('p.project_manager', 'projectManagerId')
            .addSelect('m.nick_name', 'projectManagerName')
            .addSelect('COUNT(pp.project_problem_id)', 'count')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('pp.project_problem_id IN (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('p.project_manager');
        return qb.getRawMany();
    }

    /**
     * 统计问题超时状态分布
     */
    async countOverdueStatusGroupType(params: ProjectProblemDaoDto.Search): Promise<{ overdueStatus: string, count: string }[]> {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select([
                `case when pp.solve_deadline_time is not null and pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2) then 'deadlineUnsolved'
                      when pp.solve_deadline_time is not null and pp.solve_deadline_time < ps.time AND pp.problem_status = 3 then 'deadlineSolved'
                      when pp.solve_deadline_time is not null and pp.solve_deadline_time >= ps.time AND pp.problem_status = 3 then 'solved'
                      when pp.solve_deadline_time is not null and pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2) then 'noDeadline'
                      else ''
                end as overdueStatus`,
                'count(pp.project_problem_id) AS count'
            ])
            .addSelect('COUNT(pp.project_problem_id)', 'count')
            .from(ProjectProblemEntity, 'pp')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList })
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('overdueStatus');
        return qb.getRawMany<{ overdueStatus: ProjectProblemDataReport.OverdueStatus, count: string }>();
    }

    async searchProjectProblemProjectList(params: ProjectProblemDaoDto.Search) {
        const { idList } = params;
        if (_.isEmpty(idList)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'p.project_id as projectId'
            ])
            .from(ProjectProblemEntity, 'pp')
            .innerJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = pp.problem_record_person')
            .leftJoin(PersonEntity, 'owner', 'owner.person_id = pp.problem_record_owner')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('p.project_id');
        return qb.getRawMany<{ projectId: string }>();
    }

    async searchList(params: ProjectProblemDaoDto.Search) {
        const { idList } = params;
        if (_.isEmpty(idList)) return { total: 0, items: [] };
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pp.project_problem_id AS id',
                'pp.problem_status AS status',
                `case when pp.problem_status IN (1, 2) and pp.solve_deadline_time < NOW() then 'deadlineUnsolved'
                      when pp.problem_status = 3 and pp.solve_deadline_time < ps.time then 'deadlineSolved'
                      when pp.problem_status = 3 and pp.solve_deadline_time >= ps.time then 'solved'
                      when pp.problem_status IN (1, 2) and pp.solve_deadline_time >= NOW() then 'noDeadline'
                end as overdueStatus`,
                'pp.problem_description AS description',
                'pp.problem_img AS problemImageIds',
                'pp.problem_type AS problemType',
                'pp.problem_source AS problemSource',
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, pp.problem_record_owner, m.person_id) reportPersonId",
                "if(pp.problem_source = '3' and pp.problem_record_person is null and pp.problem_record_owner is not null, owner.nick_name, m.nick_name) reportPerson",
                'pp.problem_time AS problemTime',
                'ps.time AS solvedTime',
                'pp.solve_deadline_time AS solveDeadlineTime',
                `case when pp.problem_status = 3 then TIMESTAMPDIFF(MINUTE, pp.problem_time, ps.time)
                      when pp.problem_status in (1, 2) then TIMESTAMPDIFF(MINUTE, pp.problem_time, now())
                      end AS solveDuration`,
                'ps.description AS solvedDescription',
                'ps.image AS solvedImageIds',
                'p.CONTRACT as decorationModel',
                'p.project_id as projectId',
                'p.company_id as linkCompanyId',
                'pp.wrongdoer_face_id as wrongdoerFaceId',
                'pp.solve_deadline_hours AS solveDeadlineHours',
            ])
            .from(ProjectProblemEntity, 'pp')
            .innerJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .leftJoin(ManagerEntity, 'm', 'm.manager_id = pp.problem_record_person')
            .leftJoin(PersonEntity, 'owner', 'owner.person_id = pp.problem_record_owner')
            .leftJoin(ProblemSolvedEntity, 'ps', 'ps.id = pp.problem_solved_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id IN (:...idList)', { idList });
        //所属公司
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });
        // 添加筛选条件
        if (params.decorationModel) qb.andWhere('p.contract = :decorationModel', { decorationModel: params.decorationModel });
        if (params.problemStatus) qb.andWhere('pp.problem_status = :problemStatus', { problemStatus: params.problemStatus });
        if (params.problemType) qb.andWhere('pp.problem_type = :problemType', { problemType: params.problemType });
        if (params.problemSource) qb.andWhere('pp.problem_source = :problemSource', { problemSource: params.problemSource });
        if (params.problemTimeFrom && params.problemTimeTo) qb.andWhere('pp.problem_time BETWEEN :problemTimeFrom AND :problemTimeTo', { problemTimeFrom: startOfDay(new Date(params.problemTimeFrom)), problemTimeTo: endOfDay(new Date(params.problemTimeTo)) });
        if (params?.problemTime?.from && params?.problemTime?.to) qb.andWhere('pp.problem_time >= :problemTimeFromTime and pp.problem_time <= :problemTimeToTime', { problemTimeFromTime: startOfDay(new Date(params.problemTime.from)), problemTimeToTime: endOfDay(new Date(params.problemTime.to)) });
        if (params.problemDeadlineFrom && params.problemDeadlineTo) qb.andWhere('pp.solve_deadline_time BETWEEN :problemDeadlineFrom AND :problemDeadlineTo', { problemDeadlineFrom: startOfDay(new Date(params.problemDeadlineFrom)), problemDeadlineTo: endOfDay(new Date(params.problemDeadlineTo)) });
        if (params?.problemDeadline?.from && params?.problemDeadline?.to) qb.andWhere('pp.solve_deadline_time >= :problemDeadlineFromTime and pp.solve_deadline_time <= :problemDeadlineToTime', { problemDeadlineFromTime: startOfDay(new Date(params.problemDeadline.from)), problemDeadlineToTime: endOfDay(new Date(params.problemDeadline.to)) });
        if (params.problemSolvedTimeFrom && params.problemSolvedTimeTo) qb.andWhere('ps.time BETWEEN :problemSolvedTimeFrom AND :problemSolvedTimeTo', { problemSolvedTimeFrom: startOfDay(new Date(params.problemSolvedTimeFrom)), problemSolvedTimeTo: endOfDay(new Date(params.problemSolvedTimeTo)) });
        if (params?.problemSolvedTime?.from && params?.problemSolvedTime?.to) qb.andWhere('ps.time >= :problemSolvedTimeFromTime and ps.time <= :problemSolvedTimeToTime', { problemSolvedTimeFromTime: startOfDay(new Date(params.problemSolvedTime.from)), problemSolvedTimeToTime: endOfDay(new Date(params.problemSolvedTime.to)) });
        if (params.projectAddress) {
            qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
            qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
        }
        if (params.problemOverdueStatus) {
            qb.andWhere('pp.solve_deadline_time is not null');
            if (params.problemOverdueStatus === 'deadlineUnsolved') {//超时未整改
                qb.andWhere('pp.solve_deadline_time < NOW() AND pp.problem_status IN (1, 2)');
            } else if (params.problemOverdueStatus === 'deadlineSolved') {//超时已整改
                qb.andWhere('pp.solve_deadline_time < ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'solved') {//按时整改
                qb.andWhere('pp.solve_deadline_time >= ps.time AND pp.problem_status = 3');
            } else if (params.problemOverdueStatus === 'noDeadline') {//未超时
                qb.andWhere('pp.solve_deadline_time >= NOW() AND pp.problem_status IN (1, 2)');
            }
        }
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
        //服务厂商（设备归属）
        if (params.deviceDepartmentId) {
            // 子查询：获取每个 project_id 对应的最新记录
            const subQuery = this.dataSource.createQueryBuilder()
                .select([
                    'dpi.project_id AS project_id',
                    'dpi.department_id AS department_id'
                ])
                .from(DeviceProjectInfoEntity, 'dpi')
                .innerJoin(ProjectProblemEntity, 'pp', 'pp.project_id = dpi.project_id')
                .where('dpi.delete_flag = "N"')
                .andWhere('pp.project_problem_id IN (:...idList)', { idList });
            subQuery.andWhere(qb => {
                const subQuery = qb.subQuery()
                    .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                    .from(DeviceProjectInfoEntity, 'dpi2')
                    .where('dpi2.project_id = dpi.project_id')
                    .andWhere('dpi2.delete_flag = "N"');
                return `dpi.project_binding_time = (${subQuery.getQuery()})`;
            });
            subQuery.groupBy('dpi.project_id');
            qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
            qb.setParameters(subQuery.getParameters());
        }
        qb.groupBy('pp.project_problem_id');
        qb.orderBy('pp.problem_time', 'DESC');
        DaoUtil.processPageAndSort(qb, params);
        return await DaoUtil.getPageRes<{ id: string, status: string, overdueStatus: string, description: string, problemImageIds: string, problemType: string, problemSource: string, reportPersonId: string, reportPerson: string, problemTime: string, solveDeadlineTime: string, solvedTime: string, solveDuration: string, solvedDescription: string, decorationModel: string, projectId: string, linkCompanyId: string, wrongdoerFaceId: string, solvedImageIds: string, solveDeadlineHours: string }>(qb);
    }

    async countProblemGroupDeviceDepartment(params: { idList: string[], projectManagerId?: string, linkCompanyId?: string, status?: string, }) {
        //idList为空直接返回
        if (_.isEmpty(params.idList)) return [];
        // 子查询：获取每个 project_id 对应的最新记录
        const subQuery = this.dataSource.createQueryBuilder()
            .select([
                'dpi.project_id AS project_id',
                'dpi.department_id AS department_id'
            ])
            .from(ProjectProblemEntity, 'pp')
            .innerJoin(DeviceProjectInfoEntity, 'dpi', 'dpi.project_id = pp.project_id')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_problem_id in (:...ids)', { ids: params.idList });
        subQuery.andWhere(qb => {
            const subQuery = qb.subQuery()
                .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
                .from(DeviceProjectInfoEntity, 'dpi2')
                .innerJoin(ProjectProblemEntity, 'pp2', 'pp2.project_id = dpi2.project_id')
                .where('dpi2.project_id = dpi.project_id')
                .andWhere('dpi2.delete_flag = "N"')
                .andWhere('pp2.project_problem_id in (:...ids)', { ids: params.idList });
            return `dpi.project_binding_time = (${subQuery.getQuery()})`;
        });
        subQuery.groupBy('dpi.project_id');
        // 主查询：关联子查询并完成统计
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(1) as count',
                'dpd.department_id as deviceDepartmentId',
                'dep.department_name as deviceDepartmentName'
            ])
            .from(ProjectProblemEntity, 'pp')
            .innerJoin(ProjectEntity, 'p', 'p.project_id = pp.project_id')
            .innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
            .innerJoin(DepartmentEntity, 'dep', 'dpd.department_id = dep.department_id')
            .where('pp.project_problem_id in (:...ids)', { ids: params.idList });
        if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
        if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司

        qb.groupBy('dpd.department_id');
        qb.setParameters(subQuery.getParameters());
        return await qb.getRawMany<{ count: string, deviceDepartmentId: string, deviceDepartmentName: string }>();
    }

    async searchProblemProjectList({ projectIds, startTime, endTime }: { projectIds: string[], startTime: string, endTime: string }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(1) as count',
                'pp.project_id as projectId',
            ])
            .from(ProjectProblemEntity, 'pp')
            .where('pp.delete_flag = "N"')
            .andWhere('pp.project_id IN (:...ids)', { ids: projectIds })
            .andWhere('pp.problem_time >= :startTime', { startTime })
            .andWhere('pp.problem_time < :endTime', { endTime })
            .groupBy('pp.project_id');
        return await qb.getRawMany<{ projectId: string, count: string }>();
    }

}