import { Injectable } from '@nestjs/common';
import { MyLogger } from '@yqz/nest';
import * as _ from "lodash";
import { ManagerDao } from '../dao/manager.dao';
import { Manager } from '../dto/manager.dto';
import { MyMongoDb } from '@src/modules/report/mongodb/mongodb.connection';
import { ObjectId } from 'mongodb';

@Injectable()
export class ManagerService {
    private readonly logger = new MyLogger(ManagerService.name)

    constructor(
        private managerDao: ManagerDao,
    ) { }

    // @DevTimeout(1000)
    async test() {
        // const res = await this.searchCompanyInfoMap({ companyIds: ['65'] });
        // console.log("🚀 ~ CompanyService ~ test ~ res:", res)
    }

    async searchFaceInfoMap({ faceIdList }: { faceIdList: string[] }): Promise<Record<string, { name: string, nickName: string }>> {
        if (_.isEmpty(faceIdList)) return {};
        const faces = await MyMongoDb.collections.camera.face.find({ _id: { $in: faceIdList.map(faceId => new ObjectId(faceId)) } }).toArray();
        const result: Record<string, { name: string, nickName: string }> = {};
        faces.forEach(face => { result[face._id.toHexString()] = { name: face.name, nickName: face.name }; });
        return result;
    }

    async searchManagerInfoMap({ managerIdList }: { managerIdList: string[] }): Promise<Record<string, Manager.BelongToListByDeptIdRes>> {
        if (_.isEmpty(managerIdList)) return {};
        const managers = await this.managerDao.getBelongToListByDeptId({ managerIdList });
        const result: Record<string, Manager.BelongToListByDeptIdRes> = {};
        managers.forEach(manager => { result[manager.managerId] = manager; });
        return result;
    }

}
