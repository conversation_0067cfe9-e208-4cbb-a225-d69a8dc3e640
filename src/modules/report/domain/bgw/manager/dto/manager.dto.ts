import { ApiProperty } from "@nestjs/swagger";
import { BaseReportReq } from "@src/types/report-req.type";

export namespace Manager {
    
    export type Dto = {
        personId: string;
        companyId: string;
        managerId:string;
        managerType: string;
        isShow: 'Y'| 'N',
        roleId: string;
        pageViewCount: number;
        receiveMessage: 'Y'| 'N',
        groupChat: 'Y'| 'N',
        receiveOrders: 'Y'| 'N',
        lastUpdatetime: Date;
        orderAdmin: 'Y'| 'N',
        contentAdmin: 'Y'| 'N',
        nickName: '空木之穹',
        profilePhoto: string;
        personResume: string,
        baiduAdmin: 'Y'| 'N',
        deviceAdmin: 'Y'| 'N',
        loginFlag: 'Y'| 'N',
        totalPageView: number;
        createTime: Date;
        createBy: string;
        updateTime: Date;
        updateBy: string;
        deleteFlag: 'Y'| 'N';
    }
    export class SearchReq extends BaseReportReq<Dto>{
        companyId:string
    }
    export class SearchRole {
        @ApiProperty()
        companyId: string;
        @ApiProperty()
        managerId: string;
        @ApiProperty()
        personId: string;
        @ApiProperty()//是否是公司管理员
        isCompanyAdmin: string;
        @ApiProperty()//是否是设备管理员
        isDeviceAdmin: string;
    }

    export class BelongToListByDeptIdRes {
        companyId: string;
        personId: string;
        managerId: string;
        nickName: string;
        departmentId: string;
        deleteFlag: string;
        roleId: string;
        roleName: string;
        name: string;
        departmentName: string;
        personDepartmentName: string;
        personDepartmentId: string;
    }

}