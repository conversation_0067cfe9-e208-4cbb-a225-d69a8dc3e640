import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { ProjectReportEntity } from "@src/modules/report/entity/bgw/project-report.entity";
import { DaoUtil, DevTimeout } from "@yqz/nest";
import { DataSource } from "typeorm";
import { Manager } from "../dto/manager.dto";
import { DeleteFlag } from "@yqz/nest/lib/types/delete-flag.enum";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { CmsPhotoEntity } from "@src/modules/report/entity/bgw/cms-photo.entity";
import { DepartmentMemberEntity } from "@src/modules/report/entity/bgw/department-member.entity";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import * as _ from "lodash";

@Injectable()
export class ManagerDao {
  private readonly logger = new MyLogger(ManagerDao.name)
  constructor(
    private dataSource: DataSource
  ) { }

  @DevTimeout(500)
  async test() {
    // let res = await this.searchManagerRole();
    // console.log(res);
    // this.search({companyId:'65'}).then(res=>{
    //   this.logger.log(res.total, res.items.length)
    //   this.logger.log(res.items[0])
    // })
  }

  async search(params: Manager.SearchReq) {
    DaoUtil.checkEmptyParams(params, ManagerDao.name + 'search', 'FailIfAllEmpty');
    const { companyId } = params || {}

    const qb = this.dataSource
      .createQueryBuilder()
      .from(ManagerEntity, 'm')
      .where('delete_flag = :delFlag')
      .setParameters({ delFlag: "N" })
    if (companyId)
      qb.andWhere('company_id = :companyId', { companyId })

    DaoUtil.processPageAndSort(qb, params)

    const res = await DaoUtil.getPageRes(qb)
    res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e))
    return res

  }

  async searchReportManagerInfo({ reportDate, companyId, managerId }: { reportDate: string, companyId?: string, managerId?: string }): Promise<Manager.SearchRole[]> {
    const prqb = this.dataSource.createQueryBuilder()
      .select('COUNT(1)')
      .from(ProjectReportEntity, 'pr')
      .where('pr.delete_flag = "N"')
      .andWhere('pr.COMPANY_ID = m.COMPANY_ID')
      .andWhere('pr.MANAGER_ID = m.MANAGER_ID')
      .andWhere('pr.report_date = :reportDate', { reportDate: reportDate });

    const qb = this.dataSource.getRepository(ManagerEntity)
      .createQueryBuilder('m')
      .select([
        'm.MANAGER_ID AS managerId',
        'm.person_id AS personId',
        'm.COMPANY_ID AS companyId',
        'IF(m.MANAGER_TYPE = "1","Y","N") AS isCompanyAdmin', //是否是公司管理员
        'IF(m.device_admin = "Y","Y","N") AS isDeviceAdmin',//是否是设备管理员
      ])
      .leftJoin(CompanyEntity, 'c', 'c.COMPANY_ID =m.COMPANY_ID')
      .where('m.delete_flag  = :delFlag', { delFlag: 'N' })
      .andWhere('c.delete_flag  = :delFlag', { delFlag: 'N' })
      .andWhere('m.MANAGER_TYPE != "5"')
      .andWhere('c.COMPANY_TYPE != "4"')
      .andWhere(`(${prqb.getQuery()}) = 0 `)
    if (companyId) qb.andWhere('m.COMPANY_ID = :companyId', { companyId });
    if (managerId) qb.andWhere('m.MANAGER_ID = :managerId', { managerId });
    qb.setParameters(prqb.getParameters());
    return await qb.getRawMany();
  }

  async searchManagerList(param: { managerId?: string, managerIdList?: string[], includeDel?: boolean }) {
    const qb = this.dataSource.getRepository(ManagerEntity).createQueryBuilder('m')
      .select([
        'm.manager_id id',
        'm.nick_name name',
        'cmr.role_name as roleName',
        'cp.oss_url as avatarUrl',
        'p.PERSON_MOBILE as mobile',
        'd.department_name as departmentName'
      ])
      .leftJoin(CompanyMemberRoleEntity, 'cmr', 'cmr.company_member_role_id = m.role_id')
      .leftJoin(CmsPhotoEntity, 'cp', 'cp.photo_id = m.profile_photo')
      .leftJoin(PersonEntity,'p','p.person_id = m.person_id')
      .leftJoin(DepartmentMemberEntity,'dm','dm.manager_id = m.manager_id')
      .innerJoin(DepartmentEntity,'d','d.department_id = dm.department_id')
    if (!param?.includeDel) qb.where('m.delete_flag = "N"');//不传或传false -> 查询未删除
    if (param.managerId) qb.andWhere('m.manager_id = :managerId', { managerId: param.managerId })
    if (param.managerIdList && param.managerIdList.length > 0) qb.andWhere('m.manager_id in (:managerIdList)', { managerIdList: param.managerIdList })
    qb.groupBy('m.manager_id').orderBy('m.manager_id', 'ASC');
    return await qb.getRawMany<{ id: string, name: string, roleName: string, avatarUrl: string, mobile: string, departmentName: string }>();
  }

  async find(managerId: string) {
    return await this.dataSource.manager.findOne(ManagerEntity, {
      where: {
        DELETE_FLAG: DeleteFlag.N,
        MANAGER_ID: managerId
      }
    })
  }

  async getUserBasicInfoById(managerId: string) {
    const qb = this.dataSource.getRepository(ManagerEntity)
      .createQueryBuilder('m')
      .select([
        'm.nick_name as nickname',
        'p.PERSON_NAME as name',
        'p.PERSON_MOBILE as mobile'
      ])
      .leftJoin(PersonEntity, 'p', 'p.person_id = m.person_id')
      .where('m.delete_flag = :delFlag', { delFlag: 'N' })
      .andWhere('m.manager_id = :managerId', { managerId })
    return await qb.getRawOne<{ nickname: string, name: string, mobile: string }>();
  }

  /**
   * 获取成员归属信息
   * 成员没离职 最早加入的部门
   * 成员离职了 最晚加入的部门
   * @param param0 
   * @returns 
   */
  async getBelongToListByDeptId(query: { companyId?: string, companyIdList?: string[], departmentIds?: string[], managerIdList?: string[], managerRoles?: string[], isEnableLabelYQZ?: boolean, nickName?: string }) {
    DaoUtil.checkEmptyParams(query, 'manager.dao:getBelongToListByDeptId', 'FailIfAllEmpty');
    const { companyId, companyIdList,departmentIds, managerIdList, managerRoles, isEnableLabelYQZ = false, nickName } = query;
    const selects = [
      "m.person_id as person_id",
      "m.MANAGER_ID as MANAGER_ID",
      "m.nick_name as nick_name",
      "m.delete_flag as delete_flag",
      "m.role_id as role_id",
      "m.company_id as company_id",
      'person.name as name',
      'role.role_name as role_name'
    ]
    const manager1 = this.dataSource.createQueryBuilder()
      .select([
        ...selects,
        "SUBSTRING_INDEX(GROUP_CONCAT(dm.department_id order by dm.createTime asc, dm.department_member_id asc), ',', 1) AS department_id"
      ])
      .from(ManagerEntity, 'm')
      .innerJoin(DepartmentMemberEntity, 'dm', 'm.manager_id = dm.manager_id')
      .innerJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
      .innerJoin(PersonEntity, 'person', 'person.person_id = m.person_id')
      .where("dm.delete_flag = 'N'")
      .andWhere("m.delete_flag = 'N'")
    if (isEnableLabelYQZ) manager1.andWhere("!FIND_IN_SET('一起装',person.PERSON_LABEL)")

    if (companyId) manager1.andWhere('m.COMPANY_ID = :companyId', { companyId });
    if (!_.isEmpty(companyIdList)) manager1.andWhere('m.COMPANY_ID IN (:...companyIdList)', { companyIdList });
    if (!_.isEmpty(managerIdList)) manager1.andWhere('m.manager_id in (:...managerIdList)', { managerIdList });
    if (!_.isEmpty(managerRoles)) manager1.andWhere('role.role_name IN (:...managerRoles)', { managerRoles });
    if (nickName) manager1.andWhere('m.nick_name like :nickName', { nickName: `%${nickName}%` });
    manager1.groupBy('m.MANAGER_ID')

    const manager2 = this.dataSource.createQueryBuilder()
      .select([
        ...selects,
        "SUBSTRING_INDEX(GROUP_CONCAT(dm.department_id order by dm.createTime desc, dm.department_member_id desc), ',', 1) AS department_id"
      ])
      .from(ManagerEntity, 'm')
      .innerJoin(DepartmentMemberEntity, 'dm', 'm.manager_id = dm.manager_id')
      .innerJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
      .innerJoin(PersonEntity, 'person', 'person.person_id = m.person_id')
      .where("dm.delete_flag = 'Y'")
      .andWhere("m.delete_flag = 'Y'")
    if (isEnableLabelYQZ) manager2.andWhere("!FIND_IN_SET('一起装',person.PERSON_LABEL)")

    if (companyId) manager2.andWhere('m.COMPANY_ID = :companyId', { companyId });
    if (!_.isEmpty(companyIdList)) manager2.andWhere('m.COMPANY_ID IN (:...companyIdList)', { companyIdList });
    if (!_.isEmpty(managerIdList)) manager2.andWhere('m.manager_id in (:...managerIdList)', { managerIdList });
    if (!_.isEmpty(managerRoles)) manager2.andWhere('role.role_name IN (:...managerRoles)', { managerRoles });
    if (nickName) manager2.andWhere('m.nick_name like :nickName', { nickName: `%${nickName}%` });
    manager2.groupBy('m.MANAGER_ID')

    const qb = this.dataSource
      .createQueryBuilder()
      .select([
        "dep.company_id as companyId",
        'dep.person_id as personId',
        'dep.MANAGER_ID as managerId',
        'dep.nick_name as nickName',
        'dep.department_id as departmentId',
        'dep.delete_flag as deleteFlag',
        'dep.role_id as roleId',
        'dep.role_name as roleName',
        'dep.name as name',
        'd.department_name as departmentName'
      ])
      .from('((' + manager1.getQuery() + ') UNION (' + manager2.getQuery() + '))', 'dep')
      .innerJoin(DepartmentEntity, 'd', 'dep.department_id = d.department_id ');
    // if (departmentId) qb.where("(d.department_id = :departmentId OR FIND_IN_SET(:departmentId, d.dir))", { departmentId });
    if (!_.isEmpty(departmentIds)) qb.andWhere('d.department_id IN (:...departmentIds)', { departmentIds });
    qb.setParameters(manager1.getParameters())
    qb.setParameters(manager2.getParameters())
    // console.log(qb.getQueryAndParameters())
    return await qb.getRawMany<Manager.BelongToListByDeptIdRes>();
  }

  async getProjectManagerInfoByRoleIds({ companyId, roleIds }: { companyId: string, roleIds: string[] }) {
    if (!companyId || _.isEmpty(roleIds)) return [];
    const qb = this.dataSource.getRepository(ManagerEntity).createQueryBuilder('m')
      .select([
        'm.manager_id as managerId',
        'm.nick_name as nickName',
        'role.COMPANY_MEMBER_ROLE_ID as roleId'
      ])
      .innerJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
      .where('m.company_id = :companyId', { companyId })
      .andWhere('role.COMPANY_MEMBER_ROLE_ID in (:...roleIds)', { roleIds })
      .andWhere('m.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
      .groupBy('m.manager_id')
    return await qb.getRawMany<{ managerId: string, nickName: string, roleId: string }>();
  }

}