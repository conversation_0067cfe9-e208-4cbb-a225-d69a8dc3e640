import { Body, Controller, Post, UseInterceptors } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtUserGw, MyLogger, MyUser, TransformInterceptor } from "@yqz/nest";
import { ProjectUpdateService } from "./service/project-update.service";
import { Analytics } from "./type/analytics-util.type";
import { CompanyService } from "../../bgw/company/service/company.service";

@ApiTags("project/update")
@Controller('project/update')
// @UseInterceptors(TransformInterceptor)
export class ProjectUpdateController {
    private readonly logger = new MyLogger(ProjectUpdateController.name);
    constructor(
        private readonly projectUpdateService: ProjectUpdateService,
        private readonly companyService: CompanyService,
    ) { }

    @ApiTags("analytics")
    @ApiOperation({ summary: '工地更新数据报表统计' })
    @Post("analytics/total")
    async projectUpdateAnalyticsTotal(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.projectUpdateService.projectUpdateAnalyticsTotal(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '工地更新数据报表岗位分布' })
    @Post("analytics/position")
    async projectUpdateAnalyticsPosition(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.projectUpdateService.projectUpdateAnalyticsPosition(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '工地更新数据报表员工分布' })
    @Post("analytics/person")
    async projectUpdateAnalyticsPerson(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.projectUpdateService.projectUpdateAnalyticsPerson(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '工地更新数据报表数据明细' })
    @Post("analytics/data/detail")
    async projectUpdateAnalyticsDataDetail(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        body.companyType = await this.companyService.searchCompanyType({ companyId: body.companyId });
        return await this.projectUpdateService.projectUpdateAnalyticsDataDetail(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '工地更新数据报表选项' })
    @Post("analytics/options")
    async projectUpdateAnalyticsOptions(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.projectUpdateService.projectUpdateAnalyticsOptions(body);
    }

}