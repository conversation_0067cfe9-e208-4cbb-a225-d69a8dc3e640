import { ApiProperty } from "@nestjs/swagger";
import { UtilType } from "../../../bgw/types/util.type";
import { Company } from "../../../bgw/company/dto/company.dto";
import { Common } from "@yqz/nest";

export namespace Analytics {
    export class Req {
        companyType?: Company.CompanyType;//公司类型
        @ApiProperty({ description: '返回格式' })
        format?: 'csv';
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        linkCompanyId?: string;
        companyIds?: string[];
        @ApiProperty({ description: '操作人managerId（token中的）' })
        operateManagerId?: string;
        @ApiProperty({ description: '筛选departmentId' })
        departmentId?: string;
        @ApiProperty({ description: '筛选managerId' })
        managerId?: string;
        nickName?: string;
        @ApiProperty({ description: '筛选工地地址' })
        projectAddress?: string;
        @ApiProperty({ description: '筛选岗位' })
        roleId?: string;
        roleName?: string;
        roleNames?: string[];
        @ApiProperty({ description: '筛选开始时间' })
        from?: string;
        @ApiProperty({ description: '筛选结束时间' })
        to?: string;
        pageNo?: number;
        pageSize?: number;
        projectIds?: string[];
        managerIdList?: string[];
        departmentIds?: string[];
        isPatroled?: string;
        projectDirectorId?: string;
        @ApiProperty({ description: '数据基于字段分组' })
        groupBy?: 'role' | 'manager';
        @ApiProperty({ description: '签到方式' })
        patrolSignStyle?: string[];
        signInStyle?: string[];
        noPatrolProjectShow?: string;
    }

    export const signInStyleMap = {
        "device": "face",
        "app": "lbs",
        "face": "face",
        "lbs": "lbs"
    }

    export const signInTypeNameMap: Readonly<{ [key: string]: string }> = {
        "face": "摄像头签到",
        "lbs": "app签到"
    }


    export type AnalyticsPersonType = {
        managerId: string,
        nickName?: string,
        personDepartmentId?: string,
        personDepartmentName?: string,
        projectIdList: string[],
        projectIds?: string,
        totalProject: number,
        totalPatrol: number,
        unpatroledCount?: number
        unpatroledProjectIdList?: string[],
    }

    export type AnalyticsPositionType = {
        managerId?: string;
        roleId?: string,
        roleName: string,
        managerIdList?: string[],
        totalManager: number,
        projectIdList?: string[],
        totalProject: number,
        totalPatrol: number
    }

    export type AnalyticsPersonRes = {
        addressSelects?: string[],
        analyticPersons: AnalyticsPersonType[]
    }

    export type analyticsPositionRes = {
        dealPosition?: string,
        totalPosition: number,
        dealManager?: string,
        totalManager: number,
        totalProject: number,
        totalPatrol: number,
        analyticsPositions: AnalyticsPositionType[]
    }

    export type analyticsOptionsRes = {
        positionList: { roleId: string, roleName: string }[],
        personList: { managerId: string, nickName: string }[],
        linkCompany?: {name: string, value: string}[]
    }

    export class AnalyticsList {
        patrolTime: string; // 巡检时间
        projectPatrolId: string;  // 巡检 id 方便跳转

        projectId: string;  // 所属工地
        projectName: string;  // 工地名称
        houseNumber?: string; // 最终地址
        communityName?: string; // 最终地址
        address?: string; // 最终地址

        managerId: string;  // 管理者id
        nickName?: string;  // 管理者名称 

        roleId?: string;
        roleName?: string;  // 岗位名称

        departmentName: string; // 部门名称;
        departmentId: string; // 部门id;
    }


    export const csvList: UtilType.propertyCsv[] = [
        {
            key: "linkCompanyName",
            value: "所属公司"
        }, {
            key: "departmentName",
            value: "员工部门"
        }, {
            key: "roleName",
            value: "岗位"
        }, {
            key: "nickName",
            value: "员工昵称"
        }, {
            key: "address",
            value: "巡检工地"
        }, {
            key: "patrolTime",
            value: "巡检时间"
        }, {
            key: "projectStartTime",
            value: "开工时间"
        }, {
            key: "projectCompletedTime",
            value: "完工时间"
        }
    ]

    export const csvOnsitePatrolList: UtilType.propertyCsv[] = [
        {
            key: "linkCompanyName",
            value: "所属公司"
        }, {
            key: "personDepartmentName",
            value: "员工部门"
        }, {
            key: "roleName",
            value: "岗位"
        }, {
            key: "nickName",
            value: "员工昵称"
        }, {
            key: "projectAddress",
            value: "巡检工地"
        }, {
            key: "patrolTime",
            value: "巡检日期"
        }, {
            key: "joinTime",
            value: "入场时间"
        }, {
            key: "exitTime",
            value: "离场时间"
        }, {
            key: "stayTime",
            value: "出勤时长"
        }, {
            key: "signInStyle",
            value: "签到方式"
        }, {
            key: "projectDirector",
            value: "工地负责人"
        }, {
            key: "isBindDevice",
            value: "是否绑定设备",
            options: {
                values: [
                    {
                        key: Common.Flag.N,
                        value: "否"
                    },
                    {
                        key: Common.Flag.Y,
                        value: "是"
                    }
                ]
            }
        }, {
            key: "projectStartTime",
            value: "开工日期"
        }, {
            key: "projectCompletedTime",
            value: "完工日期"
        }
    ]

    export const csvPerson: UtilType.propertyCsv[] = [
        {
            key: "nickName",
            value: "员工昵称"
        }, {
            key: "personDepartmentName",
            value: "员工部门"
        }, {
            key: "totalProject",
            value: "巡检工地数"
        }, 
        {
            key: "totalPatrol",
            value: "巡检次数"
        },
        {
            key: "unpatroledCount",
            value: "未巡检工地数"
        }
    ]

    export const csvPosition: UtilType.propertyCsv[] = [{
        key: "roleName",
        value: "岗位名称"
    }, {
        key: "totalManager",
        value: "巡检员工数"
    }, {
        key: "totalProject",
        value: "巡检工地数"
    }, {
        key: "totalPatrol",
        value: "巡检次数"
    }]
}