import { YqzMedia } from "../../../bgw/media/type/media.type";
import { UtilType } from "../../../bgw/types/util.type";

export namespace ProjectUpdate {

    export enum UpdateType {
        STANDARD = "12"
    }

    export enum SOURCE {
        CAMERA = "1",//摄像头
        MANAGER = "2",//公司成员
        OWNER = "3",//业主
        SYSTEM = "4"//系统
    }

    export class AnalyticsTotal {
        totalPosition: string;//岗位数
        totalManager: string;//员工数
        totalProject: string;//工地数
        totalDegree: string;//更新次数
        totalImg: string;//更新图片数
        dealManager?: string
        dealPosition?: string
    }

    export class AnalyticsPosition {
        roleId: string;//岗位id
        roleName: string;//岗位名
        managerId?: string;
        totalManager: string;//员工数
        totalProject: string;//工地数
        totalDegree: string;//更新次数
        totalImg: string;//更新图片数
        totalVideo: string;//更新视频数
    }

    export class AnalyticsPerson {
        managerId: string;//员工id
        nickName: string;//员工昵称
        totalProject: string;//工地数
        totalDegree: string;//更新次数
        totalImg: string;//更新图片数
    }

    export class AnalyticsDataDetail {
        departmentId: string;//部门id
        departmentName: string;//部门名
        linkCompanyId?: string;
        linkCompanyName?: string;
        roleId: string;//岗位id
        roleName: string;//岗位名
        managerId: string;//员工id
        nickName: string;//员工昵称
        projectId: string;//更新工地id
        projectUpdateId?: string;
        projectAddress: string;//更新工地地址
        updateTime: string;//更新时间
        updateContent: string;//更新内容
        updateImgs: YqzMedia[];//更新图片
        updateImgNum: string;//更新图片数
    }

    export const csvPosition: UtilType.propertyCsv[] = [
        {
            key: "roleName",
            value: "岗位名称"
        }, {
            key: "totalManager",
            value: "员工数"
        }, {
            key: "totalProject",
            value: "更新工地数"
        }, {
            key: "totalDegree",
            value: "更新次数"
        }, {
            key: "totalImg",
            value: "更新图片数"
        }, {
            key: "totalVideo",
            value: "更新视频数"
        }
    ]

    export const csvPerson: UtilType.propertyCsv[] = [
        {
            key: "nickName",
            value: "员工昵称"
        }, {
            key: "departmentName",
            value: "员工部门"
        }, {
            key: "totalProject",
            value: "更新工地数"
        }, {
            key: "totalDegree",
            value: "更新次数"
        }, {
            key: "totalImg",
            value: "更新图片数"
        }, {
            key: "totalVideo",
            value: "更新视频数"
        }
    ]

    export const csvList: UtilType.propertyCsv[] = [
        {
            key: "linkCompanyName",
            value: "所属公司"
        },
        {
            key: "departmentName",
            value: "员工部门"
        },
        {
            key: "roleName",
            value: "岗位"
        }, {
            key: "nickName",
            value: "员工昵称"
        },
        {
            key: "projectAddress",
            value: "更新工地"
        }, {
            key: "updateTime",
            value: "发布时间"
        }, {
            key: "updateContent",
            value: "更新内容"
        }, {
            key: "updateImgNum",
            value: "更新图片数"
        }, {
            key: "updateVideoNum",
            value: "更新视频数"
        }, {
            key: "projectStartTime",
            value: "开工时间"
        }, {
            key: "projectCompletedTime",
            value: "完工时间"
        }
    ]

}