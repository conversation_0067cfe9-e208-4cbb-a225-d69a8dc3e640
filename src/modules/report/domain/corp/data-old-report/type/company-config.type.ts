import { ObjectId } from "mongodb";

export namespace CompanyConfig {

    export class CompanyConfigDoc {
        _id?: string | ObjectId;

        companyId?: string;  // 所属公司

        module?: string;       // 设置的类别 比如 [report:patrol:online]

        config?: {
            isAllPosts: boolean;      // 是否全部岗位
            posts?: string[];
            isShowUnpatroled?: string      // 是否显示未巡检统计 Y/N
        }

        createTime?: Date;
        updateTime?: Date;
        createUser?: string;
        updateUser?: string;
    }

}