import { DpStatus } from "@src/modules/report/mongodb/camera/dispatch.collection";
import { UtilType } from "../../../bgw/types/util.type";
import { ApiProperty } from "@nestjs/swagger";
import { Company } from "../../../bgw/company/dto/company.dto";


export namespace DispatchType {

    export class AnalyticsTotal {
        totalPosition: string;//岗位数
        totalManager: string;//员工数
        totalProject: string;//工地数
        totalDispatch: string;//派工次数
        totalPlan: string;//计划天数
        totalActual: string;//实际天数
    }

    export class AnalyticsPosition {
        roleId?: string;//岗位id
        roleName: string;//岗位名
        totalManager: string;//员工数
        totalProject: string;//工地数
        totalPlan: string;//工期天数
    }

    export class AnalyticsPerson {
        managerId: string;//员工id
        nickName: string;//员工昵称
        totalManager?: string;//工人数
        totalProject: string;//工地数
        totalPlan?: string;//工期天数
        totalActual?: string;//实际天数
    }

    export class AnalyticsDataDetail {
        departmentId: string;//部门id
        departmentName: string;//部门名
        projectAddress: string;
        projectId: string;//工地id
        personMobile: string;
        projectManager: string;
        plan: {
            days: string;
            startTime: string;
            endTime: string;
        };
        arrive: {
            status: string;
            description: string;
            time: string;
        };
        exit: {
            status: string;
            description: string;
            time: string;
        }
    }

    export class SearchListReq {
        companyType?: Company.CompanyType;
        companyId: string;
        acceptStatus?: string;
        pageNo?: number;
        pageSize?: number;
        status?: DpStatus;
        departmentId?: string;
        projectAddress?: string;
        name?: string;
        tags?: string | string[];
        planStartTime?: string;
        planEndTime?: string;
        from?: string;
        to?: string;
        arriveStatus?: string;
        exitStatus?: string;
        roles?: string[];
        managerId?: string;
        projectIdList?: string[];
        arrivalToday?: string;
        plannedArrivalToday?: string;
    }

    export enum ArriveStatus {
        PENDING_JOIN = 'pending_join', //等待进场 
        ADVANCE_JOIN = 'advance_join', //提前入场 
        TODAY_JOIN = 'today_join', //当天入场 
        OVERDUE_JOIN = 'overdue_join', //逾期进场 
        OVERDUE_NOT_JOIN = 'overdue_not_join', //逾期未进场 
    }

    export enum ExitStatus {
        ADVANCE_EXIT = 'advance_exit', //提前退场  
        OVERDUE_EXIT = 'overdue_exit', //逾期退场
    }

    export enum AcceptStatus {
        Pending = 'pending',//待确认
        Confirmed = 'confirmed',//已接受
        Rejected = 'rejected',//已拒绝
        NotNotify = 'not_notify',//未通知
    }

    function createEnumName(name: string) {
        return Object.freeze({ name });
    }

    export const AcceptStatusValues = {
        [AcceptStatus.NotNotify]: createEnumName('未通知'),
        [AcceptStatus.Pending]: createEnumName('已通知'),
        [AcceptStatus.Confirmed]: createEnumName('已接受'),
        [AcceptStatus.Rejected]: createEnumName('已拒绝'),
    }

    export const ArriveStatusValues = {
        [ArriveStatus.PENDING_JOIN]: createEnumName('等待进场'),
        [ArriveStatus.ADVANCE_JOIN]: createEnumName('提前入场'),
        [ArriveStatus.TODAY_JOIN]: createEnumName('当天入场'),
        [ArriveStatus.OVERDUE_JOIN]: createEnumName('逾期进场'),
        [ArriveStatus.OVERDUE_NOT_JOIN]: createEnumName('逾期未进场'),
    }

    export const ExitStatusValues = {
        [ExitStatus.ADVANCE_EXIT]: createEnumName('提前退场'),
        [ExitStatus.OVERDUE_EXIT]: createEnumName('逾期退场'),
    }

    export class DispatchActualDetail {
        @ApiProperty({ description: '时间' })
        time: string;
        @ApiProperty({ description: '状态(前端展示的颜色)' })
        status: string;
        @ApiProperty({ description: '描述(前端展示的话术)' })
        description: string;
    }

    export const csvPosition: UtilType.propertyCsv[] = [
        {
            key: "roleName",
            value: "工种"
        }, {
            key: "totalManager",
            value: "派工工人数"
        }, {
            key: "totalProject",
            value: "派工工地数"
        }, {
            key: "totalPlan",
            value: "计划工期天数"
        }
    ]

    export const csvPerson: UtilType.propertyCsv[] = [
        {
            key: "nickName",
            value: "工人姓名"
        }, {
            key: "totalProject",
            value: "派工工地数"
        }, {
            key: "totalPlan",
            value: "计划工期天数"
        }, {
            key: "totalActual",
            value: "实际出勤天数"
        }
    ]


    export const csvList: UtilType.propertyCsv[] = [
        {
            key: "linkCompanyName",
            value: "所属公司"
        },
        {
            key: "departmentName",
            value: "工地归属"
        },
        {
            key: "projectAddress",
            value: "工地地址"
        },
        {
            key: "name",
            value: "工人姓名"
        },
        {
            key: "tags",
            value: "工种"
        },
        {
            key: "personMobile",
            value: "电话"
        },
        {
            key: "acceptStatusName",
            value: "派工状态"
        },
        {
            key: "plan.startTime",
            value: "计划入场时间"
        },
        {
            key: "plan.endTime",
            value: "计划退场时间"
        },
        {
            key: "plan.days",
            value: "计划施工天数"
        },
        {
            key: "arrive.time",
            value: "实际入场时间"
        },
        {
            key: "exit.time",
            value: "实际退场时间"
        },
        {
            key: "arrive.description",
            value: "进场状态"
        },
        {
            key: "exit.description",
            value: "退场状态"
        },
    ]

}