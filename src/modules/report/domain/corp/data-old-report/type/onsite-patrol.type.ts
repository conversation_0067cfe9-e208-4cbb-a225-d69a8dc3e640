export namespace OnsitePatrolType {

    export class BodyAnalyticsByPersonReq {
        companyIds: string[];
        faceEntityIds: string[];
        projectIds?: string[];
        from?: string;
        to?: string;
        faceIdentifyTypes?: string[];
        tags?: string[];
    }

    export class BodyAnalyticsByRoleReq {
        companyIds: string[];
        faceEntityIds: string[];
        projectIds?: string[];
        from?: string;
        to?: string;
        faceIdentifyTypes?: string[];
    }

    export class BodyAnalyticsListReq {
        companyIds: string[];
        faceEntityIds: string[];
        projectIds?: string[];
        from?: string;
        to?: string;
        faceIdentifyTypes?: string[];
        options?: Options;
        tags?: string[];
    }

    export type Options = {
        method?: Method;
        pageNo?: number;
        pageSize?: number;
        sort?: Sort[];
    };

    export type Sort = {
        field: string;
        order: 'ASC' | 'DESC';
    }

    export type Method = "POSITION" | "PERSON" | "LIST";

}