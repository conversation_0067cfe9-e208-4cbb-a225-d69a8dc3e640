import { Injectable } from "@nestjs/common";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { YqzException } from "@yqz/nest";
import { DataSource } from "typeorm";


@Injectable()
export class CompanyConfigDao {
    constructor(
        private readonly dataSource: DataSource
    ) { }

    /**
     * 单条目查询
     * @param query 
     * @returns 
     */
    async findOne(query: { companyId: string, module?: string }) {
        try {
            const item = await MyMongoDb.collections.bgw.companyConfig.findOne(query);
            return item;
        } catch (e) {
            throw new YqzException("company_push_set dao findOne error", e);
        }
    }

}