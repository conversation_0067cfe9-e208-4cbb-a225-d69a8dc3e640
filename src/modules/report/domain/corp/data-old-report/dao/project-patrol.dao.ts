import { Injectable } from "@nestjs/common";
import { DataSource } from "typeorm";
import { Analytics } from "../type/analytics-util.type";
import { DaoUtil, YqzException } from "@yqz/nest";
import { ProjectPatrolEntity } from "@src/modules/report/entity/bgw/project-patrol.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { PersonEntity } from "@src/modules/report/entity/bgw/person.entity";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { StageEntity } from "@src/modules/report/entity/bgw/stage.entity";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { DeleteFlag } from "@src/types/delete-flag.enum";
import * as _ from "lodash";

@Injectable()
export class ProjectPatrolBoardDao {

    constructor(
        private readonly dataSource: DataSource,
    ) { }

    private analytics(param: Analytics.Req) {
        // if (!param.companyId) throw new YqzException("请传递 companyId");
        const qb = this.dataSource.getRepository(ProjectPatrolEntity)
            .createQueryBuilder('patrol')
            .innerJoin(DepartmentEntity, 'dept', 'dept.department_id = patrol.department_id')
            .innerJoin(ProjectEntity, 'project', 'project.project_id = patrol.project_id')
            .innerJoin(CompanyEntity,'c','c.company_id = project.company_id')
            .innerJoin(ManagerEntity, 'manager', 'manager.manager_id = patrol.manager_id')
            .innerJoin(PersonEntity, 'person', 'person.PERSON_ID = manager.PERSON_ID')
            .innerJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = manager.ROLE_ID') // 岗位
            .innerJoin(CommunityEntity, 'community', 'community.COMMUNITY_ID = project.COMMUNITY_ID') // 社区
            .where("patrol.DELETE_FLAG = 'N'")
            // .andWhere("manager.COMPANY_ID = :companyId", { companyId: param.companyId })
            .andWhere('patrol.department_id in (:...departmentIds)', { departmentIds: param.departmentIds })
            .andWhere("!FIND_IN_SET('一起装',person.PERSON_LABEL)")

        if (param.projectIds?.length > 0) qb.andWhere('project.project_id in (:...projectIds)', { projectIds: param.projectIds });
        if (param.managerId) qb.andWhere("manager.manager_id = :managerId", { managerId: param.managerId })
        if (param.linkCompanyId) qb.andWhere("project.company_id = :linkCompanyId", {linkCompanyId: param.linkCompanyId})
        if (param.companyIds) qb.andWhere("project.company_id in (:...companyIds)", { companyIds: param.companyIds })
        if (param.managerIdList?.length > 0) qb.andWhere('manager.manager_id in (:...managerIdList)', { managerIdList: param.managerIdList });
        // if (param.departmentIds?.length > 0) qb.andWhere('patrol.department_id in (:...departmentIds)', { departmentIds: param.departmentIds });
        if (param.roleId) qb.andWhere("role.COMPANY_MEMBER_ROLE_ID = :roleId", { roleId: param.roleId })
        if (param.roleName) qb.andWhere("role.ROLE_NAME = :roleName", { roleName: param.roleName })
        if (!_.isEmpty(param.roleNames)) qb.andWhere("role.ROLE_NAME in (:...roleNames)", { roleNames: param.roleNames })
        if (param.from) qb.andWhere("patrol.patrol_time >= :from", { from: param.from })
        if (param.to) qb.andWhere("patrol.patrol_time <= :to", { to: param.to });

        qb.orderBy("patrol.patrol_time", 'DESC').addOrderBy("patrol.patrol_time", 'DESC')
        return qb;
    }

    async analyticsPerson(param: Analytics.Req) {
        const qb = this.analytics(param)
        const analyticPersons = await qb
            .select(["manager.manager_id managerId", "manager.nick_name nickName"])
            .addSelect("COUNT(DISTINCT project.project_id)", "totalProject")
            .addSelect("COUNT(patrol.project_patrol_id)", "totalPatrol")
            .addSelect("group_concat(patrol.project_id)","projectIds")
            .groupBy("manager.manager_id")
            .orderBy("totalProject", 'DESC').addOrderBy('totalPatrol', 'DESC').addOrderBy('managerId', 'DESC')
            .getRawMany();

        return { analyticPersons };
    }

    async analyticsPosition(param: Analytics.Req) {
        const qb = this.analytics(param)

        // 执行分组查询
        const total = await qb
            .select("COUNT(DISTINCT role.COMPANY_MEMBER_ROLE_ID)", "totalPosition")
            .addSelect("COUNT(DISTINCT manager.manager_id)", "totalManager")
            .addSelect("COUNT(DISTINCT project.project_id)", "totalProject")
            .addSelect("COUNT(patrol.project_patrol_id)", "totalPatrol")
            .getRawOne();

        const analyticsPositions = await qb
            .select(["role.COMPANY_MEMBER_ROLE_ID roleId", "role.role_name roleName"])
            .addSelect("COUNT(DISTINCT manager.manager_id)", "totalManager")
            .addSelect("COUNT(DISTINCT project.project_id)", "totalProject")
            .addSelect("COUNT(patrol.project_patrol_id)", "totalPatrol")
            .groupBy("role.COMPANY_MEMBER_ROLE_ID")
            .orderBy("totalProject", 'DESC').addOrderBy('totalPatrol', 'DESC').addOrderBy('roleId', 'DESC')
            .getRawMany();

        return { ...total, analyticsPositions };
    }

    async analyticsOptions(param: Analytics.Req) {
        const qb = this.analytics(param)

        // 执行分组查询
        const personList = await qb
            .groupBy("manager.manager_id")
            .select(["manager.manager_id managerId", "manager.nick_name nickName"])
            .getRawMany();

        const positionList = await qb
            .groupBy("role.COMPANY_MEMBER_ROLE_ID")
            .select(["role.COMPANY_MEMBER_ROLE_ID roleId", "role.role_name roleName"])
            .getRawMany();

        return { positionList, personList };
    }

    /**
     * 获取线上巡检列表及统计
     * @param param 
     * @returns 
     */
    async analyticsList(param: Analytics.Req) {
        const qb = this.analytics(param)
        qb.select([
            "DATE_FORMAT(patrol.patrol_time,'%Y-%m-%d %H:%i:%s') patrolTime",   //  主表 巡检时间 不区分年份
            "patrol.project_patrol_id projectPatrolId",       // 巡检id
            "c.company_id linkCompanyId",
            "c.company_name linkCompanyName",
            "project.project_id projectId",            // 工地 id
            "project.project_name projectName",       // 巡检工地
            "date_format(ifnull(project.project_start_time,project.createTime),'%Y-%m-%d') projectStartTime",
            "date_format(project.completed_time,'%Y-%m-%d') projectCompletedTime",
            "manager.manager_id managerId",   // 管理者: id
            "manager.nick_name nickName",     // 管理者: 昵称
            "role.COMPANY_MEMBER_ROLE_ID roleId",    // 公司成员: 岗位
            "role.ROLE_NAME roleName",    // 公司成员: 岗位
            "project.HOUSE_NUMBER houseNumber",       // 巡检工地 （地址）
            "community.community_name communityName",       // 巡检工地 （地址）
            "dept.department_name departmentName",
            "dept.department_id departmentId",
        ])
        DaoUtil.processPageAndSort(qb, { pageNo: param.pageNo, pageSize: param.pageSize })
        return DaoUtil.getPageRes<Analytics.AnalyticsList>(qb)
    }

    /**
     * 获取线上巡检列表
     * @param param 
     * @returns 
     */
    async getAnalyticsList(param: Analytics.Req) {
        const qb = this.analytics(param)
        qb.select([
            "DATE_FORMAT(patrol.patrol_time,'%Y-%m-%d %H:%i:%s') patrolTime",   //  主表 巡检时间 不区分年份
            "patrol.project_patrol_id projectPatrolId",       // 巡检id
            "c.company_id linkCompanyId",
            "c.company_name linkCompanyName",
            "project.project_id projectId",            // 工地 id
            "project.project_name projectName",       // 巡检工地
            "date_format(ifnull(project.project_start_time,project.createTime),'%Y-%m-%d') projectStartTime",
            "date_format(project.completed_time,'%Y-%m-%d') projectCompletedTime",
            "manager.manager_id managerId",   // 管理者: id
            "manager.nick_name nickName",     // 管理者: 昵称
            "role.COMPANY_MEMBER_ROLE_ID roleId",    // 公司成员: 岗位
            "role.ROLE_NAME roleName",    // 公司成员: 岗位
            "project.HOUSE_NUMBER houseNumber",       // 巡检工地 （地址）
            "community.community_name communityName",       // 巡检工地 （地址）
            "dept.department_name departmentName",
            "dept.department_id departmentId",
        ])
        DaoUtil.processPageAndSort(qb, { pageNo: param.pageNo, pageSize: param.pageSize })
        return await qb.getRawMany<Analytics.AnalyticsList>();
    }

    async getProjectIds(request: { projectAddress?: string, departmentIds: string[], companyIds?: string[] }) {
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pu.project_id AS projectId'
            ])
            .from(ProjectEntity, 'p')
            .innerJoin(ProjectPatrolEntity, 'pu', 'pu.project_id = p.project_id')
            .innerJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID')
            .innerJoin(CommunityEntity, 'c', 'p.COMMUNITY_ID = c.COMMUNITY_ID')
            .innerJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
            .innerJoin(ManagerEntity, 'm', 'm.manager_id = pu.manager_id')
            .innerJoin(PersonEntity, 'person', 'person.PERSON_ID = m.PERSON_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            .andWhere("pu.department_id in (:...departmentIds)", { departmentIds: request.departmentIds })
            .andWhere("!FIND_IN_SET('一起装',person.PERSON_LABEL)")
        if (request.projectAddress) {
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(c.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: request.projectAddress });
        }
        if (!_.isEmpty(request.companyIds)) {
            qb.andWhere('p.company_id in (:...companyIds)', { companyIds: request.companyIds })
        }
        qb.groupBy('p.project_id');
        return await qb.getRawMany();
    }

}