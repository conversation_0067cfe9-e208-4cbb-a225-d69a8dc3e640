import { Injectable } from "@nestjs/common";
import { DataSource } from "typeorm";
import { Analytics } from "../type/analytics-util.type";
import { ProjectUpdateEntity } from "@src/modules/report/entity/bgw/project-update.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { DeleteFlag } from "@yqz/nest/lib/types/delete-flag.enum";
import { ProjectUpdate } from "../type/project-update.type";
import * as _ from "lodash";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { Common, Project, YqzException } from "@yqz/nest";
import { ProjectUpdateMediaEntity } from "@src/modules/report/entity/bgw/project-update-media.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { StageEntity } from "@src/modules/report/entity/bgw/stage.entity";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { ProjectNodeEntity } from "@src/modules/report/entity/bgw/project-node.entity";

@Injectable()
export class ProjectUpdateBoardDao {

    constructor(
        private readonly dataSource: DataSource,
    ) { }

    async analyticsTotal(params: Analytics.Req) {
        const { companyId, projectIds, departmentIds, roleNames, from, to } = params;
        const qb = this.dataSource.getRepository(ProjectUpdateEntity)
            .createQueryBuilder('pu')
            .select([
                "COUNT(DISTINCT m.ROLE_ID) as totalPosition",
                "COUNT(DISTINCT pu.project_update_source_id) as totalManager",
                "COUNT(DISTINCT pu.project_id) as totalProject",
                "COUNT(pu.project_update_id) as totalDegree",
                "SUM(pu.project_update_media_count) AS imgCount"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.company_id = pu.company_id and m.manager_id = pu.project_update_source_id')
            .leftJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            // .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('m.manager_type in (:...managerTypes)', { managerTypes: ['1', '3'] });//包含普通成员、公司管理员
        qb.andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (!_.isEmpty(projectIds)) qb.andWhere("pu.project_id in (:...projectIds)", { projectIds });
        // if (!_.isEmpty(departmentIds)) qb.andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        // if (roleId) qb.andWhere("m.role_id = :roleId", { roleId });
        if (!_.isEmpty(roleNames)) qb.andWhere("role.role_name in (:...roleNames)", { roleNames });
        if (from) qb.andWhere("pu.createTime >= :from", { from });
        if (to) qb.andWhere("pu.createTime <= :to", { to });
        return await qb.getRawOne<{ totalPosition: string, totalManager: string, totalProject: string, totalDegree: string, imgCount: string }>();
    }

    async analyticsGroupCount(params: Analytics.Req) {
        const { companyId, projectIds, departmentIds, roleId, roleName, roleNames, from, to, groupBy } = params;
        const qb = this.dataSource.getRepository(ProjectUpdateEntity)
            .createQueryBuilder('pu')
            .select([
                "COUNT(DISTINCT m.ROLE_ID) as totalPosition",
                "COUNT(DISTINCT pu.project_update_source_id) as totalManager",
                "COUNT(DISTINCT pu.project_id) as totalProject",
                "COUNT(DISTINCT pu.project_update_id) as totalDegree",
                "GROUP_CONCAT(DISTINCT pu.project_id) as projectIds",
                "GROUP_CONCAT(pu.project_update_id) as updateIds"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.company_id = pu.company_id and m.manager_id = pu.project_update_source_id')
            .leftJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID');
        //数据分组
        switch (groupBy) {
            case 'role'://岗位
                qb.addSelect('m.role_id as groupId');
                qb.addSelect('role.role_name as groupName');
                qb.addSelect('m.manager_id as managerId');
                // qb.leftJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID');
                qb.groupBy('m.role_id');
                break;
            case 'manager'://成员
                qb.addSelect('m.manager_id as groupId');
                qb.addSelect('m.nick_name as groupName');
                qb.groupBy('m.manager_id');
                break;
            default:
                throw new YqzException("不支持该字段分组");
        }
        qb.where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            .andWhere("pu.department_id in (:...departmentIds)", { departmentIds })
            // .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('m.manager_type in (:...managerTypes)', { managerTypes: ['1', '3'] });//包含普通成员、公司管理员
        if (!_.isEmpty(projectIds)) qb.andWhere("pu.project_id in (:...projectIds)", { projectIds });
        // if (!_.isEmpty(departmentIds)) qb.andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (roleId) qb.andWhere("m.role_id = :roleId", { roleId });
        if (roleName) qb.andWhere("role.role_name = :roleName", { roleName });
        if (!_.isEmpty(roleNames)) qb.andWhere("role.role_name in (:...roleNames)", { roleNames });
        if (from) qb.andWhere("pu.createTime >= :from", { from });
        if (to) qb.andWhere("pu.createTime <= :to", { to });
        qb.orderBy('totalProject', 'DESC').addOrderBy('totalDegree', 'DESC').addOrderBy('groupId', 'DESC');
        return await qb.getRawMany<{ groupId: string, groupName: string, managerId?: string, totalPosition: string, totalManager: string, totalProject: string, totalDegree: string, updateIds: string, projectIds: string }>();
    }

    async analyticsGroup(params: Analytics.Req) {
        const { companyId, projectIds, departmentIds, from, to, roleNames, groupBy } = params;
        const qb = this.dataSource.getRepository(ProjectUpdateEntity)
            .createQueryBuilder('pu')
            .leftJoin(ManagerEntity, 'm', 'm.company_id = pu.company_id and m.manager_id = pu.project_update_source_id')
            .leftJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID');
        //数据分组
        switch (groupBy) {
            case 'role'://岗位
                qb.addSelect('m.role_id as groupId');
                qb.addSelect('role.role_name as groupName');
                // qb.leftJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID');
                qb.groupBy('m.role_id');
                break;
            case 'manager'://成员
                qb.addSelect('m.manager_id as groupId');
                qb.addSelect('m.nick_name as groupName');
                qb.groupBy('m.manager_id');
                break;
            default:
                throw new YqzException("不支持该字段分组");
        }
        qb.where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            // .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('m.manager_type in (:...managerTypes)', { managerTypes: ['1', '3'] })//包含普通成员、公司管理员
            .andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (!_.isEmpty(projectIds)) qb.andWhere("pu.project_id in (:...projectIds)", { projectIds });
        if (!_.isEmpty(roleNames)) qb.andWhere("role.role_name in (:...roleNames)", { roleNames });
        // if (!_.isEmpty(departmentIds)) qb.andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (from) qb.andWhere("pu.createTime >= :from", { from });
        if (to) qb.andWhere("pu.createTime <= :to", { to });
        return await qb.getRawMany<{ groupId: string, groupName: string }>();
    }

    async getManagerUPdateProjectIds(params: Analytics.Req) {
        const { companyId, projectIds, departmentIds, roleId, roleName, roleNames, from, to, managerIdList } = params;
        const qb = this.dataSource.getRepository(ProjectUpdateEntity)
            .createQueryBuilder('pu')
            .select([
                'm.manager_id as managerId',
                'm.nick_name as nickName',
                "GROUP_CONCAT(DISTINCT pu.project_id) as projectIdStr",
            ])
            .leftJoin(ManagerEntity, 'm', 'm.company_id = pu.company_id and m.manager_id = pu.project_update_source_id')
            .leftJoin(CompanyMemberRoleEntity, 'role', 'role.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            .andWhere("pu.department_id in (:...departmentIds)", { departmentIds })
            // .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('m.manager_type in (:...managerTypes)', { managerTypes: ['1', '3'] });//包含普通成员、公司管理员
        if (!_.isEmpty(projectIds)) qb.andWhere("pu.project_id in (:...projectIds)", { projectIds });
        // if (!_.isEmpty(departmentIds)) qb.andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (!_.isEmpty(managerIdList)) qb.andWhere("m.manager_id in (:...managerIdList)", { managerIdList });
        if (roleId) qb.andWhere("m.role_id = :roleId", { roleId });
        if (roleName) qb.andWhere("role.role_name = :roleName", { roleName });
        if (!_.isEmpty(roleNames)) qb.andWhere("role.role_name in (:...roleNames)", { roleNames });
        if (from) qb.andWhere("pu.createTime >= :from", { from });
        if (to) qb.andWhere("pu.createTime <= :to", { to });
        qb.groupBy('m.manager_id');
        return await qb.getRawMany<{ managerId: string, nickName: string, projectIdStr: string }>();
    }


    async analyticsDataDetail(params: Analytics.Req) {
        const { companyId, linkCompanyId, projectIds, managerId, departmentIds, roleId, roleName, roleNames, from, to, pageNo, pageSize } = params;
        const qb = this.dataSource.getRepository(ProjectUpdateEntity)
            .createQueryBuilder('pu')
            .select([
                "pu.department_id as departmentId",
                "d.department_name as departmentName",
                "m.role_id as roleId",
                "role.role_name as roleName",
                "pu.project_update_source_id as managerId",
                "m.nick_name as nickName",
                "pu.project_id as projectId",
                "pu.createTime as updateTime",
                "pu.project_update_text as updateContent",
                "pu.project_update_id as projectUpdateId",
                "c.company_id as linkCompanyId",
                "c.company_name as linkCompanyName",
                "ifnull(p.project_start_time,p.createTime) as projectStartTime",
                "p.completed_time as completedTime"
            ])
            .leftJoin(ManagerEntity, 'm', 'm.company_id = pu.company_id and m.manager_id = pu.project_update_source_id')
            .leftJoin(CompanyEntity, 'c', 'c.company_id = pu.company_id')
            .leftJoin(ProjectEntity, 'p', 'p.project_id = pu.project_id')
            .leftJoin(CompanyMemberRoleEntity, 'role', 'role.company_member_role_id = m.role_id')
            .leftJoin(DepartmentEntity, 'd', 'd.department_id = pu.department_id')
            .where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            // .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('m.manager_type in (:...managerTypes)', { managerTypes: ['1', '3'] })//包含普通成员、公司管理员
            .andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (managerId) qb.andWhere("pu.project_update_source_id = :managerId", { managerId });
        if (roleId) qb.andWhere("m.role_id = :roleId", { roleId });
        if (linkCompanyId) qb.andWhere("pu.company_id = :linkCompanyId", { linkCompanyId })
        if (roleName) qb.andWhere("role.role_name = :roleName", { roleName });
        if (!_.isEmpty(roleNames)) qb.andWhere("role.role_name in (:...roleNames)", { roleNames });
        if (!_.isEmpty(projectIds)) qb.andWhere("pu.project_id in (:...projectIds)", { projectIds });
        // if (!_.isEmpty(departmentIds)) qb.andWhere("pu.department_id in (:...departmentIds)", { departmentIds });
        if (from) qb.andWhere("pu.createTime >= :from", { from });
        if (to) qb.andWhere("pu.createTime <= :to", { to });
        const total = await qb.getCount();
        if (pageNo && pageSize) qb.offset((pageNo - 1) * pageSize).limit(pageSize);
        qb.orderBy('pu.createTime', 'DESC').addOrderBy('pu.project_update_id', 'DESC');
        const items = await qb.getRawMany();
        return { total, items };
    }

    /**
   * 查询更新图片数量
   * @param updateIds 
   * @returns 
   */
    async searchProjectUpdateMediaCountByUpdateIds(updateIds: string[]) {
        const qb = this.dataSource.getRepository(ProjectUpdateMediaEntity)
            .createQueryBuilder('pum')
            .select([
                'SUM(CASE WHEN media_type not in (4) THEN 1 ELSE 0 END) AS imgCount',
                'SUM(CASE WHEN media_type = 4 THEN 1 ELSE 0 END) AS videoCount'
            ])
            .where('pum.project_update_id IN (:...updateIds)', { updateIds })
            .andWhere('pum.delete_flag = :delFlag', { delFlag: Common.Flag.N })
        return await qb.getRawOne();
    }

    async searchProjectUpdateMediaByUpdateIds(updateIds: string[]) {
        const qb = this.dataSource.getRepository(ProjectUpdateMediaEntity)
            .createQueryBuilder('pum')
            .where('pum.project_update_id IN (:...updateIds)', { updateIds })
            .andWhere('pum.delete_flag = :delFlag', { delFlag: Common.Flag.N })
        return await qb.getMany();
    }

    async getProjectIds(request: { projectAddress?: string, departmentIds: string[], projectDirectorId?: string, companyIds?: string[] }) {
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pu.project_id AS projectId'
            ])
            .from(ProjectEntity, 'p')
            .leftJoin(ProjectUpdateEntity, 'pu', 'pu.project_id = p.project_id')
            .leftJoin(StageEntity, 's', 'p.STAGE_TEMPLATE_ID = s.stage_template_id AND p.BUSINESS_STAGE_ID = s.BUSINESS_STAGE_ID')
            .leftJoin(CommunityEntity, 'c', 'p.COMMUNITY_ID = c.COMMUNITY_ID')
            .leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'")
            .leftJoin(ManagerEntity, 'm', 'm.company_id = pu.company_id and m.manager_id = pu.project_update_source_id')
            .where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('m.manager_type in (:...managerTypes)', { managerTypes: ['1', '3'] })//包含普通成员、公司管理员
            .andWhere("pu.department_id in (:...departmentIds)", { departmentIds: request.departmentIds });
        if (request.projectAddress) {
            qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(c.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: request.projectAddress });
        }
        if (request.projectDirectorId) {
            qb.andWhere("p.project_manager = :projectDirectorId", { projectDirectorId: request.projectDirectorId })
        }
        qb.groupBy('p.project_id');
        const res = await qb.getRawMany();
        return res
    }

    async searchProjectNodeProjectUpdateInfo({ projectNodeIds }: { projectNodeIds: string[] }) {
        if (_.isEmpty(projectNodeIds)) return [];
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'pn.project_node_id as projectNodeId',
                'group_concat(DISTINCT m.nick_name SEPARATOR ";") as projectUpdator',
                'group_concat(DISTINCT cmr.role_name SEPARATOR ";") as projectUpdateRole',
                'count(DISTINCT pu.project_update_id) as projectUpdateNum',
                'count(DISTINCT pum.id) as projectUpdateImgNum'
            ])
            .from(ProjectUpdateEntity, 'pu')
            .leftJoin(ProjectNodeEntity, 'pn', "pu.project_id = pn.project_id and pu.project_node_id = pn.project_node_id")
            .leftJoin(ManagerEntity, 'm', "pu.company_id = m.company_id and pu.person_id = m.person_id and m.delete_flag = 'N'")
            .leftJoin(CompanyMemberRoleEntity, 'cmr', "m.role_id = cmr.company_member_role_id")
            .leftJoin(ProjectUpdateMediaEntity, 'pum', "pu.project_update_id = pum.project_update_id and pum.delete_flag = 'N' and pum.media_type = '1'")
            .where('pu.delete_flag = :delFlag', { delFlag: DeleteFlag.N })
            .andWhere('pu.project_update_source = :source', { source: ProjectUpdate.SOURCE.MANAGER })
            .andWhere('pu.project_update_type = :updateType', { updateType: ProjectUpdate.UpdateType.STANDARD })
            .andWhere('pn.project_node_id in (:...projectNodeIds)', { projectNodeIds })
            .groupBy('pu.project_node_id');
        return await qb.getRawMany<{ projectNodeId: string, projectUpdator: string, projectUpdateRole: string, projectUpdateNum: string, projectUpdateImgNum: string }>();
    }
}