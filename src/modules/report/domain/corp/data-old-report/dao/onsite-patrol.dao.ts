import { Injectable } from "@nestjs/common";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { MemberRoleEntity } from "@src/modules/report/entity/bgw/member-role.entity";
import { ProjectMemberEntity } from "@src/modules/report/entity/bgw/project-member.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DaoUtil, MyCache } from "@yqz/nest";
import endOfDay from "date-fns/endOfDay";
import format from "date-fns/format";
import * as _ from "lodash";
import { DataSource } from "typeorm";

@Injectable()
export class OnsitePatrolBoardDao {

    constructor(
        private readonly dataSource: DataSource,
    ) { }

    /**
     * 缓存查询成员应巡检工地列表
     * @param params 
     * @returns 
     */
    @MyCache({ cacheKey: 'OnsitePatrolBoardDao:searchProjectShouldPatroledByCache', ttl: 60 })//60s缓存
    async searchProjectShouldPatroledByCache(params: { companyIds: string[], managerIds: string[], projectIds?: string[], startDate: string, endDate: string, roleNames?: string[] }) {
        return await this.searchProjectShouldPatroledV2(params);
    }

    /**
     * 查询成员应巡检工地列表
     * @param params 
     */
    async searchProjectShouldPatroledV2(params: { companyIds: string[], managerIds: string[], projectIds?: string[], startDate: string, endDate: string, roleNames?: string[] }) {
        DaoUtil.checkEmptyParams(params, [OnsitePatrolBoardDao.name, 'searchProjectShouldPatroledV2'].join(":"), "FailIfAllEmpty")
        const { companyIds, managerIds, startDate = '2000-01-01', endDate = format(endOfDay(new Date()), 'yyyy-MM-dd'), roleNames } = params;
        const qb = this.dataSource.getRepository(ProjectEntity).createQueryBuilder('pr')
            .select([
                'm.MANAGER_ID as managerId',
                'cmr.ROLE_NAME as roleName',
                'GROUP_CONCAT(DISTINCT pr.PROJECT_ID) as projectIds'//所有应巡检项目id
            ])
            .innerJoin(ProjectMemberEntity, 'pm', 'pr.project_id = pm.project_id')
            .innerJoin(ManagerEntity, 'm', 'pm.person_id = m.person_id and m.company_id = pr.company_id and m.manager_id in (:...managerIds)', { managerIds })
            .innerJoin(MemberRoleEntity, 'mr', 'mr.PROJECT_MEMBER_ID = pm.PROJECT_MEMBER_ID AND mr.ROLE_ID in ("1","2")')//只查询工地管理员 和 工地普通成员
            .leftJoin(CompanyMemberRoleEntity, 'cmr', 'cmr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where("pr.delete_flag = 'N'")
            .andWhere("pm.delete_flag = 'N'")
            .andWhere("m.delete_flag = 'N'")
            .andWhere("mr.delete_flag = 'N' ")
            .andWhere("cmr.delete_flag = 'N' ")
            .andWhere("pr.company_id in (:...companyIds)", { companyIds })
            .andWhere("m.company_id in (:...companyIds)", { companyIds })
            .andWhere("m.manager_id in (:...managerIds)", { managerIds })
            .andWhere("mr.ROLE_ID in ('1','2')")
            //工地已完工 or 工地未完工
            .andWhere(`((pr.PROJECT_STATUS = 'Y' AND pr.createTime <= :endDate AND pr.completed_time >= :startDate) or 
        (pr.PROJECT_STATUS = 'N' AND pr.createTime <= :endDate AND (pr.project_start_time <= :endDate OR pr.project_start_time IS NULL)))`, { startDate, endDate })
            .groupBy('m.MANAGER_ID');
        if (params.projectIds?.length) {
            qb.andWhere("pr.project_id in (:...projectIds)", { projectIds: params.projectIds });
        }
        if (!_.isEmpty(params.roleNames)) {
            qb.andWhere("cmr.ROLE_NAME in (:...roleNames)", { roleNames: params.roleNames })
        }
        return (await qb.getRawMany<{ managerId: string, projectIds: string, roleName: string }>()).map(item => ({ ...item, projectIds: (item?.projectIds || "")?.split(',') }));
    }
}