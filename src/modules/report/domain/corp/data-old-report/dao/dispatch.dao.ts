import { Injectable } from "@nestjs/common";
import { DataSource } from "typeorm";
import { Analytics } from "../type/analytics-util.type";
import * as _ from "lodash";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { Filter } from "mongodb";
import { Dispatch } from "@src/modules/report/mongodb/camera/dispatch.collection";
import * as DateFns from 'date-fns';
import { DevTimeout } from "@yqz/nest";
import { BodyTag, Face } from "@src/modules/report/mongodb/camera/face.collection";
import { TagUril } from "@src/util/tag_uril.util";

@Injectable()
export class DispatchBoardDao {
    searchProjectUpdateMediaByUpdateIds(updateIds: any[]) {
        throw new Error("Method not implemented.");
    }

    constructor(

    ) { }

    // @DevTimeout(100)
    async test111() {
        const data = await this.analyticsTotal({
            companyId: "65",
            companyIds: ["65"],
            projectIds: [],
            from: "2024-11-01",
            to: "2024-11-30",
            managerIdList: [],
            departmentId: ""
        })
        console.log(`data: ${JSON.stringify(data)}`);
    }

    async analyticsTotal(params: Analytics.Req) {
        console.log(`params: ${JSON.stringify(params)}`)
        const { companyIds, projectIds, from, to } = params;

        if (_.isEmpty(companyIds)) return []

        const filters: Filter<Dispatch>[] = [
            { companyId: { $in: companyIds } },
            { deletion: { $ne: true } },
        ];
        if (!_.isEmpty(projectIds)) {
            filters.push({ projectId: { $in: projectIds } });
        }
        if (from)
            filters.push({ planStart: { $gte: DateFns.startOfDay(new Date(from)) } });
        if (to)
            filters.push({ planStart: { $lte: DateFns.endOfDay(new Date(to)) } });
        const qb = MyMongoDb.collections.camera.dispatch.aggregate([
            {
                $lookup: {
                  from: "face",  // 关联的集合名称
                  localField: "faceEntityId",  // a 集合中的 faceId 字段
                  foreignField: "faceEntityId", // b 集合中的 faceId 字段
                  as: "f"  // 结果中的新字段名称
                }
            },
            {
                $match: { $and: filters }
            },
            {
                $lookup: {
                    from: "body", // 关联 body 表
                    let: {
                        localFaceEntityId: "$faceEntityId", // 定义变量，当前表中的 faceEntityId
                        localProjectId: "$projectId" // 定义变量，当前表中的 projectId
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$faceEntityId", "$$localFaceEntityId"] }, // 匹配 faceEntityId
                                        { $eq: ["$projectId", "$$localProjectId"] } // 匹配 projectId
                                    ]
                                }
                            }
                        }
                    ],
                    as: "attendanceRecords" // 将匹配结果存入此字段
                }
            },
            {
                $addFields: {
                    // 筛选出 faceIdentifyType 为 face 的记录
                    filteredAttendanceRecords: {
                        $filter: {
                            input: "$attendanceRecords",
                            as: "record",
                            cond: {
                                $and: [
                                    {
                                        $in: ["$$record.faceIdentifyType", ["face", "lbs"]] // faceIdentifyType 在 ["face", "lbs"] 中
                                    },
                                    {
                                        $eq: ["$$record.trustedIdentity", true]
                                    },
                                    //说不需要看出勤时间 by: 萌萌
                                    // {
                                    //     $gte: ["$$record.eventTime", DateFns.startOfDay(new Date(from))] // eventTime >= 开始时间
                                    // },
                                    // {
                                    //     $lte: ["$$record.eventTime", DateFns.endOfDay(new Date(to))] // eventTime <= 结束时间
                                    // }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $addFields: {
                    // 提取出所有符合条件的日期
                    uniqueAttendanceDays: {
                        $map: {
                            input: "$filteredAttendanceRecords",
                            as: "record",
                            in: { $dateToString: { format: "%Y-%m-%d", date: "$$record.eventTime" } }
                        }
                    }
                }
            },
            {
                $addFields: {
                    // 去重实际出勤天数
                    uniqueAttendanceDays: { $setUnion: "$uniqueAttendanceDays" }
                }
            },
            {
                $group: {
                    _id: null,  // 按 companyId 分组
                    uniqueProjectsCount: { $addToSet: "$projectId" },  // 统计不同的工地数
                    uniquePersonsCount: { $addToSet: "$faceEntityId" },  // 统计不同的人员数
                    uniqueTagsCount: { $addToSet: "$f.tags" }, // 统计不同的标签数
                    totalRecords: { $sum: 1 },  // 统计总记录数
                    planDaysSum: {// 计算计划工期总和
                        $sum: {
                            $ceil: {  // 向上取整
                                $cond: [
                                    { $ifNull: ["$duration", false] }, // 如果有 duration
                                    { $toDouble: "$duration" }, // 直接取 duration（转为数值）
                                    {
                                        $cond: [
                                            {
                                                $and: [
                                                    { $gte: ["$planStart", null] }, // planStart 存在
                                                    { $gte: ["$planEnd", null] }, // planEnd 存在
                                                ]
                                            },
                                            {
                                                $add: [
                                                    {
                                                        $divide: [
                                                            {
                                                                $subtract: [
                                                                    { $toDate: "$planEnd" },
                                                                    { $toDate: "$planStart" }
                                                                ]
                                                            },
                                                            1000 * 60 * 60 * 24
                                                        ]
                                                    },
                                                    1 // 差值加 1 天
                                                ]
                                            },
                                            0 // 默认值为 0
                                        ]
                                    }
                                ]
                            }
                        }
                    },
                    actualDaysSum: {
                        $sum: { $size: "$uniqueAttendanceDays" } // 实际出勤天数为去重后的天数
                    }
                }
            },
            {
                $project: {
                    companyId: "$_id",  // 返回 companyId
                    projectsCount: { $size: "$uniqueProjectsCount" },  // 不同工地数量
                    personsCount: { $size: "$uniquePersonsCount" },  // 不同人员数量
                    tagsCount: { $size: "$uniqueTagsCount" }, // 不同标签数量
                    totalRecords: 1,  // 总记录数
                    planDaysSum: 1,  // 计划工期总和
                    actualDaysSum: 1,  // 实际工期总和
                    _id: 0  // 排除 _id 字段
                }
            }
        ])
        return await qb.toArray();
    }

    async analyticsGroup(params: Analytics.Req) {
        const { companyIds, projectIds, managerIdList, from, to } = params;

        if (_.isEmpty(companyIds)) return []
        const filters: Filter<Dispatch>[] = [
            { companyId: { $in: companyIds } },
            { deletion: { $ne: true } },
        ];
        if (!_.isEmpty(projectIds)) {
            filters.push({ projectId: { $in: projectIds } });
        }
        if (!_.isEmpty(managerIdList)) {
            filters.push({ faceEntityId: { $in: managerIdList } });
        }
        if (from)
            filters.push({ planStart: { $gte: DateFns.startOfDay(new Date(from)) } });
        if (to)
            filters.push({ planStart: { $lte: DateFns.endOfDay(new Date(to)) } });
        const qb = MyMongoDb.collections.camera.dispatch.aggregate([
            {
                $lookup: {
                  from: "face",  // 关联的集合名称
                  localField: "faceEntityId",  // a 集合中的 faceId 字段
                  foreignField: "faceEntityId", // b 集合中的 faceId 字段
                  as: "f"  // 结果中的新字段名称
                }
            },
            {
                $match: { $and: filters }
            },
            {
                $group: {
                    _id: "$f.tags",  // 按成员分组
                    uniqueProjects: { $addToSet: "$projectId" },  // 派工工地数
                    uniqueManagers: { $addToSet: "$faceEntityId" },  // 派工工人数
                    planDays: {// 工期天数
                        $sum: {
                            $ceil: {  // 向上取整
                                $cond: [
                                    { $ifNull: ["$duration", false] }, // 如果有 duration
                                    { $toDouble: "$duration" }, // 直接取 duration（转为数值）
                                    {
                                        $cond: [
                                            {
                                                $and: [
                                                    { $gte: ["$planStart", null] }, // planStart 存在
                                                    { $gte: ["$planEnd", null] }, // planEnd 存在
                                                ]
                                            },
                                            {
                                                $add: [
                                                    {
                                                        $divide: [
                                                            {
                                                                $subtract: [
                                                                    { $toDate: "$planEnd" },
                                                                    { $toDate: "$planStart" }
                                                                ]
                                                            },
                                                            1000 * 60 * 60 * 24
                                                        ]
                                                    },
                                                    1 // 差值加 1 天
                                                ]
                                            },
                                            0 // 默认值为 0
                                        ]
                                    }
                                ]
                            }
                        }
                    },
                }
            },
            {
                $project: {
                    tags: "$_id",  // 人员id
                    projectsCount: { $size: "$uniqueProjects" },  // 派工工地数
                    managersCount: { $size: "$uniqueManagers" },  // 派工工人数
                    planDays: 1,
                    _id: 0  // 排除 _id 字段
                }
            }
        ])
        return await qb.toArray();
    }

    async analyticsGroupCount(params: Analytics.Req) {
        const { companyIds, projectIds, managerIdList, from, to } = params;

        if (_.isEmpty(companyIds)) return []
        const filters: Filter<Dispatch>[] = [
            { companyId: { $in: companyIds } },
            { deletion: { $ne: true } },
        ];
        if (!_.isEmpty(projectIds)) {
            filters.push({ projectId: { $in: projectIds } });
        }
        if (!_.isEmpty(managerIdList)) {
            filters.push({ faceEntityId: { $in: managerIdList } });
        }
        if (from)
            filters.push({ planStart: { $gte: DateFns.startOfDay(new Date(from)) } });
        if (to)
            filters.push({ planStart: { $lte: DateFns.endOfDay(new Date(to)) } });
        const qb = MyMongoDb.collections.camera.dispatch.aggregate([
            {
                $match: { $and: filters }
            },
            {
                $lookup: {
                    from: "body", // 关联 body 表
                    let: {
                        localFaceEntityId: "$faceEntityId", // 定义变量，当前表中的 faceEntityId
                        localProjectId: "$projectId" // 定义变量，当前表中的 projectId
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ["$faceEntityId", "$$localFaceEntityId"] }, // 匹配 faceEntityId
                                        { $eq: ["$projectId", "$$localProjectId"] } // 匹配 projectId
                                    ]
                                }
                            }
                        }
                    ],
                    as: "attendanceRecords" // 将匹配结果存入此字段
                }
            },
            {
                $addFields: {
                    // 筛选出 faceIdentifyType 为 face 的记录
                    filteredAttendanceRecords: {
                        $filter: {
                            input: "$attendanceRecords",
                            as: "record",
                            cond: {
                                $and: [
                                    {
                                        $in: ["$$record.faceIdentifyType", ["face", "lbs"]] // faceIdentifyType 在 ["face", "lbs"] 中
                                    },
                                    {
                                        $eq: ["$$record.trustedIdentity", true]
                                    },
                                    //说不需要看出勤时间 by: 萌萌
                                    // {
                                    //     $gte: ["$$record.eventTime", DateFns.startOfDay(new Date(from))] // eventTime >= 开始时间
                                    // },
                                    // {
                                    //     $lte: ["$$record.eventTime", DateFns.endOfDay(new Date(to))] // eventTime <= 结束时间
                                    // }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $addFields: {
                    // 提取出所有符合条件的日期
                    uniqueAttendanceDays: {
                        $map: {
                            input: "$filteredAttendanceRecords",
                            as: "record",
                            in: { $dateToString: { format: "%Y-%m-%d", date: "$$record.eventTime" } }
                        }
                    }
                }
            },
            {
                $addFields: {
                    // 去重实际出勤天数
                    uniqueAttendanceDays: { $setUnion: "$uniqueAttendanceDays" }
                }
            },
            {
                $group: {
                    _id: "$faceEntityId",  // 按成员分组
                    uniqueProjects: { $addToSet: "$projectId" },  // 派工工地数
                    uniqueManagers: { $addToSet: "$faceEntityId" },  // 派工工人数
                    planDays: {// 工期天数
                        $sum: {
                            $ceil: {  // 向上取整
                                $cond: [
                                    { $ifNull: ["$duration", false] }, // 如果有 duration
                                    { $toDouble: "$duration" }, // 直接取 duration（转为数值）
                                    {
                                        $cond: [
                                            {
                                                $and: [
                                                    { $gte: ["$planStart", null] }, // planStart 存在
                                                    { $gte: ["$planEnd", null] }, // planEnd 存在
                                                ]
                                            },
                                            {
                                                $add: [
                                                    {
                                                        $divide: [
                                                            {
                                                                $subtract: [
                                                                    { $toDate: "$planEnd" },
                                                                    { $toDate: "$planStart" }
                                                                ]
                                                            },
                                                            1000 * 60 * 60 * 24
                                                        ]
                                                    },
                                                    1 // 差值加 1 天
                                                ]
                                            },
                                            0 // 默认值为 0
                                        ]
                                    }
                                ]
                            }
                        }
                    },
                    actualDays: {
                        $sum: { $size: "$uniqueAttendanceDays" } // 实际出勤天数为去重后的天数
                    }
                }
            },
            {
                $project: {
                    faceId: "$_id",  // 人员id
                    projectsCount: { $size: "$uniqueProjects" },  // 派工工地数
                    managersCount: { $size: "$uniqueManagers" },  // 派工工人数
                    planDays: 1,
                    actualDays: 1,
                    _id: 0  // 排除 _id 字段
                }
            }
        ])
        return await qb.toArray();
    }

    async analyticsDataDetail(params: Analytics.Req) {
        const { companyId, projectIds, managerId, departmentIds, roleId, from, to, pageNo, pageSize } = params;

    }

    async searchFace(params: { companyId?: string, companyIds?: string[],faceEntityId?: string, faceEntityIdList?: string[], tags?: string | string[], rank?: string, name?: string }) {
        const { companyId, companyIds, faceEntityIdList, faceEntityId, tags, rank, name } = params
        if(!companyId && _.isEmpty(companyIds) ) return []
        const companyParams = companyId || {$in: companyIds}
        const filters: Filter<Face>[] = [
            { companyId: companyParams }
        ]

        if (typeof (rank) === 'string' && rank !== '') {
            filters.push({ rank })
        }

        if (typeof (name) === 'string' && name !== '') {
            filters.push({ name: { $regex: name } })
        }

        if (typeof (faceEntityId) === 'string' && faceEntityId !== '') {
            filters.push({ faceEntityId })
        }

        if (typeof (tags) === 'string' && tags !== '') {
            if (tags === BodyTag.UNTAGGED) {
                filters.push({
                    $or: [{ tags: { $exists: false } }, { tags: null }, { tags: '' }]
                })
            } else {
                const subTags = await this.getSubTags({ companyId, tagList: [tags] })
                filters.push({ tags: { $in: subTags } })
            }

        }

        if (Array.isArray(tags) && !_.isEmpty(tags)) {
            const subTags = await this.getSubTags({ companyId, tagList: tags })
            filters.push({ tags: { $in: subTags } })
        }

        if (faceEntityIdList?.length > 0) {
            filters.push({ faceEntityId: { $in: faceEntityIdList } })
        }

        return MyMongoDb.collections.camera.face.find({ $and: filters }).toArray();


    }

    async getSubTags(params: { companyId: string, tagList: string[] }, options?: { tagsListFlag: "includes" | "excludes" }) {
        const res = await MyMongoDb.collections.camera.tags_hirachy.findOne({ companyId: params.companyId })
        if (!res || _.isEmpty(res.tags)) return [];

        const { tagsListFlag = "includes" } = options || {};
        let result: string[] = [];
        if (tagsListFlag === 'excludes') {
            for (const item of res.tags) {
                const tag = item.tag;
                if (params.tagList.includes(tag)) continue;
                result = result.concat(TagUril.getSubTagList(res?.tags || [], tag))
            }
        } else {
            for (const tag of params.tagList) {
                result = result.concat(TagUril.getSubTagList(res?.tags || [], tag))
            }
        }
        return _.uniq(result)
    }

    async searchGroupCountList(params: { companyId: string, faceEntityIdList?: string[], startTime?: Date }) {
        const { companyId, faceEntityIdList, startTime } = params;
        const filters: Filter<Body>[] = [{
            companyId: String(companyId),
            trustedIdentity: true,
        }]
        if (startTime)
            filters.push({ eventTime: { $gte: startTime } })
        if (!_.isEmpty(faceEntityIdList))
            filters.push({ faceEntityId: { $in: faceEntityIdList } })
        // 构建聚合管道
        const pipeline = [
            {
                $match: { $and: filters },
            },
            {
                $group: {
                    _id: { faceEntityId: '$faceEntityId', projectId: '$projectId' }, // 按 bodyId 和 projectId 分组
                    count: { $sum: 1 }, // 计算每组的数量
                },
            },
            {
                $project: {
                    faceEntityId: '$_id.faceEntityId',
                    projectId: '$_id.projectId',
                    count: 1,
                    _id: 0, // 排除 _id 字段
                },
            },
        ];
        return await MyMongoDb.collections.camera.body.aggregate(pipeline).toArray();
    }

}