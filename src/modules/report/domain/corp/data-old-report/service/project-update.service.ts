import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON>ormatCsv, DevTimeout, MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { Analytics } from "../type/analytics-util.type";
import { ProjectUpdate } from "../type/project-update.type";
import { YqzMedia } from "../../../bgw/media/type/media.type";
import { YqzMediaType } from "@yqz/nest/lib/types/media.type";
import { MediaService } from "../../../bgw/media/service/media.service";
import { ProjectService } from "../../../bgw/project/service/project.service";
import { AnalyticsService } from "./analytics.service";
import { ProjectUpdateBoardDao } from "../dao/project-update.dao";
import { ObjUtil } from "@src/util/obj.util";
import { ProjectDao } from "../../../bgw/project/dao/project.dao";
import { OnsitePatrolBoardDao } from "../dao/onsite-patrol.dao";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { CorpReportBasicsService } from "../../corp-report/service/basics/corp-report-basics.service";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { ManagerService } from "../../../bgw/manager/service/manager.service";
import { ManagerDao } from "../../../bgw/manager/dao/manager.dao";
import { Company } from "../../../bgw/company/dto/company.dto";

@Injectable()
export class ProjectUpdateService {
    private readonly logger = new MyLogger(ProjectUpdateService.name);
    constructor(
        private readonly projectUpdateDao: ProjectUpdateBoardDao,
        private readonly mediaService: MediaService,
        private readonly projectService: ProjectService,
        private readonly analyticsService: AnalyticsService,
        private readonly projectDao: ProjectDao,
        private readonly onsitePatrolDao: OnsitePatrolBoardDao,
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly companyDao: CompanyDao,
        private readonly managerService: ManagerService,
        private readonly managerDao: ManagerDao,
    ) { }

    // @DevTimeout(100)
    async test11() {
        const total = await this.projectUpdateAnalyticsOptions({
            companyType: Company.CompanyType.BasicCompany,
            companyId: "*********",
            departmentId: "12543",
            from: "2024-01-01",
            to: "2025-01-31"
        })
        console.log(`total: ${JSON.stringify(total)}`)
    }

    /**
     * 工地更新数据报表统计
     * @param body 
     */
    async projectUpdateAnalyticsTotal(body: Analytics.Req): Promise<ProjectUpdate.AnalyticsTotal> {
        const result: ProjectUpdate.AnalyticsTotal = { totalPosition: "0", totalManager: "0", totalProject: "0", totalDegree: "0", totalImg: "0" };
        // 统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.ProjectUpdate});
        if (!query) return result;
        /** 再次过滤 公司配置 */
        const { noManager, isAllRole, dealPostList, dealManagerList, isShowUnpatroled, isCustomerCase1 } = await this.analyticsService.onlinePathConfig(query, DateReport.Subject.ProjectUpdate);
        if (noManager) return result;
        const analyticsTotal = await this.projectUpdateDao.analyticsTotal(query);
        let dealManager = "0"
        let dealPosition = "0"
        /** 为了 dealManager 实际巡检的人数 */
        if (!isAllRole && dealManagerList instanceof Array) {
            query.groupBy = 'manager';//根据员工分组
            const groupData = await this.projectUpdateDao.analyticsGroupCount(query);
            query.groupBy = 'role';//根据员工分组
            const postData = await this.projectUpdateDao.analyticsGroupCount(query);
            const persons = groupData.map(res => {
                return {
                    managerId: res.groupId,
                    nickName: res.groupName
                }
            })
            const postRes = postData.map(res => {
                return {
                    roleId: res.groupId,
                    roleName: res.groupName
                }
            })
            const { realManagerList, realManager } = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: persons as any })

            const { realPost } = this.analyticsService.getPositionInfo({ isAllRole, dealPostList, realManagerList, postRes: postRes as any, isCustomerCase1 })

            dealManager = String(realManager);
            dealPosition = String(realPost);
        }
        result.totalPosition = analyticsTotal.totalPosition;
        result.totalManager = analyticsTotal.totalManager;
        result.totalProject = analyticsTotal.totalProject;
        result.totalDegree = analyticsTotal.totalDegree;
        result.dealManager = dealManager;
        result.dealPosition = dealPosition;
        const totalImg = analyticsTotal.imgCount ? String(analyticsTotal.imgCount) : '0';
        result.totalImg = totalImg;
        return result;
    }

    /**
     * 工地更新数据报表岗位分布
     * @param params 
     * @returns 
     */
    @CheckFormatCsv(ProjectUpdate.csvPosition)
    async projectUpdateAnalyticsPosition(body: Analytics.Req) {
        let result: ProjectUpdate.AnalyticsPosition[] = [];
        // 统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.ProjectUpdate});
        if (!query) return { items: result };
        /** 再次过滤 公司配置 */
        const { noManager, isAllRole, dealPostList, dealManagerList, isShowUnpatroled, isCustomerCase1 } = await this.analyticsService.onlinePathConfig(query, DateReport.Subject.ProjectUpdate);
        if (noManager) return { items: result };
        query.groupBy = 'role';//根据岗位分组
        const groupData = await this.projectUpdateDao.analyticsGroupCount(query);
        for (const res of groupData) {
            //统计更新图片数
            let totalImg = '0';
            let totalVideo = '0';
            const updateIds = res?.updateIds?.split(",");
            if (res.updateIds && !_.isEmpty(updateIds)) {
                const imgCountInfo = await this.projectUpdateDao.searchProjectUpdateMediaCountByUpdateIds(updateIds)
                totalImg = imgCountInfo?.imgCount || '0';
                totalVideo = imgCountInfo?.videoCount || '0';
            }
            result.push({
                roleId: res.groupId,
                roleName: res.groupName,
                managerId: res?.managerId,
                totalManager: res.totalManager,
                totalProject: res.totalProject,
                totalDegree: res.totalDegree,
                totalImg,
                totalVideo
            });
        }

        if (!isAllRole && dealManagerList instanceof Array && isShowUnpatroled === 'Y') {
            const persons = result.map(res => {
                return {
                    managerId: res?.managerId,
                    roleId: res.roleId,
                    roleName: res.roleName,
                }
            }) as any
            const { realManagerList } = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: persons })

            const { realPost, postZeros } = this.analyticsService.getPositionInfo({ postRes: result as any, isAllRole, isCustomerCase1, dealPostList, realManagerList })
            if (!_.isEmpty(postZeros))
                result.push(...postZeros.map(item => {
                    return {
                        roleId: item.roleId,
                        roleName: item.roleName,
                        totalManager: "0",
                        totalProject: "0",
                        totalDegree: "0",
                        totalImg: '0',
                        totalVideo: '0'
                    }
                }))
        }

        // 合并相同的岗位
        result = ObjUtil.mergeByFields(result, "roleName")

        return { items: result };
    }

    /**
     * 工地更新数据报表员工分布
     * @param params 
     * @returns 
     */
    @CheckFormatCsv(ProjectUpdate.csvPerson)
    async projectUpdateAnalyticsPerson(body: Analytics.Req) {
        const result = [];
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.ProjectUpdate});
        if (!query) return { items: result };
        /** 再次过滤 公司配置 并得到 本地需要分析的所有人员 dealManagerList: {}[] | null */
        let { noManager, isAllRole, dealManagerList, isShowUnpatroled } = await this.analyticsService.onlinePathConfig(query, DateReport.Subject.ProjectUpdate);
        if (noManager) return { addressSelects: [], analyticPersons: [] }
        query.groupBy = 'manager';//根据员工分组
        const groupData = await this.projectUpdateDao.analyticsGroupCount(query);
        for (const res of groupData) {
            //统计更新图片数
            let totalImg = '0';
            let totalVideo = '0';
            if (res.updateIds) {
                const updateIds = res.updateIds.split(",");
                const imgCountInfo = await this.projectUpdateDao.searchProjectUpdateMediaCountByUpdateIds(updateIds)
                totalImg = imgCountInfo?.imgCount || '0';
                totalVideo = imgCountInfo?.videoCount || '0';
            }
            result.push({
                managerId: res.groupId,
                nickName: res.groupName,
                totalProject: res.totalProject,
                totalDegree: res.totalDegree,
                totalImg,
                totalVideo,
                projectIdList: (res.projectIds || "")?.split(","),
            });
        }
        /** 如果公司层面筛选了 现场巡检岗位 则需要具体分析。 如果选择了全部 则不需要做多余分析 */
        if (!isAllRole && dealManagerList instanceof Array) {
            const {managerZeros} = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: result })
            const managerIdList = managerZeros.map(m => m.managerId)
            const managers = managerIdList.length == 0 ? [] : await this.managerDao.getBelongToListByDeptId({ managerIdList });
            const managerMap = _.keyBy(managers, 'managerId');
            result.push(...managerZeros.map(p => ({
                ...p, 
                personDepartmentId: managerMap[p.managerId]?.departmentId,
                personDepartmentName: managerMap[p.managerId]?.departmentName,
                totalDegree: '0',
                totalProject: '0',
                noUpdateCount: Number(p.unpatroledCount),
                totalImg: '0',
                totalVideo: '0',
                projectIdList: []
                } as any)))
        }
        // 筛选了岗位且公司设置展示未巡检数据
        if (!isAllRole && !!dealManagerList.length && isShowUnpatroled === 'Y') {
            const managerIds = (result || []).map(m => m.managerId);
            if (!_.isEmpty(managerIds) && body.companyId) {
                const managerPatroledList = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: query.companyIds, managerIds, startDate: query.from, endDate: query.to, roleNames: query.roleNames });
                const managerPatroledMap: { [key: string]: { managerId: string, projectIds: string[] } } = managerPatroledList.reduce((prev, cur) => { prev[cur.managerId] = cur; return prev; }, {});
                for (const person of result) {
                    const managerPatroledProjectIds = managerPatroledMap[person.managerId]?.projectIds || [];
                    person.noUpdateCount = _.difference(managerPatroledProjectIds, (person?.projectIdList || [])).length;
                    // person.noUpdateProjectIds = _.difference(managerPatroledProjectIds, (person?.projectIdList || [])).join(',');
                }
            }
        }
        //查询员工部门信息
        const managerIdList = (result || []).map(m => m.managerId);
        const managers = !_.isEmpty(managerIdList) ? await this.managerService.searchManagerInfoMap({ managerIdList }) : {};
        result.forEach(res => {
            res.departmentId = managers[res.managerId]?.departmentId || "";
            res.departmentName = managers[res.managerId]?.departmentName || "";
        });
        return { items: result };
    }

    /**
     * 工地更新数据报表数据明细
     * @param params 
     * @returns 
     */
    @CheckFormatCsv((body) => {
        if (body?.companyType === Company.CompanyType.Conglomerate) return ProjectUpdate.csvList
        return _.filter(ProjectUpdate.csvList, item => item.key !== "linkCompanyName");//不是集团公司不需要展示所属公司
    })
    async projectUpdateAnalyticsDataDetail(body: Analytics.Req) {
        const result: { total: number, items: ProjectUpdate.AnalyticsDataDetail[] } = { total: 0, items: [] };
        //统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.ProjectUpdate});
        if (!query) return result;
        /** 再次过滤 公司配置 */
        const { noManager, isAllRole, isShowUnpatroled } = await this.analyticsService.onlinePathConfig(query, DateReport.Subject.ProjectUpdate);

        const companyIds = query?.linkCompanyId ? [query?.linkCompanyId]:query?.companyIds;
        //筛选-工地负责人 | 工地地址
        let searchProjectIds = []
        if ((query.projectDirectorId || query.projectAddress) && !_.isEmpty(companyIds)) {
            searchProjectIds = await this.projectDao.getProjectIds({
                projectDirectorId: query.projectDirectorId,//绑定工地(PC)
                companyIds: companyIds,
                projectAddress: query.projectAddress
            });
            searchProjectIds = searchProjectIds.map(res => res.projectId);
            if (_.isEmpty(searchProjectIds)) return result;//筛选后没结果直接返回
        }
        if (_.isEmpty(query?.managerIdList)) return result
        // 查询应巡检工地
        const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: companyIds, projectIds: searchProjectIds, managerIds: query?.managerIdList, startDate: query.from, endDate: query.to, roleNames: query.roleNames });
        const shouldPatrols = []
        res.forEach(item => {
            item.projectIds.forEach(projectId => {
                shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName })
            })
        })
        
        // 查询成员更新过的工地
        const groupData = await this.projectUpdateDao.getManagerUPdateProjectIds({ ...query, projectIds: searchProjectIds });
        
        // 合并工地更新过的工地和应巡检的工地，去重合并到一起作为查询条件
        searchProjectIds = shouldPatrols.map(res => res.projectId);
        groupData.map(res => { if (res.projectIdStr) searchProjectIds.push(...(res.projectIdStr || "").split(",")) });
        if (_.isEmpty(searchProjectIds)) return result;
        query.projectIds = _.uniq(searchProjectIds);

        if (!isAllRole && isShowUnpatroled === 'Y') {
            const pageNo = query.pageNo
            const pageSize = query.pageSize
            delete query.pageNo
            delete query.pageSize
            const { items: list } = await this.projectUpdateDao.analyticsDataDetail(query);
            const patroledList = list as any[]
            let shouldPatroledList = []
            const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: query.companyIds, managerIds: query.managerIdList, projectIds: query.projectIds, startDate: query.from, endDate: query.to, roleNames: query.roleNames });
            res.forEach(item => {
                item.projectIds.forEach(projectId => {
                    shouldPatroledList.push({ managerId: item.managerId, projectId, roleName: item.roleName })
                })
            })
            shouldPatroledList.sort((a, b) => {
                if (a.managerId !== b.managerId) {
                    return Number(b.managerId) - Number(a.managerId)
                } else {
                    return Number(b.projectId) - Number(a.projectId)
                }
            })

            const unpatroledList = _.differenceWith(shouldPatroledList, patroledList, (a, b) => a.managerId === b.managerId && a.projectId === b.projectId)

            let mergedList = []
            if (query.isPatroled === 'N') {
                mergedList = unpatroledList
            }else if (query.isPatroled === 'Y') {
                mergedList = patroledList
            } else {
                mergedList = [...patroledList, ...unpatroledList]
            }

            const total = mergedList.length;
            let items;
            if (pageNo && pageSize) {
                items = mergedList.slice((pageNo - 1) * pageSize, pageNo * pageSize)
            } else {
                items = mergedList;
            }

            result.total = total;
            result.items = items;
        } else {
            const { total, items } = await this.projectUpdateDao.analyticsDataDetail(query);
            result.total = total;
            result.items = items;
        }
        const managerIds = [];
        const projectIds = [];
        const updateIds = [];
        result.items.map(res => {
            if (res.managerId && !managerIds.includes(res.managerId)) managerIds.push(res.managerId);
            if (res.projectId && !projectIds.includes(res.projectId)) projectIds.push(res.projectId);
            if (res.projectUpdateId && !updateIds.includes(res.projectUpdateId)) updateIds.push(res.projectUpdateId);
        });
        //查询员工部门信息
        // const managerMap = await this.managerService.searchManagerInfoMap({ managerIdList: managerIds });
        const managers = managerIds.length == 0 ? [] : await this.managerDao.getBelongToListByDeptId({ managerIdList: managerIds });
        const managerMap = _.keyBy(managers, 'managerId');
        //查询工地地址
        const extraInfoMap = await this.projectService.getProjectAddressByProjectIds({ projectIds: projectIds });
        //工地信息
        const projects = projectIds.length == 0 ? [] : await this.projectDao.getOnsiteProjectInfos({ projectIdList: projectIds });
        const projectMap = _.keyBy(projects, 'projectId');
        //查询图片
        const updateMediaList = !_.isEmpty(updateIds) ? await this.projectUpdateDao.searchProjectUpdateMediaByUpdateIds(updateIds) : [];
        const mediaMap = !_.isEmpty(updateMediaList) ? await this.mediaService.searchParadigm({ mediaIdList: updateMediaList.map(media => media.photoId) }) : {};
        const updateMediaMap = updateMediaList.reduce((item, { projectUpdateId, photoId }) => {
            if (!item[projectUpdateId]) {
                item[projectUpdateId] = [];
            }
            if (mediaMap[photoId]) {
                item[projectUpdateId].push(mediaMap[photoId]);
            }
            return item;
        }, {});
        //组装数据
        result.items = result.items.map(res => {
            //处理图片
            const updateImgs: YqzMedia[] = updateMediaMap[res.projectUpdateId] ? updateMediaMap[res.projectUpdateId] : [];
            const updateImgNum = updateImgs.filter(img => img.mediaType != YqzMediaType.VIDEO).length;
            const updateVideoNum = updateImgs.filter(img => img.mediaType == YqzMediaType.VIDEO).length;
            return {
                departmentId: res?.departmentId || managerMap[res.managerId]?.departmentId || "",
                departmentName: res?.departmentName || managerMap[res.managerId]?.departmentName || "",
                linkCompanyId: res.linkCompanyId,
                linkCompanyName: res.linkCompanyName,
                roleId: res.roleId,
                roleName: res.roleName,
                managerId: res.managerId,
                nickName: res.nickName || managerMap[res.managerId]?.nickName,
                projectId: res.projectId,
                projectStartTime: projectMap[res.projectId]?.projectStartTime?DateFns.format(projectMap[res.projectId]?.projectStartTime,"yyyy-MM-dd"):"",
                projectCompletedTime: projectMap[res.projectId]?.projectCompletedTime?DateFns.format(projectMap[res.projectId]?.projectCompletedTime,"yyyy-MM-dd"):"",
                projectAddress: extraInfoMap[res.projectId] || "",
                updateTime: res.updateTime ? DateFns.format(new Date(res.updateTime), 'yyyy-MM-dd HH:mm:ss') : "未更新",
                updateContent: res.updateContent,
                updateImgs,
                updateImgNum: updateImgNum > 0 ? `${String(updateImgNum)}张图片` : "",
                updateVideoNum: updateVideoNum > 0 ? `${String(updateVideoNum)}个视频` : "",
            }
        });

        return result;
    }

    async projectUpdateAnalyticsOptions(body: Analytics.Req) {
        const result = { roleList: [], managerList: [], linkCompany: [] };
        //统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.ProjectUpdate});
        if (!query) return { items: result };

        // /** 再次过滤 公司配置 */
        const { noManager, isAllRole, dealPostList, dealManagerList } = await this.analyticsService.onlinePathConfig(query, DateReport.Subject.ProjectUpdate);
        if (noManager) return result

        //根据岗位分组
        const { items: roleList } = await this.projectUpdateAnalyticsPosition(body);
        result.roleList = _.uniqBy(roleList.filter(res => Number(res.totalManager || 0) > 0).map(res => {
            return { name: res.roleName, value: res.roleName };
        }), "name");
        //根据成员分组
        const { items: managerList } = await this.projectUpdateAnalyticsPerson(body);
        result.managerList = managerList.map(res => {
            return { name: res.nickName, value: res.managerId };
        });
        // 根据部门id获取集团公司
        const companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(query.departmentId);
        const companyList = await this.companyDao.searchCompanyList({ companyIds });
        result.linkCompany = companyList.map(item => {
            return {
                name: item.companyName,
                value: item.id,
            };
        })

        // if (!isAllRole && dealPostList instanceof Array) {
        //     result.roleList = _.uniqBy(dealPostList.map(post => ({ roleId: post.roleId, roleName: post.name })), "roleName")
        // }

        // if (!isAllRole && dealManagerList instanceof Array) {
        //     /** 为了 dealManager 实际巡检的人数 */
        //     const persons = await this.projectUpdateDao.analyticsGroup({ departmentIds: query.departmentIds, groupBy: 'manager', departmentId: null });
        //     if (!isAllRole && dealManagerList instanceof Array) {
        //         let dealManager = dealManagerList.length
        //         dealManagerList.forEach(manager => {
        //             let i = 0;
        //             for (; i < persons.length; i++) {
        //                 const item = persons[i];
        //                 if (item.groupId == manager.managerId) break;
        //             }
        //             /** 如果 真实巡检人员 中没有 应该的巡检人员 则为这些人填补0为数据 */
        //             if (i == persons.length) {
        //                 /** 去掉 离职人员 未签到的人员数量 并且不把此人加入到巡检人员列表 */
        //                 if (manager.deleteFlag != 'N') {
        //                     dealManager--;
        //                     return;
        //                 }
        //             }

        //             /** 筛选出 所有的 巡检员工 + 应该巡检的在职员工 */
        //             let j = 0;
        //             for (; j < result.managerList.length; j++) {
        //                 const item = result.managerList[j];
        //                 if (item.managerId == manager.managerId) break;
        //             }
        //             if (j == result.managerList.length) result.managerList.push({ managerId: manager.managerId, nickName: manager.nickName })
        //         })
        //     }
        // }
        return result;
    }
}