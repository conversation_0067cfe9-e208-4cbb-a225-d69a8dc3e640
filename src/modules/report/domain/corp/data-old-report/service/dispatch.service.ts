import { Injectable } from "@nestjs/common";
import { CheckFormatCsv, DevT<PERSON>out, <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { Analytics } from "../type/analytics-util.type";
import { AnalyticsService } from "./analytics.service";
import { DispatchBoardDao } from "../dao/dispatch.dao";
import { ProjectDao } from "../../../bgw/project/dao/project.dao";
import { DispatchType } from "../type/dispatch.type";
import { ManagerDao } from "../../../bgw/manager/dao/manager.dao";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { Flag } from "@yqz/nest/lib/types/common.type";
import { Filter, WithId } from "mongodb";
import { Dispatch } from "@src/modules/report/mongodb/camera/dispatch.collection";
import { ObjUtil } from "@src/util/obj.util";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { CorpReportBasicsService } from "../../corp-report/service/basics/corp-report-basics.service";
import { Company } from "../../../bgw/company/dto/company.dto";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { DataReportModuleFilter } from "../../data-report/dto/data-report-module-filter.dto";

@Injectable()
export class DispatchService {
    private readonly logger = new MyLogger(DispatchService.name);
    projectUpdateDao: any;
    projectService: any;
    mediaService: any;
    constructor(
        private readonly dispatchBoardDao: DispatchBoardDao,
        private readonly analyticsService: AnalyticsService,
        private readonly projectDao: ProjectDao,
        private readonly managerDao: ManagerDao,
        private readonly companyDao: CompanyDao,
        private readonly corpReportBasicsService: CorpReportBasicsService,
    ) { }

    // @DevTimeout(100)
    async test11() {
        const total = await this.searchList({
            "companyId": "65",
            "departmentId":"13",
            "from":"2024-01-01",
            "to":"2025-01-31",
            "pageNo": 1,
            "pageSize": 10
        })
        console.log(`total: ${JSON.stringify(total)}`)
    }

    /**
     * 统筹派工数据报表统计
     * @param body 
     */
    async dispatchAnalyticsTotal(body: Analytics.Req): Promise<DispatchType.AnalyticsTotal> {
        const result: DispatchType.AnalyticsTotal = { totalPosition: "0", totalManager: "0", totalProject: "0", totalDispatch: "0", totalPlan: "0", totalActual: "0" };
        // 统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.Dispatch});
        if (!query) return result;
        const projectIds = await this.projectDao.searchProjectIdsByDept({ departmentIds: query.departmentIds })
        if (_.isEmpty(projectIds)) return result;
        query.projectIds = projectIds.map(res => res.projectId);
        const [analyticsTotal] = await this.dispatchBoardDao.analyticsTotal(query);
        result.totalPosition = String(analyticsTotal?.tagsCount||"0");
        result.totalManager = String(analyticsTotal?.personsCount||"0");
        result.totalProject = String(analyticsTotal?.projectsCount||"0");
        result.totalDispatch = String(analyticsTotal?.totalRecords||"0");
        result.totalPlan = String(analyticsTotal?.planDaysSum||"0");
        result.totalActual = String(analyticsTotal?.actualDaysSum||"0");
        return result;
    }

    /**
     * 统筹派工数据报表岗位分布
     * @param params 
     * @returns 
     */
    @CheckFormatCsv(DispatchType.csvPosition)
    async dispatchAnalyticsPosition(body: Analytics.Req) {
        let result: DispatchType.AnalyticsPosition[] = [];
        // 统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.Dispatch});
        if (!query) return { items: result };
        const projectIds = await this.projectDao.searchProjectIdsByDept({ departmentIds: query.departmentIds })
        if (_.isEmpty(projectIds)) return result;
        query.projectIds = projectIds.map(res => res.projectId);
        const list = await this.dispatchBoardDao.analyticsGroupCount(query);
        const faceIdList = list.map(res => res.faceId);
        const faceList = !_.isEmpty(faceIdList) ? await this.dispatchBoardDao.searchFace({ companyIds: query.companyIds, faceEntityIdList: faceIdList }) : [];
        const faceMap = _.keyBy(faceList, 'faceEntityId');
        const personList = []
        list.map(res => {
            const face = faceMap[res.faceId];
            personList.push({
                roleName: face?.tags || "",
                totalProject: String(res.projectsCount) || "0",
                totalManager: String(res.managersCount) || "0",
                totalPlan: String(res.planDays) || "0",
                // totalActual: String(res.actualDays) || "0"
            });
        })

        // // 合并相同岗位的数据
        // result = _.reduce(personList, (acc, current) => {
        //     const existing = acc.find(item => item.roleName === current.roleName);
        //     if (existing) {
        //         existing.totalProject = Number(existing.totalProject);
        //         existing.totalPlan = Number(existing.totalPlan);
        //         existing.totalActual = Number(existing.totalActual);
        //         // 如果存在相同的 name，进行合并  
        //         existing.totalProject += Number(current.totalProject);
        //         existing.totalPlan += Number(current.totalPlan);
        //         existing.totalActual += Number(current.totalActual);
        //     } else {
        //         // 如果不存在，则添加到结果数组中  
        //         acc.push({ ...current });
        //     }
        //     return acc;
        // }, []);
        // 合并相同的岗位
        result = ObjUtil.mergeByFields(personList,"roleName")

        return { items: result };
    }

    /**
     * 统筹派工数据报表岗位分布
     * @param params 
     * @returns 
     */
    @CheckFormatCsv(DispatchType.csvPosition)
    async dispatchAnalyticsPositionV2(body: Analytics.Req) {
        let result: DispatchType.AnalyticsPosition[] = [];
        // 统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.Dispatch});
        if (!query) return { items: result };
        const projectIds = await this.projectDao.searchProjectIdsByDept({ departmentIds: query.departmentIds })
        if (_.isEmpty(projectIds)) return { items: result };
        query.projectIds = projectIds.map(res => res.projectId);
        const list = await this.dispatchBoardDao.analyticsGroup(query);
        result = list.map(item => {
            return {
                roleName: item?.tags[0],
                totalManager: String(item?.managersCount || "0"),
                totalPlan: String(item?.planDays || "0"),
                totalProject: String(item?.projectsCount || "0")
            }
        })

        return { items: result };
    }

    /**
     * 统筹派工数据报表员工分布
     * @param params 
     * @returns 
     */
    @CheckFormatCsv(DispatchType.csvPerson)
    async dispatchAnalyticsPerson(body: Analytics.Req) {
        const result: DispatchType.AnalyticsPerson[] = [];
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.Dispatch});
        if (!query) return { items: result };
        const projectIds = await this.projectDao.searchProjectIdsByDept({ departmentIds: query.departmentIds })
        if (_.isEmpty(projectIds)) return result;
        query.projectIds = projectIds.map(res => res.projectId);
        //筛选- 姓名 || 标签
        if (body.roleName) {
            const faceList = await this.dispatchBoardDao.searchFace({ companyId: String(query.companyId), tags: body.roleName });
            const faceIdList = faceList.map(x => x.faceEntityId);
            if (_.isEmpty(faceIdList)) return result;
            query.managerIdList = faceIdList
            if (_.isEmpty(faceIdList)) return result;
        }
        const list = await this.dispatchBoardDao.analyticsGroupCount(query);
        const faceIdList = list.map(res => res.faceId);
        const faceList = !_.isEmpty(faceIdList) ? await this.dispatchBoardDao.searchFace({ companyIds: query.companyIds, faceEntityIdList: faceIdList }) : [];
        const faceMap = _.keyBy(faceList, 'faceEntityId');
        list.map(res => {
            const face = faceMap[res.faceId];
            result.push({
                managerId: res.faceId || "",
                nickName: face?.name || "",
                totalProject: String(res.projectsCount) || "0",
                totalPlan: String(res.planDays) || "0",
                totalActual: String(res.actualDays) || "0"
            });
        })
        return { items: result };
    }

    /**
     * 统筹派工数据报表数据明细
     * @param params 
     * @returns 
     */
    @CheckFormatCsv((body) => {
        if (body?.companyType === Company.CompanyType.Conglomerate) return DispatchType.csvList
        return _.filter(DispatchType.csvList, item => item.key !== "linkCompanyName");//不是集团公司不需要展示所属公司
    })
    async searchList(params: DispatchType.SearchListReq) {
        const { companyId, pageNo, pageSize, from, to, acceptStatus, status, departmentId, projectAddress, arriveStatus, exitStatus, name, tags, plannedArrivalToday, arrivalToday } = params;
        // 校验必传参数
        if (!companyId)
            throw new YqzException("必传参数不能为空");
        const result = { total: 0, items: [] };
        // 统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...params, module: DateReport.Subject.Dispatch});
        if (!query) return result;
        const scopeProjectIds = await this.projectDao.searchProjectIdsByDept({ departmentIds: query.departmentIds })
        if (_.isEmpty(scopeProjectIds) && _.isEmpty(query.companyIds)) return result;
        // 筛选条件
        if (query.linkCompanyId) query.companyIds = [query.linkCompanyId]
        const filters: Filter<Dispatch>[] = [
            { companyId: {$in: query.companyIds} },
            { deletion: { $ne: true } },
        ];
        const todayStart = DateFns.startOfDay(new Date());
        const todayEnd = DateFns.endOfDay(new Date());
        let searchProjectIds = scopeProjectIds.map(res => res.projectId);
        //筛选-工地归属 || 工地地址
        if (projectAddress && !_.isEmpty(searchProjectIds)) {
            searchProjectIds = await this.projectDao.getProjectIds({
                projectAddress: projectAddress,//绑定工地(PC)
                projectIds: searchProjectIds,
            });
            searchProjectIds = searchProjectIds.map(res => String(res.projectId))
        }
        if (_.isEmpty(searchProjectIds)) return result;
        filters.push({ projectId: { $in: searchProjectIds } })
        //筛选- 姓名 || 标签
        if (name || tags) {
            const faceList = await this.dispatchBoardDao.searchFace({ companyId: String(companyId), name, tags });
            const faceIdList = faceList.map(x => x.faceEntityId);
            if (_.isEmpty(faceIdList)) return result;
            filters.push({ faceEntityId: { $in: faceIdList } });
        }
        // 筛选 - 进场状态
        if (arriveStatus) {
            const arriveStatusMap: Record<DispatchType.ArriveStatus, any> = {
                [DispatchType.ArriveStatus.PENDING_JOIN]: { actualStart: null, planStart: { $gt: DateFns.startOfDay(new Date()) } },
                [DispatchType.ArriveStatus.ADVANCE_JOIN]: {
                    actualStart: { $ne: null },
                    $expr: {
                        $gt: [
                            { $dateToString: { format: "%Y-%m-%d", date: "$planStart", timezone: 'Asia/Shanghai' } },
                            { $dateToString: { format: "%Y-%m-%d", date: "$actualStart", timezone: 'Asia/Shanghai' } }
                        ]
                    },
                },
                [DispatchType.ArriveStatus.TODAY_JOIN]: {
                    actualStart: { $ne: null },
                    $expr: {
                        $eq: [
                            { $dateToString: { format: "%Y-%m-%d", date: "$planStart", timezone: 'Asia/Shanghai' } },
                            { $dateToString: { format: "%Y-%m-%d", date: "$actualStart", timezone: 'Asia/Shanghai' } }
                        ]
                    },
                },
                [DispatchType.ArriveStatus.OVERDUE_JOIN]: {
                    actualStart: { $ne: null },
                    $expr: {
                        $lt: [
                            { $dateToString: { format: "%Y-%m-%d", date: "$planStart", timezone: 'Asia/Shanghai' } },
                            { $dateToString: { format: "%Y-%m-%d", date: "$actualStart", timezone: 'Asia/Shanghai' } }
                        ]
                    },
                },
                [DispatchType.ArriveStatus.OVERDUE_NOT_JOIN]: { actualStart: null, planStart: { $lt: DateFns.startOfDay(new Date()) } },
            };
            filters.push(arriveStatusMap[arriveStatus]);
        }
        // 筛选 - 退场状态
        if (exitStatus) {
            const exitStatusMap: Record<DispatchType.ExitStatus, any> = {
                [DispatchType.ExitStatus.ADVANCE_EXIT]: {
                    actualEnd: { $ne: null },
                    $expr: {
                        $gte: [
                            { $dateToString: { format: "%Y-%m-%d", date: "$planEnd", timezone: 'Asia/Shanghai' } },
                            { $dateToString: { format: "%Y-%m-%d", date: "$actualEnd", timezone: 'Asia/Shanghai' } }
                        ]
                    },
                },
                [DispatchType.ExitStatus.OVERDUE_EXIT]: {
                    actualEnd: { $ne: null },
                    $expr: {
                        $lt: [
                            { $dateToString: { format: "%Y-%m-%d", date: "$planEnd", timezone: 'Asia/Shanghai' } },
                            { $dateToString: { format: "%Y-%m-%d", date: "$actualEnd", timezone: 'Asia/Shanghai' } }
                        ]
                    },
                },
            };
            filters.push(exitStatusMap[exitStatus]);
        }
        // 筛选 - 今日到场
        if (arrivalToday) {
            const bodySignCountTodyList = await this.dispatchBoardDao.searchGroupCountList({
                companyId,
                startTime: todayStart,
            })
            const bodySignTodyList = bodySignCountTodyList.filter(item => item.count > 0).map(item => ({
                faceEntityId: item.faceEntityId,
                projectId: item.projectId
            }))
            switch (arrivalToday) {
                case Flag.Y:
                    if (_.isEmpty(bodySignTodyList)) {
                        return result
                    }
                    filters.push({
                        $or: bodySignTodyList
                    })
                    break;
                case Flag.N:
                    if (!_.isEmpty(bodySignTodyList)) {
                        filters.push({
                            $nor: bodySignTodyList
                        })
                    }
                    break;
            }
        }
        // 筛选 - 今日应到场
        if (plannedArrivalToday) {
            switch (plannedArrivalToday) {
                case Flag.Y:
                    filters.push({
                        planStart: { $lte: todayEnd },
                        $or: [
                            { planEnd: { $exists: false } },
                            { planEnd: { $gte: todayStart } }
                        ]
                    })
                    break;
                case Flag.N:
                    filters.push({
                        $or: [
                            {
                                planStart: { $exists: false },
                            },
                            {
                                planEnd: { $exists: false },
                                planStart: { $lt: todayStart }
                            },
                            {
                                planEnd: { $lt: todayStart }
                            },
                            {
                                planStart: { $gt: todayEnd },
                            }
                        ]
                    })
                    break;
            }
        }
        if (status) filters.push({ status });
        //查询派工记录类型
        if (acceptStatus === DispatchType.AcceptStatus.NotNotify) filters.push({ accepted: { $exists: false }, reminders: { $exists: false } });//未通知
        if (acceptStatus === DispatchType.AcceptStatus.Pending) filters.push({ accepted: { $exists: false }, reminders: { $exists: true } });//已通知
        if (acceptStatus === DispatchType.AcceptStatus.Confirmed) filters.push({ accepted: true });//已接受
        if (acceptStatus === DispatchType.AcceptStatus.Rejected) filters.push({ accepted: false });//已拒绝
        if (from) filters.push({ planStart: { $gte: DateFns.startOfDay(new Date(from)) } });
        if (to) filters.push({ planStart: { $lte: DateFns.endOfDay(new Date(to)) } });
        //查询总数
        result.total = await MyMongoDb.collections.camera.dispatch.countDocuments({ $and: filters });
        const pipeline: any[] = [{ $match: { $and: filters } }];// 构建查询聚合管道
        pipeline.push({ $sort: { createAt: -1 } });// createAt 降序排列
        if (pageNo && pageSize) {
            pipeline.push({ $skip: (pageNo - 1) * pageSize }); // 跳过前 (pageNo - 1) * pageSize 个记录
            pipeline.push({ $limit: pageSize }); // 限制返回的记录数为 pageSize
        }
        // 查询列表
        const items = await MyMongoDb.collections.camera.dispatch.aggregate<WithId<Dispatch>>(pipeline).toArray();
        //查询工地信息
        const projectIds = _.uniq(items.map(x => x.projectId).filter(x => !!x));
        const projectList = !_.isEmpty(projectIds) ? await this.projectDao.addressSearch({ projectIdList: projectIds }) : [];
        const projectMap = _.keyBy(projectList, 'projectId')
        //查询公司信息
        const companyIdList = _.uniq(items.map(x => x.companyId).filter(x => !!x))
        const companyList = companyIdList.length == 0 ? [] : await this.companyDao.searchCompanyList({ companyIds: companyIdList });
        const companyMap = _.keyBy(companyList, 'id');
        //查询成员信息
        const faceIdList = _.uniq(items.map(x => x.faceEntityId).filter(x => !!x));
        const managerList = !_.isEmpty(faceIdList) ? await this.managerDao.searchManagerList({ managerIdList: faceIdList }) : [];
        const managerMap = _.keyBy(managerList, 'id');
        const faceList = !_.isEmpty(faceIdList) ? await this.dispatchBoardDao.searchFace({ companyId: String(companyId), faceEntityIdList: faceIdList }) : [];
        const faceMap = _.keyBy(faceList, 'faceEntityId');
        const bodySignCountTodyList = await this.dispatchBoardDao.searchGroupCountList({
            companyId,
            startTime: todayStart,
            faceEntityIdList: faceIdList,
        })
        const bodySignCountTodyMap = _.reduce(bodySignCountTodyList, (result, item) => {
            const key = `${item.faceEntityId}-${item.projectId}`;
            result[key] = item;
            return result;
        }, {});
        //组装数据
        items.map(res => {
            const id = res._id.toHexString();
            const projectInfo = projectMap[res.projectId];
            const companyInfo = companyMap[res.companyId]
            const planStart = res.planStart ? new Date(res.planStart) : null;
            const planEnd = res.planEnd ? new Date(res.planEnd) : null;
            const actualInfo = acceptStatus !== DispatchType.AcceptStatus.Rejected ? this.getDispatchActualStatus(res) : { arrive: null, exit: null };
            // 计算天数，格式化日期
            // const days = (_.isDate(planStart) && _.isDate(planEnd)
            //         ? DateFns.differenceInDays(DateFns.startOfDay(planEnd), DateFns.startOfDay(planStart)) + 1
            //         : 0);
            const days = res.duration && _.isDate(planStart) && _.isDate(planEnd)
                ? res.duration
                : (_.isDate(planStart) && _.isDate(planEnd)
                    ? DateFns.differenceInDays(DateFns.startOfDay(planEnd), DateFns.startOfDay(planStart)) + 1
                    : 0);
            const startTime = _.isDate(planStart) ? DateFns.format(planStart, "yyyy-MM-dd") : "";
            const endTime = _.isDate(planEnd) ? DateFns.format(planEnd, "yyyy-MM-dd") : "";
            const acceptStatusCode = this.getAcceptStatusKey(res.accepted, res.reminders) as DispatchType.AcceptStatus
            // 今日到场
            const arrivalToday = bodySignCountTodyMap[`${res?.faceEntityId}-${res?.projectId}`]?.count > 0 ? Flag.Y : Flag.N
            // 今日应到场
            const plannedArrivalToday = res.planStart && res.planEnd && !DateFns.isBefore(new Date(), DateFns.startOfDay(new Date(res.planStart))) && !DateFns.isAfter(new Date(), DateFns.endOfDay(new Date(res.planEnd))) ? Flag.Y : Flag.N
            result.items.push({
                id,
                linkCompanyId: res.companyId,
                linkCompanyName: companyInfo?.companyName || "",
                departmentId: projectInfo?.departmentId || "",
                departmentName: projectInfo?.departmentName || "",
                projectId: res.projectId,
                projectAddress: projectInfo?.projectAddress || "",//小区+门牌（详细地址）
                personMobile: (managerMap[res.faceEntityId]?.mobile || "").replace(/^Tim/, ""), // 如果有 "Tim" 前缀，则移除
                name: faceMap[res.faceEntityId]?.name || "",
                tags: faceMap[res.faceEntityId]?.tags || "",
                plan: {
                    days: String(days),
                    startTime: startTime,
                    endTime: endTime
                },
                arrive: actualInfo.arrive,//进场
                exit: actualInfo.exit,//退场
                acceptStatus: acceptStatusCode,
                acceptStatusName: DispatchType.AcceptStatusValues[acceptStatusCode]?.name,
                faceEntityId: res?.faceEntityId,
                arrivalToday,
                plannedArrivalToday,
                projectStartTime: projectInfo?.projectStartTime || "",
                projectCompletedTime: projectInfo?.projectCompletedTime || ""
            })
        });
        return result;
    }

    private getAcceptStatusKey(accepted: boolean, reminders?: any[]) {
        if (accepted === undefined) {
            if (reminders && reminders?.length > 0) return DispatchType.AcceptStatus.Pending;
            return DispatchType.AcceptStatus.NotNotify;
        }
        if (!accepted) return DispatchType.AcceptStatus.Rejected;
        return DispatchType.AcceptStatus.Confirmed;
    }

    /**
   * 查询进场/退场实际状态
   * @param dispatchId 
   */
    private getDispatchActualStatus(dispatch: WithId<Dispatch>) {
        const result = { arrive: new DispatchType.DispatchActualDetail(), exit: new DispatchType.DispatchActualDetail() };
        this.logger.log(`getDispatchActualStatus dispatch: ${JSON.stringify(dispatch)}`);
        // 统一时间
        const planStart = DateFns.startOfDay(new Date(dispatch.planStart));//计划进场
        const planEnd = DateFns.startOfDay(new Date(dispatch.planEnd));//计划退场
        const actualStart = dispatch.actualStart ? DateFns.startOfDay(new Date(dispatch.actualStart)) : null; // 实际进场
        const actualEnd = dispatch.actualEnd ? DateFns.startOfDay(new Date(dispatch.actualEnd)) : null; // 实际退场
        const today = DateFns.startOfDay(new Date()); // 当前日期
        // 计算偏差天数
        const daysFromPlanStart = DateFns.differenceInDays(today, planStart);//今天 - 计划开始
        const startDeviation = actualStart ? DateFns.differenceInDays(actualStart, planStart) : null;//实际开始 - 计划开始
        const daysFromPlanEnd = DateFns.differenceInDays(today, planEnd);//今天 - 计划结束
        const endDeviation = actualEnd ? DateFns.differenceInDays(actualEnd, planEnd) : null;//实际结束 - 计划结束
        if (planStart) {//进场
            result.arrive.status = this.getActualStatus(daysFromPlanStart, startDeviation, 'arrival', !!actualStart);
            result.arrive.description = DispatchType.ArriveStatusValues[result.arrive.status]?.name;
            result.arrive.time = dispatch.actualStart ? DateFns.format(dispatch.actualStart, "yyyy-MM-dd") : null;
        }
        if (planEnd) {//退场
            result.exit.status = this.getActualStatus(daysFromPlanEnd, endDeviation, 'exit', !!actualEnd);
            result.exit.description = DispatchType.ExitStatusValues[result.exit.status]?.name;
            result.exit.time = dispatch.actualEnd ? DateFns.format(dispatch.actualEnd, "yyyy-MM-dd") : null;
        }
        this.logger.log(`getDispatchActualDetail result: ${JSON.stringify(result)}`);
        return result;
    }

    /**
   * 获取进场/退出实际状态
   * @param deviation 今日与计划偏差值
   * @param actualDeviation 实际与计划偏差值
   * @param statusType 偏差状态类型，表示是与进场相关还是退场相关
   * @param isActive 是否处于目标状态（如是否已进场或是否已退场）
   */
    private getActualStatus(
        deviation: number,
        actualDeviation: number,
        statusType: 'arrival' | 'exit',
        isActive: boolean
    ): DispatchType.ArriveStatus | DispatchType.ExitStatus | null {
        // 规则生成函数，用于按需生成规则数组
        const createRules = (): ((dev: number, actDev: number) => DispatchType.ArriveStatus | DispatchType.ExitStatus | null)[] => {
            switch (statusType) {
                case 'arrival':
                    return isActive
                        ? [
                            (_, actDev) => (actDev < 0 ? DispatchType.ArriveStatus.ADVANCE_JOIN : null),
                            (_, actDev) =>
                                actDev >= 0 && actDev < 1 ? DispatchType.ArriveStatus.TODAY_JOIN : null,
                            (_, actDev) => (actDev >= 1 ? DispatchType.ArriveStatus.OVERDUE_JOIN : null),
                        ]
                        : [
                            (dev) => (dev <= 0 ? DispatchType.ArriveStatus.PENDING_JOIN : null),
                            (dev) => (dev > 0 ? DispatchType.ArriveStatus.OVERDUE_NOT_JOIN : null),
                        ];
                case 'exit':
                    return isActive
                        ? [
                            (_, actDev) => (actDev <= 0 ? DispatchType.ExitStatus.ADVANCE_EXIT : null),
                            (_, actDev) => (actDev > 0 ? DispatchType.ExitStatus.OVERDUE_EXIT : null),
                        ]
                        : [
                            () => null, // 未退场，返回 null
                        ];
                default:
                    return [];
            }
        };

        // 遍历规则并返回第一个匹配的状态
        for (const rule of createRules()) {
            const status = rule(deviation, actualDeviation); // 执行规则函数
            if (status !== null) {
                return status; // 返回第一个匹配的状态
            }
        }

        return null; // 如果无规则匹配，返回 null
    }

    /**
     * 统筹派工数据报表选项
     * @param body 
     * @returns 
     */
    async dispatchAnalyticsOptions(body: Analytics.Req) {
        const result: { roleList: DataReportModuleFilter.SelectFilterValue[]; linkCompany: DataReportModuleFilter.SelectFilterValue[] } = { roleList: [], linkCompany: [] };
        //统一处理数据报表请求参数
        const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.Dispatch});
        if (!query) return result;
        //查询岗位
        const tagRes = await this.dispatchAnalyticsPositionV2(body);
        result.roleList = (tagRes?.items || []).map(res => {
            return { name: res.roleName, value: res.roleName };
        });
        // 根据部门id获取集团公司
        const companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(query.departmentId);
        const companyList = await this.companyDao.searchCompanyList({ companyIds });
        result.linkCompany = companyList.map(item => {
            return {
                name: item.companyName,
                value: item.id,
            };
        });
        return result;
    }
}