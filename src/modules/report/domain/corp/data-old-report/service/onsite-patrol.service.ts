import { Injectable } from "@nestjs/common";
import { CheckFormatCsv, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import { Analytics } from "../type/analytics-util.type";
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { AnalyticsService } from "./analytics.service";
import { RoleDao } from "../../../bgw/role/dao/role.dao";
import { OnsitePatrolBoardDao } from "../dao/onsite-patrol.dao";
import { DepartmentMemberDao } from "../../../bgw/department/dao/department-member.dao";
import { ObjUtil } from "@src/util/obj.util";
import { ProjectDao } from "../../../bgw/project/dao/project.dao";
import { ManagerDao } from "../../../bgw/manager/dao/manager.dao";
import { CorpReportBasicsService } from "../../corp-report/service/basics/corp-report-basics.service";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { Company } from "../../../bgw/company/dto/company.dto";
import { DataReportConfigService } from "../../data-report/service/data-report-config.service";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { OnsitePatrolType } from "../type/onsite-patrol.type";
import { BodyDao } from "../../../camera/body/body.dao";

@Injectable()
export class OnsitePatrolService {
    private readonly logger = new MyLogger(OnsitePatrolService.name);
    constructor(
        private readonly analyticsService: AnalyticsService,
        private readonly roleDao: RoleDao,
        private readonly onsitePatrolDao: OnsitePatrolBoardDao,
        private readonly departmentMemberDao: DepartmentMemberDao,
        private readonly projectDao: ProjectDao,
        private readonly managerDao: ManagerDao,
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly companyDao: CompanyDao,
        private readonly dataReportConfigService: DataReportConfigService,
        private readonly bodyDao: BodyDao
    ) { }

    // @DevTimeout(100)
    async test111() {
        const total = await this.analyticsOptions({
            companyId: "*********",
            departmentId: "12543",
            from: "2024-01-01",
            to: "2025-01-31",
            pageNo: 1,
            pageSize: 10,
        })
        console.log(`total: ${JSON.stringify(total)}`)
    }

    /**
     * 根据部门id获取集团公司列表
     * @param departmentId 部门id
     * @returns 集团公司列表
     */
    private async getCompanyListByDeptId(departmentId: string) {
        if (!departmentId) return [];
        const companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(departmentId);
        if (_.isEmpty(companyIds)) return [];
        const companyList = await this.companyDao.searchCompanyList({ companyIds });
        return companyList;
    }

    /** 现场巡检分析 选项  */
    async analyticsOptions(query: Analytics.Req) {
        try {
            const promiseList = [
                // 岗位
                this.searchAnalyticsRoleOptions(query),
                // 工地负责人列表
                this.searchAnalyticsProjectManagerOptions(query),
                // 根据部门id获取集团公司
                this.getCompanyListByDeptId(query.departmentId),
                // 签到方式
                this.dataReportConfigService.dataConfigSearch({ companyId: query.companyId, module: DateReport.Subject.OnsitePatrol })
            ]
            const [roleNameList, directors, companyList, config] = await Promise.all(promiseList);
            const signInTypes = ((config as { category: "home_decor" | "construction"; configs: any[]; })?.configs[0]?.patrolSignStyle || []).map(item => {
                return {
                    value: Analytics.signInStyleMap[item],
                    name: Analytics.signInTypeNameMap[Analytics.signInStyleMap[item]]
                }
            });
            return {
                personList: (directors as { id: string, name: string }[]).map(item => {
                    return {
                        name: item.name,
                        value: item.id,
                    }
                }),
                positionList: (roleNameList as string[])?.map(roleName => {
                    return {
                        name: roleName,
                        value: roleName,
                    }
                }),
                signInTypes,
                linkCompany: (companyList as { id: string, name: string, companyName: string }[])?.map(item => {
                    return {
                        name: item.companyName,
                        value: item.id,
                    }
                })
            }
        } catch (e) {
            this.logger.log(`projectSignInService analyticsOptions 错误: ${String(e)}`);
            throw new YqzException(`projectSignInService analyticsOptions 错误: ${String(e)}`)
        }
    }

    /**
     * 现场巡检分析 工地负责人筛选项
     * @param query 
     * @returns 工地负责人筛选项
     */
    async searchAnalyticsProjectManagerOptions(query: Analytics.Req) {
        const projectIds = [];
        /** 根据公司配置 company_config 再次过滤 query  */
        let { noManager, isAllRole, dealManagerList, divisionCompanyIdList, isShowUnpatroled } = await this.analyticsService.onsiteBodyParse(query);
        if (noManager) return [];
        // 查询应巡检工地
        let searchProjectIds = [];
        const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: divisionCompanyIdList, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to });
        const shouldPatrols = [];
        res.forEach(item => {
            item.projectIds.forEach(projectId => { shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName }) })
        });
        searchProjectIds = shouldPatrols;
        searchProjectIds = searchProjectIds.map(res => res.projectId);
        if (!isAllRole && dealManagerList instanceof Array && isShowUnpatroled === 'Y') {
            /** 应巡检工地(已巡检 + 未巡检) */
            projectIds.push(...searchProjectIds);
        } else {
            /** 已巡检工地查询 */
            const analyticsProjectIdList = await this.bodyDao.getAnalyticsProjectIdList({
                companyIds: divisionCompanyIdList,
                faceEntityIds: query.managerIdList,
                projectIds: searchProjectIds,
                from: query.from,
                to: query.to,
                faceIdentifyTypes: query.patrolSignStyle,
                tags: !_.isEmpty(query.roleNames) ? query.roleNames : []
            });
            projectIds.push(...analyticsProjectIdList)
        }
        if (_.isEmpty(projectIds)) return [];
        // 查询工地负责人列表
        return await this.projectDao.searchProjectDirectors({ projectIds: _.uniq(projectIds) });
    }

    /** 现场巡检分析 员工分布 */
    @CheckFormatCsv(Analytics.csvPerson, "analyticPersons")
    async searchAnalyticsPerson(query: Analytics.Req): Promise<Analytics.AnalyticsPersonRes> {
        try {
            const { companyId } = query;
            /** 根据公司配置 company_config 再次过滤 query  */
            let { noManager, isAllRole, dealManagerList, isShowUnpatroled, divisionCompanyIdList } = await this.analyticsService.onsiteBodyParse(query);
            if (noManager) return { analyticPersons: [] }
            // 查询应巡检工地
            let searchProjectIds = []
            const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: divisionCompanyIdList, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to });
            const shouldPatrols = []
            res.forEach(item => {
                item.projectIds.forEach(projectId => {
                    shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName })
                })
            })
            searchProjectIds = shouldPatrols
            searchProjectIds = searchProjectIds.map(res => res.projectId);
            //筛选-员工昵称 模糊匹配
            if (query.nickName || !_.isEmpty(query.roleNames)) {
                const managers = await this.managerDao.getBelongToListByDeptId({ companyIdList: divisionCompanyIdList, managerIdList: query.managerIdList, nickName: query.nickName, managerRoles: query.roleNames });
                if (_.isEmpty(managers)) return { analyticPersons: [] };
                query.managerIdList = managers.map(res => res.managerId);
            }
            /** 获取 bodyDB 数据 */
            const params = {
                companyIds: divisionCompanyIdList,
                faceEntityIds: query.managerIdList,
                projectIds: searchProjectIds,
                from: query.from,
                to: query.to,
                faceIdentifyTypes: query.patrolSignStyle,
                tags: !_.isEmpty(query.roleNames) ? query.roleNames : []
            }
            const persons = await this.batchAnalyticsPerson(params);
            const managerIds = (persons?.analyticPersons || [])?.map(m => m.managerId);
            const deptList = _.isEmpty(managerIds) ? [] : await this.departmentMemberDao.searchDeptInfoList({ managerIds });
            const managerDeptMap = _.keyBy(deptList, 'managerId')
            /** 为统计数据加入员工名称 可以直接通过 dealManagerList 获取manager信息 不需要重新从数据库中查找 */
            for (const person of persons.analyticPersons) {
                const manager = dealManagerList.find(mg => mg.managerId == person.managerId)
                if (manager) person["nickName"] = manager.nickName;
                if (managerDeptMap[person.managerId]) {
                    person["personDepartmentId"] = managerDeptMap[person.managerId].deptId;
                    person["personDepartmentName"] = managerDeptMap[person.managerId].deptName;
                }
            }
            /** 如果公司层面筛选了 现场巡检岗位 则需要具体分析。 如果选择了全部 则不需要做多余分析 */
            if (!isAllRole && dealManagerList instanceof Array) {
                const { managerZeros } = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: persons.analyticPersons, roleNames: query.roleNames })
                const managerIdList = managerZeros.map(m => m.managerId)
                const managers = managerIdList.length == 0 ? [] : await this.managerDao.getBelongToListByDeptId({ managerIdList });
                const managerMap = _.keyBy(managers, 'managerId');
                persons.analyticPersons.push(...managerZeros.map(p => ({
                    ...p,
                    personDepartmentId: managerMap[p.managerId]?.departmentId,
                    personDepartmentName: managerMap[p.managerId]?.departmentName,
                    totalPatrol: Number(p.totalPatrol),
                    totalProject: Number(p.totalProject),
                    unpatroledCount: Number(p.unpatroledCount),
                    unpatroledProjectIdList: p.unpatroledProjectIdList
                } as any)))
            }
            // 筛选了岗位且公司设置展示未巡检数据
            if (!isAllRole && !!dealManagerList.length && isShowUnpatroled === 'Y') {
                const managerIds = (persons?.analyticPersons || []).map(m => m.managerId);
                if (!_.isEmpty(managerIds) && companyId) {
                    const managerPatroledList = query.signInStyle && !_.isEmpty(query.signInStyle) ? [] : await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: divisionCompanyIdList, managerIds, projectIds: searchProjectIds, startDate: query.from, endDate: query.to });
                    const managerPatroledMap: { [key: string]: { managerId: string, projectIds: string[] } } = managerPatroledList.reduce((prev, cur) => { prev[cur.managerId] = cur; return prev; }, {});
                    for (const person of persons.analyticPersons) {
                        let managerPatroledProjectIds = managerPatroledMap[person.managerId]?.projectIds || [];
                        person['unpatroledCount'] = _.difference(managerPatroledProjectIds, (person?.projectIdList || [])).length;
                        person['unpatroledProjectIdList'] = _.difference(managerPatroledProjectIds, (person?.projectIdList || []));
                    }
                }
            }
            return persons;
        } catch (e) {
            this.logger.log(`projectSignInService analyticsPerson 错误: ${String(e)}`);
            throw new YqzException(`projectSignInService analyticsPerson 错误: ${String(e)}`)
        }
    }

    /**
     * 现场巡检分析 岗位筛选项
     * @param query 
     * @returns 岗位筛选项
     */
    async searchAnalyticsRoleOptions(query: Analytics.Req): Promise<string[]> {
        const roleNames: string[] = [];
        //统一处理数据报表请求参数
        let { noManager, isAllRole, dealPostList, divisionCompanyIdList, isShowUnpatroled } = await this.analyticsService.onsiteBodyParse(query);
        if (noManager) return [];
        // 查询应巡检工地
        let searchProjectIds = []
        const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: divisionCompanyIdList, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to });
        const shouldPatrols = []
        res.forEach(item => {
            item.projectIds.forEach(projectId => {
                shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName })
            })
        });
        searchProjectIds = shouldPatrols
        searchProjectIds = searchProjectIds.map(res => res.projectId);
        if (!isAllRole && !_.isEmpty(dealPostList) && isShowUnpatroled === 'Y') {
            /** 应巡检岗位(已巡检 + 未巡检) */
            dealPostList.forEach(item => { if (!roleNames.includes(item.name)) roleNames.push(item.name); });
        } else {
            /** 已巡检岗位查询 */
            const analyticsRoleNameList = await this.bodyDao.getAnalyticsRoleNameList({
                companyIds: divisionCompanyIdList,
                faceEntityIds: query.managerIdList,
                projectIds: searchProjectIds,
                tags: !_.isEmpty(query.roleNames) ? query.roleNames : [],
                from: query.from,
                to: query.to,
                faceIdentifyTypes: query.patrolSignStyle,
            });
            roleNames.push(...analyticsRoleNameList);
        }
        return _.uniq(roleNames);
    }

    /** 现场巡检分析 岗位分布  */
    @CheckFormatCsv(Analytics.csvPosition, "analyticsPositions")
    async searchAnalyticsPosition(query: Analytics.Req): Promise<Analytics.analyticsPositionRes> {
        try {
            const result: Analytics.analyticsPositionRes = { dealPosition: '', totalPosition: 0, dealManager: '', totalManager: 0, totalProject: 0, totalPatrol: 0, analyticsPositions: [] };
            //统一处理数据报表请求参数
            let { noManager, isAllRole, isCustomerCase1, dealPostList, dealManagerList, divisionCompanyIdList } = await this.analyticsService.onsiteBodyParse(query);
            if (noManager) return result;
            // 查询应巡检工地
            let searchProjectIds = []
            const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: divisionCompanyIdList, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to });
            const shouldPatrols = []
            res.forEach(item => {
                item.projectIds.forEach(projectId => {
                    shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName })
                })
            });
            searchProjectIds = shouldPatrols
            searchProjectIds = searchProjectIds.map(res => res.projectId);
            //筛选-员工昵称 模糊匹配
            if (query.nickName) {
                const managers = await this.managerDao.getBelongToListByDeptId({ companyIdList: divisionCompanyIdList, managerIdList: query.managerIdList, nickName: query.nickName });
                if (_.isEmpty(managers)) return result;
                query.managerIdList = managers.map(res => res.managerId);
            }
            /** 从 camera service 中获取 bodyDB 数据 */
            const params = {
                companyIds: divisionCompanyIdList,
                faceEntityIds: query.managerIdList,
                projectIds: searchProjectIds,
                tags: !_.isEmpty(query.roleNames) ? query.roleNames : [],
                from: query.from,
                to: query.to,
                faceIdentifyTypes: query.patrolSignStyle,
            }
            const postRes = await this.batchAnalyticsPosition(params);
            if (!postRes || !(postRes.analyticsPositions instanceof Array)) return result;
            /** 因为 body 的 tags 即 roleName 但是没有 roleId 所以在此加入 roleId 因为 bodyTags与role同步了 可以直接用 */
            const roleNames: string[] = [];
            postRes.analyticsPositions.forEach(position => {
                if (!roleNames.includes(position.roleName)) roleNames.push(position.roleName);
            })
            if (roleNames.length > 0) {
                // 如果是集团公司需要查询集团下全部公司的岗位
                const companyIds: string[] = divisionCompanyIdList;
                const roleList = await this.roleDao.searchList({ companyIds, names: roleNames })
                for (const position of postRes.analyticsPositions) {
                    const item = roleList.find(role => role.roleName == position.roleName)
                    if (item) position['roleId'] = item.companyMemberRoleId;
                }
            }
            /** 为了 dealManager 实际巡检的人数 */
            if (!isAllRole && dealManagerList instanceof Array) {
                const persons = await this.batchAnalyticsPerson({
                    companyIds: divisionCompanyIdList, faceEntityIds: query.managerIdList, from: query.from, to: query.to,
                });
                const { realManagerList, realManager, managerZeros } = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: persons.analyticPersons, roleNames: query.roleNames })
                persons.analyticPersons.push(...managerZeros)
                const { realPost, postZeros } = this.analyticsService.getPositionInfo({ postRes: postRes.analyticsPositions, isAllRole, isCustomerCase1, dealPostList, realManagerList })
                postRes.analyticsPositions.push(...postZeros)
                postRes['dealManager'] = String(realManager);
                postRes['dealPosition'] = String(realPost);
            }
            // 合并相同的岗位
            postRes.analyticsPositions = ObjUtil.mergeByFields(postRes.analyticsPositions, "roleName")
            return postRes;
        } catch (e) {
            this.logger.log(`projectSignInService analyticsPosition_2 错误: ${String(e)}`);
            throw new YqzException(`projectSignInService analyticsPosition_2 错误: ${String(e)}`)
        }
    }

    /** 现场巡检分析 数据明细 */
    @CheckFormatCsv((body) => {
        if (body?.companyType === Company.CompanyType.Conglomerate) return Analytics.csvOnsitePatrolList
        return _.filter(Analytics.csvOnsitePatrolList, item => item.key !== "linkCompanyName");//不是集团公司不需要展示所属公司
    })
    async analyticsList(query: Analytics.Req) {
        try {
            this.logger.log("analytics/list analyticsList_v2 service: ", JSON.stringify(query))
            const { companyId } = query;
            const result = { total: 0, items: [] }
            //统一处理数据报表请求参数
            /** 根据公司配置 company_config 再次过滤 query  */
            const { noManager, isAllRole, isShowUnpatroled, divisionCompanyIdList } = await this.analyticsService.onsiteBodyParse(query);
            if (noManager) return result;
            const companyIds = query?.linkCompanyId ? [query?.linkCompanyId] : divisionCompanyIdList
            //筛选-工地负责人
            let searchProjectIds = []
            if ((query.projectDirectorId || query.projectAddress) && !_.isEmpty(companyIds)) {
                searchProjectIds = await this.projectDao.getProjectIds({
                    projectDirectorId: query.projectDirectorId,//绑定工地(PC)
                    companyIds: companyIds,
                    projectAddress: query.projectAddress
                });
                searchProjectIds = searchProjectIds.map(res => res.projectId);
            }
            // 查询应巡检工地
            const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: companyIds, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to });
            const shouldPatrols = []
            res.forEach(item => {
                item.projectIds.forEach(projectId => {
                    shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName })
                })
            })
            searchProjectIds = shouldPatrols
            searchProjectIds = searchProjectIds.map(res => res.projectId);
            //筛选-员工昵称 模糊匹配
            if (query.nickName) {
                const managers = await this.managerDao.getBelongToListByDeptId({ companyIdList: divisionCompanyIdList, managerIdList: query.managerIdList, nickName: query.nickName });
                if (_.isEmpty(managers)) return result;
                query.managerIdList = managers.map(res => res.managerId);
            }
            if (_.isEmpty(searchProjectIds)) return result
            query.projectIds = searchProjectIds;
            if (!isAllRole && isShowUnpatroled === 'Y') {
                // 获取未巡检人员 + 已巡检人员 (指定岗位)
                const { total, items } = await this.customAnalyticsListV2({
                    companyIds: companyIds,
                    faceEntityIds: query.managerIdList,
                    projectIds: query.projectIds,
                    from: query.from,
                    to: query.to,
                    options: { pageNo: query.pageNo, pageSize: query.pageSize, },
                    faceIdentifyTypes: query.patrolSignStyle,
                    tags: !_.isEmpty(query.roleNames) ? query.roleNames : [],
                    isPatroled: query.isPatroled as 'N' | 'Y'
                });
                /** 根据 bodyDB 的数据 转化为需要的数据格式 */
                const rows = await this.analyticsService.bodyDocToItem_v2({ companyId: String(companyId), startTime: query.from, endTime: query.to, items });
                return { total, items: rows };
            } else {
                // 获取已巡检人员 (全部/指定岗位)
                const companyIds = query?.linkCompanyId ? [query?.linkCompanyId] : divisionCompanyIdList
                const { total, items } = await this.patroledAnalyticsListV2({
                    companyIds: companyIds,
                    faceEntityIds: query.managerIdList,
                    projectIds: query.projectIds,
                    from: query.from,
                    to: query.to,
                    options: { pageNo: query.pageNo, pageSize: query.pageSize, },
                    faceIdentifyTypes: query.patrolSignStyle,
                    tags: !_.isEmpty(query.roleNames) ? query.roleNames : []
                });
                if (items.length == 0) return result;
                /** 根据 bodyDB 的数据 转化为需要的数据格式 */
                const rows = await this.analyticsService.bodyDocToItem_v2({ companyId: String(companyId), startTime: query.from, endTime: query.to, items });
                return { total, items: rows };
            }
        } catch (e) {
            this.logger.log(`projectSignInService analyticsList 错误: ${String(e)}`);
            throw new YqzException(`projectSignInService analyticsList 错误: ${String(e)}`)
        }
    }

    /**
     * 过滤 faceEntityIds 和 projectIds
     * @param query 
     * @returns 
     */
    // private async filterFaceEntityIdsAndProjectIds(query: { companyIds: string[], faceEntityIds: string[], projectIds?: string[], tags?: string[], from?: string, to?: string }) {
    // const { companyIds, faceEntityIds, projectIds, tags, from, to } = query;
    // const promiseList = [
    //     this.bodyDao.getBodyFaceEntityIds({ comapnyIds: companyIds, faceEntityIds: faceEntityIds, tags, startTime: from, endTime: to }),
    //     this.bodyDao.getBodyProjectIds({ comapnyIds: companyIds, projectIds: projectIds, tags, startTime: from, endTime: to })
    // ];
    // const [faceEntityIdsResult, projectIdsResult] = await Promise.all(promiseList);
    // return { faceEntityIds: faceEntityIdsResult, projectIds: projectIdsResult };
    // }

    /**
     * 人员统计
     * @param query 
     * @returns 
     */
    private async batchAnalyticsPerson(query: OnsitePatrolType.BodyAnalyticsByPersonReq) {
        if (!query.from || !query.to) throw new YqzException("时间参数不能为空");
        try {
            //过滤一下faceEntityIds 和 projectIds
            // const { faceEntityIds: faceEntityIdsResult, projectIds: projectIdsResult } = await this.filterFaceEntityIdsAndProjectIds(query);
            // if (_.isEmpty(faceEntityIdsResult) || _.isEmpty(projectIdsResult)) return { analyticPersons: [] };
            // query.faceEntityIds = faceEntityIdsResult;
            // query.projectIds = projectIdsResult;
            // 分批查询
            const batchDays = 10;// 每次查询10天
            const fromDate = new Date(query.from);
            const toDate = new Date(query.to);
            let current = fromDate;
            const batchQueries = [];
            // 1. 先分批，把每个批次的查询参数都准备好
            while (current <= toDate) {
                const batchEnd = DateFns.min([DateFns.addDays(current, batchDays - 1), toDate]);
                const batchQuery = { ...query, from: DateFns.format(current, "yyyy-MM-dd"), to: DateFns.format(batchEnd, "yyyy-MM-dd") };
                batchQueries.push(batchQuery);
                current = DateFns.addDays(batchEnd, 1);
            }
            // 2. 并发查询
            const promiseList = await Promise.all(
                batchQueries.map(async (batchQuery) => { return this.bodyDao.analyticsPerson(batchQuery); })
            );
            // 3. 数据处理
            const managerMap = new Map<string, { totalPatrol: number, totalProjectSet: Set<string>, managerId: string }>();
            for (const promiseResult of promiseList) {
                promiseResult.forEach(item => {
                    // 成员统计
                    const key = item.managerId;
                    if (!managerMap.has(key)) {
                        managerMap.set(key, {
                            totalPatrol: 0,
                            managerId: item.managerId,
                            totalProjectSet: new Set<string>(),
                        });
                    }
                    const group = managerMap.get(key)!;
                    group.totalPatrol += item.totalPatrol || 0;
                    (item.projectIdList || []).forEach(pid => group.totalProjectSet.add(pid));
                });
            }
            // 4. 组装聚合后的数组
            const analyticPersons = _.orderBy(
                Array.from(managerMap.values()).map(group => ({
                    totalPatrol: group.totalPatrol,
                    managerId: group.managerId,
                    totalProject: group.totalProjectSet.size,
                    projectIdList: Array.from(group.totalProjectSet),
                })),
                ['totalProject', 'totalPatrol', 'managerId'],
                ['desc', 'desc', 'desc']
            );
            // 5. 返回结果
            return { analyticPersons };
        } catch (e) {
            throw new YqzException(`face-search.service bodyAnalyticsByPerson 错误: ${String(e)}`);
        }
    }

    /**
     * 岗位统计
     * @param query 
     * @returns 
     */
    private async batchAnalyticsPosition(query: OnsitePatrolType.BodyAnalyticsByRoleReq) {
        if (!query.from || !query.to) throw new YqzException("时间参数不能为空");
        try {
            //过滤一下faceEntityIds 和 projectIds
            // const { faceEntityIds: faceEntityIdsResult, projectIds: projectIdsResult } = await this.filterFaceEntityIdsAndProjectIds(query);
            // if (_.isEmpty(faceEntityIdsResult) || _.isEmpty(projectIdsResult)) return { totalPatrol: 0, totalPosition: 0, totalProject: 0, totalManager: 0, analyticsPositions: [] };
            // query.faceEntityIds = faceEntityIdsResult;
            // query.projectIds = projectIdsResult;
            // 分批查询
            const batchDays = 11;// 每次查询11天
            const fromDate = new Date(query.from);
            const toDate = new Date(query.to);
            let current = fromDate;
            const batchQueries = [];
            // 1. 先分批，把每个批次的查询参数都准备好
            while (current <= toDate) {
                const batchEnd = DateFns.min([DateFns.addDays(current, batchDays - 1), toDate]);
                const batchQuery = { ...query, from: DateFns.format(current, "yyyy-MM-dd"), to: DateFns.format(batchEnd, "yyyy-MM-dd") };
                batchQueries.push(batchQuery);
                current = DateFns.addDays(batchEnd, 1);
            }
            // 2. 并发查询
            const promiseList = await Promise.all(
                batchQueries.map(async (batchQuery) => { return this.bodyDao.analyticsPosition(batchQuery) })
            );
            let totalPatrol = 0;
            const totalProjectIds = new Set<string>();
            const totalManagerIds = new Set<string>();
            const totalPositions = new Set<string>();
            const roleMap = new Map<string, { totalPatrol: number, roleName: string, totalProjectSet: Set<string>, totalManagerSet: Set<string> }>();
            // 3. 数据处理
            for (const promiseResult of promiseList) {
                promiseResult.forEach(item => {
                    // 统计
                    (item.projectIdList || []).forEach(pid => totalProjectIds.add(pid));
                    (item.managerIdList || []).forEach(mid => totalManagerIds.add(mid));
                    totalPositions.add(item.roleName);
                    totalPatrol += item.totalPatrol || 0;
                    // 岗位统计
                    const key = item.roleName;
                    if (!roleMap.has(key)) {
                        roleMap.set(key, {
                            totalPatrol: 0,
                            roleName: item.roleName,
                            totalProjectSet: new Set<string>(),
                            totalManagerSet: new Set<string>(),
                        });
                    }
                    const group = roleMap.get(key)!;
                    group.totalPatrol += item.totalPatrol || 0;
                    (item.projectIdList || []).forEach(pid => group.totalProjectSet.add(pid));
                    (item.managerIdList || []).forEach(mid => group.totalManagerSet.add(mid));
                })
            }
            // 4. 组装聚合后的数组
            const analyticsPositions = _.orderBy(
                Array.from(roleMap.values()).map(group => ({
                    totalPatrol: group.totalPatrol,
                    roleName: group.roleName,
                    totalProject: group.totalProjectSet.size,
                    totalManager: group.totalManagerSet.size,
                })),
                ['totalPatrol', 'totalProject', 'totalManager', 'roleName'],
                ['desc', 'desc', 'desc', 'desc']
            );
            // 5. 返回结果
            return {
                totalPatrol,
                totalPosition: totalPositions.size,
                totalProject: totalProjectIds.size,
                totalManager: totalManagerIds.size,
                analyticsPositions
            }
        } catch (e) {
            throw new YqzException(`face-search.service bodyAnalyticsByRole 错误: ${String(e)}`);
        }
    }

    /**
     * 获取bodyDB 数据
     * @param query 
     * @returns 
     */
    private async bodyAnalyticsList(query: OnsitePatrolType.BodyAnalyticsListReq) {
        if (_.isEmpty(query.companyIds) || !query.from || !query.to) throw new YqzException("必传参数不能为空");
        try {
            //过滤一下faceEntityIds 和 projectIds
            // const { faceEntityIds: faceEntityIdsResult, projectIds: projectIdsResult } = await this.filterFaceEntityIdsAndProjectIds(query);
            // if (_.isEmpty(faceEntityIdsResult) || _.isEmpty(projectIdsResult)) return { total: 0, items: [] }
            // query.faceEntityIds = faceEntityIdsResult;
            // query.projectIds = projectIdsResult;
            // if (_.isEmpty(query.projectIds)) return { total: 0, items: [] }
            const { options = {} } = query;
            const { pageNo, pageSize, sort } = options;
            const promiseList = [
                this.bodyDao.analyticsList(query, { pageNo, pageSize, sort }),// 获取bodyDB 数据
                this.bodyDao.analyticsCount(query)// 获取bodyDB 统计数量
            ]
            const [res, total] = await Promise.all(promiseList);
            const items = (res as any[]).map(item => {
                return {
                    ...item,
                    timeInterval: DateFns.differenceInMilliseconds(new Date(item.latestTime), new Date(item.earliestTime)),
                    arrivalTime: DateFns.format(new Date(item.earliestTime), "HH:mm:ss"),
                    leaveTime: DateFns.format(new Date(item.latestTime), "HH:mm:ss"),
                }
            })
            return { total, items };
        } catch (e) {
            throw new YqzException(`face-search.service bodyAnalyticsList 错误: ${String(e)}`);
        }
    }

    /**
     * 获取bodyDB 数据 v2 （已巡检人员）
     * 分批次查询，在内存做分页
     * @param query 
     */
    private async patroledAnalyticsListV2(query: OnsitePatrolType.BodyAnalyticsListReq) {
        if (_.isEmpty(query.companyIds) || !query.from || !query.to) throw new YqzException("必传参数不能为空");
        try {
            const result = { total: 0, items: [] };
            // 分批查询
            const batchDays = 11;// 每次查询11天
            const fromDate = new Date(query.from);
            const toDate = new Date(query.to);
            let current = fromDate;
            const batchQueries = [];
            // 1. 先分批，把每个批次的查询参数都准备好
            while (current <= toDate) {
                const batchEnd = DateFns.min([DateFns.addDays(current, batchDays - 1), toDate]);
                const batchQuery = {
                    companyIds: query?.companyIds,
                    faceEntityIds: query?.faceEntityIds,
                    projectIds: query?.projectIds,
                    faceIdentifyTypes: query?.faceIdentifyTypes,
                    tags: query?.tags,
                    from: DateFns.format(current, "yyyy-MM-dd"),
                    to: DateFns.format(batchEnd, "yyyy-MM-dd")
                    // options?: query?.options,//这里不需要分页
                };
                batchQueries.push(batchQuery);
                current = DateFns.addDays(batchEnd, 1);
            }
            // 2. 并发查询
            const promiseList = await Promise.all(
                batchQueries.map(async (batchQuery) => { return this.bodyDao.analyticsListV2(batchQuery) })
            );
            // 3. 数据处理
            const list = [];
            for (const promiseResult of promiseList) {
                list.push(...promiseResult);
            }
            if (_.isEmpty(list)) return result;
            result.total = list.length;
            // 4. 排序
            const sortedItems = _.orderBy(list, ['eventTime', 'managerId', 'projectId'], ['desc', 'desc', 'desc']);
            // 5. 内存分页
            if (query?.options?.pageNo && query?.options?.pageSize) {
                const { pageNo, pageSize } = query.options;
                const startIndex = (pageNo - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                result.items = sortedItems.slice(startIndex, endIndex)
            } else {
                result.items = sortedItems;
            }
            result.items = result.items.map(item => {
                return {
                    ...item,
                    timeInterval: DateFns.differenceInMilliseconds(new Date(item.latestTime), new Date(item.earliestTime)),
                    arrivalTime: DateFns.format(new Date(item.earliestTime), "HH:mm:ss"),
                    leaveTime: DateFns.format(new Date(item.latestTime), "HH:mm:ss"),
                }
            })
            // 6. 返回结果
            return result;
        } catch (e) {
            throw new YqzException(`face-search.service patroledAnalyticsListV2 错误: ${String(e)}`);
        }
    }

    /**
     * 获取bodyDB 数据 v2 （已巡检人员+未巡检）
     * @param query 
     */
    private async customAnalyticsListV2(query: OnsitePatrolType.BodyAnalyticsListReq & { isPatroled: 'N' | 'Y' }) {
        if (_.isEmpty(query.companyIds) || !query.from || !query.to) throw new YqzException("必传参数不能为空");
        try {
            const result = { total: 0, items: [] };
            // 1. 获取已巡检人员
            const { items: list } = await this.patroledAnalyticsListV2({
                companyIds: query.companyIds,
                faceEntityIds: query.faceEntityIds,
                projectIds: query.projectIds,
                from: query.from,
                to: query.to,
                faceIdentifyTypes: query.faceIdentifyTypes,
                tags: query.tags
            });
            const patroledList = list as any[];
            let shouldPatroledList = [];// 未巡检人员列表
            // 2. 如果未指定签到类型，则需要查询未巡检人员
            if (!query.faceIdentifyTypes || _.isEmpty(query.faceIdentifyTypes)) {
                const res = await this.onsitePatrolDao.searchProjectShouldPatroledByCache({ companyIds: query.companyIds, managerIds: query.faceEntityIds, projectIds: query.projectIds, startDate: query.from, endDate: query.to, roleNames: query.tags });
                res.forEach(item => {
                    item.projectIds.forEach(projectId => {
                        shouldPatroledList.push({ managerId: item.managerId, projectId, roleName: item.roleName })
                    })
                });
            }
            // 3. 排序
            shouldPatroledList = _.orderBy(shouldPatroledList, ['managerId', 'projectId'], ['desc', 'desc']);
            // 4. 合并未巡检人员和已巡检人员
            const unpatroledList = _.differenceWith(shouldPatroledList, patroledList, (a, b) => a.managerId === b.managerId && a.projectId === b.projectId)
            let mergedList = [];// 合并后的列表
            // 根据isPatroled 参数，返回不同的列表
            if (query.isPatroled === 'N') {
                mergedList = unpatroledList
            } else if (query.isPatroled === 'Y') {
                mergedList = patroledList
            } else {
                mergedList = [...patroledList, ...unpatroledList]
            }
            if (_.isEmpty(mergedList)) return result;
            result.total = mergedList.length;
            // 5. 内存分页
            if (query?.options?.pageNo && query?.options?.pageSize) {
                const { pageNo, pageSize } = query.options;
                const startIndex = (pageNo - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                result.items = mergedList.slice(startIndex, endIndex)
            } else {
                result.items = mergedList;
            }
            // 6. 返回结果
            return result;
        } catch (e) {
            throw new YqzException(`face-search.service customAnalyticsListV2 错误: ${String(e)}`);
        }
    }

}