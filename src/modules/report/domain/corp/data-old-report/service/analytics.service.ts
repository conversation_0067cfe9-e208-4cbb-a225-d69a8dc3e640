import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import { Analytics } from "../type/analytics-util.type";
import * as _ from "lodash";
import * as DateFns from "date-fns";
import format from "date-fns/format";
import { DepartmentDao } from "../../../bgw/department/dao/department.dao";
import { DimDepartmentDao } from "../../../etl/dao/dim-department.dao";
import { RoleDao } from "../../../bgw/role/dao/role.dao";
import { ManagerDao } from "../../../bgw/manager/dao/manager.dao";
import { DepartmentService } from "../../../bgw/department/service/department.service";
import { CompanyCustomConfigService } from "../../../bgw/company/service/company-custom-config.service";
import { ProjectDao } from "../../../bgw/project/dao/project.dao";
import { formatDateToYYYYMMDD, formatMillisecondsDuration } from "@src/util/datetime.util";
import { DepartmentMemberDao } from "../../../bgw/department/dao/department-member.dao";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { DataReportConfigService } from "../../data-report/service/data-report-config.service";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { DeviceDao } from "../../../camera/device/dao/device.dao";
import { DimDeviceDao } from "../../../etl/dao/dim-device.dao";

@Injectable()
export class AnalyticsService {
    private readonly logger = new MyLogger(AnalyticsService.name)
    constructor(
        private readonly departmentDao: DepartmentDao,
        private readonly departmentMemberDao: DepartmentMemberDao,
        private readonly departmentService: DepartmentService,
        private readonly dimDepartmentDao: DimDepartmentDao,
        private readonly roleDao: RoleDao,
        private readonly managerDao: ManagerDao,
        private readonly companyCustomConfigService: CompanyCustomConfigService,
        private readonly projectDao: ProjectDao,
        private readonly dataReportConfigService: DataReportConfigService,
        private readonly companyDao: CompanyDao,
        private readonly deviceDao: DeviceDao,
        private readonly dimDeviceDao: DimDeviceDao,
    ) { }

    async onlineBodyParse(body: Analytics.Req& { module?: string }) {
        const { companyId, departmentId } = body;
        if (!companyId) throw new YqzException("必传参数不能为空");
        try {
            if (!departmentId) {
                const { items: [root] } = await this.departmentDao.searchDept({ companyId, level: 0 })
                body.departmentId = root.departmentId;
            }
            const query = _.cloneDeep(body);
            /** 格式化日期 */
            if (query.from && query.from.length > 0) query.from = format(new Date(query.from), 'yyyy-MM-dd 00:00:00');
            else delete query.from;
            if (query.to && query.to.length > 0) query.to = format(new Date(query.to), 'yyyy-MM-dd 23:59:59');
            else delete query.to;
            /** 集团公司部门 */
            const { departmentIdList, divisionCompanyIdList } = await this.getEtlDeptListByTimeRange({ deptId: body.departmentId, deptDateType: 'real_time', startTime: query.from, endTime: query.to });
            query.departmentIds = departmentIdList;
            if (_.isEmpty(query.departmentIds)) return null;

            /** 部门的筛选 */
            if (!body.departmentId) throw new YqzException("请传入 部门id");
            if (!query.departmentIds.includes(body.departmentId)) throw new YqzException("部门不存在");

            // 公司设置
            const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: body?.module || DateReport.Subject.OnlinePatrol });
            if (config && config.configs.filter(item => !!item).length === 1) {
                query.noPatrolProjectShow = config?.configs[0]?.noPatrolProjectShow || "N";
                if (config.configs[0].roleScope === "all") {
                    query.roleNames = query?.roleNames || [];
                } else {
                    query.roleNames = !_.isEmpty(query?.roleNames) ? _.intersection(query?.roleNames, config.configs[0].roles) : config.configs[0].roles;
                }
            }
            return {
                ...query,
                companyIds: divisionCompanyIdList
            }
        } catch (e) {
            this.logger.log(`bodyParse fail body: ${JSON.stringify(body)}, error:${JSON.stringify(e)}`)
            return null;
        }
    }

    private async getEtlDeptListByTimeRange(params: { deptId: string, deptDateType: 'real_time' | 'history', startTime: string, endTime: string }) {
        let { deptId, deptDateType = 'real_time', startTime, endTime } = params;
        const departmentIdList: string[] = [];
        const divisionCompanyIdList: string[] = [];//记录集团部门关联的公司id，实时查询要查询到集团关联公司下的所有部门
        //查询实时 本部门 及 子部门id（不校验删除、集团关联公司查询公司所有部门、包含自己）
        if (deptDateType === 'real_time') {
            //查询当前部门
            const dept = await this.departmentDao.searchDeptInfoList({ deptId: deptId });
            dept.map(res => {
                departmentIdList.push(res.departmentId);
                //查询集团部门关联公司的公司id
                if (res?.type === 'division' && res?.linkCompanyId) { divisionCompanyIdList.push(res?.linkCompanyId); }
            });
            //查询子部门
            const deptList = await this.departmentDao.searchDeptInfoList({ companyId: dept[0]?.companyId, includeDir: deptId });
            deptList.map(res => {
                departmentIdList.push(res.departmentId);
                //查询集团部门关联公司的公司id
                if (res?.type === 'division' && res?.linkCompanyId) { divisionCompanyIdList.push(res?.linkCompanyId); }
            });
            //查询集团关联公司下的所有部门（不排除掉根部好像也没有关系，数据后面会去重掉）
            if (!_.isEmpty(divisionCompanyIdList)) {
                const divisionDept = await this.departmentDao.searchDeptInfoList({ companyIds: divisionCompanyIdList });
                divisionDept.map(res => departmentIdList.push(res.departmentId));
            }
            divisionCompanyIdList.push(dept[0].companyId);
        }
        //查询历史 本部门 及 子部门id（不校验删除、包含自己）
        if (deptDateType === 'history') {
            const dimDeptList = await this.dimDepartmentDao.search({ departmentIdList: [deptId], startTime: startTime, endTime: endTime });
            for (const dimDept of dimDeptList) {
                departmentIdList.push(dimDept.departmentId);//包含自己
                const deptIdList = dimDept?.childDepartmentIdList && '' !== dimDept.childDepartmentIdList ? dimDept.childDepartmentIdList.split(',') : [];
                departmentIdList.push(...deptIdList);
            }
        }
        // // 查询部门工地idList
        // const dimDepartDataList = _.isEmpty(departmentIdList)? [] : await this.dimDepartmentDataDao.searchList({departmentIdList,from: startTime,to: endTime})
        // dimDepartDataList.map(item => {
        //     const projectIdList = item?.projectIdList?.split(',') || [];
        //     if(!_.isEmpty(projectIdList))
        //         divisionProjectIdList.push(...new Set(projectIdList)) 
        // })
        return {
            departmentIdList,
            divisionCompanyIdList: divisionCompanyIdList,
        };
    }


    /**
     * 对参数的格式化
     * @param body 
     * @returns 
     * 最重要的是 获取到 query.managerIdList 作为查询 body 的参数
     * 其次是获取 有哪些岗位 需要巡检 即 dealPostList
     */
    async onsiteBodyParse(query: Analytics.Req, module: string = "report:patrol:onsite") {
        try {
            const { companyId } = query;
            if (!companyId) throw new YqzException("必传参数不能为空");
            /** 格式化日期 */
            if (query.from && query.from.length > 0) query.from = format(new Date(query.from), 'yyyy-MM-dd 00:00:00');
            else delete query.from;
            if (query.to && query.to.length > 0) query.to = format(new Date(query.to), 'yyyy-MM-dd 23:59:59');
            else delete query.to;
            /** 集团公司部门 */
            const { departmentIdList, divisionCompanyIdList } = await this.getEtlDeptListByTimeRange({ deptId: query.departmentId, deptDateType: 'real_time', startTime: query.from, endTime: query.to });
            query.departmentIds = departmentIdList;
            if (_.isEmpty(departmentIdList) || _.isEmpty(divisionCompanyIdList)) return { noManager: true };
            if (!query.departmentIds.includes(query.departmentId)) throw new YqzException("部门不存在");
            /** 判断是否为根部门 （在这的作用为 筛选 不在任何部门的人 后面分析还要加上<如果是根部门 要加上所有的岗位即便分析数据中没有人>） */
            const rootDept = await this.departmentService.getRootDept(companyId);
            const isRoot: boolean = rootDept !== null && rootDept.departmentId == query.departmentId;
            /** 公司设置 */
            let isAllRole: boolean = true; 
            let isShowUnpatroled = 'N';
            let configPosts = [];
            const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.OnsitePatrol });
            if (config && config.configs.filter(item => !!item).length === 1) {
                isAllRole = config?.configs[0]?.roleScope === "all"?true:false;  // 公司配置中是否 选择全部的岗位 在产品那叫全员
                isShowUnpatroled = config?.configs[0]?.noPatrolProjectShow || 'N'; // 是否显示未巡检统计
                configPosts = config?.configs[0]?.roles || [];
                query.patrolSignStyle = config?.configs[0]?.patrolSignStyle || [];
            }
            /** 定制情况1
             *  公司层面做了 岗位设定 并且前端筛选了根部门。  
             *  这种情况下: 要显示 所有设定的岗位 即便 此岗位的人员不在任何部门
             */
            const isCustomerCase1: boolean = isRoot && configPosts.length > 0
            /** 最终要做 筛选想的 岗位设置 */
            let selectRoles = [...configPosts];
            // 需要分析岗位的特殊处理
            if (isCustomerCase1) {
                const roleList = await this.roleDao.searchList({ companyIds: [companyId],names: configPosts })
                selectRoles = roleList.map(role => role.roleName);
            }
            if (query.roleName && _.isString(query.roleName)) { //筛选了roleName，roleName是string
                /** 如果公司做了配置 要检查 配置中是否有此 岗位  selectRoleIds.length > 0 的判断 是因为 如果为[] 则为全部 所以肯定在范围内 */
                if (selectRoles.length > 0 && !selectRoles.includes(query.roleName)) throw new YqzException("您筛选的 岗位 没有被设置");
                selectRoles = [query.roleName]; // 如果筛选了岗位, 则只有这一个岗位
            }
            if (query.roleName && _.isArray(query.roleName)) {//筛选了roleName，roleName是string[]
                if (selectRoles.length > 0 && _.difference(query.roleName, selectRoles).length > 0) throw new YqzException("您筛选的 岗位 没有被设置");
                selectRoles = query.roleName;
            }
            if (!_.isEmpty(selectRoles)) {
                query.roleNames = selectRoles;
            }
            /** 员工的筛选： 根据部门 和 岗位 以及前端筛选的员工 */
            const managerIdList: string[] = query.managerId ? [query.managerId] : [];
            // 如果是 特殊情况 筛选员工时 不能加部门 要把所有人 筛选出来
            const departmentIds = isCustomerCase1 ? [] : query.departmentIds;
            // 筛选 应该要巡检的 所有员工 包括了 离职员工 和 所有设置的岗位
            const isEnableLabelYQZ = true; // 剔除一起装人员
            const dealManagerList = await this.managerDao.getBelongToListByDeptId({
                companyIdList: divisionCompanyIdList, departmentIds: departmentIds, managerIdList, isEnableLabelYQZ,
                // managerRoles: selectRoles,
            });
            if (dealManagerList.length == 0) return { noManager: true };
            /** 给传递到query的managerIdList参数赋值 dealManagerList中的managerId有为空的数据 所以加了 forEach去掉空的 */
            if (!query.managerIdList) query.managerIdList = [];
            dealManagerList.forEach(item => { if (item.managerId) query.managerIdList.push(item.managerId) });
            query.managerIdList = _.uniq(query.managerIdList);
            /* 需要分析的所有人员 和 岗位 */
            let dealPostList: any = dealManagerList.reduce((dealPosts, item) => {
                const hasValue = dealPosts.some(post => post.roleId == item.roleId);
                if (!hasValue && item.roleId) dealPosts.push({ roleId: item.roleId, name: item.roleName })
                return dealPosts;
            }, []); // 参与的岗位
            /** dealPostList 的 特殊情况 要处理的岗位 就要是配置的所有岗位 */
            if (isCustomerCase1 && dealPostList.length !== configPosts.length) {
                const roleList = await this.roleDao.searchList({companyIds: divisionCompanyIdList, names: configPosts})
                dealPostList = roleList.map(role => ({ roleId: role.companyMemberRoleId, name: role.roleName }))
            }
            if(query.signInStyle && !_.isEmpty(query.signInStyle)) {
                query.patrolSignStyle = query.signInStyle.map(item => {
                    return Analytics.signInStyleMap[item]
                })
            } else {
                query.patrolSignStyle = query.patrolSignStyle.map(item => {
                    return Analytics.signInStyleMap[item]
                })
            }
            if (!_.isEmpty(query.patrolSignStyle) && query.patrolSignStyle.includes('face')) {
                query.patrolSignStyle.push(...['attribute','manual', 'manual_face'])
            }
            return { isRoot, isCustomerCase1, isAllRole, dealPostList, dealManagerList, isShowUnpatroled, divisionCompanyIdList };
        } catch (e) {
            this.logger.log(`bodyParse fail body: ${JSON.stringify(query)}, error:${JSON.stringify(e)}`)
            return { noManager: true };
        }
    }

    getRealManagerInfo(query: { isAllRole: boolean, dealManagerList: any, persons: Analytics.AnalyticsPersonType[], roleNames?: string[] }) {
        const { isAllRole, dealManagerList, persons, roleNames } = query;
        const realManagerList: any = [];
        let realManager = -1;
        const managerZeros: Analytics.AnalyticsPersonType[] = [];
        if (!isAllRole && dealManagerList instanceof Array) {
            realManager = 0;
            dealManagerList.forEach(manager => {
                /** 如果 已经巡检过的人员 中没有 应该的巡检人员 则为这些人填补0为数据 */
                if (!_.isEmpty(roleNames) && roleNames.includes(manager.roleName)) {//如果配置了岗位 则只统计配置的岗位
                    const person = persons.find(item => item.managerId == manager.managerId);
                    if (!person) {
                        /** 真实应该巡检的人员 包括（已经离职但是有巡检记录的人员 和 未离职的所有应改巡检的人员
                         * 如果未离职 但应该巡检的人员 没有巡检 则为其加0 managerZeros
                         * realManagerList 过滤掉 dealManagerList中 未巡检的离职人员
                         */
                        /** 应该巡检 但没有巡检记录的在职人员加0 记录 NNNNNNNN 与下面的 NNNNN 对应 并集就是上面的描述 */
                        if (manager.deleteFlag == 'N') {
                            realManager++;
                            realManagerList.push(manager);
                            managerZeros.push({
                                managerId: manager.managerId,
                                nickName: manager.nickName,
                                totalProject: 0,
                                totalPatrol: 0,
                                projectIdList: [],
                            })
                        }
                    } else {
                        /** 如果 此人参与了 则为真实参与人 只要是有巡检记录的人 全部加上 */
                        realManager++;
                        realManagerList.push(manager);
                    }
                }
            })
            // persons.push(...managerZeros);
        }
        return { realManagerList, realManager, managerZeros };
    }

    getPositionInfo(
        query: {
            postRes: Analytics.AnalyticsPositionType[],
            isAllRole: boolean,
            isCustomerCase1?: boolean,
            realManagerList: any,
            dealPostList: any,
        }
    ) {
        const { postRes, isAllRole, isCustomerCase1, dealPostList, realManagerList } = query;
        let realPost = -1;  // (totalPosition / dealPosition) 如果为-1 的话 前端就不显示 / dealPosition
        const postZeros: Analytics.AnalyticsPositionType[] = [];
        /** 如果公司设置了 部分岗位的配置 则需要进一步分析。 假如做了全部配置 则不需要这个分析 */
        if (!isAllRole && dealPostList instanceof Array) {
            realPost = 0;
            /** 根据 实际的巡检人 realManager 重新计算 实际的巡检岗位 realPost */
            const realPostMap: any = {}; // 格式为 {[roleId]: roleName}
            if (isCustomerCase1) {
                dealPostList.forEach(post => {
                    if (!realPostMap[post.roleId]) {
                        realPost++;
                        realPostMap[post.roleId] = post.name;
                    }
                })
            } else {
                realManagerList.forEach(manager => {
                    if (!realPostMap[manager.roleId]) {
                        realPost++;
                        realPostMap[manager.roleId] = manager.roleName;
                    }
                })
            }

            for (let roleId in realPostMap) {
                const roleName = realPostMap[roleId];
                let i = 0;
                for (; i < postRes.length; i++) {
                    const item = postRes[i];
                    if (item.roleId == roleId) break;
                }
                /** 如果 已经巡检过的岗位 中没有 应该的巡检岗位 则为这些岗位填补0为数据 */
                if (i == postRes.length) {
                    postZeros.push({
                        roleId: roleId,
                        roleName,
                        totalManager: 0,
                        totalProject: 0,
                        totalPatrol: 0,
                    })
                }
            }
            // postRes.push(...postZeros);
        }
        return { realPost, postZeros }
    }

    /**
     * 直接从 body 分析 因为 body 加入了 tags。v2分析：优点快速 缺点：tags不准确，不能直接岗位Id 筛选。 所以保留 bodyAnalytics 
     * 从 camera service 中获取 bodyDB 数据
     * @param param 
     * @returns 
     */
    async bodyDocToItem_v2(param: {
        companyId: string,
        startTime: string,
        endTime: string,
        items: { companyId: string, managerId: string, projectId: string, eventTime?: string,earliestTime?: string, latestTime?: string, timeInterval?: number, uniqueTypes: string[], roleName?: string, isBindDevice?: string }[]
    }) {
        const { companyId, items } = param
        const managerIdList: string[] = [];
        const projectIdList: string[] = [];
        const companyIdList: string[] = [];
        items.forEach(item => {
            if (!managerIdList.includes(item.managerId)) managerIdList.push(item.managerId);
            if (!projectIdList.includes(item.projectId)) projectIdList.push(item.projectId);
            if (!companyIdList.includes(item.companyId)) companyIdList.push(item.companyId);
        });
        /** 根据遍历的 Ids 找到相应的数据 并范式化 managerMap projectMap 以便后面组合数据 */
        const promiseList = [
            this.companyCustomConfigService.isCustomCompany({ companyId, customType: 'project_address' }),//是否为定制公司 以便做地址配置 
            !_.isEmpty(managerIdList) ? this.managerDao.getBelongToListByDeptId({ managerIdList }) : [],// 根据 managerIdList 找到相应的 manager
            !_.isEmpty(managerIdList) ? this.departmentMemberDao.searchDeptInfoList({ managerIds: managerIdList }) : [],// 根据 managerIdList 找到相应的 managerDept
            !_.isEmpty(projectIdList) ? this.projectDao.getOnsiteProjectInfos({ projectIdList }) : [],// 根据 projectIdList 找到相应的 project
            !_.isEmpty(companyIdList) ? this.companyDao.searchCompanyList({ companyIds: companyIdList }) : [],// 根据 companyIdList 找到相应的 company
            this.getProjectBindDeviceMap({ projectIds: projectIdList, companyIds: companyIdList, startTime: param.startTime, endTime: param.endTime })// 根据 projectIdList 和 companyIdList 找到相应的 bindProjectMap
        ];
        const [isCustomCompany, managers, managerDeptList, projects, companyList, bindProjectMap] = await Promise.all(promiseList);
        const managerMap: Record<string, any> = _.keyBy(managers, 'managerId');
        const managerDeptMap: Record<string, any> = _.keyBy(managerDeptList, 'managerId');
        const projectMap: Record<string, any> = _.keyBy(projects, 'projectId');
        const companyMap: Record<string, any> = _.keyBy(companyList, 'id');
        const rows: any = [];
        /** 格式化数据 */
        items.forEach(item => {
            const manager = managerMap[item.managerId];
            const project = projectMap[item.projectId];
            const company = companyMap[item.companyId];
            const managerDept = managerDeptMap[item.managerId];
            if (!manager) throw new YqzException(`管理员不存在: 参数: {companyId: ${companyId}, managerId: ${item.managerId}}`);
            if (!project) throw new YqzException(`工地不存在: 参数: {companyId: ${companyId}, projectId: ${item.projectId}}`);
            // 签到方式
            let signInStyle = ""
            if (!_.isEmpty(item.uniqueTypes)) {
                const signInStyleMap = {
                    "face": "摄像头签到",
                    "attribute": "摄像头签到",
                    "manual": "摄像头签到",
                    "lbs": "app签到",
                    "manual_face": "摄像头签到"
                }
                const signInStyleList = []
                item.uniqueTypes = _.sortBy(item.uniqueTypes, (type) => {  
                    return _.indexOf(['lbs','face'], type);
                });  
                item.uniqueTypes.map(uniqueType => {
                    if (signInStyleMap[uniqueType])
                        signInStyleList.push(signInStyleMap[uniqueType]);
                })
                signInStyle = _.uniq(signInStyleList)?.join('+');
            }
            // 如果巡检时间是同一分钟，则离场时间加一分钟
            const isSameMinute = DateFns.differenceInMinutes(new Date(item.latestTime), new Date(item.earliestTime))
            if (isSameMinute < 1) {
                item.latestTime = format(DateFns.addMinutes(new Date(item.latestTime), 1), 'yyyy-MM-dd HH:mm:ss')
                item.timeInterval = 60000
            }
            // 判断是否绑定设备
            const projectId = Number(item.projectId)
            item.isBindDevice = "N"
            if (!!bindProjectMap[projectId] && !_.isEmpty(bindProjectMap[projectId]) && bindProjectMap[projectId]?.includes(formatDateToYYYYMMDD(new Date(item.eventTime)))) {
                item.isBindDevice = "Y"
            }
            const row = {
                patrolTime: item.eventTime ? format(new Date(item.eventTime), 'yyyy-MM-dd') : '未巡检',
                joinTime: item.earliestTime ? format(new Date(item.earliestTime), 'HH:mm:ss') : '',
                exitTime: item.latestTime ? format(new Date(item.latestTime), 'HH:mm:ss') : '',
                stayTime: item.timeInterval !== 0 ? formatMillisecondsDuration(item.timeInterval,true,true,true,false) : '',
                signInStyle: signInStyle,
                projectAddress: ((isCustomCompany as Boolean) ? project.projectName : project.communityName) + project.houseNumber,
                projectId: project.projectId,
                managerId: manager.managerId,
                nickName: manager.nickName,
                linkCompanyId: item.companyId,
                linkCompanyName: company?.companyName,
                roleName: item.roleName,
                departmentName: manager.departmentName,
                personDepartmentId: managerDept.deptId,
                personDepartmentName: managerDept.deptName,
                projectDirectorId: project.projectDirectorId,
                projectDirector: project.projectDirector,
                isBindDevice: item.isBindDevice,
                projectStartTime: project.projectStartTime ? format(new Date(project.projectStartTime), 'yyyy-MM-dd') : '',
                projectCompletedTime: project.projectCompletedTime ? format(new Date(project.projectCompletedTime), 'yyyy-MM-dd') : '',
            };
            rows.push(row);
        })
        return rows;
    }

    /**
     * 获取项目绑定设备映射
     * @param params 
     * @returns 
     */
    private async getProjectBindDeviceMap(params: { projectIds: string[], companyIds: string[], startTime: string, endTime: string }) {
        const { projectIds, companyIds, startTime, endTime } = params;
        let bindProjectMap = {};
        if (_.isEmpty(projectIds)) return bindProjectMap;
        if (!_.isEmpty(projectIds)) {
            const promiseList = [
                //获取历史绑定记录
                this.dimDeviceDao.searchProjectDateList({ companyIdList: companyIds, projectIdList: projectIds, startTime, endTime }),
                //获取当前绑定状态
                this.deviceDao.searchBindDeviceProject({ projectIds })
            ]
            const [bindProject, bindProjectIds] = await Promise.all(promiseList);
            const bindProjectList = bindProject || [];
            const projectIdList = (bindProjectIds as any[]).map(res => Number(res.projectId));
            // 生成映射，使用Set去重
            bindProjectMap = _.reduce(bindProjectList, (acc, obj) => {
                if (!acc[obj.projectId]) {
                    acc[obj.projectId] = new Set();
                }
                if (projectIdList.includes(Number(obj.projectId))) {
                    acc[obj.projectId].add(formatDateToYYYYMMDD(new Date()));
                }
                acc[obj.projectId].add(obj.date);
                return acc;
            }, {});
            // 将Set转换回数组
            Object.keys(bindProjectMap).forEach(key => {
                bindProjectMap[key] = Array.from(bindProjectMap[key]);
            });
        }
        return bindProjectMap;
    }

    /**
     * 
     * @param query 
     * @param module 查找哪个模块的 company_config
     * @returns 
     */
    async onlinePathConfig(query: Analytics.Req, module: string) {
        try {
            const { companyId } = query;
            if (!companyId) throw new YqzException("必传参数不能为空");

            /** 判断是否为根部门 （在这的作用为 筛选 不在任何部门的人 后面分析还要加上<如果是根部门 要加上所有的岗位即便分析数据中没有人>） */
            const rootDept = await this.departmentService.getRootDept(companyId);
            const isRoot: boolean = rootDept !== null && rootDept.departmentId === query.departmentId;
            // 公司设置
            let isAllRole: boolean = true; 
            let isShowUnpatroled = 'N';
            let configPosts = [];
            const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: module });
            if (config && config.configs.length === 1) {
                isAllRole = config?.configs[0]?.roleScope === "all"?true:false;  // 公司配置中是否 选择全部的岗位 在产品那叫全员
                isShowUnpatroled = config?.configs[0]?.noPatrolProjectShow || 'N'; // 是否显示未巡检统计
                configPosts = config?.configs[0]?.roles || [];
            }
            const isCustomerCase1: boolean = isRoot && configPosts.length > 0
            /** 最终要做 筛选想的 岗位设置 */
            let selectRoles = [...configPosts];
            if (query.roleName) { /** 如果前端筛选了 roleId */
                /** 如果公司做了配置 要检查 配置中是否有此 岗位  selectRoleIds.length > 0 的判断 是因为 如果为[] 则为全部 所以肯定在范围内 */
                if (selectRoles.length > 0 && !selectRoles.includes(query.roleName)) throw new YqzException("您筛选的 岗位 没有被设置");
                selectRoles = [query.roleName]; // 如果筛选了岗位, 则只有这一个岗位
            }

            // 筛选部门成员
            let deptInfoList = []
            if(query.departmentIds) deptInfoList = await this.departmentMemberDao.searchDeptInfoList({departmentIds: query.departmentIds, companyIds: query?.companyIds })
            const managerIds = []
            if (!deptInfoList || deptInfoList.length == 0) return { noManager: true };
            deptInfoList.forEach(item => {
                if(item.managerId) managerIds.push(item.managerId)
            })
            // const dimDeptDataList = await this.dimDepartmentDataDao.searchList({ departmentIdList: query.departmentIds, from: TimeUtil.formatDate(new Date(query.from),'yyyyMMdd'), to: TimeUtil.formatDate(new Date(query.to),'yyyyMMdd') });
            // if (!dimDeptDataList || dimDeptDataList.length == 0) return { noManager: true };
            // const managerIds = []
            // dimDeptDataList.forEach(item => {
            //     const managerIdList = item?.managerIdList? item?.managerIdList?.split(","):[]
            //     if (!_.isEmpty(managerIdList)) 
            //         managerIds.push(...managerIdList)
            // })
            query.managerIdList = managerIds

            /* 需要分析的所有人员 和 岗位 */
            let dealPostList: any = null; // 参与的岗位
            let dealManagerList: any = null; // 参与的管理者

            /** 如果 非全部岗位，则需要判断是否有对应的岗位 并且肯定读取到了 configRes */
            if (!isAllRole) {

                /** 根据 筛选的岗位 找出所有 应该 参与巡检的员工 dealManagerList */
                dealManagerList = await this.managerDao.getBelongToListByDeptId({
                    companyIdList: query.companyIds,
                    managerRoles: selectRoles,
                    managerIdList: query.managerIdList
                });
                if (!dealManagerList || dealManagerList.length == 0) return { noManager: true };
                /** 并且把 managerIdList 挂在到 query 去查找数据 以便查询巡检记录 */
                query.managerIdList = dealManagerList.map(manager => manager.managerId);
                if (query.managerId) query.managerIdList = [query.managerId];   // 这里暂时没有问题 因为分析数据不会传递 managerId 但是有缺陷 没时间修复了
                /** 根据 所有参与巡检的员工 再次筛选 本次要参与的部门 （因为 有的岗位 不在部门里） */
                selectRoles = dealManagerList.map(manager => manager.roleName);

                // // /** 判断是否为根部门 */
                // const rootDept = await this.departmentService.getRootDept(companyId);
                // const isRoot: boolean = rootDept !== null && rootDept.departmentId === query.departmentId;
                if (isRoot) {
                    /** 因为有些员工是外部员工， 他们有岗位 但是不一定有部门, 万一前端设置了这个岗位 要显示出来, 最好的方式是 不被设置此岗位， 暂时这么处理 */
                    dealPostList = await this.roleDao.searchList({ companyIds: query.companyIds,names: configPosts })
                } else {
                    dealPostList = await this.roleDao.searchList({ companyIds: query.companyIds,names: selectRoles })
                }

            }

            dealPostList = dealPostList?.map(role => {
                return {
                    ...role,
                    roleId: role.companyMemberRoleId,
                    name: role.roleName
                }
            })

            return { isAllRole, dealPostList, dealManagerList, isShowUnpatroled, isCustomerCase1 };
        } catch (e) {
            this.logger.log(`bodyParse fail body: ${JSON.stringify(query)}, error:${JSON.stringify(e)}`)
            return { noManager: true };
        }
    }

}