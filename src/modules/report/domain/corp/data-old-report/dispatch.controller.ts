import { Body, Controller, Post, UseInterceptors } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtUserGw, MyLogger, MyUser, TransformInterceptor } from "@yqz/nest";
import { Analytics } from "./type/analytics-util.type";
import { DispatchService } from "./service/dispatch.service";
import { DispatchType } from "./type/dispatch.type";
import { CompanyService } from "../../bgw/company/service/company.service";

@ApiTags("dispatch")
@Controller('dispatch')
export class DispatchController {
    private readonly logger = new MyLogger(DispatchController.name);
    constructor(
        private readonly dispatchService: DispatchService,
        private readonly companyService: CompanyService,
    ) { }

    @ApiTags("analytics")
    @ApiOperation({ summary: '统筹派工数据报表统计' })
    @Post("analytics/total")
    async projectUpdateAnalyticsTotal(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.dispatchService.dispatchAnalyticsTotal(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '统筹派工数据报表岗位分布' })
    @Post("analytics/position")
    async projectUpdateAnalyticsPosition(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.dispatchService.dispatchAnalyticsPositionV2(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '统筹派工数据报表员工分布' })
    @Post("analytics/person")
    async projectUpdateAnalyticsPerson(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.dispatchService.dispatchAnalyticsPerson(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '统筹派工数据报表数据明细' })
    @Post("analytics/list")
    async projectUpdateAnalyticsDataDetail(@Body() body: DispatchType.SearchListReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        body.companyType = await this.companyService.searchCompanyType({ companyId: body.companyId });
        return await this.dispatchService.searchList(body);
    }

    @ApiTags("analytics")
    @ApiOperation({ summary: '统筹派工数据报表选项' })
    @Post("analytics/options")
    async projectUpdateAnalyticsOptions(@Body() body: Analytics.Req, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body?.companyId;
        return await this.dispatchService.dispatchAnalyticsOptions(body);
    }

}