import { Body, Controller, Post } from "@nestjs/common";
import { ApiOperation } from "@nestjs/swagger";
import { JwtUserGw, <PERSON><PERSON><PERSON>ger, MyUser } from "@yqz/nest";
import { OnlinePatrolService } from "./service/online-patrol.service";
import { Analytics } from "./type/analytics-util.type";
import { CompanyService } from "../../bgw/company/service/company.service";

@Controller('online/patrol')
export class OnlinePatrolController {
    private readonly logger = new MyLogger(OnlinePatrolController.name);
    constructor(
        private readonly onlinePatrolService: OnlinePatrolService,
         private readonly companyService: CompanyService,
    ) { }

    @ApiOperation({ summary: '线上巡检分析 选项' })
    @Post("analytics/options")
    async analyticsOptions(@Body() query: Analytics.Req, @MyUser() user: JwtUserGw) {
        query.companyId = user.targetCompany.companyId || query.companyId;
        return await this.onlinePatrolService.analyticsOptions(query);
    }

    @ApiOperation({ summary: '线上巡检分析 员工分布' })
    @Post("analytics/person")
    async analyticsPerson(@Body() query: Analytics.Req, @MyUser() user: JwtUserGw) {
        query.companyId = user.targetCompany.companyId || query.companyId;
        return await this.onlinePatrolService.analyticsPerson(query);
    }

    @ApiOperation({ summary: '线上巡检分析 岗位分布' })
    @Post("analytics/position")
    async analyticsPosition(@Body() query: Analytics.Req, @MyUser() user: JwtUserGw) {
        query.companyId = user.targetCompany.companyId || query.companyId;
        return await this.onlinePatrolService.analyticsPosition(query);
    }

    @ApiOperation({ summary: '线上巡检分析 数据明细 ' })
    @Post("analytics/list")
    async analyticsList(@Body() query: Analytics.Req, @MyUser() user?: JwtUserGw) {
        query.companyId = user.targetCompany.companyId || query.companyId;
        query.companyType = await this.companyService.searchCompanyType({ companyId: query.companyId });
        return await this.onlinePatrolService.analyticsList(query);
    }
}