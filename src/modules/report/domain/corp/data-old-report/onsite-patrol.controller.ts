import { Body, Controller, Post } from "@nestjs/common";
import { JwtUser<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MyUser } from "@yqz/nest";
import { OnsitePatrolService } from "./service/onsite-patrol.service";
import { ApiOperation } from "@nestjs/swagger";
import { Analytics } from "./type/analytics-util.type";
import { CompanyService } from "../../bgw/company/service/company.service";

@Controller('onsite/patrol')
export class OnsitePatrolController {
    private readonly logger = new MyLogger(OnsitePatrolController.name);
    constructor(
        private readonly onsitePatrolService: OnsitePatrolService,
        private readonly companyService: CompanyService,
    ) { }

    @ApiOperation({ summary: '现场巡检分析 选项' })
    @Post("analytics/options")
    async analyticsOptions(@Body() query: Analytics.Req, @MyUser() user: JwtUserGw) {
        // query.companyId = '65'
        query.companyId = user?.targetCompany?.companyId || query?.companyId;
        return await this.onsitePatrolService.analyticsOptions(query);
    }

    @ApiOperation({ summary: '现场巡检分析 员工分布' })
    @Post("analytics/person")
    async searchAnalyticsPerson(@Body() query: Analytics.Req, @MyUser() user: JwtUserGw) {
        // query.companyId = '*********'
        query.companyId = user?.targetCompany?.companyId || query?.companyId;
        return await this.onsitePatrolService.searchAnalyticsPerson(query);
    }

    @ApiOperation({ summary: '现场巡检分析 岗位分布' })
    @Post("analytics/position")
    async searchAnalyticsPosition(@Body() query: Analytics.Req, @MyUser() user: JwtUserGw) {
        // query.companyId = '*********'
        query.companyId = user?.targetCompany?.companyId || query?.companyId;
        return await this.onsitePatrolService.searchAnalyticsPosition(query);
    }

    @ApiOperation({ summary: '现场巡检分析 数据明细 ' })
    @Post("analytics/list")
    async analyticsList(@Body() query: Analytics.Req, @MyUser() user?: JwtUserGw) {
        // query.companyId = '*********'
        query.companyId = user?.targetCompany?.companyId || query?.companyId;
        query.companyType = await this.companyService.searchCompanyType({ companyId: query.companyId });
        return await this.onsitePatrolService.analyticsList(query);
    }

}