import { DateReport } from '../../data-report/dto/data-report.dto';
import { Sql } from '../type/sql.type';
import { AcceptanceReportTemplates } from './acceptance-report';
import { AiDetectionTemplates } from './ai-detection';
import { CustomerEvaluateTemplates } from './customer-evaluate';
import { DeviceTemplates } from './device';
import { NodeOverdueTemplates } from './node-overdue';
import { ProjectTemplates } from './project';
import { ProjectProblemTemplates } from './project-problem';

export const SubjectTemplates: Record<DateReport.Subject, Sql.Template> = {
    [DateReport.Subject.Delivery]: null,
    [DateReport.Subject.Project]: ProjectTemplates,
    [DateReport.Subject.Device]: DeviceTemplates,
    [DateReport.Subject.AcceptanceReport]: AcceptanceReportTemplates,
    [DateReport.Subject.AiDetection]: AiDetectionTemplates,
    [DateReport.Subject.NodeOverdue]: NodeOverdueTemplates,
    [DateReport.Subject.ProjectUpdate]: null,
    [DateReport.Subject.OnlinePatrol]: null,
    [DateReport.Subject.OnsitePatrol]: null,
    [DateReport.Subject.Dispatch]: null,
    [DateReport.Subject.Report]: null,
    [DateReport.Subject.ProjectProblem]: ProjectProblemTemplates,
    [DateReport.Subject.Evaluate]: CustomerEvaluateTemplates,
    [DateReport.Subject.Live]: null,
};
