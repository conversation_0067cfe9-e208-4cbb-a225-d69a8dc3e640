
import { DateReport } from "../../data-report/dto/data-report.dto";
import { AcceptanceReportDataReport } from "../../data-report/service/basic/dto/acceptance-report-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const AcceptanceReportTemplates: Sql.Template = {
    name: DateReport.Subject.AcceptanceReport,// 线上验收
    topics: {
        [AcceptanceReportDataReport.Topic.AcceptanceReportTotal]: {
            id: 'ar.id',
            name: '验收报告总数',
            baseSql: `
                            select ar.id id, ar.project_id projectId
                            from ${DaoUtil.getDbName("bgw.acceptance_report")} ar
                            left join ${DaoUtil.getDbName("bgw.project")} p on ar.project_id = p.project_id
                            where ar.delete_flag = 'N' and p.delete_flag = 'N'
                            `,
            //支持的筛选
            filterOptions: {
                companyId: 'ar.company_id',
                departmentId: 'p.department_id',
            },
        },
        [AcceptanceReportDataReport.Topic.NewAcceptanceReport]: {
            id: 'ar.id',
            name: '新增验收报告',
            baseSql: `
                            select ar.id id, ar.project_id projectId
                            from ${DaoUtil.getDbName("bgw.acceptance_report")} ar
                            left join ${DaoUtil.getDbName("bgw.project")} p on ar.project_id = p.project_id
                            where ar.delete_flag = 'N' and p.delete_flag = 'N'
                            `,
            //支持的筛选
            filterOptions: {
                companyId: 'ar.company_id',
                projectId: 'ar.project_id',
                acceptanceDate: 'ar.acceptance_date',
                departmentId: 'p.department_id'
            },
        },
        [AcceptanceReportDataReport.Topic.RejectedAcceptanceReport]: {
            id: 'ar.id',
            name: '审核不通过验收报告',
            baseSql: `
                            select ar.id id, ar.project_id projectId
                            from ${DaoUtil.getDbName("bgw.acceptance_report")} ar
                            inner join ${DaoUtil.getDbName("bgw.acceptance_report_log")} arl on arl.acceptance_report_id = ar.id and arl.operate_type = '11' and arl.delete_flag = 'N'
                            left join ${DaoUtil.getDbName("bgw.project")} p on ar.project_id = p.project_id
                            where ar.delete_flag = 'N' 
                            and p.delete_flag = 'N'
                            and arl.delete_flag = 'N'
                            and arl.operate_type = 11
                            `,
            //支持的筛选
            filterOptions: {
                companyId: 'ar.company_id',
                projectId: 'ar.project_id',
                acceptanceDate: 'ar.acceptance_date',
                departmentId: 'p.department_id'
            },
        },
    },
};