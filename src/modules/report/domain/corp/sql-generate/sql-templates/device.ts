
import { DateReport } from "../../data-report/dto/data-report.dto";
import { DeviceDataReport } from "../../data-report/service/basic/dto/device-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const DeviceTemplates: Sql.Template = {
    name: DateReport.Subject.Device,// 设备概况
    topics: {
        [DeviceDataReport.Topic.DeviceTotal]: {
            id: 'd.yqz_device_id',
            name: '当前设备总数(实时)',
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N'
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentInternalIdleDevice]: {
            id: 'd.yqz_device_id',
            name: '当前库内闲置设备(实时)',//所选部门在当前未领用的设备
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.project_manager_id is null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentExternalIdleDevice]: {
            id: 'd.yqz_device_id',
            name: '当前库外闲置设备(实时)',//所选部门在当前已领用但未绑工地的设备
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.project_manager_id is not null and d.project_id is null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentReviceDevice]: {
            id: 'd.yqz_device_id',
            name: '当前已领用设备(实时)',//所选部门在当前已领用的设备
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.project_manager_id is not null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentInstallDevice]: {
            id: 'd.yqz_device_id',
            name: '当前已安装设备(实时)',//所选部门在当前已安装的设备
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.project_manager_id is not null and d.project_id is not null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentOnlineDevice]: {
            id: 'd.yqz_device_id',
            name: '当前在线设备(实时)',//所选部门在当前绑定了工地的设备中状态为在线的设备
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.is_online = 1 and d.project_manager_id is not null and d.project_id is not null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentOfflineDevice]: {
            id: 'd.yqz_device_id',
            name: '当前离线设备(实时)',//所选部门在当前绑定了工地的设备中状态为离线的设备
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.is_online = 0 and d.project_manager_id is not null and d.project_id is not null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.CurrentOfflineDeviceByDay]: {
            id: 'd.yqz_device_id',
            name: '当前离线>=N天设备(实时)',//所选部门在当前绑定了工地的设备中状态为离线且离线超过一定天数的设备（天数可自定义配置）
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.is_online = 0 and d.project_manager_id is not null and d.project_id is not null
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
                offlineDay: 'DATEDIFF(NOW(), d.onoffline_time)',
            }
        },
        [DeviceDataReport.Topic.ExpiredDevice]: {
            id: 'd.yqz_device_id',
            name: '当前已过期设备(实时)',
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N' and d.status = 3
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.SoonExpiredDevice]: {
            id: 'd.yqz_device_id',
            name: '当前即将过期设备(实时)',
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N'
                            `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.InstallErrorDevice]: {
            id: 'd.yqz_device_id',
            name: '当前安装异常设备(实时)',
            baseSql: `
                            select d.yqz_device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("camera.device_status")} d
                            where d.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.yqz_device_id',
            }
        },
        [DeviceDataReport.Topic.DeviceCount]: {
            id: 'd.device_id',
            name: '设备数',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.InternalIdleDevice]: {
            id: 'd.device_id',
            name: '库内闲置设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.ExternalIdleDevice]: {
            id: 'd.device_id',
            name: '库外闲置设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.ReviceDevice]: {
            id: 'd.device_id',
            name: '已领用设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                            where d.manager_id is not null
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.InstallDevice]: {
            id: 'd.device_id',
            name: '已安装设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                            where d.project_id is not null
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.OfflineDevice]: {
            id: 'd.device_id',
            name: '异常离线设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.OnlineDevice]: {
            id: 'd.device_id',
            name: '正常在线设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        },
        [DeviceDataReport.Topic.OfflineDeviceByDay]: {
            id: 'd.device_id',
            name: '离线>=N天设备',
            baseSql: `
                            select d.device_id deviceId, d.company_id companyId
                            from ${DaoUtil.getDbName("etl.dim_device")} d
                    `,
            //支持的筛选
            filterOptions: {
                yqzDeviceId: 'd.device_id',
                companyId: 'd.company_id',
                time: 'd.date'
            }
        }
    }
}