import { DateReport } from "../../data-report/dto/data-report.dto";
import { CustomerEvaluateDataReport } from "../../data-report/service/basic/dto/customer-evaluate-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const CustomerEvaluateTemplates: Sql.Template = {
    name: DateReport.Subject.Evaluate,
    topics: {
        [CustomerEvaluateDataReport.Topic.EvaluateTotal]: {
            id: 'e.evaluate_id',
            name: '评价总数',
            baseSql: `
                select e.evaluate_id id
                from ${DaoUtil.getDbName("bgw.evaluate")} e
                where e.delete_flag = 'N' AND e.evaluate_type = '3'
            `,
            filterOptions: {
                evaluateId: 'e.evaluate_id',
            }
        },
        [CustomerEvaluateDataReport.Topic.EvaluateNew]: {
            id: 'e.evaluate_id',
            name: '新增评价',
            baseSql: `
            select e.evaluate_id id
            from ${DaoUtil.getDbName("bgw.evaluate")} e
            where e.delete_flag = 'N' AND e.evaluate_type = '3'
        `,
            filterOptions: {
                evaluateId: 'e.evaluate_id',
                from: 'e.evaluate_time',
                to: 'e.evaluate_time',
            }
        },
        [CustomerEvaluateDataReport.Topic.EvaluateGoodNew]: {
            id: 'e.evaluate_id',
            name: '新增好评',
            baseSql: `
                select e.evaluate_id id
                from ${DaoUtil.getDbName("bgw.evaluate")} e
                where e.delete_flag = 'N' AND e.evaluate_type = '3'
            `,
            filterOptions: {
                evaluateId: 'e.evaluate_id',
                from: 'e.evaluate_time',
                to: 'e.evaluate_time',
            },
        },
        [CustomerEvaluateDataReport.Topic.EvaluateBadNew]: {
            id: 'e.evaluate_id',
            name: '新增差评',
            baseSql: `
                select e.evaluate_id id
                from ${DaoUtil.getDbName("bgw.evaluate")} e
                where e.delete_flag = 'N' AND e.evaluate_type = '3'
            `,
            filterOptions: {
                evaluateId: 'e.evaluate_id',
                from: 'e.evaluate_time',
                to: 'e.evaluate_time',
            }
        }
    }
};