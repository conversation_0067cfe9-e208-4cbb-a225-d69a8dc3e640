import { Project } from "@yqz/nest";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { ProjectProblemDataReport } from "../../data-report/service/basic/dto/project-problem-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const ProjectProblemTemplates: Sql.Template = {
    name: DateReport.Subject.ProjectProblem,// 工地问题
    topics: {
        [ProjectProblemDataReport.Topic.ProjectProblemTotal]: {
            id: 'pp.project_problem_id',
            name: '工地问题',
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
            `,
            filterOptions: {
                projectIds: 'pj.project_id',
                statusList: 'pp.problem_status',
            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.ProjectProblemNew]: {
            id: 'pp.project_problem_id',
            name: '新增问题',
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
            `,
            filterOptions: {
                projectIds: 'pj.project_id',
                from: 'pp.problem_time',
                to: 'pp.problem_time',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.NorectifiedOverdueNew]: {
            id: 'pp.project_problem_id',
            name: '新增超时未整改',
            // problem_status = '1'或者 '2',solve_deadline_time < now()
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId,
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status in ('1', '2')
                and pp.solve_deadline_time < now()
            `,
            filterOptions: {
                companyId: 'pj.company_id',
                projectIds: 'pp.project_id',
                from: 'pp.problem_time',
                to: 'pp.problem_time',
                departmentId: 'pj.department_id',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.NorectifiedProblemNew]: {
            id: 'pp.project_problem_id',
            name: '新增未整改',
            // problem_status = '1'或者 '2' (待处理或处理中的问题)
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status in ('1', '2')
            `,
            filterOptions: {
                projectIds: 'pj.project_id',
                from: 'pp.problem_time',
                to: 'pp.problem_time',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.RectifiedProblemNew]: {
            id: 'pp.project_problem_id',
            name: '新增已整改',
            // problem_status = '3' (已处理的问题)
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status = '3'
            `,
            filterOptions: {
                projectIds: 'pj.project_id',
                from: 'pp.problem_time',
                to: 'pp.problem_time',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.OverdueRectifiedNew]: {
            id: 'pp.project_problem_id',
            name: '新增超时整改',
            // problem_status = '3' (已处理) 且 problem_solved_time > solve_deadline_time (整改时间超过截止时间)
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status = '3'
                and pp.problem_solved_time > pp.solve_deadline_time
            `,
            filterOptions: {
                companyId: 'pj.company_id',
                projectIds: 'pp.project_id',
                from: 'pp.problem_time',
                to: 'pp.problem_time',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.NorectifiedProblemNow]: {
            id: 'pp.project_problem_id',
            name: '当前未整改',
            baseSql: `
            select pp.project_problem_id id, pp.project_id projectId
            from ${DaoUtil.getDbName("bgw.project_problem")} pp
            left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
            where pj.delete_flag = 'N' and pp.delete_flag = 'N'
            and pp.problem_status in ('1', '2')
        `,
            filterOptions: {
                projectIds: 'pj.project_id',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.NorectifiedOverdueNow]: {
            id: 'pp.project_problem_id',
            name: '当前超时未整改',
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId,
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status in ('1', '2')
                and pp.solve_deadline_time < now()
            `,
            filterOptions: {
                companyId: 'pj.company_id',
                projectIds: 'pp.project_id',
                departmentId: 'pj.department_id',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.RectifiedProblemNow]: {
            id: 'pp.project_problem_id',
            name: '当前已整改',
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status = '3'
            `,
            filterOptions: {
                projectIds: 'pj.project_id',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
        [ProjectProblemDataReport.Topic.OverdueRectifiedNow]: {
            id: 'pp.project_problem_id',
            name: '当前超时整改',
            baseSql: `
                select pp.project_problem_id id, pp.project_id projectId
                from ${DaoUtil.getDbName("bgw.project_problem")} pp
                left join ${DaoUtil.getDbName("bgw.project")} pj on pp.project_id = pj.project_id
                where pj.delete_flag = 'N' and pp.delete_flag = 'N'
                and pp.problem_status = '3'
                and pp.problem_solved_time > pp.solve_deadline_time
            `,
            filterOptions: {
                companyId: 'pj.company_id',
                projectIds: 'pp.project_id',
                statusList: 'pp.problem_status',

            },
            sortOptions: {
                problemTime: 'pp.problem_time',
            }
        },
    },
};
