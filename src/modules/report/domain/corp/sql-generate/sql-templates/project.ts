
import { DateReport } from "../../data-report/dto/data-report.dto";
import { ProjectDateReport } from "../../data-report/service/basic/dto/project-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const ProjectTemplates: Sql.Template = {
    name: DateReport.Subject.Project,// 工地概况
    topics: {
        [ProjectDateReport.Topic.ProjectTotal]: {
            id: 'p.project_id',
            name: '当前工地总数(实时)',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                departmentId: 'p.department_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.ProjectInWork]: {
            id: 'p.project_id',
            name: '当前在建工地总数(实时)',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N' and p.project_status = 'N'
                    and (p.project_start_time is null or p.project_start_time < CURDATE() + INTERVAL 1 DAY)
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                departmentId: 'p.department_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.ProblemProject]: {
            id: 'p.project_id',
            name: '当前问题工地(实时)',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    left join ${DaoUtil.getDbName("bgw.project_problem")} pp on p.project_id = pp.project_id
                    where p.delete_flag = 'N' and pp.delete_flag = 'N' and pp.problem_status in ('1', '2')
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                departmentId: 'p.department_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.TodayNoWorkProject]: {
            id: 'p.project_id',
            name: '今日停工工地(实时)',//所选部门下绑定摄像头的在建工地中今日未施工的工地数
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    left join ${DaoUtil.getDbName("camera.device_status")} ds on p.project_id = ds.project_id
                    left join ${DaoUtil.getDbName("camera.device_project_status")} dps on p.project_id = dps.project_id
                    where p.delete_flag = 'N' and ds.delete_flag = 'N' and dps.delete_flag = 'N'
                    and p.project_status = 'N' 
                    and dps.last_work_time < CURDATE()
                    and (p.project_start_time is null or p.project_start_time <= CURDATE() + INTERVAL 1 DAY)
                    group by p.project_id
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                departmentId: 'p.department_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.TodayNoWorkProjectByDay]: {
            id: 'p.project_id',
            name: '今日停工>=N天工地(实时)',//所选部门下绑定摄像头的在建工地中今日未施工且连续未施工天数>=N天的工地数（天数可自定义配置）
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    left join ${DaoUtil.getDbName("camera.device_status")} ds on p.project_id = ds.project_id
                    left join ${DaoUtil.getDbName("camera.device_project_status")} dps on p.project_id = dps.project_id
                    where p.delete_flag = 'N' and ds.delete_flag = 'N' and dps.delete_flag = 'N'
                    and p.project_status = 'N' 
                    and (p.project_start_time is null or p.project_start_time <= CURDATE() + INTERVAL 1 DAY)
                    group by p.project_id
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                departmentId: 'p.department_id',
                projectId: 'p.project_id',
                noWorkDays: 'DATEDIFF(DATE_FORMAT(NOW(),"%Y-%m-%d"), DATE_FORMAT(dps.last_work_time, "%Y-%m-%d"))'
            }
        },
        [ProjectDateReport.Topic.NoCompletedProject]: {
            id: 'p.project_id',
            name: '待设完工工地(实时)',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N' and p.project_status = 'N' and p.project_start_time is not null
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                departmentId: 'p.department_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.ProjectCount]: {
            id: 'p.project_id',
            name: '工地数',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.NewStartProject]: {
            id: 'p.project_id',
            name: '新开工工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
                time: 'p.project_start_time'
            }
        },
        [ProjectDateReport.Topic.NewCompletedProject]: {
            id: 'p.project_id',
            name: '新完工工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
                time: 'p.completed_time'
            }
        },
        [ProjectDateReport.Topic.NewProblemProject]: {
            id: 'p.project_id',
            name: '新增问题工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    inner join ${DaoUtil.getDbName("bgw.project_problem")} pp on p.project_id = pp.project_id
                    where p.delete_flag = 'N' and pp.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
                time: 'pp.problem_time'
            }
        },
        [ProjectDateReport.Topic.DelayProject]: {
            id: 'p.project_id',
            name: '延期工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    inner join ${DaoUtil.getDbName("bgw.project_node")} pn on pn.project_id = p.project_id and pn.delete_flag = 'N'
                    inner join ${DaoUtil.getDbName("bgw.project_schedule_modified")} psm on psm.project_node_id = pn.project_node_id and psm.delete_flag = 'N'
                    where p.bind_project_node = 'Y'
                    and psm.type = 'delay'
                    group by p.project_id
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
                time: 'pn.deadline_time',
                originTime: 'pn.origin_deadline_time',
            }
        },
        [ProjectDateReport.Topic.OverdueProject]: {
            id: 'p.project_id',
            name: '逾期工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    and p.bind_project_node = 'Y'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.InactiveProject]: {
            id: 'p.project_id',
            name: '停工工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id',
            }
        },
        [ProjectDateReport.Topic.InactiveProjectByDay]: {
            id: 'p.project_id',
            name: '停工>=N天工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id'
            }
        },
        [ProjectDateReport.Topic.OwnerFocusProject]: {
            id: 'p.project_id',
            name: '业主特别关注工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id'
            }
        },
        [ProjectDateReport.Topic.OwnerNegativeReviewProject]: {
            id: 'p.project_id',
            name: '差评工地',
            baseSql: `
                    select p.project_id id
                    from ${DaoUtil.getDbName("bgw.project")} p
                    where p.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                companyId: 'p.company_id',
                projectId: 'p.project_id'
            }
        },

    },
};