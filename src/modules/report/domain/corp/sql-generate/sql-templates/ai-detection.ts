
import { DateReport } from "../../data-report/dto/data-report.dto";
import { AiDetectionDataReport } from "../../data-report/service/basic/dto/ai-detection-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const AiDetectionTemplates: Sql.Template = {
    name: DateReport.Subject.AiDetection,// 示例
    topics: {
        [AiDetectionDataReport.Topic.TidinessDetection]: {
            id: 'tid.project_tidiness_detection_id',
            name: '工地整洁度',
            baseSql: `
                    select tid.project_tidiness_detection_id id, tid.project_id projectId
                    from ${DaoUtil.getDbName("camera.project_tidiness_detection")} tid
                    where tid.delete_flag = 'N'
                    `,
            //支持的筛选
            filterOptions: {
                projectId: 'tid.project_id',
                detectionTime: 'tid.detection_time',
            },
        },
        [AiDetectionDataReport.Topic.ActionDetection]: {
            id: 'sec.project_security_detection_id',
            name: '行为识别',
            baseSql: `
                    select sec.project_security_detection_id id,sec.project_id projectId
                    from ${DaoUtil.getDbName("camera.project_security_detection")} sec
                    where sec.delete_flag = 'N'
                    and (${AiDetectionDataReport.ActionReportType.getEnums().map(code => `find_in_set('${code}', sec.risk_type)`).join(' or ')})
                    `,
            //支持的筛选
            filterOptions: {
                projectId: 'sec.project_id',
                detectionTime: 'sec.detection_time',
            },
        },
        [AiDetectionDataReport.Topic.BodyDetection]: {
            id: 'sec.project_security_detection_id',
            name: '物体识别',
            baseSql: `
                    select sec.project_security_detection_id id, sec.project_id projectId
                    from ${DaoUtil.getDbName("camera.project_security_detection")} sec
                    where sec.delete_flag = 'N'
                    and (${AiDetectionDataReport.BodyReportType.getEnums().map(code => `find_in_set('${code}', sec.risk_type)`).join(' or ')})
                    `,
            //支持的筛选
            filterOptions: {
                projectId: 'sec.project_id',
                detectionTime: 'sec.detection_time',
            },
        },
    },
};