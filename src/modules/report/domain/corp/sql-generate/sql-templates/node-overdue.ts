import { DateReport } from "../../data-report/dto/data-report.dto";
import { NodeOverdueDataReport } from "../../data-report/service/basic/dto/node-overdue-data-report.dto";
import { Sql } from "../type/sql.type";
import { DaoUtil } from "@src/util/dao.util";

export const NodeOverdueTemplates: Sql.Template = {
    name: DateReport.Subject.NodeOverdue,
    topics: {
        [NodeOverdueDataReport.Topic.ControlsNode]: {
            id: 'pn.project_node_id',
            name: "管控节点数",
            baseSql: `
                select pn.project_node_id id
                from ${DaoUtil.getDbName("bgw.project")} p
                inner join ${DaoUtil.getDbName("bgw.project_node")} pn on pn.project_id = p.project_id and pn.delete_flag = 'N'
                where p.delete_flag = 'N'
                and p.bind_project_node = 'Y'
            `,
            filterOptions: {
                projectNodeId: 'pn.project_node_id',
                time: 'pn.deadline_time',
                originTime: 'pn.origin_deadline_time',
            }
        },
        [NodeOverdueDataReport.Topic.DelayNode]: {
            id: 'pn.project_node_id',
            name: "延期节点数",
            baseSql: `
                select pn.project_node_id id
                from ${DaoUtil.getDbName("bgw.project")} p
                inner join ${DaoUtil.getDbName("bgw.project_node")} pn on pn.project_id = p.project_id and pn.delete_flag = 'N'
                inner join ${DaoUtil.getDbName("bgw.project_schedule_modified")} psm on psm.project_node_id = pn.project_node_id and psm.delete_flag = 'N'
                where p.bind_project_node = 'Y'
                and psm.type = 'delay'
                group by pn.project_node_id
            `,
            filterOptions: {
                projectNodeId: 'pn.project_node_id',
                time: 'pn.deadline_time',
                originTime: 'pn.origin_deadline_time',
            }
        },
        [NodeOverdueDataReport.Topic.OverdueNode]: {
            id: 'pn.project_node_id',
            name: "逾期节点数",
            baseSql: `
                select pn.project_node_id id
                from ${DaoUtil.getDbName("bgw.project")} p
                inner join ${DaoUtil.getDbName("bgw.project_node")} pn on pn.project_id = p.project_id and pn.delete_flag = 'N'
                where p.bind_project_node = 'Y'
                group by pn.project_node_id
            `,
            filterOptions: {
                projectNodeId: 'pn.project_node_id',
                time: 'pn.deadline_time',
                originTime: 'pn.origin_deadline_time',
            }
        }
    }
};
