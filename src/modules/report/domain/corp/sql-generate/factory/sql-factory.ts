import { Injectable } from "@nestjs/common";
import { DefaultSqlData } from "../sql-data";
import { DevTimeout, MyLogger, YqzException } from "@yqz/nest";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { SubjectTemplates } from "../sql-templates";
import { Sql } from "../type/sql.type";
import { SqlBuilder } from "../sql-builder";
import { DaoUtil } from "@src/util/dao.util";
import { CustomChartsDao } from "../../custom-charts/dao/custom-charts.dao";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { AcceptanceReportDataReport } from "../../data-report/service/basic/dto/acceptance-report-data-report.dto";

@Injectable()
export class SqlFactory {
    private readonly logger = new MyLogger(SqlFactory.name);

    constructor(
        private readonly sqlConfigRepo: CustomChartsDao,
        private readonly dataSource: DataSource,
    ) { }

    // @DevTimeout(100)
    async test111() {
        const data = await this.createSqlData({
            companyId: "65",
            subject: DateReport.Subject.AcceptanceReport,
            topic: AcceptanceReportDataReport.Topic.AcceptanceReportTotal,
            addFilters: [{
                key: "departmentId",
                value: "13",
                operator: Sql.Operator.equal
            },{
                key: "companyId",
                value: "65",
                operator: Sql.Operator.equal
            }],
            dynamicSort: {
                "startTime": "asc"
            },
            dynamicLimit: 10,
            dynamicOffset: 0,
        })
        console.log(`data: `,JSON.stringify(data))
        const list = await this.executeSql(data.sql,data.params)
        console.log(`list: `,list)
    }
    async createSqlData(params: {companyId: string, subject: DateReport.Subject, topic: string, addFilters?: Sql.Column[],dynamicSort?: Record<string, string>,dynamicLimit?: number,dynamicOffset?: number}): Promise<DefaultSqlData> {
        const {companyId, subject, topic, addFilters, dynamicSort, dynamicLimit, dynamicOffset} = params
        try {
            const template = this.getTemplate(subject, topic);
            if (!template) throw new YqzException(`Template not found for subject '${subject}' and topic '${topic}'`);

            const config = await this.sqlConfigRepo.getSqlConfigBySubjectAndTopic({companyId, subject, topic});
            // if (_.isEmpty(config)) throw new YqzException(`Configuration not found for subject '${subject}' and topic '${topic}'`);

            const conditions = config.filter(res => res.type === Sql.ColumnType.filter).map(item => {
                return new Sql.Column({
                    key: item.key,
                    operator: Sql.Operator[item?.operator],
                    value: item.value,
                    logic: item.logic,
                    nested: item.nested
                })
            });
            const groupBys = config.filter(res => res.type === Sql.ColumnType.group_by);

            // 添加自定义过滤条件
            const conditionAllList: Sql.Column[] = [...conditions, ...(addFilters || [])]

            // 自定义分组条件配置处理
            const selectedGroups = (groupBys || [])?.reduce((result, info) => {
                const key = info.key;
                if (!result[key]) result[key] = [];
                result[key].push(info.value);
                return result;
            }, {});

            const { sql, params } = SqlBuilder.build({
                baseSql: template.baseSql,
                filterOptions: template.filterOptions,
                groupOptions: template.groupOptions,
                selectedFilters: conditionAllList,
                selectedGroups,
                sortOptions: template.sortOptions,
                dynamicSort, 
                dynamicLimit,
                dynamicOffset
            });
            // this.logger.log(`createSqlData return sql: ${sql} params: ${JSON.stringify(params)}`)

            const idSql = SqlBuilder.buildIdSql(sql, template.id);
            this.logger.log(`createSqlData return idSql: ${idSql} id: ${template.id}`)

            return  new DefaultSqlData(sql, idSql, params);
        } catch (error) {
            this.logger.error(`[SqlFactory] Error creating SQL object: ${error.message}`);
            throw error;
        }
    }

    async executeSql(sql: string, params: any[]): Promise<any[]> {
        if (!sql.trim().toUpperCase().startsWith('SELECT')) {
          throw new YqzException('Only SELECT queries are allowed');
        }
    
        return this.dataSource.query(sql, params);
    }

    private getTemplate(subject: string, topic: string) {
        const template = SubjectTemplates[subject]?.topics[topic];
        return template;
    }
}