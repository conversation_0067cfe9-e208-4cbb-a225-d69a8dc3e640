import { YqzException } from "@yqz/nest";
import { Sql } from "./type/sql.type";

export class SqlBuilder {
    static build(
        dto: {
            baseSql: string,
            filterOptions: Record<string, string>,
            groupOptions: Record<string, string>,
            selectedFilters: Sql.Column[],
            selectedGroups: Record<string, string>,
            sortOptions?: Record<string, string>,
            dynamicSort?: Record<string, string>,
            dynamicLimit?: number,
            dynamicOffset?: number,
        }
    ): { sql: string; params: any[] } {
        const { baseSql, filterOptions, groupOptions, selectedFilters, selectedGroups, sortOptions, dynamicSort, dynamicLimit, dynamicOffset } = dto;

        const [queryWithoutOrder, groupByClause, orderByClause, limitClause, offsetClause] = this.splitBaseSql(
            baseSql,
        );

        const additionalFilters = this.generateFilters(
            selectedFilters,
            filterOptions,
        );

        const finalWhereClause = this.appendWhereClause(
            queryWithoutOrder,
            additionalFilters,
        );

        const groups = Object.keys(selectedGroups || {})
            .map((key) => groupOptions[key])
            .join(', ');
        const finalGroupBy = groups ? `GROUP BY ${groups}` : groupByClause;

        const orders = Object.keys(dynamicSort || {})
            .filter((key) => sortOptions&&sortOptions[key])
            .map((key) => `${sortOptions[key]} ${dynamicSort[key]}`)
            .join(', ');
        console.log(`orders`,orders)
        const finalOrderBy = orders ? `ORDER BY ${orders}` : orderByClause;
        const finalLimit =
            dynamicLimit !== undefined ? `LIMIT ${dynamicLimit}` : limitClause;
        const finalOffset =
            dynamicOffset !== undefined ? `OFFSET ${dynamicOffset}` : offsetClause;

        const sql = `
                ${finalWhereClause}
                ${finalGroupBy ? finalGroupBy : ''}
                ${finalOrderBy ? finalOrderBy : ''} 
                ${finalLimit ? finalLimit : ''}
                ${finalOffset ? finalOffset : ''}
                `.trim();

        const params = this.extractParams(selectedFilters);

        return { sql, params };
    }

    static buildIdSql(baseSql: string, id: string): string {
        if (!id) return ""
        if (baseSql.toLocaleLowerCase().indexOf('from') === -1) return ""
        const [queryWithoutOrder, groupByClause] = this.splitBaseSql(baseSql);
        const sql = `${queryWithoutOrder} ${groupByClause ? groupByClause : ''}`
        return `SELECT ${id} as id ${sql.substring(
            sql.toLocaleLowerCase().indexOf('from'),
        )}`;
    }

    private static splitBaseSql(
        baseSql: string,
    ): [string, string | null, string | null, string | null, string | null] {
        const groupByMatch = baseSql.match(/GROUP BY [\s\S]+?(?=ORDER BY|$)/i);
        const orderByMatch = baseSql.match(/ORDER BY [\s\S]+?(?=LIMIT|$)/i);
        const limitMatch = baseSql.match(/LIMIT [\s\S]+$/i);
        const offsetMatch = baseSql.match(/OFFSET\s+\d+/i);

        const queryWithoutOrder = baseSql
            .replace(/GROUP BY [\s\S]+?(?=ORDER BY|LIMIT|OFFSET|$)/i, '')
            .replace(/ORDER BY [\s\S]+?(?=LIMIT|OFFSET|$)/i, '')
            .replace(/LIMIT\s+\d+/i, '')
            .replace(/OFFSET\s+\d+/i, '')
            .trim();

        const groupByClause = groupByMatch ? groupByMatch[0] : null;
        const orderByClause = orderByMatch ? orderByMatch[0] : null;
        const limitClause = limitMatch ? limitMatch[0] : null;
        const offsetClause = offsetMatch ? offsetMatch[0] : null;

        return [queryWithoutOrder, groupByClause, orderByClause, limitClause, offsetClause];
    }

    private static generateFilters(
        selectedFilters: Sql.Column[],
        filterOptions: Record<string, string>,
    ): string {
        return (selectedFilters || [])
            .map((filter) => this.processFilterCondition(filter, filterOptions))
            .filter((condition) => condition)
            .join(` AND `);
    }

    private static processFilterCondition(
        filter: Sql.Column,
        filterOptions: Record<string, string>,
    ): string {
        const { key, operator, value, logic, nested } = filter;
        const mappedField = filterOptions[key];
        let condition = '';
        const operatorValue = operator || Sql.Operator.equal; // 默认操作符为 =
        const placeholder = value ? `?` : ''; // 如果没有值，则不传
        condition = operatorValue !== Sql.Operator.no_filter && mappedField ? `${mappedField} ${operatorValue} ${placeholder}`:condition;

        // 特殊处理 IN 操作符
        if (operatorValue?.toUpperCase() === Sql.Operator.in || operatorValue?.toUpperCase() === Sql.Operator.not_in) {
            if (!Array.isArray(value) || value.length === 0) {
                throw new YqzException(`Filter value for 'IN' operator must be a non-empty array.`);
            }
            condition = `${mappedField} ${operatorValue} (${placeholder})`;
        }

        if (nested && nested.length > 0) {
            const nestedConditions = nested
                .map((nestedFilter) =>
                    this.processFilterCondition(nestedFilter, filterOptions),
                )
                .filter((cond) => cond)
                .join(` ${logic || 'AND'} `);

            condition = condition?`(${condition} ${nestedConditions})`:`(${nestedConditions})`;
        }

        return condition;
    }

    private static extractParams(filters: Sql.Column[]): any[] {
        const params: any[] = [];
        const traverse = (filter: Sql.Column[]) => {
            for (const f of filter) {
                if(f.value) params.push(f.value);
                if (f.nested && f.nested.length > 0) {
                    traverse(f.nested);
                }
            }
        };
        traverse(filters);
        return params;
    }

    private static appendWhereClause(
        queryWithoutOrder: string,
        additionalFilters: string,
    ): string {
        if (!additionalFilters) {
            return queryWithoutOrder;
        }

        // 定义一个更精确的正则表达式，只匹配 WHERE 子句，且不包括 ORDER BY 和 LIMIT
        const whereRegex = /\bWHERE\b([\s\S]*?)(?=\bORDER BY\b|\bLIMIT\b|$)/i;
        const match = queryWithoutOrder.match(whereRegex);

        if (match) {
            const existingWhere = match[1].trim();

            // 检查 existingWhere 是否为空
            if (existingWhere) {
                // 构建新的 WHERE 子句
                const newWhere = `WHERE (${existingWhere}) AND (${additionalFilters})`;

                // 替换旧的 WHERE 子句
                return queryWithoutOrder.replace(whereRegex, newWhere);
            } else {
                // 如果原有 WHERE 子句后面没有条件，直接添加 additionalFilters
                return queryWithoutOrder.replace(/\bWHERE\b/i, `WHERE (${additionalFilters})`);
            }
        } else {
            // 如果没有 WHERE 子句，直接添加
            return `${queryWithoutOrder} WHERE ${additionalFilters}`;
        }
    }
}