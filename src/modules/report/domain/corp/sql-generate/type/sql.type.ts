import { ObjectId } from "mongodb";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { ObjUtil } from "@src/util/obj.util";

export namespace Sql {

    export class Topic {
        id: string;
        name?: string;
        desc?: string;
        baseSql: string;
        filterOptions: Record<string, string>;
        groupOptions?: Record<string, string>;
        sortOptions?: Record<string, string>;
    }

    export class Template {
        name?: string;
        topics?: Record<string, Topic>;
    }

    export enum ColumnType {
        filter ='filter',// 条件
        group_by = 'group_by',// 分组
        sort = 'sort',// 排序
        limit = 'limit', // 分页
    }

    export enum Operator {
        equal = '=',
        not_equal = '!=',
        greater_than = '>',
        greater_than_or_equal = '>=',
        less_than = '<',
        less_than_or_equal = '<=',
        in = 'IN',
        not_in = 'NOT IN',
        like = 'LIKE',
        not_like = 'NOT LIKE',
        is_null = 'IS NULL',
        is_not_null = 'IS NOT NULL',
        no_filter = 'no_filter',// 特殊占位，表示字段不参与筛选
    }

    export const OperatorMapping = ObjUtil.createMappings(Operator)

    export class Column {
        constructor({key, operator, value, logic, nested}:{key: string, operator?: Operator, value?: string, logic?: 'AND'|'OR', nested?: Column[]}) {
            this.key = key;
            this.operator = operator;
            this.value = value;
            this.logic = logic;
            this.nested = nested;
        }
        key: string;
        operator?: Operator;
        value?: any;
        logic?: 'AND' | 'OR';
        nested?: Column[];
    }

    export class ColumnConfig {
        id?: ObjectId; // 用于映射 MongoDB 的 `_id`
        companyId: string;
        subject: DateReport.Subject;
        topic: string;
        key: string;
        type: ColumnType;
        value: string;
        logic?: 'AND'|'OR';
        nested?: []; // 用于嵌套条件
        operator?: string;
        deletion?: boolean;
        createBy?: string;
        createTime?: Date;
        updateBy?: string;
        updateTime?: Date;
    }
    
}