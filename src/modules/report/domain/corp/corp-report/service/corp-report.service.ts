import { Injectable } from "@nestjs/common";
import { CsvUtil } from "@src/util/csv.util";
import { DevTimeout, JwtUser<PERSON>w, <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import _ from "lodash";
import { CompanyDecorationDao } from "../../../bgw/common/company-decoration.dao";
import { DepartmentDao } from "../../../bgw/department/dao/department.dao";
import { DepartmentService } from "../../../bgw/department/service/department.service";
import { CorpReport } from "../dto/corp-report.dto";
import { CorpReportDevice } from "../dto/device.dto";
import { CorpReportPatrol } from "../dto/patrol.dto";
import { CorpReportProject } from "../dto/project.dto";
import { CorpReportSecurity } from "../dto/security.dto";
import { CorpReportSignIn } from "../dto/sign-in.dto";
import { CorpReportTidiness } from "../dto/tidiness.dto";
import { CorpReportBasicsService } from "./basics/corp-report-basics.service";
import { CorpReportDeviceService } from "./basics/corp-report-device.service";
import { CorpReportPatrolService } from "./basics/corp-report-patrol.service";
import { CorpReportProjectService } from "./basics/corp-report-project.service";
import { CorpReportSecurityService } from "./basics/corp-report-security.service";
import { CorpReportSignInService } from "./basics/corp-report-signin.service";
import { CorpReportTidinessService } from "./basics/corp-report-tidiness.service";

@Injectable()
export class CorpReportService {
  private readonly logger = new MyLogger(CorpReportService.name)
  constructor(
    private readonly departmentService: DepartmentService,
    private readonly corpReportDeviceService: CorpReportDeviceService,
    private readonly corpReportTidinessService: CorpReportTidinessService,
    private readonly corpReportSecurityService: CorpReportSecurityService,
    private readonly corpReportSignInService: CorpReportSignInService,
    private readonly corpReportPatrolService: CorpReportPatrolService,
    private readonly corpReportProjectService: CorpReportProjectService,
    private readonly companyDecorationDao: CompanyDecorationDao,
    private readonly departmentDao: DepartmentDao,
    private readonly corpReportBasicsService: CorpReportBasicsService,
  ) { }

  @DevTimeout(100)
  async test() {
    // this.departmentService.getDirectChildDepts({ departmentId: '12547' }).then(res =>
    //   console.log(res))
    // this.departmentService.getRootDept('65').then(res =>
    //   console.log(res))

    // const entity = await this.companyDecorationDao.search('65');
    // console.log(entity.deliverKanbanSet)
    // console.log(JSON.parse(entity.deliverKanbanSet)?.deptDateType)
    
  }

  /**
   * 校验deptId，如果没有则取当前登陆token的company对应的dept
   * @param deptId 
   * @param user 
   * @returns 
   */
  private async getDeptId(user: JwtUserGw, deptId?: string) {
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      deptId = department.departmentId;
    }
    return deptId;
  }

  /**
   * 集团看板-设备概况
   * @param params 
   * @returns 
   */
  async corpReportDevice(params: CorpReport.StatisticsReq & { companyIds?: string[], deviceOfflineDay?: number }, user: JwtUserGw): Promise<CorpReportDevice.DeviceStatisticsRes> {
    params.deptId = await this.getDeptId(user, params.deptId);
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.deviceOfflineDay = corpReportSet.deviceOfflineDay;
    //设备不与公司强绑定，所以根据dept需查询公司list
    params.companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportDeviceService.getDeviceStatistics(params);
    return result;
  }

  /**
   * 集团看板-设备概况详情
   * @param params 
   * @returns 
   */
  async corpReportDeviceDetail(params: CorpReportDevice.DeviceDetailReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.deviceOfflineDay = corpReportSet.deviceOfflineDay;
    //设备不与公司强绑定，所以根据dept需查询公司list
    params.companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') return await this.corpReportDeviceService.corpReportDeviceDetailCsv(params);
    const result = await this.corpReportDeviceService.corpReportDeviceDetail(params);
    return result;
  }

  /**
   * 集团看板-设备概况详情筛选
   * @param params 
   * @returns 
   */
  async corpReportDeviceDetailOptions(params: CorpReportDevice.DeviceDetailReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.deviceOfflineDay = corpReportSet.deviceOfflineDay;
    //设备不与公司强绑定，所以根据dept需查询公司list
    params.companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportDeviceService.corpReportDeviceDetailOptions(params);
    return result;
  }

  /**
   * 集团看板-工地概况
   * @param params 
   * @returns 
   */
  async corpReportProject(params: CorpReport.StatisticsReq & { projectInactiveDay?: number }, user: JwtUserGw): Promise<CorpReportProject.ProjectStatisticsRes> {
    params.deptId = await this.getDeptId(user, params.deptId);
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.projectInactiveDay = corpReportSet.projectInactiveDay;
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportProjectService.getProjectStatistics(params);
    return result;
  }

  /**
   * 集团看板-工地概况详情
   * @param params 
   * @returns 
   */
  async corpReportProjectDetail(params: CorpReportProject.ProjectDetailReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.projectInactiveDay = corpReportSet.projectInactiveDay;
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') return await this.corpReportProjectService.corpReportProjectDetailCsv(params);
    const result = await this.corpReportProjectService.corpReportProjectDetail(params);
    return result;
  }

  /**
   * 集团看板-工地概况详情筛选
   * @param params 
   * @returns 
   */
  async corpReportProjectDetailOptions(params: CorpReportProject.ProjectDetailReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.projectInactiveDay = corpReportSet.projectInactiveDay;
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportProjectService.corpReportProjectDetailOptions(params);
    return result;
  }

  /**
   * 集团看板-整洁度
   * @param params 
   * @returns 
   */
  async corpReportTidiness(params: CorpReport.StatisticsReq, user: JwtUserGw): Promise<CorpReportTidiness.TidinessStatisticsRes> {
    params.deptId = await this.getDeptId(user, params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportTidinessService.corpReportTidiness(params);
    return result;
  }

  /**
   * 集团看板-整洁度详情
   * @param params 
   * @returns 
   */
  async corpReportTidinessDetail(params: CorpReportTidiness.TidinessDetailReq, user: JwtUserGw) {
    // params.deptId = await this.getDeptId(user, params.deptId);
    // //查询报表的部门数据类型(根据用户所登陆的公司)
    // const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    // params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    // if (params.format == 'csv') return await this.corpReportTidinessService.corpReportTidinessDetailCsv(params);
    // const result = await this.corpReportTidinessService.corpReportTidinessDetail(params);
    // return result;
  }

  /**
   * 集团看板-整洁度详情筛选
   * @param params 
   * @returns 
   */
  async corpReportTidinessDetailOptions(params: CorpReportTidiness.TidinessDetailReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportTidinessService.corpReportTidinessDetailOptions(params);
    return result;
  }

  /**
   * 集团看板-文明施工
   * @param params 
   * @returns 
   */
  async corpReportSecurity(params: CorpReport.StatisticsReq, user: JwtUserGw): Promise<CorpReportSecurity.SecurityStatisticsRes> {
    params.deptId = await this.getDeptId(user, params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSecurityService.corpReportSecurity(params);
    return result;
  }

  /**
  * 集团看板-文明施工详情
  * @param params 
  * @returns 
  */
  async corpReportSecurityDetail(params: CorpReportSecurity.SecurityDetailReq, user: JwtUserGw) {
    // params.deptId = await this.getDeptId(user, params.deptId);
    // //查询报表的部门数据类型(根据用户所登陆的公司)
    // const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    // params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    // if (params.format == 'csv') return await this.corpReportSecurityService.corpReportSecurityDetailCsv(params);
    // const result = await this.corpReportSecurityService.corpReportSecurityDetail(params);
    // return result;
  }

  /**
  * 集团看板-文明施工详情筛选
  * @param params 
  * @returns 
  */
  async corpReportSecurityDetailOptions(params: CorpReportSecurity.SecurityDetailReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSecurityService.corpReportSecurityDetailOptions(params);
    return result;
  }


  /**
   * 集团看板服务签到
   * @param params 
   * @returns 
   */
  async corpReportSignIn(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSignInService.getSignStatistics(params)
    return result
  }

  /**
   * 签到工地列表
   * @param params 
   * @returns 
   */
  async corpReportSignProjectList(params: CorpReportSignIn.SignInProjectListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportSignInService.corpReportSignProjectList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['工地地址'] = e.projectAddress
        map['签到人数'] = e.signInPersonNum
        map['签到次数'] = e.signInNum
        map['工地负责人'] = e.projectManagerName
        return map
      }))
    }
    const result = await this.corpReportSignInService.corpReportSignProjectList(params)
    return result
  }

  /**
   * 签到人员列表
   * @param params 
   * @returns 
   */
  async corpReportSignPersonList(params: CorpReportSignIn.SignInPersonListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportSignInService.corpReportSignPersonList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['用户昵称'] = e.nickName
        map['签到工地数'] = e.signInProjectNum
        map['签到次数'] = e.signInNum
        return map
      }))
    }
    const result = await this.corpReportSignInService.corpReportSignPersonList(params)
    return result
  }

  /**
   *签到次数列表
   * @param params 
   * @returns 
   */
  async corpReportSignTotalList(params: CorpReportSignIn.SignInTotalListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportSignInService.corpReportSignTotalList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['用户昵称'] = e.nickName
        map['签到工地'] = e.projectAddress
        map['人脸图片'] = e.faceOssUrl
        map['签到图片'] = e.signInPhoto.mediaUri
        map['签到时间'] = e.signInTime
        map['工地负责人'] = e.projectManagerName
        return map
      }))
    }
    const result = await this.corpReportSignInService.corpReportSignTotalList(params)
    return result
  }

  /**
   * 无人签到工地列表
   * @param params 
   * @returns 
   */
  async corpReportSignInNoBodyProjectList(params: CorpReportSignIn.SignInNoBodyProjectListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportSignInService.corpReportSignInNoBodyProjectList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['工地地址'] = e.projectAddress
        map['无人签到天数'] = e.signInNoBodyProjectNum
        map['工地负责人'] = e.projectManagerName
        return map
      }))
    }
    const result = await this.corpReportSignInService.corpReportSignInNoBodyProjectList(params)

    return result
  }


  async corpReportSignInProjectListOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSignInService.corpReportSignInProjectListOptions(params)
    return result
  }


  async corpReportSignInPersonListOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSignInService.corpReportSignInPersonListOptions(params)
    return result
  }


  async corpReportSignInTotalListOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSignInService.corpReportSignInTotalListOptions(params)
    return result
  }


  async corpReportSignInNoBodyProjectListOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportSignInService.corpReportSignInNoBodyProjectListOptions(params)
    return result
  }

  async corpReportSearchOptions(params: CorpReport.SearchOptionsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    let result = [];
    //查询配置信息
    const corpReportSet = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
    params.deviceOfflineDay = corpReportSet.deviceOfflineDay;
    params.projectInactiveDay = corpReportSet.projectInactiveDay;
    params.signInNoBodyProjectDay = corpReportSet.signInNoBodyProjectDay;
    //设备概况地区分布统计
    if (CorpReportDevice.ModelList.includes(params.options)) {
      result = await this.corpReportDeviceService.corpReportDeviceDistribute(params);
    }
    //工地概况地区分布统计
    if (CorpReportProject.ModelList.includes(params.options)) {
      result = await this.corpReportProjectService.corpReportProjectDistribute(params);
    }
    //整洁度地区分布统计
    if (CorpReportTidiness.ModelList.includes(params.options)) {
      result = await this.corpReportTidinessService.corpReportTidinessDistribute(params);
    }
    //文明施工地区分布统计
    if (CorpReportSecurity.ModelList.includes(params.options)) {
      result = await this.corpReportSecurityService.corpReportSecurityDistribute(params);
    }
    //服务签到地区分布统计
    if (CorpReportSignIn.ModelList.includes(params.options)) {
      result = await this.corpReportSignInService.corpReportSignInDistribute(params)
    }
    //工地巡检地区分布统计
    if (CorpReportPatrol.ModelList.includes(params.options)) {
      result = await this.corpReportPatrolService.corpReportPatrolDistribute(params)
    }
    return result
  }

  /**
   * 集团看板工地巡检
   * @param params 
   * @returns 
   */
  async corpReportPatrol(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    /** 如果未传递 deptId 则以当前用户所在公司的总部门为 deptId */
    if (!deptId) { 
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportPatrolService.getPatrolStatistics(params)
    return result
  }

  /**
   * 智慧工地列表
   * @param dayType
   * @param etlDateList
   * */
  async searchPatrolProjectList(params: CorpReportPatrol.PatrolWisdomProjectListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportPatrolService.searchPatrolProjectList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['工地地址'] = e.projectAddress
        map['巡检人数'] = e.patrolPersonNum
        map['巡检次数'] = e.patrolNum
        map['发现问题数'] = e.problemNum
        map['问题处理率'] = e.problemDealRate
        map['问题平均处理时长'] = e.problemAvgDealTime
        map['工地负责人'] = e.projectManagerName
        return map
      }))
    }
    const result = await this.corpReportPatrolService.searchPatrolProjectList(params)
    return result
  }

  /**
   * 巡检总次数
   * @param dayType
   * @param etlDateList
   * */
  async searchPatrolTotalList(params: CorpReportPatrol.PatrolTotalListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportPatrolService.searchPatrolTotalList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['用户昵称'] = e.nickName
        map['工地地址'] = e.projectAddress
        map['巡检时间'] = e.patrolTime
        map['工地负责人'] = e.projectManagerName
        return map
      }))
    }
    const result = await this.corpReportPatrolService.searchPatrolTotalList(params)
    return result
  }

  /**
   * 发现问题数
   * @param dayType
   * @param etlDateList
   * */
  async searchPatrolProblemList(params: CorpReportPatrol.PatrolProblemNumListReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    if (params.format == 'csv') {
      // 返回csv格式
      delete params.pageNo;
      delete params.pageSize;
      const { items } = await this.corpReportPatrolService.searchPatrolProblemList(params)
      return CsvUtil.convert2Csv(items.map(e => {
        let map = {}
        map['所属公司'] = e.linkCompanyName
        map['问题图片'] = e.problemPhoto.filter(res => res.mediaUri).map(res => res.mediaUri).join(',')
        map['问题状态'] = e.problemStatusName
        map['问题分类'] = e.problemTypeName
        map['问题描述'] = e.problemDescription ? e.problemDescription : ""
        map['工地地址'] = e.projectAddress
        map['报告人'] = e.reportName
        map['报告日期'] = e.reportDate ? e.reportDate : ""
        map['整改日期'] = e.solvedDate ? e.solvedDate : ""
        map['整改图片'] = e.solvedPhoto.filter(res => res.mediaUri).map(res => res.mediaUri).join(',')
        map['整改描述'] = e.solvedDescription ? e.solvedDescription : ""
        map['工地负责人'] = e.projectManagerName
        return map
      }))
    }
    const result = await this.corpReportPatrolService.searchPatrolProblemList(params)
    return result
  }

  async corpReportPatrolWisdomProjectListOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportPatrolService.corpReportPatrolWisdomProjectListOptions(params)
    return result
  }

  async corpReportPatrolTotalOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportPatrolService.corpReportPatrolTotalOptions(params)
    return result
  }

  async corpReportPatrolProblemNumListOptions(params: CorpReport.StatisticsReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    //查询报表的部门数据类型(根据用户所登陆的公司)
    const entity = await this.companyDecorationDao.search(user.targetCompany.companyId);
    params.deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    const result = await this.corpReportPatrolService.corpReportPatrolProblemNumListOptions(params)
    return result
  }

  /**
   * 看板配置查询
   * @param params 
   * @param user 
   * @returns 
   */
  async corpReportSetSearch(params: CorpReport.deliverKanbanSetReq, user: JwtUserGw) {
    params.deptId = await this.getDeptId(user, params.deptId);
    return await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
  }

  async corpReportSetUpdate(params: CorpReport.deliverKanbanSetReq, user: JwtUserGw) {
    let { deptId } = params;
    //未传deptId即，当前人token所在的deptId
    if (!deptId) {
      const department = await this.departmentService.getRootDept(user.targetCompany.companyId);
      params.deptId = department.departmentId;
    }
    // const departmentEntity = await this.departmentDao.get(params.deptId)
    const parents = await this.departmentDao.searchDeptInfoList({ deptId: params.deptId });//(不校验删除)
    const departmentEntity = !_.isEmpty(parents) ? parents[0] : null;
    if (!departmentEntity) throw new YqzException('部门不存在');
    const entity = await this.companyDecorationDao.search(departmentEntity.companyId)
    //暂不支持对部门数据类型进行修改
    const deptDateType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
    params.deviceOfflineDay = params.deviceOfflineDay >= 0 ? params.deviceOfflineDay : 7
    params.projectInactiveDay = params.projectInactiveDay >= 0 ? params.projectInactiveDay : 7
    params.signInNoBodyProjectDay = params.signInNoBodyProjectDay >= 0 ? params.signInNoBodyProjectDay : 7
    let deliverKanbanSet = {
      deviceOfflineDay: params.deviceOfflineDay,
      projectInactiveDay: params.projectInactiveDay,
      signInNoBodyProjectDay: params.signInNoBodyProjectDay,
      deptDateType
    }
    entity.deliverKanbanSet = JSON.stringify(deliverKanbanSet)
    await this.companyDecorationDao.update(entity)
  }

}