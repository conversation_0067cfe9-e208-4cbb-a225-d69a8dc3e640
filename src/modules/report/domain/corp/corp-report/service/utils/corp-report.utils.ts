import * as DateFns from "date-fns";

/**
 * 根据传入时间、周期获取时间范围list
 * @param date 时间
 * @param dayType 单位
 * @param time 次数
 * @param format 时间格式
 * @returns 
 */
function getTimeFrameList(date: string, dayType: 'day' | 'week' | 'month' | 'year', time: number, format?: string): { startTime: string, endTime: string }[] {
    let result: { startTime: string, endTime: string }[] = [];

    /** date 为今天也就是 endTime */
    let myDate = new Date(date);
    let endTime = DateFns.format(myDate, format ? format : "yyyy-MM-dd");

    /** 根据 dayType 获取时间 time次 范围list 每次push上一个时间段 */
    for (let i = 1; i <= time; i++) {
        let startTime = '';
        switch (dayType) {
            case 'day': startTime = DateFns.format(DateFns.sub(myDate, { days: i }), format ? format : "yyyy-MM-dd"); break;
            case 'week': startTime = DateFns.format(DateFns.sub(myDate, { weeks: i }), format ? format : "yyyy-MM-dd"); break;
            case 'month': startTime = DateFns.format(DateFns.sub(myDate, { months: i }), format ? format : "yyyy-MM-dd"); break;
            case 'year': startTime = DateFns.format(DateFns.sub(myDate, { years: i }), format ? format : "yyyy-MM-dd"); break;
        }
        result.push({ startTime: startTime, endTime: endTime });
        endTime = startTime;
    }
    return result;
}

export const CorpReportUtils = {
    getTimeFrameList
}