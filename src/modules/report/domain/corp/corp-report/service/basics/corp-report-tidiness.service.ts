import { Injectable } from "@nestjs/common";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";
import { ProjectDetectionDao } from "@src/modules/report/domain/camera/project-detection/dao/detection.dao";
import { TimeUtil } from "@src/util";
import { CsvUtil } from "@src/util/csv.util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import { CorpReport } from "../../dto/corp-report.dto";
import { CorpReportTidiness } from "../../dto/tidiness.dto";
import { CorpReportBasicsService } from "./corp-report-basics.service";
import * as DateFns from "date-fns";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";

@Injectable()
export class CorpReportTidinessService {
    private readonly logger = new MyLogger(CorpReportTidinessService.name)
    constructor(
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly projectDetectionDao: ProjectDetectionDao,
        private readonly departmentService: DepartmentService,
        private readonly projectService: ProjectService
    ) { }

    @DevTimeout(100)
    test() {
        // this.corpReportTidiness({ deptId: '12551', date: '2022-12-17', dayType: 'week' }).then(res =>
        //     console.log(JSON.stringify(res))
        // )
    }

    /**
     * 整洁度
     * @param params 
     * @returns 
     */
    async corpReportTidiness(params: CorpReport.StatisticsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 7 });
        const tidinessDataList = await this.getTidinessDateList(dayType, etlDateList);
        const result = new CorpReportTidiness.TidinessStatisticsRes();
        const groups = new CorpReportTidiness.TidinessType().getGroups();
        //生成多线图数据结构
        const tidinessTable = await this.corpReportBasicsService.getMultipleGraphs(tidinessDataList, 'tidinessTable', groups, dayType);
        //生成比例图数据结构
        const tidinessTotal = await this.corpReportBasicsService.getProportionChart(tidinessDataList, 'tidinessTotal', groups, dayType, false);
        result.tidinessTotal = tidinessTotal;
        result.tidinessTable = tidinessTable;
        return result;
    }

    /**
     * 整洁度详情
     * @param params 
     */
    async corpReportTidinessDetail(params: CorpReportTidiness.TidinessDetailReq) {
        // const { deptId, deptDateType = 'real_time', date, dayType } = params;
        // const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 1 });
        // const tidinessDataList = await this.getTidinessDateList(dayType, etlDateList);
        // params.tidinessType = params.tidinessType ? params.tidinessType : 'tidinessTotal';
        // params.tidinessDetectionIds = tidinessDataList[params.tidinessType][tidinessDataList[params.tidinessType].length - 1];
        // if (_.isEmpty(params.tidinessDetectionIds)) return { total: 0, items: [] };
        // //筛选 工地地址
        // if (params.projectAddress) {
        //     const projectIds = tidinessDataList.projectIds[tidinessDataList.projectIds.length - 1];
        //     params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: projectIds, projectAddress: params.projectAddress });
        //     if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        // }
        // params.sortBy = [
        //     { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
        //     { field: "eventTime", order: 'descending' },//监测时间
        // ]
        // if (params.eventTimeSort) {
        //     params.sortBy = [
        //         { field: "eventTime", order: 'ASC' == params.eventTimeSort ? 'ascending' : 'descending' },//监测时间
        //         { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
        //     ]
        // }
        // const result = await this.projectDetectionDao.searchTidinessList({ ...params, idList: params.tidinessDetectionIds });
        // //获取工地地址
        // const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds: result.items.map(res => res.projectId) });
        // result.items.map(res => {
        //     res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
        //     res.tidinessType = new CorpReportTidiness.TidinessType().getValueByCode(res.tidinessType);
        //     res.photo = {
        //         mediaId: "",
        //         mediaType: "1",
        //         mediaUri: String(res.photo),
        //         mediaResourceUri: String(res.photo),
        //         creationDate: "",
        //         address: ""
        //     }
        //     delete res.linkCompanyCreateTime;
        // })
        // return result;
    }

    /**
     * 整洁度详情csv格式（导出）
     * @param params 
     */
    async corpReportTidinessDetailCsv(params: CorpReportTidiness.TidinessDetailReq) {
        // delete params.pageNo;
        // delete params.pageSize;
        // const res = await this.corpReportTidinessDetail(params);
        // return CsvUtil.convert2Csv(res.items.map(e => {
        //     return {
        //         '所属公司': e.linkCompanyName ? e.linkCompanyName : '',
        //         '工地地址': e.projectAddress ? e.projectAddress : '',
        //         '工地整洁度': new CorpReportTidiness.TidinessType()[e.tidinessType].name,
        //         '图片': e.photo.mediaUri ? e.photo.mediaUri.replace(process.env.OSS_CAMERA_DEFAULT_URL, process.env.OSS_CAMERA_CUSTOM_URL) : '',
        //         '监测时间': e.eventTime ? e.eventTime : '',
        //         '工地负责人': e.projectManagerName ? e.projectManagerName : '',
        //     }
        // }));
    }

    /**
     * 整洁度详情筛选
     * @param params 
     */
    async corpReportTidinessDetailOptions(params: CorpReportTidiness.TidinessDetailReq) {
        // const { deptId, deptDateType = 'real_time', date, dayType } = params;
        // const result = new CorpReportTidiness.TidinessDetailOptionsRes();
        // const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 1 });
        // const tidinessDataList = await this.getTidinessDateList(dayType, etlDateList);
        // params.tidinessType = params.tidinessType ? params.tidinessType : 'tidinessTotal';
        // const tidinessDetectionIds = tidinessDataList[params.tidinessType][tidinessDataList[params.tidinessType].length - 1];
        // result.tidinessType = new CorpReportTidiness.TidinessType().getEnum();
        // if (_.isEmpty(tidinessDetectionIds)) return result;
        // const tidinessListRes = await this.projectDetectionDao.searchTidinessList({ idList: tidinessDetectionIds });
        // tidinessListRes.items.map(res => {
        //     let linkCompany = { id: res.linkCompanyId, name: res.linkCompanyName };
        //     let projectManager = { id: res.projectManagerId, name: res.projectManagerName };
        //     if (!_.some(result.linkCompany, linkCompany) && linkCompany.id && linkCompany.name) result.linkCompany.push(linkCompany);
        //     if (!_.some(result.projectManager, projectManager) && projectManager.id && projectManager.name) result.projectManager.push(projectManager);
        // })
        // return result;
    }

    /**
     * 整洁度地区分布统计
     * @param params 
     * @returns 
     */
    async corpReportTidinessDistribute(params: CorpReport.SearchOptionsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, options } = params;
        const dept = await this.corpReportBasicsService.getDirectChildDepts({ departmentId: deptId, deptDateType, date, dayType });
        const deptList = [];
        for (let item of dept) {
            const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId: item.departmentId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 1 });
            const tidinessDataList = await this.getTidinessDateList(dayType, etlDateList);
            const total = tidinessDataList[options][tidinessDataList[options].length - 1].length;
            const badNum = tidinessDataList.bad[tidinessDataList.bad.length - 1].length;
            deptList.push({
                deptId: item.departmentId,
                deptName: item.departmentName,
                total: total,
                rate: total != 0 ? Math.round(_.divide(badNum, total) * 100) : '--'
            });
        }
        return _.orderBy(deptList, ['rate', 'total'], ['desc', 'desc']);
    }

    /**
     * 整洁度数据列表
     * @param dayType 
     * @param etlDateList 
     * @returns 
     */
    private async getTidinessDateList(dayType: 'day' | 'week' | 'month', etlDateList: { category: { startDate: string, endDate: string }, data: any }[]) {
        const projectIds = [];
        const category = [];
        const tidinessTotal = [];
        const good = [];
        const middle = [];
        const bad = [];
        const unknown = [];
        for (const etlDate of etlDateList) {
            const startTime = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'yyyy-MM-dd');
            const endTime = TimeUtil.formatDate(new Date(etlDate.category.endDate), 'yyyy-MM-dd');
            let date = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'MM.dd');
            if (!(dayType == 'day')) date = date + "-" + TimeUtil.formatDate(DateFns.subDays(new Date(etlDate.category.endDate), 1), 'MM.dd');
            category.push(date);
            const projectIdList = [];
            const totalIdList = [];
            const goodIdList = [];
            const middleIdList = [];
            const badIdList = [];
            const unknownIdList = [];
            if (!_.isEmpty(etlDate.data.projectIdList)) {
                const detectionList = await this.projectDetectionDao.searchTidiness({ projectIds: etlDate.data.projectIdList, startTime, endTime });
                detectionList.items.map(res => {
                    projectIdList.push(res.projectId);
                    totalIdList.push(res.projectTidinessDetectionId);
                    if (res.tidinessType == 1) goodIdList.push(res.projectTidinessDetectionId);
                    if (res.tidinessType == 2) middleIdList.push(res.projectTidinessDetectionId);
                    if (res.tidinessType == 3) badIdList.push(res.projectTidinessDetectionId);
                    if (res.tidinessType == 4) unknownIdList.push(res.projectTidinessDetectionId);
                })
            }
            projectIds.push(projectIdList);
            tidinessTotal.push(totalIdList);
            good.push(goodIdList);
            middle.push(middleIdList);
            bad.push(badIdList);
            unknown.push(unknownIdList);
        }
        return { category, projectIds, tidinessTotal, good, middle, bad, unknown };
    }

}