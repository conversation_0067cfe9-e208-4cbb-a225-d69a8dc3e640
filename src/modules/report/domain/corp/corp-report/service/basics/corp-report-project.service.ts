import { Injectable } from "@nestjs/common";
import { TimeUtil } from "@src/util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import { CorpReport } from "../../dto/corp-report.dto";
import { CorpReportProject } from "../../dto/project.dto";
import { CorpReportBasicsService } from "./corp-report-basics.service";
import { ProjectDao } from "@src/modules/report/domain/bgw/project/dao/project.dao";
import { NodeOverdueEventDao } from "@src/modules/report/domain/bgw/node-overdue-event/dao/node-overdue-event.dao";
import { ProjectInactiveRecordDao } from "@src/modules/report/domain/camera/project-inactive-record/dao/project-inactive-record.dao";
import { CsvUtil } from "@src/util/csv.util";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";
import * as DateFns from "date-fns";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";

@Injectable()
export class CorpReportProjectService {
    private readonly logger = new MyLogger(CorpReportProjectService.name)
    constructor(
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly nodeOverdueEventDao: NodeOverdueEventDao,
        private readonly projectInactiveRecordDao: ProjectInactiveRecordDao,
        private readonly projectDao: ProjectDao,
        private readonly departmentService: DepartmentService,
        private readonly projectService: ProjectService
    ) { }

    @DevTimeout(100)
    test() {
        // this.corpReportProjectDetail({ deptId: '12551', date: '2022-12-16', dayType: 'day', pageNo: 1, pageSize: 5 }).then(res =>
        //     console.log(JSON.stringify(res))
        // )
        // this.corpReportDeviceDetail({
        //     deptId: '12551',
        //     date: '2022-12-17',
        //     dayType: 'day',
        //     searchModel: 'deviceInstall'
        // }).then(res =>
        //     console.log(JSON.stringify(res))
        // )
    }

    /**
     * 工地概览
     * @param params 
     * @returns 
     */
    async getProjectStatistics(params: CorpReport.StatisticsReq & { projectInactiveDay?: number }) {
        const { deptId, deptDateType = 'real_time', date, dayType, projectInactiveDay } = params;
        const models = _.union(CorpReportProject.ModelList, ['wisdomProject', 'commonProject']);
        const projectDataList = await this.getProjectDataListByModel({ models, deptId, deptDateType, date, dayType, projectInactiveDay, times: 7 });//(7个周期)
        const result = new CorpReportProject.ProjectStatisticsRes();
        for (const modelName of CorpReportProject.ModelList) {
            if (modelName == 'projectTotal') {
                //生成比例图数据结构
                const groups = new CorpReportProject.ProjectType().getGroups();
                result[modelName] = await this.corpReportBasicsService.getProportionChart(projectDataList, 'projectTotal', groups, dayType, false);
            } else {
                //根据模块生成折线图数据结构
                result[modelName] = await this.corpReportBasicsService.getBrokenLineDiagram(projectDataList, modelName, dayType, true);
            }
        }
        result.projectInactiveDay = projectInactiveDay;
        return result;
    }

    /**
     * 工地列表
     * @param params 
     * @returns 
     */
    async corpReportProjectDetail(params: CorpReportProject.ProjectDetailReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, name = 'projectTotal', projectType, isWork, isOverdue, projectInactiveDay, projectAddress } = params;
        let models = [name, 'wisdomProject'];
        if (projectType) models = _.union(models, [projectType])
        if (isWork) models = _.union(models, ['projectNormal', 'projectInactive']);
        if (isOverdue) models = _.union(models, ['projectOverdue', 'projectNoOverdue']);
        const projectDataList = await this.getProjectDataListByModel({ models, deptId, deptDateType, date, dayType, projectInactiveDay, times: 1 });//当前周期
        let projectList = projectDataList[name][projectDataList[name].length - 1];
        if (projectType) projectList = projectDataList[projectType][projectDataList[projectType].length - 1];
        if (isWork) {
            if ('Y' == isWork) {
                let projectNormal = projectDataList['projectNormal'][projectDataList['projectNormal'].length - 1];
                projectList = _.intersection(projectList, projectNormal);
            }
            if ('N' == isWork) {
                let projectInactive = projectDataList['projectInactive'][projectDataList['projectInactive'].length - 1];
                projectList = _.intersection(projectList, projectInactive);
            }
        }
        if (isOverdue) {
            if ('Y' == isOverdue) {
                let projectOverdue = projectDataList['projectOverdue'][projectDataList['projectOverdue'].length - 1];
                projectList = _.intersection(projectList, projectOverdue);
            }
            if ('N' == isOverdue) {
                let projectNoOverdue = projectDataList['projectNoOverdue'][projectDataList['projectNoOverdue'].length - 1];
                projectList = _.intersection(projectList, projectNoOverdue);
            }
        }
        //筛选 工地地址
        if (projectAddress && !_.isEmpty(projectList)) {
            projectList = await this.projectService.getProjectIdsByAddress({ projectIds: projectList, projectAddress });
        }
        const aiProjectIdList = projectDataList['wisdomProject'][projectDataList['wisdomProject'].length - 1];//智慧工地
        if (_.isEmpty(projectList)) return { total: 0, items: [] };
        let time = projectDataList.dateRange[projectDataList.dateRange.length - 1];
        //查询工地列表
        params.startTime = time.startTime;
        params.endTime = time.endTime;
        params.projectIds = projectList;
        params.sortBy = [
            { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
            { field: "projectCreateTime", order: 'descending' },//工地创建时间
            { field: "projectId", order: 'descending' },//工地id
        ];
        if (params.overdueTimeSort) {
            params.sortBy = [
                { field: "overdueTime", order: 'ASC' == params.overdueTimeSort ? 'ascending' : 'descending' },//逾期时间
                { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
                { field: "projectCreateTime", order: 'descending' },//工地创建时间
                { field: "projectId", order: 'descending' },//工地id
            ];
        }
        if (params.lastWorkTimeSort) {
            params.sortBy = [
                { field: "lastWorkTime", order: 'ASC' == params.lastWorkTimeSort ? 'ascending' : 'descending' },//最后施工时间
                { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
                { field: "projectCreateTime", order: 'descending' },//工地创建时间
                { field: "projectId", order: 'descending' },//工地id
            ];
        }
        if (params.noWorkDaySort) {
            params.sortBy = [
                { field: "noWorkDay", order: 'ASC' == params.noWorkDaySort ? 'ascending' : 'descending' },//无人施工天数
                { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
                { field: "projectCreateTime", order: 'descending' },//工地创建时间
                { field: "projectId", order: 'descending' },//工地id
            ];
        }
        const result = await this.projectDao.searchProjectList(params);
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({projectIds:result.items.map(res => res.projectId)});
        result.items.map(res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            res.overdueTime && Number(res.overdueTime) > 0 ? res.isOverdue = 'Y' : res.isOverdue = 'N';
            res.projectType = 'commonProject';
            if (aiProjectIdList.includes(res.projectId)) {
                res.projectType = 'wisdomProject';
                res.isWork = res.noWorkDay && Number(res.noWorkDay) > 0 ? 'N' : 'Y';
                res.lastWorkTime = res.lastWorkTime ? res.lastWorkTime : TimeUtil.formatDate(TimeUtil.intervalDay(new Date(time.endTime), -1), 'yyyy-MM-dd');
            }
            res.isWork = res.isWork ? res.isWork : '--';
            res.noWorkDay = res.noWorkDay && Number(res.noWorkDay) > 0 ? res.noWorkDay : '--';
            res.lastWorkTime = res.lastWorkTime ? res.lastWorkTime : '--';
            delete res.linkCompanyCreateTime;
        })
        return result;
    }

    /**
     * 工地列表csv格式（导出）
     * @param params 
     * @returns 
     */
    async corpReportProjectDetailCsv(params: CorpReportProject.ProjectDetailReq) {
        delete params.pageNo;
        delete params.pageSize;
        const res = await this.corpReportProjectDetail(params);
        return CsvUtil.convert2Csv(res.items.map(e => {
            return {
                '所属公司': e.linkCompanyName ? e.linkCompanyName : '',
                '工地地址': e.projectAddress ? e.projectAddress : '',
                '创建时间': e.projectCreateTime ? e.projectCreateTime : '',
                '工地类型': new CorpReportProject.ProjectType()[e.projectType].name,
                '施工状态': e.isWork == '--' ? '' : e.isWork == 'N' ? '无人施工' : '正常施工',
                '最后施工日期': e.lastWorkTime == '--' ? '' : e.lastWorkTime,
                '无人施工天数': e.noWorkDay == '--' ? '' : e.noWorkDay,
                '逾期状态': e.isOverdue == 'N' ? '正常' : '逾期',
                '逾期时间': e.overdueTime ? e.overdueTime : '',
                '工地负责人': e.projectManagerName ? e.projectManagerName : '',
            }
        }));
    }

    /**
     * 工地列表筛选
     * @param params 
     * @returns 
     */
    async corpReportProjectDetailOptions(params: CorpReportProject.ProjectDetailReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, name = 'projectTotal', projectInactiveDay } = params;
        const result = new CorpReportProject.ProjectDetailOptionsRes();
        const projectDataList = await this.getProjectDataListByModel({ models: [name], deptId, deptDateType, date, dayType, projectInactiveDay, times: 1 });//当前周期
        const projectIds = projectDataList[name][projectDataList[name].length - 1];
        result.projectType = new CorpReportProject.ProjectType().getEnum();
        result.isWork = [{ name: '正常施工', value: 'Y' }, { name: '无人施工', value: 'N' }];
        result.isOverdue = [{ name: '正常', value: 'N' }, { name: '逾期', value: 'Y' }];
        if (_.isEmpty(projectIds)) return result;
        const time = projectDataList.dateRange[projectDataList.dateRange.length - 1];
        const startTime = time.startTime;
        const endTime = time.endTime;
        const projectListRes = await this.projectDao.searchProjectList({ projectIds, startTime, endTime });
        projectListRes.items.map(async res => {
            const linkCompany = { id: res.linkCompanyId, name: res.linkCompanyName };
            const projectManager = { id: res.projectManagerId, name: res.projectManagerName };
            if (!_.some(result.linkCompany, linkCompany) && linkCompany.id && linkCompany.name) result.linkCompany.push(linkCompany);
            if (!_.some(result.projectManager, projectManager) && projectManager.id && projectManager.name) result.projectManager.push(projectManager);
        })
        return result;
    }

    /**
     * 工地概况地区分布统计
     * @param params 
     * @returns 
     */
    async corpReportProjectDistribute(params: CorpReport.SearchOptionsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, projectInactiveDay } = params;
        const dept = await this.corpReportBasicsService.getDirectChildDepts({ departmentId: deptId, deptDateType, date, dayType });
        const deptList = [];
        for (const item of dept) {
            const projectDataList = await this.getProjectDataListByModel({ models: [params.options], deptId: item.departmentId, deptDateType, date, dayType, projectInactiveDay, times: 1 });//当前周期
            const total = projectDataList[params.options][projectDataList[params.options].length - 1].length;
            deptList.push({
                deptId: item.departmentId,
                deptName: item.departmentName,
                total: total
            });
        }
        return _.orderBy(deptList, ['total'], 'desc');
    }

    /**
     * 工地情况数据列表
     * @param param0 
     * @returns 
     */
    private async getProjectDataListByModel({ models, deptId, deptDateType = 'real_time', date, dayType, projectInactiveDay, times }: { models: string[], deptId: string, deptDateType: 'real_time' | 'history', date: string, dayType: 'day' | 'week' | 'month', projectInactiveDay: number, times?: number }) {
        return null;
        // const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList', 'aiProjectIdList'], time: times ? times : 1 });
        // const result = {
        //     dateRange: [],
        //     category: []
        // };
        // models = _.uniq(models);
        // models.map(key => _.extend(result, { [key]: [] }));
        // for (const etlDate of etlDateList) {
        //     const startTime = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'yyyy-MM-dd');
        //     const endTime = TimeUtil.formatDate(new Date(etlDate.category.endDate), 'yyyy-MM-dd');
        //     result.dateRange.push({ startTime, endTime });
        //     let date = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'MM.dd');
        //     if (!(dayType == 'day')) date = date + "-" + TimeUtil.formatDate(DateFns.subDays(new Date(etlDate.category.endDate), 1), 'MM.dd');
        //     result.category.push(date);
        //     //总列表
        //     if (result['projectTotal']) result['projectTotal'].push(etlDate.data.projectIdList);
        //     //智慧工地
        //     if (result['wisdomProject']) result['wisdomProject'].push(etlDate.data.aiProjectIdList);
        //     //普通工地
        //     if (result['commonProject']) result['commonProject'].push(_.difference(etlDate.data.projectIdList, etlDate.data.aiProjectIdList));
        //     //逾期工地 || 未逾期工地
        //     if (result['projectOverdue'] || result['projectNoOverdue']) {
        //         let overdueProjectList = [];
        //         let noOverdueProjectList = [];
        //         if (!_.isEmpty(etlDate.data.projectIdList)) {
        //             let nodeOverdueList = await this.nodeOverdueEventDao.searchOverdueProjectListV2({ projectIds: etlDate.data.projectIdList, startTime, endTime });
        //             nodeOverdueList.map(res => {
        //                 res.overdueTime > 0 ? overdueProjectList.push(res.projectId) : noOverdueProjectList.push(res.projectId)
        //             });
        //         }
        //         if (result['projectOverdue']) result['projectOverdue'].push(overdueProjectList);
        //         if (result['projectNoOverdue']) result['projectNoOverdue'].push(noOverdueProjectList);
        //     }
        //     //正常施工工地 || 停工工地  || 停工≥n天工地
        //     if (result['projectNormal'] || result['projectInactive'] || result['projectInactiveByDay']) {
        //         const normalProjectList = [];
        //         const inactiveProjectList = [];
        //         const inactiveByDayProjectList = [];
        //         if (!_.isEmpty(etlDate.data.aiProjectIdList)) {
        //             const inactiveList = await this.projectInactiveRecordDao.searchInactiveProjectList({ projectIds: etlDate.data.aiProjectIdList, startTime, endTime });
        //             inactiveList.map(res => {
        //                 //无人施工工地 || 正常施工工地
        //                 res.inactiveDay > 0 ? inactiveProjectList.push(res.projectId) : normalProjectList.push(res.projectId);
        //                 //≥n天无人施工工地
        //                 if (res.inactiveDay >= Number(projectInactiveDay)) inactiveByDayProjectList.push(res.projectId);
        //             });
        //         }
        //         if (result['projectNormal']) result['projectNormal'].push(normalProjectList);
        //         if (result['projectInactive']) result['projectInactive'].push(inactiveProjectList);
        //         if (result['projectInactiveByDay']) result['projectInactiveByDay'].push(inactiveByDayProjectList);
        //     }
        // }
        // return result;
    }

}