import { Injectable } from "@nestjs/common";
import { CompanyDao } from "@src/modules/report/domain/bgw/company/dao/company.dao";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";
import { ManagerDao } from "@src/modules/report/domain/bgw/manager/dao/manager.dao";
import { ProjectSignInDao } from "@src/modules/report/domain/bgw/project-sign-in/dao/project-sign-in.dao";
import { ProjectDao } from "@src/modules/report/domain/bgw/project/dao/project.dao";
import { ProjectNoFacesignEventDao } from "@src/modules/report/domain/camera/project-no-facesign-event/dao/project-no-facesign-event.dao";
import { TimeUtil } from "@src/util";
import { MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import { CorpReport } from "../../dto/corp-report.dto";
import { CorpReportSignIn } from "../../dto/sign-in.dto";
import * as DateFns from "date-fns";
import { CorpReportBasicsService } from "./corp-report-basics.service";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";

@Injectable()
export class CorpReportSignInService {
    private readonly logger = new MyLogger(CorpReportSignInService.name)
    constructor(
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly projectSignInDao: ProjectSignInDao,
        private readonly projectNoFacesignEventDao: ProjectNoFacesignEventDao,
        private readonly projectDao: ProjectDao,
        private readonly companyDao: CompanyDao,
        private readonly managerDao: ManagerDao,
        private readonly departmentService: DepartmentService,
        private readonly projectService: ProjectService,
    ) { }

    /**
     * 签到报表
     * 
    */
    async getSignStatistics(params: CorpReport.StatisticsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        //无人签到天数
        const set = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
        const signInNoBodyProjectDay = set.signInNoBodyProjectDay;
        //本周期
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 7 });
        const signInDateList = await this.getSignInList(dayType, etlDateList,signInNoBodyProjectDay);
        const result = new CorpReportSignIn.StatisticsSignInRes();
        //签到工地数
        const signInProject = new CorpReport.DeliveryStatisticsRes();
        const signInProjectDetails = new CorpReport.Details();
        signInProject.name = 'signInProject'
        signInProject.type = dayType;
        signInProject.total = signInDateList.signInProject[signInDateList.signInProject.length - 1].length;
        signInProjectDetails.category = signInDateList.category;
        signInProjectDetails.data = signInDateList.signInProject.map(res => res.length);
        signInProject.details = signInProjectDetails;
        //上期签到工地数
        const beforeSignInProject = signInDateList.signInProject[signInDateList.signInProject.length - 2].length;
        signInProject.sequential = !beforeSignInProject || beforeSignInProject == 0 ? "--" : Math.round(_.divide(signInProject.total - beforeSignInProject, beforeSignInProject) * 100);//环比(本期数-上期数）/上期数*100%
        result.signInProject = signInProject;
        //签到人数
        const signInPerson = new CorpReport.DeliveryStatisticsRes();
        const signInPersonDetails = new CorpReport.Details();
        signInPerson.name = 'signInPerson'
        signInPerson.type = dayType;
        signInPerson.total = signInDateList.signInPerson[signInDateList.signInPerson.length - 1].length;
        signInPersonDetails.category = signInDateList.category;
        signInPersonDetails.data = signInDateList.signInPerson.map(res => res.length);
        signInPerson.details = signInPersonDetails;
        //上期签到人数
        const beforeSignInPerson = signInDateList.signInPerson[signInDateList.signInPerson.length - 2].length;
        signInPerson.sequential = !beforeSignInPerson || beforeSignInPerson == 0 ? "--" : Math.round(_.divide(signInPerson.total - beforeSignInPerson, beforeSignInPerson) * 100);//环比(本期数-上期数）/上期数*100%
        result.signInPerson = signInPerson;
        //签到次数
        const signInTotal = new CorpReport.DeliveryStatisticsRes();
        const signInTotalDetails = new CorpReport.Details();
        signInTotal.name = 'signInTotal'
        signInTotal.type = dayType;
        signInTotal.total = signInDateList.signInTotal[signInDateList.signInTotal.length - 1].length;
        signInTotalDetails.category = signInDateList.category;
        signInTotalDetails.data = signInDateList.signInTotal.map(res => res.length);
        signInTotal.details = signInTotalDetails;
        //上期签到次数
        const beforeSignInTotal = signInDateList.signInTotal[signInDateList.signInTotal.length - 2].length;
        signInTotal.sequential = !beforeSignInTotal || beforeSignInTotal == 0 ? "--" : Math.round(_.divide(signInTotal.total - beforeSignInTotal, beforeSignInTotal) * 100);//环比(本期数-上期数）/上期数*100%
        result.signInTotal = signInTotal;
        //工地平均签到数
        const signInAvgProject = new CorpReport.DeliveryStatisticsRes();
        const signInAvgProjectDetails = new CorpReport.Details();
        signInAvgProject.name = 'signInAvgProject'
        signInAvgProject.type = dayType;
        signInAvgProject.total = signInDateList.signInAvgProject[signInDateList.signInAvgProject.length - 1]
        signInAvgProjectDetails.category = signInDateList.category;
        signInAvgProjectDetails.data = signInDateList.signInAvgProject
        signInAvgProject.details = signInAvgProjectDetails;
        //上期工地平均签到数
        const beforeSignInAvgProject = signInDateList.signInAvgProject[signInDateList.signInAvgProject.length - 2];
        signInAvgProject.sequential = !beforeSignInAvgProject || beforeSignInAvgProject == 0 ? "--" : Math.round(_.divide(signInAvgProject.total - beforeSignInAvgProject, beforeSignInAvgProject) * 100);//环比(本期数-上期数）/上期数*100%
        result.signInAvgProject = signInAvgProject;
        //无人签到工地数
        const signInNoBodyProject = new CorpReport.DeliveryStatisticsRes();
        const signInNoBodyProjectDetails = new CorpReport.Details();
        signInNoBodyProject.name = 'signInNoBodyProject'
        signInNoBodyProject.type = dayType;
        signInNoBodyProject.total = signInDateList.signInNoBodyProject[signInDateList.signInNoBodyProject.length - 1].length;
        signInNoBodyProjectDetails.category = signInDateList.category;
        signInNoBodyProjectDetails.data = signInDateList.signInNoBodyProject.map(res => res.length);
        signInNoBodyProject.details = signInNoBodyProjectDetails;
        //上期无人签到工地数
        const beforeSignInNoBodyProject = signInDateList.signInNoBodyProject[signInDateList.signInNoBodyProject.length - 2].length;
        signInNoBodyProject.sequential = beforeSignInNoBodyProject == 0 ? "--" : Math.round(_.divide(signInNoBodyProject.total - beforeSignInNoBodyProject, beforeSignInNoBodyProject) * 100);//环比(本期数-上期数）/上期数*100%
        result.signInNoBodyProject = signInNoBodyProject;
        result.signInNoBodyProjectDay = signInNoBodyProjectDay
        return result;
    }

    /**
     * 签到工地列表
     * 
    */
    async corpReportSignProjectList(params: CorpReportSignIn.SignInProjectListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 });
        const signInList = await this.getSignInList(dayType, etlDateList);
        params.signInIds = signInList.signInTotal[signInList.signInTotal.length - 1];
        if (_.isEmpty(params.signInIds)) return { total: 0, items: [] };
        //筛选 工地地址
        if (params.projectAddress) {
            const signInProject = signInList.signInProject[signInList.signInProject.length - 1];
            params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: signInProject, projectAddress: params.projectAddress });
            if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        }
        const list = await this.projectSignInDao.searchBiSignInProjectList(params)
        const projectIds = list.map(res => res.projectId)
        let projectManager = []
        const projectManagerMap = {}
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        projectManager.map(res => {projectManagerMap[res.id] = res})
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds: list.map(res => res.projectId) });
        list.map(res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            res.projectManagerName = projectManagerMap[res.projectManagerId]?projectManagerMap[res.projectManagerId].name:""
        })
        const count = await this.projectSignInDao.searchBiSignInProjectCount(params)
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 签到人员列表
     * 
    */
    async corpReportSignPersonList(params: CorpReportSignIn.SignInPersonListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const signInList = await this.getSignInList(dayType, etlDateList);
        params.signInIds = signInList.signInTotal[signInList.signInTotal.length - 1];
        if (_.isEmpty(params.signInIds)) return { total: 0, items: [] };
        const list = await this.projectSignInDao.searchBiSignInPersonList(params)
        const count = await this.projectSignInDao.searchBiSignInPersonCount(params)
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 签到次数列表
     * 
    */
    async corpReportSignTotalList(params: CorpReportSignIn.SignInTotalListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const signInList = await this.getSignInList(dayType, etlDateList);
        params.signInIds = signInList.signInTotal[signInList.signInTotal.length - 1];
        if (_.isEmpty(params.signInIds)) return { total: 0, items: [] };
        //筛选 工地地址
        if (params.projectAddress) {
            const signInProject = signInList.signInProject[signInList.signInProject.length - 1];
            params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: signInProject, projectAddress: params.projectAddress });
            if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        }
        const list = await this.projectSignInDao.searchBiSignInTotalList(params)
        const projectIds = list.map(res => res.projectId)
        let projectManager = []
        const projectManagerMap = {}
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        projectManager.map(res => {projectManagerMap[res.id] = res})
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds });
        const count = await this.projectSignInDao.searchBiSignInTotalListCount(params)
        list.map(res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            res.projectManagerName = projectManagerMap[res.projectManagerId]?projectManagerMap[res.projectManagerId].name:""
            res.signInPhoto = {
                mediaId: "",
                mediaType: "1",
                mediaUri: res.signInPhoto ? String(res.signInPhoto) : "",
                mediaResourceUri: res.signInPhoto ? String(res.signInPhoto) : "",
                creationDate: "",
                address: ""
            }
        })
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 无人签到工地列表
     * 
    */
    async corpReportSignInNoBodyProjectList(params: CorpReportSignIn.SignInNoBodyProjectListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        //无人签到天数
        const set = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const signInList = await this.getSignInList(dayType, etlDateList,set.signInNoBodyProjectDay);
        params.projectIds = signInList.signInNoBodyProject[signInList.signInNoBodyProject.length - 1];
        if (_.isEmpty(params.projectIds)) return { total: 0, items: [] };
        //筛选 工地地址
        if (params.projectAddress) {
            params.projectIds = await this.projectService.getProjectIdsByAddress({ projectIds: params.projectIds, projectAddress: params.projectAddress });
            if (_.isEmpty(params.projectIds)) return { total: 0, items: [] };
        }
        const list = await this.projectSignInDao.searchBiSignInNoBodyProjectList(params)
        const count = await this.projectSignInDao.searchBiSignInNoBodyProjectCount(params)
        const projectIds = list.map(res => res.projectId)
        let projectManager = []
        const projectManagerMap = {}
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        projectManager.map(res => {projectManagerMap[res.id] = res})
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds });
        list.map(res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            res.projectManagerName = projectManagerMap[res.projectManagerId]?projectManagerMap[res.projectManagerId].name:""
        })
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 签到工地列表筛选
     * 
    */
    async corpReportSignInProjectListOptions(params: CorpReportSignIn.SignInProjectListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 });
        const signInList = await this.getSignInList(dayType, etlDateList);
        params.signInIds = signInList.signInTotal[signInList.signInTotal.length - 1];
        if (_.isEmpty(params.signInIds)) return { projectManager: [], linkCompany: [] };
        const result = await this.projectSignInDao.searchBiSignInProjectList(params)
        const projectIds = result.map(res => res.projectId)
        const companyIds = result.map(res => res.linkCompanyId)
        let projectManager = []
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        let linkCompany = []
        if (companyIds && companyIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        return {
            projectManager: projectManager,
            linkCompany: linkCompany
        }
    }

    /**
     * 签到人员列表筛选
     * 
    */
    async corpReportSignInPersonListOptions(params: CorpReportSignIn.SignInPersonListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const signInList = await this.getSignInList(dayType, etlDateList);
        params.signInIds = signInList.signInTotal[signInList.signInTotal.length - 1];
        if (_.isEmpty(params.signInIds)) return { total: 0, items: [] };
        let result = await this.projectSignInDao.searchBiSignInPersonList(params)
        const managerIds = result.map(res => res.managerId)
        const companyIds = result.map(res => res.linkCompanyId)
        let manager = []
        if (managerIds && managerIds.length > 0) {
            manager = await this.managerDao.searchManagerList({ managerIdList: managerIds })
        }
        let linkCompany = []
        if (managerIds && managerIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        return {
            manager: manager,
            linkCompany: linkCompany
        }
    }

    /**
     * 签到次数列表筛选
     * 
    */
    async corpReportSignInTotalListOptions(params: CorpReportSignIn.SignInTotalListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const signInList = await this.getSignInList(dayType, etlDateList);
        params.signInIds = signInList.signInTotal[signInList.signInTotal.length - 1];
        if (_.isEmpty(params.signInIds)) return { total: 0, items: [] };
        let result = await this.projectSignInDao.searchBiSignInTotalList(params)
        const managerIds = result.map(res => String(res.managerId))
        const companyIds = result.map(res => String(res.linkCompanyId))
        const projectIds = result.map(res => res.projectId)
        let manager = []
        let projectManager = []
        let linkCompany = []
        if (managerIds && managerIds.length > 0) {
            manager = await this.managerDao.searchManagerList({ managerIdList: managerIds })
        }
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        if (companyIds && companyIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        return {
            manager: manager,
            projectManager: projectManager,
            linkCompany: linkCompany
        }
    }

    /**
     * 无人签到工地列表筛选
     * 
    */
    async corpReportSignInNoBodyProjectListOptions(params: CorpReportSignIn.SignInNoBodyProjectListReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        let set = await this.corpReportBasicsService.searchDeliverKanBanSet(params.deptId);
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const signInList = await this.getSignInList(dayType, etlDateList, set.signInNoBodyProjectDay);
        params.projectIds = signInList.signInNoBodyProject[signInList.signInNoBodyProject.length - 1];
        if (_.isEmpty(params.projectIds)) return { total: 0, items: [] };
        const result = await this.projectSignInDao.searchBiSignInNoBodyProjectList(params)
        const companyIds = result.map(res => String(res.linkCompanyId))
        const projectIds = result.map(res => res.projectId)
        let projectManager = []
        let linkCompany = []
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        if (projectIds && projectIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        return {
            projectManager: projectManager,
            linkCompany: linkCompany
        }
    }

    /**
     * 服务签到地区分布统计
     * @param params 
     * @returns 
     */
    async corpReportSignInDistribute(params: CorpReport.SearchOptionsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, options, signInNoBodyProjectDay } = params;
        const dept = await this.corpReportBasicsService.getDirectChildDepts({ departmentId: deptId, deptDateType, date, dayType });
        const deptList = [];
        for (let item of dept) {
            const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId: item.departmentId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 });
            const signInDataList = await this.getSignInList(dayType, etlDateList, signInNoBodyProjectDay);
            let total
            if(["signInAvgProject"].includes(options)){
                total = signInDataList[options][signInDataList[options].length - 1];
            }else{
                total = signInDataList[options][signInDataList[options].length - 1].length;
            }
            deptList.push({
                deptId: item.departmentId,
                deptName: item.departmentName,
                total: total
            });
        }
        return ["signInAvgProject"].includes(options) ? _.orderBy(deptList, ['total'], 'asc') : _.orderBy(deptList, ['total'], 'desc');
    }




    /**
     * 签到列表
     * @param dayType
     * @param etlDateList
     * */
    async getSignInList(dateType: 'day' | 'week' | 'month', etlDateList: { category: { startDate: string, endDate: string }, data: any }[], signInNoBodyProjectDay?: number) {
        const category = [];
        const signInProject = []
        const signInPerson = []
        const signInTotal = []
        const signInNoBodyProject = []
        const signInAvgProject = []
        for (let etlDate of etlDateList) {
            const startTime = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'yyyy-MM-dd');
            const endTime = TimeUtil.formatDate(new Date(etlDate.category.endDate), 'yyyy-MM-dd');
            let date = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'MM.dd');
            if (!(dateType == 'day')) date = date + "-" + TimeUtil.formatDate(DateFns.subDays(new Date(etlDate.category.endDate), 1), 'MM.dd');
            category.push(date);
            const signInProjectList = []
            const signInPersonList = []
            const signInTotalList = []
            const signInNoBodyProjectList = []
            if (!_.isEmpty(etlDate.data.aiProjectIdList)) {
                const signInList = await this.projectSignInDao.searchSignIn({ projectIds: etlDate.data.aiProjectIdList, startTime, endTime })
                const signInNoBodyList = await this.projectNoFacesignEventDao.search({ projectIdList: etlDate.data.aiProjectIdList, startTime, endTime, signInNoBodyProjectDay })
                signInList.items.map(res => {
                    signInTotalList.push(res.projectSignInId)
                    signInProjectList.push(res.projectId)
                    signInPersonList.push(res.managerId)
                });
                signInNoBodyList.map(res => {
                    signInNoBodyProjectList.push(res.projectId)
                });
            }
            signInProject.push(_.uniqBy(signInProjectList, res => res))
            signInPerson.push(_.uniqBy(signInPersonList, res => res))
            signInTotal.push(signInTotalList)
            signInNoBodyProject.push(_.uniqBy(signInNoBodyProjectList, res => res))
            signInAvgProject.push(_.round(_.uniqBy(signInProjectList, res => res).length>0?_.divide(signInTotalList.length,_.uniqBy(signInProjectList, res => res).length):0, 2))
        }
        return { category, signInProject, signInPerson, signInTotal, signInNoBodyProject, signInAvgProject }
    }
}