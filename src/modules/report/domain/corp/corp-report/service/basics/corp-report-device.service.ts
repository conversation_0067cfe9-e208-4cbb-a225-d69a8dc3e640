import { Injectable } from "@nestjs/common";
import { DeviceErrorDao } from "@src/modules/report/domain/camera/device-error/dao/device-error.dao";
import { DeviceOfflineEventDao } from "@src/modules/report/domain/camera/device-offline-event/dao/device-offline-event.dao";
import { DeviceDao } from "@src/modules/report/domain/camera/device/dao/device.dao";
import { DimDeviceDao } from "@src/modules/report/domain/etl/dao/dim-device.dao";
import { TimeUtil } from "@src/util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import { CorpReport } from "../../dto/corp-report.dto";
import { CorpReportDevice } from "../../dto/device.dto";
import { CorpReportBasicsService } from "./corp-report-basics.service";
import { CsvUtil } from "@src/util/csv.util";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";
import * as DateFns from "date-fns";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";

@Injectable()
export class CorpReportDeviceService {
    private readonly logger = new MyLogger(CorpReportDeviceService.name)
    constructor(
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly dimDeviceDao: DimDeviceDao,
        private readonly deviceDao: DeviceDao,
        private readonly deviceErrorDao: DeviceErrorDao,
        private readonly deviceOfflineEventDao: DeviceOfflineEventDao,
        private readonly departmentService: DepartmentService,
        private readonly projectService: ProjectService
    ) { }

    @DevTimeout(100)
    test() {
        // this.getDeviceStatistics({ deptId: '12551', date: '2022-12-16', dayType: 'day' }).then(res =>
        //     console.log(JSON.stringify(res))
        // )
        // this.corpReportDeviceDetail({
        //     deptId: '12551',
        //     date: '2022-12-17',
        //     dayType: 'day',
        //     searchModel: 'deviceInstall'
        // }).then(res =>
        //     console.log(JSON.stringify(res))
        // )
    }

    /**
     * 设备概览
     * @param params 
     * @returns 
     */
    async getDeviceStatistics(params: CorpReport.StatisticsReq & { companyIds?: string[], deviceOfflineDay?: number }) {
        const { deptId, deptDateType = 'real_time', companyIds, date, dayType, deviceOfflineDay } = params;
        //本周期
        const deviceDataList = await this.getDeviceDataList({ deptId, deptDateType, companyIds, date, dayType, deviceOfflineDay, times: 7 });
        const result = new CorpReportDevice.DeviceStatisticsRes();
        for (const modelName of CorpReportDevice.ModelList) {
            let needSequential = true;
            if (modelName == 'deviceTotal') needSequential = false;//当前设备总数不需要计算环比
            //根据模块生成折线图数据结构
            const res = await this.corpReportBasicsService.getBrokenLineDiagram(deviceDataList, modelName, dayType, needSequential);
            if (modelName == 'deviceInstall' || modelName == 'deviceIdle') res.rate = Math.round(_.divide(res.total, result['deviceTotal'].total) * 100);//率（安装/总数）;
            if (modelName == 'deviceOnline' || modelName == 'deviceOffline' || modelName == 'deviceOfflineByDay') res.rate = Math.round(_.divide(res.total, result['deviceInstall'].total) * 100);//率（在线/安装）
            res.rate = res.rate ? res.rate : '--';
            result[modelName] = res;
        }
        result.deviceOfflineDay = deviceOfflineDay;
        return result;
    }

    /**
     * 设备列表
     * @param params 
     * @returns 
     */
    async corpReportDeviceDetail(params: CorpReportDevice.DeviceDetailReq) {
        const { deptId, deptDateType = 'real_time', companyIds, date, dayType, name = 'deviceTotal', deviceOfflineDay, projectAddress } = params;
        const deviceDataList = await this.getDeviceDataList({ deptId, deptDateType, companyIds, date, dayType, deviceOfflineDay, times: 1 });
        params.yqzDeviceIds = deviceDataList[name][deviceDataList[name].length - 1];
        if (_.isEmpty(params.yqzDeviceIds)) return { total: 0, items: [] };
        //设备离线时间段
        const time = deviceDataList.dateRange[deviceDataList.dateRange.length - 1];
        params.startTime = time.startTime;
        params.endTime = time.endTime;
        //筛选 工地地址
        if (projectAddress) {
            const list = await this.deviceDao.searchDeviceList({
                companyIds,
                yqzDeviceIds: params.yqzDeviceIds,
                bindProject: 'Y',
                startTime: params.startTime,
                endTime: params.endTime
            });
            const projectIds = list.items.map(res => res.projectId);
            if (_.isEmpty(projectIds)) return { total: 0, items: [] };
            params.projectIds = await this.projectService.getProjectIdsByAddress({ projectIds: projectIds, projectAddress });
            if (_.isEmpty(params.projectIds)) return { total: 0, items: [] };
        }
        params.sortBy = [
            { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
            { field: "managerReviceTime", order: 'descending' },//领用时间
            { field: "projectBindTime", order: 'descending' },//绑定工地时间
            { field: "linkCompanyTime", order: 'descending' }//绑定公司时间
        ]
        if (params.reviceTimeSort) {
            params.sortBy = [
                { field: "managerReviceTime", order: 'ASC' == params.reviceTimeSort ? 'ascending' : 'descending' },//领用时间
                { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
                { field: "projectBindTime", order: 'descending' }, //绑定工地时间
                { field: "linkCompanyTime", order: 'descending' }//绑定公司时间
            ];
        }
        if (params.offLineTimeSort) {
            params.sortBy = [
                { field: 'isOnline', order: 'ASC' == params.offLineTimeSort ? 'descending' : 'ascending' },//在线离线
                { field: 'diffSec', order: 'ASC' == params.offLineTimeSort ? 'descending' : 'ascending' },//离线时间
                { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
                { field: "managerReviceTime", order: 'descending' },//领用时间
                { field: "projectBindTime", order: 'descending' },//绑定工地时间
                { field: "linkCompanyTime", order: 'descending' }//绑定公司时间
            ];
        }
        if ('deviceInstall' == name) {
            params.bindProject = 'Y'
        }
        //查询设备列表
        const result = await this.deviceDao.searchDeviceList(params);
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds: result.items.map(res => res.projectId) });
        result.items.map(async res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            if (res.diffSec) {
                res.offLineTime = TimeUtil.formatSecondsDuration(res.diffSec);
            } else {
                res.offLineTime = "";
            }
            if (!res.deviceManagerId || !res.projectId) {
                res.isOnline = '';
                res.offLineTime = '';
            }
            delete res.diffSec;
            delete res.projectId;
            delete res.linkCompanyCreateTime;
            delete res.linkCompanyTime;
        })
        return result;
    }

    /**
     * 设备列表csv格式（导出）
     * @param params 
     */
    async corpReportDeviceDetailCsv(params: CorpReportDevice.DeviceDetailReq) {
        delete params.pageNo;
        delete params.pageSize;
        const res = await this.corpReportDeviceDetail(params);
        return CsvUtil.convert2Csv(res.items.map(e => {
            return {
                '所属公司': e.linkCompanyName ? e.linkCompanyName : '',
                '序列号': e.deviceSerial ? e.deviceSerial : '',
                '领用人': e.deviceManagerName ? e.deviceManagerName : '',
                '领用时间': e.managerReviceTime ? e.managerReviceTime : '',
                '绑定工地': e.projectAddress ? e.projectAddress : '',
                '绑定时间': e.projectBindTime ? e.projectBindTime : '',
                '在线状态': !e.isOnline ? "" : e.isOnline == 'N' ? '离线' : '在线',
                '离线时长': e.offLineTime ? e.offLineTime : '',
                '工地负责人': e.projectManagerName ? e.projectManagerName : '',
            }
        }));
    }

    /**
     * 设备列表筛选
     * @param params 
     * @returns 
     */
    async corpReportDeviceDetailOptions(params: CorpReportDevice.DeviceDetailReq) {
        const { deptId, deptDateType = 'real_time', companyIds, date, dayType, name = 'deviceTotal', deviceOfflineDay } = params;
        const result = new CorpReportDevice.DeviceDetailOptionsRes();
        const deviceDataList = await this.getDeviceDataList({ deptId, deptDateType, companyIds, date, dayType, deviceOfflineDay, times: 1 });
        result.isOnline = [{ name: '在线', value: 'Y' }, { name: '离线', value: 'N' }];
        const yqzDeviceIds = deviceDataList[name][deviceDataList[name].length - 1];
        if (_.isEmpty(yqzDeviceIds)) return result;
        const time = deviceDataList.dateRange[deviceDataList.dateRange.length - 1];
        const startTime = time.startTime;
        const endTime = time.endTime;
        //查询工地列表
        const deviceListRes = await this.deviceDao.searchDeviceList({ companyIds, yqzDeviceIds, startTime, endTime });
        deviceListRes.items.map(async res => {
            const deviceManager = { id: res.deviceManagerId, name: res.deviceManagerName };
            const linkCompany = { id: res.linkCompanyId, name: res.linkCompanyName };
            const projectManager = { id: res.projectManagerId, name: res.projectManagerName };
            if (!_.some(result.deviceManager, deviceManager) && deviceManager.id && deviceManager.name) result.deviceManager.push(deviceManager);
            if (!_.some(result.linkCompany, linkCompany) && linkCompany.id && linkCompany.name) result.linkCompany.push(linkCompany);
            if (!_.some(result.projectManager, projectManager) && projectManager.id && projectManager.name) result.projectManager.push(projectManager);
        })
        return result;
    }

    /**
     * 设备概况地区分布统计
     * @param params 
     * @returns 
     */
    async corpReportDeviceDistribute(params: CorpReport.SearchOptionsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, options, deviceOfflineDay } = params;
        const dept = await this.corpReportBasicsService.getDirectChildDepts({ departmentId: deptId, deptDateType, date, dayType });
        const deptList = [];
        for (const item of dept) {
            //设备不与公司强绑定，所以根据dept需查询公司list
            const companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(item.departmentId);
            const deviceDataList = await this.getDeviceDataList({ deptId: item.departmentId, deptDateType, companyIds, date, dayType, deviceOfflineDay, times: 1 });
            const total = deviceDataList[options][deviceDataList[options].length - 1].length;
            deptList.push({
                deptId: item.departmentId,
                deptName: item.departmentName,
                total: total
            });
        }
        return _.orderBy(deptList, ['total'], 'desc');
    }


    /**
     * 设备情况数据列表
     * @param param0 
     * @returns 
     */
    private async getDeviceDataList({ deptId, deptDateType = 'real_time', companyIds, date, dayType, deviceOfflineDay, times }: { deptId: string, deptDateType: 'real_time' | 'history', companyIds: string[], date: string, dayType: 'day' | 'week' | 'month', deviceOfflineDay: number, times?: number }) {
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['deviceIdList'], time: times });
        const dateRange = [];
        const category = [];
        const deviceTotal = [];
        const deviceInstall = [];
        const deviceIdle = [];
        const deviceOnline = [];
        const deviceOffline = [];
        const deviceOfflineByDay = [];
        for (const etlDate of etlDateList) {
            const startTime = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'yyyyMMdd');
            const endTime = TimeUtil.formatDate(new Date(etlDate.category.endDate), 'yyyyMMdd');
            dateRange.push({ startTime, endTime });
            let date = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'MM.dd');
            if (!(dayType == 'day')) date = date + "-" + TimeUtil.formatDate(DateFns.subDays(new Date(etlDate.category.endDate), 1), 'MM.dd');
            category.push(date);
            //总列表
            deviceTotal.push(etlDate.data.deviceIdList);
            // 安装列表
            let deviceInstallList = [];
            if (!_.isEmpty(etlDate.data.deviceIdList)) {
                const bindDeviceList = await this.dimDeviceDao.search({ companyIdList: companyIds, deviceIdList: etlDate.data.deviceIdList, startTime: startTime, endTime: endTime, isBindProject: 'Y' });
                let bindDeviceIdList = [];
                bindDeviceList.items.map(res => bindDeviceIdList.push(res.deviceId));
                deviceInstallList = _.uniq(bindDeviceIdList);
            }
            deviceInstall.push(deviceInstallList);
            //闲置列表(总列表-安装列表)
            let deviceIdleList = [];
            deviceIdleList = _.difference(etlDate.data.deviceIdList, deviceInstallList);
            deviceIdle.push(deviceIdleList);
            //离线列表
            let deviceOfflineList = [];
            if (!_.isEmpty(deviceInstallList)) {
                let deviceErrorList = await this.deviceErrorDao.searchDeviceErrorList({ companyIds, devices: deviceInstallList, startTime, endTime });
                deviceOfflineList = deviceErrorList.map(res => res.yqzDeviceId);
            }
            deviceOffline.push(deviceOfflineList);
            //在线列表(安装列表-离线列表)
            let deviceOnlineList = [];
            deviceOnlineList = _.difference(deviceInstallList, deviceOfflineList);
            deviceOnline.push(deviceOnlineList);
            //离线≥n天列表
            let deviceOfflineByDayList = [];
            //查询当前登陆公司的离线天数设置（单位：天）
            let offLineSec = deviceOfflineDay * 24 * 60 * 60;//单位：秒
            if (!_.isEmpty(deviceInstallList) && offLineSec > 0) {
                let offlineEventList = await this.deviceOfflineEventDao.searchDeviceOfflineEventList({ companyIds, devices: deviceInstallList, startTime, endTime });
                offlineEventList.map(res => { if (res.offLineSec >= offLineSec) return deviceOfflineByDayList.push(res.yqzDeviceId) });
            }
            deviceOfflineByDay.push(deviceOfflineByDayList);
        }
        return { dateRange, category, deviceTotal, deviceInstall, deviceIdle, deviceOnline, deviceOffline, deviceOfflineByDay };
    }

}