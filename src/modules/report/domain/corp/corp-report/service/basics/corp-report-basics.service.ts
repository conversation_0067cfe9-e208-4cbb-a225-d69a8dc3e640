import { Injectable } from "@nestjs/common";
import { DimDepartmentDao } from "@src/modules/report/domain/etl/dao/dim-department.dao";
import { DevTimeout, MyLogger, YqzException } from "@yqz/nest";
import { CorpReportUtils } from "../utils/corp-report.utils";
import * as _ from "lodash";
import { TimeUtil } from "@src/util";
import * as DateFns from "date-fns";
import { CorpReport } from "../../dto/corp-report.dto";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { DimDepartmentDataDao } from "@src/modules/report/domain/etl/dao/dim-department-data.dao";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";

@Injectable()
export class CorpReportBasicsService {
  private readonly logger = new MyLogger(CorpReportBasicsService.name)
  constructor(
    private readonly dimDepartmentDao: DimDepartmentDao,
    private readonly dimDepartmentDataDao: DimDepartmentDataDao,
    private readonly companyDecorationDao: CompanyDecorationDao,
    private readonly departmentDao: DepartmentDao,
    private readonly departmentService: DepartmentService
  ) { }

  @DevTimeout(100)
  test() {
    // this.getDeptEtlDateList({deptId:'12551', date:'2022-12-31', dayType:'month', etlList:['deviceIdList']}).then(res =>
    //   console.log(res)
    // )
    // let etlList = ['manager_id_list']
    // let result = {}
    // etlList.forEach(res => _.extend(result, { [res]: [] }))
    // console.log(result)

    // const dateList = CorpReportUtils.getTimeFrameList('2022-12-17', 'week', 1);
    // console.log(dateList)


    // const dedateList = CorpReportUtils.getTimeFrameList('2022-12-10', 'week', 1);
    // console.log(dedateList)
  }

  /**
   * 查询每个周期的部门切片数据（按周期统计）
   * @param deptId id
   * @param dateType 查询部门切片类型(real_time实时、history历史)，默认：real_time实时
   * @param date 查询时间（最后一个周期时间）
   * @param dayType 时间周期类型（按天、周、月、年）
   * @param etlList 要查询的部门切片数据[projectIdList、managerIdList、deviceIdList、aiProjectIdList]
   * @param time 次数周期（默认往前7个周期，为一环）
   * @param sort 返回排序（默认升序）
   * @returns 
   */
  async getDeptEtlDateList(params: { deptId: string, deptDateType: 'real_time' | 'history', date: string, dayType: 'day' | 'week' | 'month' | 'year', etlList: string[], time?: number, sort?: 'ASC' | 'DESC' }): Promise<{
    category: {
      startDate: string; endDate: string;
    }; data: CorpReport.DimDeptData;
  }[]> {
    let { deptId, deptDateType = 'real_time', date, dayType, etlList, time, sort } = params;
    /** 当传月份的时候，后台暂时将时间处理为当月最后一天（前端找不到为啥日期会传1号） */
    const myDate = dayType === 'month' ? DateFns.endOfMonth(new Date(date)) : new Date(date);
    date = DateFns.format(DateFns.addDays(myDate, 1), 'yyyy-MM-dd');
    /** 根据时间类型 获取 time次 时间段 */
    const dateList = CorpReportUtils.getTimeFrameList(date, dayType, time ? time : 7);
    let result: { category: { startDate: string, endDate: string }, data: CorpReport.DimDeptData }[] = [];
    etlList.forEach(key => _.extend(result, { [key]: [] }));
    for (const date of dateList) {//要查询的时间
      const dates = { startDate: date.startTime, endDate: date.endTime };
      const data = await this.getDeptEtlDateListByTimeRange({ deptId, deptDateType, startTime: date.startTime, endTime: date.endTime, etlList });
      result.push({ category: dates, data: data });
    }
    sort = sort ? sort : 'ASC';
    if (sort === 'ASC') result = _.reverse(result);
    return result;
  }

  /**
   * 查询一个周期时间段内部门切片数据
   * @param deptId 部门id
   * @param deptDateType 查询部门切片类型(real_time实时、history历史)，默认：real_time实时
   * @param startTime 一个周期的开始时间
   * @param endTime 一个周期的结束时间
   * @param etlList 需要查询的切片[projectIdList、managerIdList、deviceIdList、aiProjectIdList]
   * @returns
   */
  private async getDeptEtlDateListByTimeRange(params: { deptId: string, deptDateType: 'real_time' | 'history', startTime: string, endTime: string, etlList: string[] }) {
    let { deptId, deptDateType = 'real_time', startTime, endTime, etlList } = params;
    const result: CorpReport.DimDeptData = { companyIdList: [], departmentIdList: [], directChildDepartmentIdList: [] };
    startTime = TimeUtil.formatDate(new Date(startTime), 'yyyyMMdd');
    endTime = TimeUtil.formatDate(new Date(endTime), 'yyyyMMdd');
    etlList.forEach(key => _.extend(result, { [key]: [] }));
    const dimDeptList = await this.dimDepartmentDao.search({ departmentIdList: [deptId], startTime: startTime, endTime: endTime });
    const departmentIdList: string[] = [];
    if (deptDateType === 'real_time') {//查询实时 本部门 及 子部门id（不校验删除、集团关联公司查询公司所有部门、包含自己）
      const divisionCompanyIdList: string[] = [];//记录集团部门关联的公司id，实时查询要查询到集团关联公司下的所有部门
      //查询当前部门
      const dept = await this.departmentDao.searchDeptInfoList({ deptId: deptId });
      dept.map(res => {
        departmentIdList.push(res.departmentId);//包含自己
        //查询集团部门关联公司的公司id
        if (res?.type === 'division' && res?.linkCompanyId) { divisionCompanyIdList.push(res?.linkCompanyId); }
      });
      //查询子部门
      const deptList = await this.departmentDao.searchDeptInfoList({ companyId: dept[0]?.companyId, includeDir: deptId });
      deptList.map(res => {
        departmentIdList.push(res.departmentId);
        //查询集团部门关联公司的公司id
        if (res?.type === 'division' && res?.linkCompanyId) { divisionCompanyIdList.push(res?.linkCompanyId); }
      });
      //查询集团关联公司下的所有部门（不排除掉根部好像也没有关系，数据后面会去重掉）
      if (!_.isEmpty(divisionCompanyIdList)) {
        const divisionDept = await this.departmentDao.searchDeptInfoList({ companyIds: divisionCompanyIdList });
        divisionDept.map(res => departmentIdList.push(res.departmentId));
      }
    }
    for (const dimDept of dimDeptList) {
      if (deptDateType === 'history') {//查询历史 本部门 及 子部门id（不校验删除、包含自己）
        departmentIdList.push(dimDept.departmentId);//包含自己
        const deptIdList = dimDept?.childDepartmentIdList && '' !== dimDept.childDepartmentIdList ? dimDept.childDepartmentIdList.split(',') : [];
        departmentIdList.push(...deptIdList);
      }
      //查询部门切片数据
      if (_.isEmpty(departmentIdList)) return result;
      const dimDeptDataList = await this.dimDepartmentDataDao.searchList({ date: dimDept.date, departmentIdList });
      for (const key of etlList) {
        const list: string[] = [];
        dimDeptDataList.map(data => {
          if (data[key] && '' !== data[key]) {
            list.push(...String(data[key]).split(","));
          }
        });
        result[key] = _.union(result[key], list);//去重
      }
      //记录部门id（包含子部门）
      result.departmentIdList = _.union(result.departmentIdList, departmentIdList);
      //记录公司
      result.companyIdList = _.union(result.companyIdList, dimDeptDataList.map(data => data.companyId));
    }
    return result;
  }

  /**
   * 获取折线图（单线）
   * @param dataList 数据源
   * @param modelName 模块名
   * @param dayType 日期类型，天、周、月
   * @param needSequential 是否需要环比
   * @returns 
   */
  async getBrokenLineDiagram(dataList: any, modelName: string, dayType: 'day' | 'week' | 'month', needSequential: boolean) {
    const total = dataList[modelName][dataList[modelName].length - 1].length;
    let result = new CorpReport.DeliveryStatisticsRes();
    let details = new CorpReport.Details();
    result.type = dayType;
    result.name = modelName;
    result.total = total;
    details.category = dataList.category;
    details.data = dataList[modelName].map(res => res.length);
    result.details = details;
    //上期数据（环比(本期数-上期数）/上期数*100%）
    if (needSequential) {
      const beforeTotal = dataList[modelName][dataList[modelName].length - 2].length;
      result.sequential = beforeTotal != 0 ? Math.round(_.divide(total - beforeTotal, beforeTotal) * 100) : '--';
    }
    return result;
  }

  /**
   * 获取多线图
   * @param dataList 数据源
   * @param modelName 模块名
   * @param dayType 日期类型，天、周、月
   * @param needSequential 是否需要环比
   * @returns 
   */
  async getMultipleGraphs(dataList: any, modelName: string, groups: any, dayType: 'day' | 'week' | 'month') {
    let result = new CorpReport.DeliveryStatisticsRes();
    let details = new CorpReport.Details();
    result.type = dayType;
    result.name = modelName;
    details.category = dataList.category;
    details.groups = groups;
    details.data = {}
    for (let key in groups) {
      const list = dataList[key].map(res => res.length);
      _.extend(details.data, { [key]: list });
    }
    result.details = details;
    return result;
  }

  /**
   * 获取比例图(只用于汇总Total)
   * @param dataList 数据源
   * @param modelName 模块名(适用于Total)
   * @param groups 分组
   * @param dayType 日期类型，天、周、月
   * @param needSequential 是否需要环比
   * @returns 
   */
  async getProportionChart(dataList: any, modelName: string, groups: any, dayType: 'day' | 'week' | 'month', needSequential: boolean) {
    let result = new CorpReport.DeliveryStatisticsRes();
    let details = new CorpReport.GroupDetails();
    const totalNum = dataList[modelName][dataList[modelName].length - 1].length;
    result.type = dayType;
    result.name = modelName;
    result.total = totalNum;
    details.groups = groups;
    details.data = {}
    for (let key in groups) {
      const num = dataList[key][dataList[key].length - 1].length;
      _.extend(details.data, { [key]: { rate: totalNum != 0 ? Math.round(_.divide(num, totalNum) * 100) : '--', count: num } });
    }
    if (needSequential) {
      //上期识别总数
      const beforeTotal = dataList[modelName][dataList[modelName].length - 2].length;
      //环比(本期数-上期数）/上期数*100%
      result.sequential = beforeTotal != 0 ? Math.round(_.divide(totalNum - beforeTotal, beforeTotal) * 100) : '--';
    }
    result.details = details;
    return result;
  }

  /**
   * 看板配置查询
   * @param deptId 
   * @returns 
   */
  async searchDeliverKanBanSet(deptId: string) {
    // const departmentEntity = await this.departmentDao.get(deptId);
    const parents = await this.departmentDao.searchDeptInfoList({ deptId });//(不校验删除)
    const departmentEntity = !_.isEmpty(parents) ? parents[0] : null;
    if (!departmentEntity) throw new YqzException('部门不存在');
    const entity = await this.companyDecorationDao.search(departmentEntity.companyId);
    let set = JSON.parse(entity.deliverKanbanSet);
    return {
      deviceOfflineDay: set.deviceOfflineDay ? set.deviceOfflineDay : 7,
      projectInactiveDay: set.projectInactiveDay ? set.projectInactiveDay : 7,
      signInNoBodyProjectDay: set.signInNoBodyProjectDay ? set.signInNoBodyProjectDay : 7
    }
  }

  /**
   * 根据部门id获取对于的companyId（有历史数据不校验部门删除）
   * @param deptId 
   * @returns 
   */
  async searchCompanyIdsByDeptId(deptId: string) {
    const companyIds = [];
    //当前部门(不校验删除)
    const department = await this.departmentDao.searchDeptInfoList({ deptId });
    companyIds.push(department[0]?.linkCompanyId ? department[0].linkCompanyId : department[0].companyId);
    //子部门(不校验删除)
    const departmentList = await this.departmentDao.searchDeptInfoList({ companyId: department[0].companyId, includeDir: deptId });
    departmentList.map(res => {
      const companyId = res?.linkCompanyId ? res.linkCompanyId : res.companyId;
      if (!companyIds.includes(companyId)) companyIds.push(companyId);
    });
    return companyIds;
  }

  /**
   * 查询直属部门信息(实时/历史)(不校验部门删除)
   * @param deptId 部门id
   * @param deptDateType 查询部门切片类型(real_time实时、history历史)，默认：real_time实时
   */
  async getDirectChildDepts({ departmentId, deptDateType = 'real_time', date, dayType, time }: { departmentId: string, deptDateType: 'real_time' | 'history', date: string, dayType: 'day' | 'week' | 'month', time?: number }) {
    /** 历史（不校验删除） */
    if (deptDateType === 'history') {
      //当传月份的时候，后台暂时将时间处理为当月最后一天（前端找不到为啥日期会传1号）
      const myDate = dayType === 'month' ? DateFns.endOfMonth(new Date(date)) : new Date(date);
      date = DateFns.format(DateFns.addDays(myDate, 1), 'yyyy-MM-dd');
      //根据时间类型 获取 time次 时间段
      const dateList = CorpReportUtils.getTimeFrameList(date, dayType, time ? time : 7);
      //取最后一个周期的开始时间和结束时间
      const startTime = TimeUtil.formatDate(new Date(dateList[dateList.length - 1].startTime), 'yyyyMMdd');
      const endTime = TimeUtil.formatDate(new Date(dateList[dateList.length - 1].endTime), 'yyyyMMdd');
      const dimDeptList = await this.dimDepartmentDao.search({ departmentIdList: [departmentId], startTime: startTime, endTime: endTime });
      const departmentIdList: string[] = [];
      for (const dimDept of dimDeptList) {//一个周期内的直接子部门数据切片
        if (deptDateType === 'history') {
          const deptIdList = dimDept?.directChildDepartmentIdList && '' !== dimDept.directChildDepartmentIdList ? dimDept.directChildDepartmentIdList.split(',') : [];
          departmentIdList.push(...deptIdList);
        }
      }
      //查询部门数据(不校验删除)
      if (_.isEmpty(departmentIdList)) return [];
      return await this.departmentDao.searchDeptInfoList({ deptIds: _.union(departmentIdList) });
    }
    /** 实时（不校验删除） */
    const parents = await this.departmentDao.searchDeptInfoList({ deptId: departmentId });
    let parent = !_.isEmpty(parents) ? parents[0] : null;
    if (!parent) throw new YqzException('部门不存在');
    //集团部门关联公司
    if (parent.type === 'division' && parent.linkCompanyId) parent = await this.departmentService.getRootDept(parent.linkCompanyId);
    const parentPath = parent.dir ? [parent.dir, parent.departmentId].join(',') : parent.departmentId;
    //查询直接子部门数据
    return await this.departmentDao.searchDeptInfoList({ companyId: parent.companyId, dir: parentPath });
  }

}