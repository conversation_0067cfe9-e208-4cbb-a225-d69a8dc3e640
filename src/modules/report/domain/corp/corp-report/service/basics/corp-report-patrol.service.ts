import { Injectable } from "@nestjs/common";
import { CmsPhotoDao } from "@src/modules/report/domain/bgw/common/cms-photo.dao";
import { CommonParameterDao } from "@src/modules/report/domain/bgw/common/common-parameter.dao";
import { CompanyDao } from "@src/modules/report/domain/bgw/company/dao/company.dao";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";
import { ManagerDao } from "@src/modules/report/domain/bgw/manager/dao/manager.dao";
import { ProjectPatrolDao } from "@src/modules/report/domain/bgw/project-patrol/project-patrol.dao";
import { ProjectProblemDao } from "@src/modules/report/domain/bgw/project-problem/dao/project-problem.dao";
import { ProjectDao } from "@src/modules/report/domain/bgw/project/dao/project.dao";
import { TimeUtil } from "@src/util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import _, { forEach } from "lodash";
import { CorpReport } from "../../dto/corp-report.dto";
import { CorpReportPatrol } from "../../dto/patrol.dto";
import { CorpReportBasicsService } from "./corp-report-basics.service";
import * as DateFns from "date-fns";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";

@Injectable()
export class CorpReportPatrolService {
    private readonly logger = new MyLogger(CorpReportPatrolService.name)
    constructor(
        private readonly projectProblemDao: ProjectProblemDao,
        private readonly projectPatrolDao: ProjectPatrolDao,
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly departmentService: DepartmentService,
        private readonly companyDao: CompanyDao,
        private readonly projectDao: ProjectDao,
        private readonly managerDao: ManagerDao,
        private readonly commonParameterDao: CommonParameterDao,
        private readonly cmsPhotoDao: CmsPhotoDao,
        private readonly projectService: ProjectService
    ) { }

    // @DevTimeout(500)
    async getPatrolStatisticsTest() {
        const data = await this.getPatrolStatistics({ date: '2023-10-15', deptDateType: 'real_time', dayType: 'day', deptId: '13' })
        console.log(200, "data")
    }
    /**
     * 巡检报表
     * 
    */
    async getPatrolStatistics(params: CorpReport.StatisticsReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        //本周期
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 7 });
        console.log(etlDateList[0].data)
        const patrolDateList = await this.getPatrolList(dayType, etlDateList);
        let result = new CorpReportPatrol.StatisticsPatrolRes();
        //智慧工地数
        let wisdomProject = new CorpReport.DeliveryStatisticsRes();
        let wisdomProjectDetails = new CorpReport.Details();
        wisdomProject.name = 'wisdomProject';
        wisdomProject.type = dayType;
        wisdomProject.total = patrolDateList.wisdomProject[patrolDateList.wisdomProject.length - 1].length;
        wisdomProjectDetails.category = patrolDateList.category;
        wisdomProjectDetails.data = patrolDateList.wisdomProject.map(res => res.length);
        wisdomProject.details = wisdomProjectDetails;
        result.wisdomProject = wisdomProject;
        //巡检工地数
        let patrolProject = new CorpReport.DeliveryStatisticsRes();
        let patrolProjectDetails = new CorpReport.Details();
        patrolProject.name = 'patrolProject'
        patrolProject.type = dayType;
        patrolProject.total = patrolDateList.patrolProject[patrolDateList.patrolProject.length - 1].length;
        patrolProjectDetails.category = patrolDateList.category;
        patrolProjectDetails.data = patrolDateList.patrolProject.map(res => res.length);
        patrolProject.details = patrolProjectDetails;
        //上期巡检工地数
        const beforePatrolProject = patrolDateList.patrolProject[patrolDateList.patrolProject.length - 2].length;
        patrolProject.sequential = !beforePatrolProject || beforePatrolProject==0?"--":Math.round(_.divide(patrolProject.total - beforePatrolProject, beforePatrolProject) * 100);//环比(本期数-上期数）/上期数*100%
        result.patrolProject = patrolProject;
        //巡检总次数
        let patrolTotal = new CorpReport.DeliveryStatisticsRes();
        let patrolTotalDetails = new CorpReport.Details();
        patrolTotal.name = 'patrolTotal'
        patrolTotal.type = dayType;
        patrolTotal.total = patrolDateList.patrolTotal[patrolDateList.patrolTotal.length - 1].length;
        patrolTotalDetails.category = patrolDateList.category;
        patrolTotalDetails.data = patrolDateList.patrolTotal.map(res => res.length);
        patrolTotal.details = patrolTotalDetails;
        //上期巡检总次数
        const beforePatrolTotal = patrolDateList.patrolTotal[patrolDateList.patrolTotal.length - 2].length;
        patrolTotal.sequential = !beforePatrolTotal || beforePatrolTotal==0?"--":Math.round(_.divide(patrolTotal.total - beforePatrolTotal, beforePatrolTotal) * 100);//环比(本期数-上期数）/上期数*100%
        result.patrolTotal = patrolTotal;
        //工地平均巡检数
        let patrolAvgProjectNum = new CorpReport.DeliveryStatisticsRes();
        let patrolAvgProjectNumDetails = new CorpReport.Details();
        patrolAvgProjectNum.name = 'patrolAvgProjectNum'
        patrolAvgProjectNum.type = dayType;
        patrolAvgProjectNum.total = patrolDateList.patrolAvgProjectNum[patrolDateList.patrolAvgProjectNum.length - 1];
        patrolAvgProjectNumDetails.category = patrolDateList.category;
        patrolAvgProjectNumDetails.data = patrolDateList.patrolAvgProjectNum;
        patrolAvgProjectNum.details = patrolAvgProjectNumDetails;
        //上期工地平均巡检数
        const beforePatrolAvgProjectNum = patrolDateList.patrolAvgProjectNum[patrolDateList.patrolAvgProjectNum.length - 2];
        patrolAvgProjectNum.sequential = !beforePatrolAvgProjectNum || beforePatrolAvgProjectNum==0?"--":Math.round(_.divide(patrolAvgProjectNum.total - beforePatrolAvgProjectNum, beforePatrolAvgProjectNum) * 100);//环比(本期数-上期数）/上期数*100%
        result.patrolAvgProjectNum = patrolAvgProjectNum;
        //巡检问题数
        let patrolProblemNum = new CorpReport.DeliveryStatisticsRes();
        let patrolProblemNumDetails = new CorpReport.Details();
        patrolProblemNum.name = 'patrolProblemNum'
        patrolProblemNum.type = dayType;
        patrolProblemNum.total = patrolDateList.patrolProblemNum[patrolDateList.patrolProblemNum.length - 1].length;
        patrolProblemNumDetails.category = patrolDateList.category;
        patrolProblemNumDetails.data = patrolDateList.patrolProblemNum.map(res => res.length);
        patrolProblemNum.details = patrolProblemNumDetails;
        //上期巡检问题数
        const beforePatrolProblemNum = patrolDateList.patrolProblemNum[patrolDateList.patrolProblemNum.length - 2].length;
        patrolProblemNum.sequential = !beforePatrolProblemNum || beforePatrolProblemNum==0?"--":Math.round(_.divide(patrolProblemNum.total - beforePatrolProblemNum, beforePatrolProblemNum) * 100);//环比(本期数-上期数）/上期数*100%
        result.patrolProblemNum = patrolProblemNum;
        //问题工地
        let problemProjectRate = new CorpReport.DeliveryStatisticsRes();
        let problemProjectRateDetails = new CorpReport.Details();
        problemProjectRate.name = 'problemProjectRate'
        problemProjectRate.type = dayType;
        problemProjectRate.total = patrolDateList.problemProject[patrolDateList.problemProject.length - 1].length;
        problemProjectRate.rate = 0 == patrolProject.total ? '--' : Math.round(_.divide(problemProjectRate.total, patrolProject.total) * 100);
        problemProjectRateDetails.category = patrolDateList.category;
        problemProjectRateDetails.data = patrolDateList.problemProject.map(res => res.length);
        problemProjectRate.details = problemProjectRateDetails;
        //上期问题工地
        const beforeProblemProjectRate = patrolDateList.problemProject[patrolDateList.problemProject.length - 2].length;
        problemProjectRate.sequential = !beforeProblemProjectRate || beforeProblemProjectRate==0?"--":Math.round(_.divide(problemProjectRate.total - beforeProblemProjectRate, beforeProblemProjectRate) * 100);//环比(本期数-上期数）/上期数*100%
        result.problemProjectRate = problemProjectRate;
        //问题处理率
        let problemDealRate = new CorpReport.DeliveryStatisticsRes();
        let problemDealRateDetails = new CorpReport.Details();
        problemDealRate.name = 'problemDealRate'
        problemDealRate.type = dayType;
        problemDealRate.rate = patrolDateList.problemDealRate[patrolDateList.problemDealRate.length - 1];
        problemDealRateDetails.category = patrolDateList.category;
        problemDealRateDetails.data = patrolDateList.problemDealRate;
        problemDealRate.details = problemDealRateDetails;
        //上期问题处理率
        const beforeProblemDealRate = patrolDateList.problemDealRate[patrolDateList.problemDealRate.length - 2];
        problemDealRate.sequential = !beforeProblemDealRate || beforeProblemDealRate==0?"--":Math.round(_.divide(Number(problemDealRate.rate) - beforeProblemDealRate, beforeProblemDealRate) * 100);//环比(本期数-上期数）/上期数*100%
        result.problemDealRate = problemDealRate;
        //平均处理时长（时长）
        let problemDealTime = new CorpReport.DeliveryStatisticsRes();
        let problemDealTimeDetails = new CorpReport.Details();
        problemDealTime.name = 'problemDealTime'
        problemDealTime.type = dayType;
        let problemDealTimeList = patrolDateList.problemDealTime.map(res => (res && res.length > 0) ? _.round(_.mean(res)/60,1) : 0)
        problemDealTime.total = _.round(problemDealTimeList[problemDealTimeList.length - 1],1);
        problemDealTimeDetails.category = patrolDateList.category;
        problemDealTimeDetails.data = problemDealTimeList;
        problemDealTime.details = problemDealTimeDetails;
        //上期平均处理时长（时长）
        const beforeProblemDealTime = _.round(problemDealTimeList[problemDealTimeList.length - 2],1);
        problemDealTime.sequential = !beforeProblemDealTime || beforeProblemDealTime==0?"--":Math.round(_.divide(Number(problemDealTime.rate) - beforeProblemDealTime, beforeProblemDealTime) * 100);//环比(本期数-上期数）/上期数*100%
        result.problemDealTime = problemDealTime;

        return result;
    }

    /**
     * 智慧工地列表
     * @param dayType
     * @param etlDateList
     * */
    async searchPatrolProjectList(params: CorpReportPatrol.PatrolWisdomProjectListReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({deptId, deptDateType, date, dayType, etlList:['aiProjectIdList'], time: 1})
        const patrolList = await this.getPatrolList(dayType, etlDateList);
        params.startTime = etlDateList[etlDateList.length - 1].category.startDate
        params.endTime = etlDateList[etlDateList.length - 1].category.endDate
        params.projectIdList = patrolList.wisdomProject[patrolList.wisdomProject.length - 1];
        params.problemIdList = patrolList.patrolProblemNum[patrolList.patrolProblemNum.length - 1]
        params.patrolIdList = patrolList.patrolTotal[patrolList.patrolTotal.length - 1]
        //筛选 工地地址
        if (params.projectAddress && !_.isEmpty(params.projectIdList)) {
            params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: params.projectIdList, projectAddress: params.projectAddress });
        }
        switch (params.name){
            case 'problemProjectRate':
                params.findProblemCount = 1
                if (_.isEmpty(params.problemIdList)) return { total: 0, items: [] };
                break;
            case 'patrolProject':
                params.patrolCount = 1
                if (_.isEmpty(params.patrolIdList)) return { total: 0, items: [] };
                break;
        }
        if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        const list = await this.projectPatrolDao.searchPatrolProjectList(params)
        const projectIds = list.map(res => res.projectId)
        let projectManager = []
        let projectManagerMap = {}
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        projectManager.map(res => {projectManagerMap[res.id] = res})
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({projectIds:list.map(res => res.projectId)});
        list.map(res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            res.patrolNum = res.patrolNum?res.patrolNum:"0"
            res.patrolPersonNum = res.patrolPersonNum?res.patrolPersonNum:"0"
            res.problemDealRate = res.problemDealRate?res.problemDealRate:"--"
            res.problemNum = res.problemNum?res.problemNum:"0"
            res.problemAvgDealTime = res.problemAvgDealTime?res.problemAvgDealTime:"--"
            res.projectManagerName = projectManagerMap[res.projectManagerId]?projectManagerMap[res.projectManagerId].name:""
        })
        const count = await this.projectPatrolDao.searchPatrolProjectCount(params)
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 巡检总次数
     * @param dayType
     * @param etlDateList
     * */
    async searchPatrolTotalList(params: CorpReportPatrol.PatrolTotalListReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({deptId, deptDateType, date, dayType, etlList:['aiProjectIdList'], time: 1})
        const patrolList = await this.getPatrolList(dayType, etlDateList);
        // let patrolProjectIdList = [];
        // patrolList.wisdomProject.map(res => patrolProjectIdList = _.union(patrolProjectIdList, res));
        params.patrolIdList = patrolList.patrolTotal[patrolList.patrolTotal.length - 1];
        if (_.isEmpty(params.patrolIdList)) return { total: 0, items: [] };
        //筛选 工地地址
        if (params.projectAddress) {
            const patrolProject = patrolList.patrolProject[patrolList.patrolProject.length - 1];
            params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: patrolProject, projectAddress: params.projectAddress });
            if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        }
        const list = await this.projectPatrolDao.searchPatrolTotalList(params)
        const projectIds = list.map(res => res.projectId)
        let projectManager = []
        let projectManagerMap = {}
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })

        }
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({projectIds});
        projectManager.map(res => {projectManagerMap[res.id] = res})
        list.map(res => {
            res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
            res.projectManagerName = projectManagerMap[res.projectManagerId]?projectManagerMap[res.projectManagerId].name:""
        })
        const count = await this.projectPatrolDao.searchPatrolTotalCount(params)
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 发现问题数
     * @param dayType
     * @param etlDateList
     * */
    async searchPatrolProblemList(params: CorpReportPatrol.PatrolProblemNumListReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 })
        const patrolList = await this.getPatrolList(dayType, etlDateList);
        // let patrolProjectIdList = [];
        // patrolList.wisdomProject.map(res => patrolProjectIdList = _.union(patrolProjectIdList, res));
        params.problemIdList = patrolList.patrolProblemNum[patrolList.patrolProblemNum.length - 1];
        if (_.isEmpty(params.problemIdList)) return { total: 0, items: [] };
        //筛选 工地地址
        if (params.projectAddress) {
            const problemProject = patrolList.problemProject[patrolList.problemProject.length - 1];
            params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: problemProject, projectAddress: params.projectAddress });
            if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        }
        let list = await this.projectPatrolDao.searchPatrolProblemList(params)
        const projectIds = [];
        // const managerIds = [];
        list.map(res => {
            projectIds.push(res.projectId);
            // managerIds.push(res.reportId);
        });
        let projectManager = []
        let projectManagerMap = {}
        let manager = []
        // let managerMap = {}
        // if (managerIds && managerIds.length > 0) {
            // manager = await this.managerDao.searchManagerList({ managerIdList: managerIds })
        // }
        // manager.map(res => {managerMap[res.id] = res})
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        projectManager.map(res => {projectManagerMap[res.id] = res})
        let count = await this.projectPatrolDao.searchPatrolProblemCount(params)
        const problemType = await this.commonParameterDao.search('problem_type')
        const problemStatus = await this.commonParameterDao.search('problem_status')
        //获取工地地址
        const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds });
        let typeMap = {}
        let statusMap = {}
        problemType.map(res => {
            typeMap[res.parameterCode] = res.parameterName
        })
        problemStatus.map(res => {
            statusMap[res.parameterCode] = res.parameterName
        })
        for(let item of list){
            item.projectAddress = addressMap[item.projectId] ? addressMap[item.projectId] : "";
            // item.reportName = managerMap[item.reportId]?managerMap[item.reportId].name:""
            item.projectManagerName = projectManagerMap[item.projectManagerId]?projectManagerMap[item.projectManagerId].name:""
            item.problemTypeName = typeMap[item.problemType]
            item.problemStatusName = statusMap[item.problemStatus]
            item.problemPhoto = item.problemPhoto?item.problemPhoto.split(","):[]
            item.solvedPhoto = item.solvedPhoto?item.solvedPhoto.split(","):[]
            let problemPhoto = []
            let solvedPhoto = []
            for(let img of item.problemPhoto){
                const res = await this.cmsPhotoDao.search(img)
                if(res){
                    problemPhoto.push({
                        mediaId: img,
                        mediaType: "1",
                        mediaUri: res.ossUrl ? String(res.ossUrl) : "",
                        mediaResourceUri: res.ossUrl ? String(res.ossUrl) : "",
                        creationDate: "",
                        address: ""
                    })
                }
            }
            for(let img of item.solvedPhoto){
                const res = await this.cmsPhotoDao.search(img)
                if(res){
                    solvedPhoto.push({
                        mediaId: img,
                        mediaType: "1",
                        mediaUri: res.ossUrl ? String(res.ossUrl) : "",
                        mediaResourceUri: res.ossUrl ? String(res.ossUrl) : "",
                        creationDate: "",
                        address: ""
                    })
                }
            }
            item.problemPhoto = problemPhoto
            item.solvedPhoto = solvedPhoto
        }
        return {
            total: +count,
            items: list
        }
    }

    /**
     * 工地巡检地区分布统计
     * @param params 
     * @returns 
     */
     async corpReportPatrolDistribute(params: CorpReport.SearchOptionsReq) {
        let { deptId, deptDateType = 'real_time', date, dayType, options } = params;
        const dept = await this.corpReportBasicsService.getDirectChildDepts({ departmentId: deptId, deptDateType, date, dayType });
        let deptList = [];
        for (let item of dept) {
            const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId: item.departmentId, deptDateType, date, dayType, etlList: ['aiProjectIdList'], time: 1 });
            const patrolDataList = await this.getPatrolList(dayType, etlDateList);
            let total = 0
            if("problemProjectRate" == options) options = "problemProject";
            if(["patrolAvgProjectNum","problemDealRate"].includes(options)){
                total = patrolDataList[options][patrolDataList[options].length - 1];
            }else if(["problemDealTime"].includes(options)){
                let problemDealTimeList = patrolDataList.problemDealTime.map(res => (res && res.length > 0) ? _.mean(res) : 0)
                total = _.round(problemDealTimeList[problemDealTimeList.length - 1],1);
            }else{
                total = patrolDataList[options][patrolDataList[options].length - 1].length;
            }
            deptList.push({
                deptId: item.departmentId,
                deptName: item.departmentName,
                total: total
            });
        }
        if(["wisdomProject","patrolProject","patrolTotal","patrolAvgProjectNum","problemDealRate"].includes(options)){
            deptList = _.orderBy(deptList, ['total'], 'asc')
        }else{
            deptList = _.orderBy(deptList, ['total'], 'desc')
        }
        return deptList;
    }

    /**
     * 智慧工地列表筛选
     * 
    */
    async corpReportPatrolWisdomProjectListOptions(params: CorpReportPatrol.PatrolWisdomProjectListReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({deptId, deptDateType, date, dayType, etlList:['aiProjectIdList'], time: 1})
        const patrolList = await this.getPatrolList(dayType, etlDateList);
        // let patrolProjectIdList = [];
        // patrolList.wisdomProject.map(res => patrolProjectIdList = _.union(patrolProjectIdList, res));
        params.projectIdList = patrolList.wisdomProject[patrolList.wisdomProject.length - 1];
        params.problemIdList = patrolList.patrolProblemNum[patrolList.patrolProblemNum.length - 1]
        params.patrolIdList = patrolList.patrolTotal[patrolList.patrolTotal.length - 1]
        params.startTime = etlDateList[etlDateList.length - 1].category.startDate
        params.endTime = etlDateList[etlDateList.length - 1].category.endDate
        switch(params.name){
            case 'problemProjectRate':
                params.findProblemCount = 1
                if (_.isEmpty(params.problemIdList)) return { total: 0, items: [] };
                break;
            case 'patrolProject':
                params.patrolCount = 1
                if (_.isEmpty(params.patrolIdList)) return { total: 0, items: [] };
                break;
        }
        if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        const result = await this.projectPatrolDao.searchPatrolProjectList(params)
        const companyIds = result.map(res => String(res.linkCompanyId))
        const projectIds = result.map(res => res.projectId)
        let projectManager = []
        let linkCompany = []
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        if (companyIds && companyIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        return {
            projectManager: projectManager,
            linkCompany: linkCompany
        }
    }

    /**
     * 巡检次数列表筛选
     * 
    */
    async corpReportPatrolTotalOptions(params: CorpReportPatrol.PatrolTotalListReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({deptId, deptDateType, date, dayType, etlList:['aiProjectIdList'], time: 1})
        const patrolList = await this.getPatrolList(dayType, etlDateList);
        // let patrolProjectIdList = [];
        // patrolList.wisdomProject.map(res => patrolProjectIdList = _.union(patrolProjectIdList, res));
        params.patrolIdList = patrolList.patrolTotal[patrolList.patrolTotal.length - 1];
        if (_.isEmpty(params.patrolIdList)) return { total: 0, items: [] };
        const result = await this.projectPatrolDao.searchPatrolTotalList(params)
        const managerIds = result.map(res => String(res.managerId))
        const companyIds = result.map(res => String(res.linkCompanyId))
        const projectIds = result.map(res => res.projectId)
        let manager = []
        let projectManager = []
        let linkCompany = []
        if (managerIds && managerIds.length > 0) {
            manager = await this.managerDao.searchManagerList({ managerIdList: managerIds })
        }
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        if (companyIds && companyIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        return {
            manager: manager,
            projectManager: projectManager,
            linkCompany: linkCompany
        }
    }

    /**
     * 巡检问题数列表筛选
     * 
    */
    async corpReportPatrolProblemNumListOptions(params: CorpReportPatrol.PatrolProblemNumListReq) {
        let { deptId, deptDateType = 'real_time', date, dayType } = params;
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({deptId, deptDateType, date, dayType, etlList:['aiProjectIdList'], time: 1})
        const patrolList = await this.getPatrolList(dayType, etlDateList);
        // let patrolProjectIdList = [];
        // patrolList.wisdomProject.map(res => patrolProjectIdList = _.union(patrolProjectIdList, res));
        params.problemIdList = patrolList.patrolProblemNum[patrolList.patrolProblemNum.length - 1];
        if (_.isEmpty(params.problemIdList)) return { total: 0, items: [] };
        let result = await this.projectPatrolDao.searchPatrolProblemList(params)
        const report = [];
        const companyIds = [];
        const projectIds = [];
        result.map(res => {
            report.push({ id: String(res.reportId), name: res.reportName });
            companyIds.push(String(res.linkCompanyId));
            projectIds.push(res.projectId);
        });
        // const reportIds = result.map(res => String(res.reportId))
        // const companyIds = result.map(res => String(res.linkCompanyId))
        // const projectIds = result.map(res => res.projectId)
        // let report = []
        let projectManager = []
        let linkCompany = []
        // if (reportIds && reportIds.length > 0) {
        //     report = await this.managerDao.searchManagerList({ managerIdList: reportIds })
        // }
        if (projectIds && projectIds.length > 0) {
            projectManager = await this.projectDao.searchProjectDirectorList({ projectIdList: projectIds })
        }
        if (companyIds && companyIds.length > 0) {
            linkCompany = await this.companyDao.searchCompanyList({ companyIds })
        }
        const problemType = await this.commonParameterDao.search('problem_type')
        const problemStatus = await this.commonParameterDao.search('problem_status')
        const type = problemType.map(res => {
            return {
                id: res.parameterCode,
                name: res.parameterName
            }
        })
        const status = problemStatus.map(res => {
            return {
                id: res.parameterCode,
                name: res.parameterName
            }
        })
        return {
            report: _.uniqBy(report, 'id'),
            projectManager: projectManager,
            linkCompany: linkCompany,
            problemType: type,
            problemStatus: status
        }
    }

    /**
     * 巡检列表
     * @param dayType
     * @param etlDateList
     * */
    async getPatrolList(dateType: 'day' | 'week' | 'month', etlDateList: { category: { startDate: string, endDate: string }, data: any }[]) {
        const category = [];
        const wisdomProject = []
        const patrolAvgProjectNum = []
        const patrolTotal = []
        const patrolProblemNum = []
        const problemDealProblem = []
        const problemDealTime = []
        const patrolProject = []
        const problemDealRate = []
        const dealProblemProject = []
        const problemProject = []
        for (let etlDate of etlDateList) {
            const startTime = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'yyyy-MM-dd');
            const endTime = TimeUtil.formatDate(new Date(etlDate.category.endDate), 'yyyy-MM-dd');
            let date = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'MM.dd');
            if (!(dateType == 'day')) date = date + "-" + TimeUtil.formatDate(DateFns.subDays(new Date(etlDate.category.endDate), 1), 'MM.dd');
            category.push(date);
            let wisdomProjectList = []
            const patrolTotalList = []
            const patrolProblemNumList = []
            const problemDealProblemList = []
            const problemProjectList = []
            const dealProblemProjectList = []
            const problemDealTimeList = []
            const patrolProjectList = []
            if (!_.isEmpty(etlDate.data.aiProjectIdList)) {
                const projectProblemList = await this.projectProblemDao.searchProjectProblem({ projectIds: etlDate.data.aiProjectIdList, projectSource:'projectPatrol', startTime, endTime })
                const dealProblemList = await this.projectProblemDao.searchProjectProblem({ projectIds: etlDate.data.aiProjectIdList, projectSource:'projectPatrol', startTime, endTime, status: '3' })
                const projectPatrolNum = await this.projectPatrolDao.searchProjectPatrol({ projectIds: etlDate.data.aiProjectIdList, startTime, endTime })
                wisdomProjectList = etlDate.data.aiProjectIdList
                projectPatrolNum.items.map(item => {
                    patrolTotalList.push(item.projectPatrolId)
                    patrolProjectList.push(item.projectId)
                })
                projectProblemList.items.map(item => {
                    patrolProblemNumList.push(item.projectProblemId)
                    problemProjectList.push(item.projectId)
                })
                dealProblemList.items.map(item => {
                    problemDealProblemList.push(item.projectProblemId)
                    if(item.problemSolvedTime && item.problemTime) problemDealTimeList.push(TimeUtil.dateDiff(item.problemSolvedTime, item.problemTime, 'min'))
                    dealProblemProjectList.push(item.projectId)
                })
            }
            let problemTotal = patrolProblemNumList.length
            let dealProblemNum = problemDealProblemList.length
            let problemRate = problemTotal < 1 ? 0 : Math.round(_.divide(dealProblemNum,problemTotal) * 100)
            wisdomProject.push(wisdomProjectList)
            patrolAvgProjectNum.push(_.round(_.uniqBy(patrolProjectList, res => res).length>0?_.divide(patrolTotalList.length,_.uniqBy(patrolProjectList, res => res).length):0, 2))
            patrolTotal.push(patrolTotalList)
            patrolProblemNum.push(patrolProblemNumList)
            problemDealProblem.push(problemDealProblemList)
            problemDealRate.push(problemRate)
            problemDealTime.push(problemDealTimeList)
            patrolProject.push(_.uniqBy(patrolProjectList, res => res))
            problemProject.push(_.uniqBy(problemProjectList, res => res))
            dealProblemProject.push(_.uniqBy(dealProblemProjectList, res => res))
        }
        return { category, wisdomProject, patrolAvgProjectNum, patrolTotal, patrolProblemNum, problemDealProblem, problemDealTime, patrolProject, problemDealRate, dealProblemProject, problemProject }
    }

}