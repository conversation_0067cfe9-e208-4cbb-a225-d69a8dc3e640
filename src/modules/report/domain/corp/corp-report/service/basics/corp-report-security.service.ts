import { Injectable } from "@nestjs/common";
import { ProjectDetectionDao } from "@src/modules/report/domain/camera/project-detection/dao/detection.dao";
import { TimeUtil } from "@src/util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import * as _ from "lodash";
import { CorpReport } from "../../dto/corp-report.dto";
import { CorpReportSecurity } from "../../dto/security.dto";
import { CorpReportBasicsService } from "./corp-report-basics.service";
import { CsvUtil } from "@src/util/csv.util";
import { DepartmentService } from "@src/modules/report/domain/bgw/department/service/department.service";
import * as DateFns from "date-fns";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";

@Injectable()
export class CorpReportSecurityService {
    private readonly logger = new MyLogger(CorpReportSecurityService.name)
    constructor(
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly projectDetectionDao: ProjectDetectionDao,
        private readonly departmentService: DepartmentService,
        private readonly projectService: ProjectService
    ) { }

    @DevTimeout(100)
    test() {
        // this.corpReportSecurity({ deptId: '12551', date: '2022-12-20', dayType: 'week' }).then(res =>
        //     console.log(JSON.stringify(res))
        // )
        // const groups = new CorpReportSecurity.SecurityType().getGroups();
        // console.log(groups)
        // const en = new CorpReportSecurity.SecurityType().getEnum();
        // console.log(en)
        // const ea = new CorpReportSecurity.SecurityType().getEmptyArray();
        // console.log(ea)
    }

    /**
     * 文明施工图表
     * @param params 
     * @returns 
     */
    async corpReportSecurity(params: CorpReport.StatisticsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType } = params;
        //本周期
        const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 7 });
        const securityDateList = await this.getSecurityDateList(dayType, etlDateList);
        const result = new CorpReportSecurity.SecurityStatisticsRes();
        const groups = new CorpReportSecurity.SecurityType().getGroups();
        //生成多线图数据结构
        const securityTable = await this.corpReportBasicsService.getMultipleGraphs(securityDateList, 'securityTable', groups, dayType);
        //生成比例图数据结构
        const securityTotal = await this.corpReportBasicsService.getProportionChart(securityDateList, 'securityTotal', groups, dayType, true);
        result.securityTotal = securityTotal;
        result.securityTable = securityTable;
        return result;
    }

    /**
     * 文明施工详情
     * @param params 
     */
    async corpReportSecurityDetail(params: CorpReportSecurity.SecurityDetailReq) {
        // const { deptId, deptDateType = 'real_time', date, dayType } = params;
        // const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 1 });
        // const securityDateList = await this.getSecurityDateList(dayType, etlDateList);
        // params.securityType = params.securityType ? params.securityType : 'securityTotal';
        // params.securityDetectionIds = securityDateList[params.securityType][securityDateList[params.securityType].length - 1];
        // if (_.isEmpty(params.securityDetectionIds)) return { total: 0, items: [] };
        // //筛选 工地地址
        // if (params.projectAddress) {
        //     const projectIds = securityDateList.projectIds[securityDateList.projectIds.length - 1];
        //     params.projectIdList = await this.projectService.getProjectIdsByAddress({ projectIds: projectIds, projectAddress: params.projectAddress });
        //     if (_.isEmpty(params.projectIdList)) return { total: 0, items: [] };
        // }
        // params.sortBy = [
        //     { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
        //     { field: "eventTime", order: 'descending' },//监测时间
        // ]
        // if (params.eventTimeSort) {
        //     params.sortBy = [
        //         { field: "eventTime", order: 'ASC' == params.eventTimeSort ? 'ascending' : 'descending' },//监测时间
        //         { field: "linkCompanyCreateTime", order: 'ascending' },//所属公司创建时间
        //     ]
        // }
        // const result = await this.projectDetectionDao.searchSecurityList({ ...params, idList: params.securityDetectionIds });
        // //获取工地地址
        // const addressMap = await this.projectService.getProjectAddressByProjectIds({ projectIds: result.items.map(res => res.projectId) });
        // result.items.map(res => {
        //     res.projectAddress = addressMap[res.projectId] ? addressMap[res.projectId] : "";
        //     let securityTypeList = String(res.securityType).split(",");
        //     let securityType = [];
        //     securityTypeList.map(code => {
        //         const typeValue = new CorpReportSecurity.SecurityType().getValueByCode(code);
        //         if (typeValue) securityType.push(typeValue);
        //     })
        //     res.securityType = securityType;
        //     res.photo = {
        //         mediaId: "",
        //         mediaType: "1",
        //         mediaUri: String(res.photo),
        //         mediaResourceUri: String(res.photo),
        //         creationDate: "",
        //         address: ""
        //     }
        //     delete res.linkCompanyCreateTime;
        // })
        // return result;
    }

    /**
     * 文明施工详情csv格式（导出）
     * @param params 
     */
    async corpReportSecurityDetailCsv(params: CorpReportSecurity.SecurityDetailReq) {
        // delete params.pageNo;
        // delete params.pageSize;
        // const res = await this.corpReportSecurityDetail(params);
        // return CsvUtil.convert2Csv(res.items.map(e => {
        //     return {
        //         '所属公司': e.linkCompanyName ? e.linkCompanyName : '',
        //         '工地地址': e.projectAddress ? e.projectAddress : '',
        //         '报告类型': e.securityType.map(res => {
        //             const typeValue = new CorpReportSecurity.SecurityType()[res].name;
        //             if (typeValue) return typeValue;
        //         }).join('、'),
        //         '图片': e.photo.mediaUri ? e.photo.mediaUri.replace(process.env.OSS_CAMERA_DEFAULT_URL, process.env.OSS_CAMERA_CUSTOM_URL) : '',
        //         '监测时间': e.eventTime ? e.eventTime : '',
        //         '工地负责人': e.projectManagerName ? e.projectManagerName : '',
        //     }
        // }));
    }

    /**
     * 文明施工详情筛选
     * @param params 
     */
    async corpReportSecurityDetailOptions(params: CorpReportSecurity.SecurityDetailReq) {
        // const { deptId, deptDateType = 'real_time', date, dayType } = params;
        // const result = new CorpReportSecurity.SecurityDetailOptionsRes();
        // const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 1 });
        // const securityDateList = await this.getSecurityDateList(dayType, etlDateList);
        // params.securityType = params.securityType ? params.securityType : 'securityTotal';
        // const securityDetectionIds = securityDateList[params.securityType][securityDateList[params.securityType].length - 1];
        // result.securityType = new CorpReportSecurity.SecurityType().getEnum();
        // if (_.isEmpty(securityDetectionIds)) return result;
        // const securityListRes = await this.projectDetectionDao.searchSecurityList({ idList: securityDetectionIds });
        // securityListRes.items.map(res => {
        //     const linkCompany = { id: res.linkCompanyId, name: res.linkCompanyName };
        //     const projectManager = { id: res.projectManagerId, name: res.projectManagerName };
        //     if (!_.some(result.linkCompany, linkCompany) && linkCompany.id && linkCompany.name) result.linkCompany.push(linkCompany);
        //     if (!_.some(result.projectManager, projectManager) && projectManager.id && projectManager.name) result.projectManager.push(projectManager);
        // })
        // return result;
    }

    /**
     * 整洁度地区分布统计
     * @param params 
     * @returns 
     */
    async corpReportSecurityDistribute(params: CorpReport.SearchOptionsReq) {
        const { deptId, deptDateType = 'real_time', date, dayType, options } = params;
        const dept = await this.corpReportBasicsService.getDirectChildDepts({ departmentId: deptId, deptDateType, date, dayType });
        const deptList = [];
        for (const item of dept) {
            const etlDateList = await this.corpReportBasicsService.getDeptEtlDateList({ deptId: item.departmentId, deptDateType, date, dayType, etlList: ['projectIdList'], time: 1 });
            const securityDateList = await this.getSecurityDateList(dayType, etlDateList);
            const total = securityDateList[options][securityDateList[options].length - 1].length;
            deptList.push({
                deptId: item.departmentId,
                deptName: item.departmentName,
                total: total
            });
        }
        return _.orderBy(deptList, ['total'], 'desc');
    }


    /**
     * 文明施工数据列表
     * @param dayType 
     * @param etlDateList 
     * @returns 
     */
    private async getSecurityDateList(dayType: 'day' | 'week' | 'month', etlDateList: { category: { startDate: string, endDate: string }, data: any }[]) {
        const projectIds = [];
        const category = [];
        const securityTotal = [];
        const smoke = [];
        const slippers = [];
        const workClothes = [];
        const electroMobiles = [];
        for (const etlDate of etlDateList) {
            const startTime = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'yyyy-MM-dd');
            const endTime = TimeUtil.formatDate(new Date(etlDate.category.endDate), 'yyyy-MM-dd');
            let date = TimeUtil.formatDate(new Date(etlDate.category.startDate), 'MM.dd');
            if (!(dayType == 'day')) date = date + "-" + TimeUtil.formatDate(DateFns.subDays(new Date(etlDate.category.endDate), 1), 'MM.dd');
            category.push(date);
            const projectIdList = [];
            const totalIdList = [];
            const smokeIdList = [];
            const slippersIdList = [];
            const workClothesIdList = [];
            const electroMobileIdList = [];
            if (!_.isEmpty(etlDate.data.projectIdList)) {
                const detectionList = await this.projectDetectionDao.searchSecurity({ projectIds: etlDate.data.projectIdList, startTime, endTime, riskType: ['0', '4', '5', '7'] });
                detectionList.items.map(res => {
                    projectIdList.push(res.projectId);
                    totalIdList.push(res.projectSecurityDetectionId);
                    const riskType = res.riskType.split(',')
                    if (riskType.includes('0')) smokeIdList.push(res.projectSecurityDetectionId);//抽烟
                    if (riskType.includes('5')) slippersIdList.push(res.projectSecurityDetectionId);//拖鞋
                    if (riskType.includes('4')) workClothesIdList.push(res.projectSecurityDetectionId);//工服
                    if (riskType.includes('7')) electroMobileIdList.push(res.projectSecurityDetectionId);//电瓶车
                })
            }
            projectIds.push(projectIdList);
            securityTotal.push(_.concat(smokeIdList, slippersIdList, workClothesIdList, electroMobileIdList));
            smoke.push(smokeIdList);
            slippers.push(slippersIdList);
            workClothes.push(workClothesIdList);
            electroMobiles.push(electroMobileIdList);
        }
        return { category, projectIds, securityTotal, smoke, slippers, workClothes, electroMobiles };
    }

}