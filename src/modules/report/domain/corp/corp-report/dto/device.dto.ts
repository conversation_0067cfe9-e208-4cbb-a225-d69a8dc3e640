import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "./corp-report.dto";
import { IsNotEmpty } from "class-validator";
import { Device } from "../../../camera/device/types/device.type";

export namespace CorpReportDevice {

    export const ModelList = ['deviceTotal', 'deviceInstall', 'deviceIdle', 'deviceOnline', 'deviceOffline', 'deviceOfflineByDay'];

    export class DeviceDetailReq extends Device.DeviceDetailReq {
        @ApiProperty({ description: "集团id" })
        deptId?: string;
        @ApiProperty({ description: "查询部门切片类型(real_time实时、history历史)" })
        deptDateType: 'real_time' | 'history';
        @IsNotEmpty()
        @ApiProperty({ description: "日期" })
        date: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期类型:day-天,week-周,month-月", enum: ['day', 'week', 'month'] })
        dayType: 'day' | 'week' | 'month';
        @ApiProperty({ description: "查询类型 设备总数-'deviceTotal',安装数-'deviceInstall',闲置数-'deviceIdle',在线数-'deviceOnline',离线数-'deviceOffline',离线≥n天设备数-'deviceOfflineByDay'" })
        name?: string;
        deviceOfflineDay?: number;
        companyIds?: string[];
    }

    export class DeviceStatisticsRes {
        @ApiProperty({ description: "当前设备总数", type: CorpReport.DeliveryStatisticsRes })
        deviceTotal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "安装数", type: CorpReport.DeliveryStatisticsRes })
        deviceInstall: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "闲置数", type: CorpReport.DeliveryStatisticsRes })
        deviceIdle: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "在线数", type: CorpReport.DeliveryStatisticsRes })
        deviceOnline: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "离线数", type: CorpReport.DeliveryStatisticsRes })
        deviceOffline: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "离线≥n天设备数", type: CorpReport.DeliveryStatisticsRes })
        deviceOfflineByDay: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "离线天数", required: false })
        deviceOfflineDay?: number;
    }

    export class DeviceDetailOptionsRes {
        @ApiProperty({ description: "领用人", type: [CorpReport.InfoDto] })
        deviceManager: CorpReport.InfoDto[];
        @ApiProperty({ description: "在线状态", type: [CorpReport.InfoDto] })
        isOnline: CorpReport.InfoDto[];
        @ApiProperty({ description: "工地负责人", type: [CorpReport.InfoDto] })
        projectManager: CorpReport.InfoDto[];
        @ApiProperty({ description: "所属公司", type: [CorpReport.InfoDto] })
        linkCompany: CorpReport.InfoDto[];
        constructor() {
            return {
                deviceManager: [],
                isOnline: [],
                projectManager: [],
                linkCompany: [],
            }
        }
    }

    export class DeviceDetailRes {
        @ApiProperty({ description: "数据列表", type: [Device.DeviceDetail] })
        items: Device.DeviceDetail[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

}