import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "./corp-report.dto"

export namespace CorpReportSignIn {

    export const ModelList = ['signInProject', 'signInPerson', 'signInTotal', 'signInNoBodyProject', 'signInAvgProject', 'signInNoBodyProjectDay'];

    export class SignInProjectListOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "工地负责人" })
        projectManager: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
    }

    export class SignInPersonListOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "用户昵称" })
        manager: CorpReport.InfoDto[];
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
    }

    export class SignInTotalListOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "用户昵称" })
        manager: CorpReport.InfoDto[];
        @ApiProperty({ type: [CorpReport.InfoDto], description: "工地负责人" })
        projectManager: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
    }
    export class SignInNoBodyProjectListOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "工地负责人" })
        projectManager: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
    }
    export class SignInProjectListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 签到工地数-'signInProject', 签到人员数-'signInPerson', 签到次数-'signInTotal', 无人签到工地-'signInNoBodyProject', 工地平均签到次数-'signInAvgProject' " })
        name?: string;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        signInIds?: string[];
        projectIdList?: string[];
        @ApiProperty({ description: "签到人数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        signInPersonSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "签到次数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        signInTotalSort?: 'DESC' | 'ASC';
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class SignInPersonListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 签到工地数-'signInProject', 签到人员数-'signInPerson', 签到次数-'signInTotal', 无人签到工地-'signInNoBodyProject', 工地平均签到次数-'signInAvgProject' " })
        name?: string;
        @ApiProperty({ description: "用户id" })
        managerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        signInIds?: string[];
        @ApiProperty({ description: "签到工地排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        signInProjectSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "签到次数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        signInTotalSort?: 'DESC' | 'ASC';
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class SignInTotalListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 签到工地数-'signInProject', 签到人员数-'signInPerson', 签到次数-'signInTotal', 无人签到工地-'signInNoBodyProject', 工地平均签到次数-'signInAvgProject' " })
        name?: string;
        @ApiProperty({ description: "用户id" })
        managerId?: number;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        signInIds?: string[];
        projectIdList?: string[];
        @ApiProperty({ description: "签到工地排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        signInTimeSort?: 'DESC' | 'ASC';
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class SignInNoBodyProjectListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 签到工地数-'signInProject', 签到人员数-'signInPerson', 签到次数-'signInTotal', 无人签到工地-'signInNoBodyProject', 工地平均签到次数-'signInAvgProject' " })
        name?: string;
        @ApiProperty({ description: "用户id" })
        managerId?: number;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        projectIds?: string[];
        @ApiProperty({ description: "无人签到天数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        signInNoBodySort?: 'DESC' | 'ASC';
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class StatisticsSignInRes {
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "签到工地数" })
        signInProject: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "签到人数" })
        signInPerson: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "签到次数" })
        signInTotal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "工地平均签到次数" })
        signInAvgProject: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "≥n天无人签到工地数" })
        signInNoBodyProject: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "无人签到天数" })
        signInNoBodyProjectDay?: number;
    }

    export class SignInProject {
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "工地负责人名" })
        projectManagerName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司名" })
        linkCompanyName?: string;
        @ApiProperty({ description: "签到人数" })
        signInPersonNum: number;
        @ApiProperty({ description: "签到次数" })
        signInNum: number;
    }

    export class SignInPerson {
        @ApiProperty({ description: "用户id" })
        managerId?: number;
        @ApiProperty({ description: "用户昵称" })
        nickName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司名" })
        linkCompanyName?: string;
        @ApiProperty({ description: "签到工地数" })
        signInProjectNum: number;
        @ApiProperty({ description: "签到次数" })
        signInNum: number;
    }

    export class SignInTotal {
        @ApiProperty({ description: "用户id" })
        managerId?: number;
        @ApiProperty({ description: "用户昵称" })
        nickName?: string;
        @ApiProperty({ description: "工地id" })
        projectId?: string;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "工地负责人名" })
        projectManagerName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司名" })
        linkCompanyName?: string;
        @ApiProperty({ description: "人脸图片" })
        faceOssUrl: string;
        @ApiProperty({ description: "签到图片" })
        signInPhoto: CorpReport.Img;
        @ApiProperty({ description: "签到时间" })
        signInTime: string;
    }

    export class SignInNoBodyProject extends CorpReport.Company{
        @ApiProperty({ description: "工地id" })
        projectId?: string;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "工地负责人名" })
        projectManagerName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司名" })
        linkCompanyName?: string;
    }

    export class SignInProjectRes {
        @ApiProperty({ description: "数据列表", type: [SignInProject] })
        items: SignInProject[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

    export class SignInPersonRes {
        @ApiProperty({ description: "数据列表", type: [SignInPerson] })
        items: SignInPerson[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

    export class SignInTotalRes {
        @ApiProperty({ description: "数据列表", type: [SignInTotal] })
        items: SignInTotal[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

    export class SignInNoBodyProjectRes {
        @ApiProperty({ description: "数据列表", type: [SignInNoBodyProject] })
        items: SignInNoBodyProject[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

}