import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "./corp-report.dto";
import { IsNotEmpty } from "class-validator";
import { Detection } from "../../../camera/project-detection/dto/detection.dto";

export namespace CorpReportTidiness {

    export const ModelList = ['tidinessTotal'];

    export class TidinessType {
        'good' = { name: '优', id: 'good' };
        'middle' = { name: '良', id: 'middle' };
        'bad' = { name: '差', id: 'bad' };
        'unknown' = { name: '未知', id: 'unknown' };
        constructor() {
            this.good = this.good;
            this.middle = this.middle;
            this.bad = this.bad;
            this.unknown = this.unknown;
        }
        getValueByCode?(code: string) {
            switch (String(code)) {
                case '1': return this.good.id;
                case '2': return this.middle.id;
                case '3': return this.bad.id;
                case '4': return this.unknown.id;
            }
        }
        getGroups?() {
            return {
                'good': { name: '优', id: 'good' },
                'middle': { name: '良', id: 'middle' },
                'bad': { name: '差', id: 'bad' },
                'unknown': { name: '未知', id: 'unknown' }
            }
        }
        getEnum?() {
            return [
                { name: '优', id: 'good' },
                { name: '良', id: 'middle' },
                { name: '差', id: 'bad' },
                { name: '未知', id: 'unknown' }
            ]
        }
    }


    export class TidinessDetailReq extends Detection.DetectionDetailReq {
        @ApiProperty({ description: "集团id" })
        deptId?: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期" })
        date: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期类型:day-天,week-周,month-月", enum: ['day', 'week', 'month'] })
        dayType: 'day' | 'week' | 'month';
        @ApiProperty({ description: "查询部门切片类型(real_time实时、history历史)" })
        deptDateType: 'real_time' | 'history';
    }

    export class TidinessStatisticsRes {
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "识别总数" })
        tidinessTotal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "图表" })
        tidinessTable: CorpReport.DeliveryStatisticsRes;
    }

    export class TidinessDetailOptionsRes {
        @ApiProperty({ description: "整洁度", type: [CorpReport.InfoDto] })
        tidinessType: CorpReport.InfoDto[];
        @ApiProperty({ description: "工地负责人", type: [CorpReport.InfoDto] })
        projectManager: CorpReport.InfoDto[];
        @ApiProperty({ description: "所属公司", type: [CorpReport.InfoDto] })
        linkCompany: CorpReport.InfoDto[];
        constructor() {
            this.tidinessType = [];
            this.projectManager = [];
            this.linkCompany = [];
        }
    }

    export class TidinessDetailRes {
        @ApiProperty({ description: "数据列表", type: [Detection.DetectionDetail] })
        items: Detection.DetectionDetail[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

}