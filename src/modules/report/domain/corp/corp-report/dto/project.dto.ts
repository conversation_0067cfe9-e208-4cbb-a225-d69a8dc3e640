import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "./corp-report.dto";
import { IsNotEmpty } from "class-validator";
import { Project } from "../../../bgw/project/dto/project.dto";

export namespace CorpReportProject {

    export const ModelList = ['projectTotal', 'projectOverdue', 'projectNormal', 'projectInactive', 'projectInactiveByDay'];

    export class ProjectType {
        'commonProject' = { name: '普通工地', value: 'commonProject' };
        'wisdomProject' = { name: '智慧工地', value: 'wisdomProject' };
        constructor() {
            this.commonProject = this.commonProject;
            this.wisdomProject = this.wisdomProject;
        }
        getGroups?() {
            return {
                'commonProject': { name: '普通工地', value: 'commonProject' },
                'wisdomProject': { name: '智慧工地', value: 'wisdomProject' }
            }
        }
        getEnum?() {
            return [
                { name: '普通工地', value: 'commonProject' },
                { name: '智慧工地', value: 'wisdomProject' }
            ]
        }
    }

    export class ProjectDetailReq extends Project.ProjectDetailReq {
        @ApiProperty({ description: "集团id" })
        deptId?: string;
        @ApiProperty({ description: "查询部门切片类型(real_time实时、history历史)" })
        deptDateType: 'real_time' | 'history';
        @IsNotEmpty()
        @ApiProperty({ description: "日期" })
        date: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期类型:day-天,week-周,month-月", enum: ['day', 'week', 'month'] })
        dayType: 'day' | 'week' | 'month';
        @ApiProperty({ description: "查询类型 工地总数-'projectTotal',逾期工地-'projectOverdue',正常施工-'projectNormal',无人施工-'projectInactive',≥n天无人施工-'projectInactiveByDay'" })
        name?: string;
        projectInactiveDay?: number;
    }

    export class ProjectStatisticsRes {
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "工地总数" })
        projectTotal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "逾期工地" })
        projectOverdue: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "正常施工" })
        projectNormal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "无人施工" })
        projectInactive: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "≥n天无人施工" })
        projectInactiveByDay: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ description: "无人施工天数" })
        projectInactiveDay?: number;
    }

    export class ProjectDetailOptionsRes {
        @ApiProperty({ description: "工地类型", type: [CorpReport.InfoDto] })
        projectType: CorpReport.InfoDto[];
        @ApiProperty({ description: "施工状态", type: [CorpReport.InfoDto] })
        isWork: CorpReport.InfoDto[];
        @ApiProperty({ description: "逾期状态", type: [CorpReport.InfoDto] })
        isOverdue: CorpReport.InfoDto[];
        @ApiProperty({ description: "工地负责人", type: [CorpReport.InfoDto] })
        projectManager: CorpReport.InfoDto[];
        @ApiProperty({ description: "所属公司", type: [CorpReport.InfoDto] })
        linkCompany: CorpReport.InfoDto[];
        constructor() {
            return {
                projectType: [],
                isWork: [],
                isOverdue: [],
                projectManager: [],
                linkCompany: [],
            }
        }
    }

    export class ProjectDetailRes {
        @ApiProperty({ description: "数据列表", type: [Project.ProjectDetail] })
        items: Project.ProjectDetail[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

}