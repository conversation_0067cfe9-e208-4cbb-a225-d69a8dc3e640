import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export namespace CorpReport {

    export class StatisticsReq {
        @ApiProperty({ description: "集团id" })
        deptId?: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期" })
        date?: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期类型:day-天,week-周,month-月", enum: ['day', 'week', 'month'] })
        dayType?: 'day' | 'week' | 'month';
        @ApiProperty({ description: "查询部门切片类型(real_time实时、history历史)" })
        deptDateType: 'real_time' | 'history';
    }

    export class Details<T> {
        @ApiProperty({ description: "范畴:横坐标(时间)" })
        category: string[];
        @ApiProperty({ description: "分组(多线图)", required: false })
        groups?: T;
        @ApiProperty({ description: "数据:纵坐标" })
        data: number[] | { [key in keyof T]: number[] };
    }

    export class GroupDetails<T> {
        @ApiProperty({ description: "分组(多线图)", required: false })
        groups?: T;
        @ApiProperty({ description: "数据：占比、数量" })
        data: { [key in keyof T]: { rate: number, count: number } };
    }


    export class DeliveryStatisticsRes<T = unknown> {
        @ApiProperty({ description: "分组类型", enum: ['day', 'week', 'month'] })
        type: 'day' | 'week' | 'month';
        @ApiProperty({ description: "模块名" })
        name?: string;
        @ApiProperty({ description: "总数", required: false })
        total?: number;
        @ApiProperty({ description: "率", required: false })
        rate?: number | string;
        @ApiProperty({ description: "环比", required: false })
        sequential?: number | string;
        @ApiProperty({ description: "详情", type: Details<T> })
        details?: Details<T> | GroupDetails<T>;
    }


    export class InfoDto {
        @ApiProperty({ description: "id" })
        id?: number | string;
        @ApiProperty({ description: "昵称" })
        name: string;
        @ApiProperty({ description: "值" })
        value?: string;
    }

    export class Company {
        @ApiProperty({ type: InfoDto, description: "用户昵称" })
        manager?: InfoDto;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ type: InfoDto, description: "工地负责人" })
        projectManager?: InfoDto
        @ApiProperty({ type: InfoDto, description: "所属公司" })
        linkCompany?: InfoDto
    }

    export type Img = {
        mediaId: string;
        mediaType: string;
        mediaUri: string;
        mediaResourceUri: string;
        creationDate: string;
        address: string;
    }

    class Distribute {
        @ApiProperty({ description: "地区deptId" })
        deptId: string;
        @ApiProperty({ description: "地区名" })
        deptName: string;
        @ApiProperty({ description: "总数" })
        total: number;
        @ApiProperty({ description: "百分比", required: false })
        percentage?: number;
    }

    export class DistributeRes {
        @ApiProperty({ description: "数据分布", type: [Distribute] })
        list: Distribute[];
    }

    export class SearchListOptionsRes {
        @ApiProperty({ type: [InfoDto], description: "用户昵称" })
        manager?: InfoDto[];
        @ApiProperty({ type: [InfoDto], description: "工地负责人" })
        projectManager?: InfoDto[]
        @ApiProperty({ type: [InfoDto], description: "所属公司" })
        company?: InfoDto[]
        @ApiProperty({ type: [InfoDto], description: "报告人" })
        report?: InfoDto[];
        @ApiProperty({ type: [InfoDto], description: "问题类型" })
        problemType?: InfoDto[];
        @ApiProperty({ type: [InfoDto], description: "问题状态", enum: [{ id: 0, name: '误判' }, { id: 1, name: '待处理' }, { id: 2, name: '处理中' }, { id: 3, name: '已处理' }, { id: 4, name: '非工作人员' }] })
        problemStatus?: InfoDto[];
    }

    export class SearchListOptionsReq extends StatisticsReq {
        @ApiProperty({ description: "选择列表", enum: ['signInProjectList', 'signInPersonList', 'signInTotalList', 'signInNoBodyProjectList', 'wisdomProjectList', 'patrolTotalList', 'patrolProblemNumList'] })
        options: string
    }

    const modelName = ['deviceTotal', 'deviceInstall', 'deviceIdle', 'deviceOnline', 'deviceOffline', 'deviceOfflineByDay',
        'projectTotal', 'projectOverdue', 'projectNormal', 'projectInactive', 'projectInactiveByDay',
        'tidinessTotal',
        'securityTotal',
        'signInProject', 'signInPerson', 'signInTotal', 'signInNoBodyProject', 'wisdomProject', 'patrolTotal', 'patrolProblemNum']

    export class SearchOptionsReq extends StatisticsReq {
        @ApiProperty({ description: "选择列表", enum: modelName })
        options: string;
        deviceOfflineDay?: number;
        projectInactiveDay?: number;
        signInNoBodyProjectDay?: number;
    }

    export class SearchOptionsRes {
        @ApiProperty({ type: InfoDto, description: "地区id" })
        deptId: number;
        @ApiProperty({ type: InfoDto, description: "地区名" })
        deptName: string;
        @ApiProperty({ description: "总数" })
        total: number;
        @ApiProperty({ description: "百分比" })
        percentage?: number;
    }

    export class deliverKanbanSetReq {
        @ApiProperty({ description: "集团id" })
        deptId?: string;
        @ApiProperty({ description: "日期" })
        date?: string;
        @ApiProperty({ description: "日期类型:day-天,week-周,month-月", enum: ['day', 'week', 'month'] })
        dayType?: 'day' | 'week' | 'month';
        @ApiProperty({ description: "设备离线天数" })
        deviceOfflineDay?: number;
        @ApiProperty({ description: "工地无人施工天数" })
        projectInactiveDay?: number;
        @ApiProperty({ description: "工地无人签到天数" })
        signInNoBodyProjectDay?: number;
    }

    export class deliverKanbanSetRes {
        @ApiProperty({ description: "设备离线天数" })
        deviceOfflineByDay: number;
        @ApiProperty({ description: "工地无人施工天数" })
        projectInactiveByDay: number;
        @ApiProperty({ description: "工地无人签到天数" })
        signInNoBodyProjectDay: number;
    }

    export class DimDeptData {
        @ApiProperty({ description: "直接子部门id列表" })
        directChildDepartmentIdList: string[];
        @ApiProperty({ description: "所有子部门id列表" })
        departmentIdList: string[];
        @ApiProperty({ description: "公司id列表" })
        companyIdList: string[];
        @ApiProperty({ description: "工地id列表" })
        projectIdList?: string[];
        @ApiProperty({ description: "管理员id列表" })
        managerIdList?: string[];
        @ApiProperty({ description: "设备id列表" })
        deviceIdList?: string[];
        @ApiProperty({ description: "智慧工地（绑定设备的工地）id列表" })
        aiProjectIdList?: string[];
    }

}