import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "./corp-report.dto";

export namespace CorpReportPatrol {
    
    export const ModelList = ['wisdomProject', 'patrolProject', 'patrolTotal', 'patrolAvgProjectNum', 'patrolProblemNum', 'problemProjectRate', 'problemDealRate', 'problemDealTime'];

    export class PatrolWisdomProjectListOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "工地负责人" })
        projectManager: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
    }
    export class PatrolTotalOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "用户昵称" })
        manager: CorpReport.InfoDto[];
        @ApiProperty({ type: [CorpReport.InfoDto], description: "工地负责人" })
        projectManager: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
    }
    export class PatrolProblemNumListOptionsRes {
        @ApiProperty({ type: [CorpReport.InfoDto], description: "工地负责人" })
        projectManager: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "所属公司" })
        linkCompany: CorpReport.InfoDto[]
        @ApiProperty({ type: [CorpReport.InfoDto], description: "报告人" })
        report: CorpReport.InfoDto[];
        @ApiProperty({ type: [CorpReport.InfoDto], description: "问题类型" })
        problemType: CorpReport.InfoDto[];
        @ApiProperty({ type: [CorpReport.InfoDto], description: "问题状态", enum: [{ id: 0, name: '误判' }, { id: 1, name: '待处理' }, { id: 2, name: '处理中' }, { id: 3, name: '已处理' }, { id: 4, name: '非工作人员' }] })
        problemStatus: CorpReport.InfoDto[];
    }

    export class PatrolProblemNumListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 智慧工地-'wisdomProject', 巡检工地-'patrolProject', 巡检总次数-'patrolTotal', 工地平均巡检次数-'patrolAvgProjectNum', 巡检问题数-'patrolProblemNum', 问题工地占比-'problemProjectRate', 问题处理率-'problemDealRate', 平均处理时长-'problemDealTime' " })
        name?: string;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        problemIdList?: string[];
        projectIdList?: string[];
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "报告人" })
        reportId?: number;
        @ApiProperty({ description: "问题类型" })
        problemType?: number;
        @ApiProperty({ description: "问题状态" })
        problemStatus?: number;
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class PatrolTotalListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 智慧工地-'wisdomProject', 巡检工地-'patrolProject', 巡检总次数-'patrolTotal', 工地平均巡检次数-'patrolAvgProjectNum', 巡检问题数-'patrolProblemNum', 问题工地占比-'problemProjectRate', 问题处理率-'problemDealRate', 平均处理时长-'problemDealTime' " })
        name?: string;
        @ApiProperty({ description: "用户id" })
        managerId?: number;
        patrolIdList?: string[];
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        projectIdList?: string[];
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "问题处理率排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        patrolTimeSort?: 'DESC' | 'ASC';
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class PatrolWisdomProjectListReq extends CorpReport.StatisticsReq{
        @ApiProperty({ description: "查询类型 智慧工地-'wisdomProject', 巡检工地-'patrolProject', 巡检总次数-'patrolTotal', 工地平均巡检次数-'patrolAvgProjectNum', 巡检问题数-'patrolProblemNum', 问题工地占比-'problemProjectRate', 问题处理率-'problemDealRate', 平均处理时长-'problemDealTime' " })
        name?: string;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        projectIdList?: string[];
        patrolIdList?: string[];
        problemIdList?: string[];
        startTime?: string;
        endTime?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "公司id" })
        linkCompanyId?: number;
        findProblemCount?: number;
        patrolCount?: number;
        @ApiProperty({ description: "巡检人数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        patrolPersonSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "巡检次数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        patrolTotalSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "发现问题数排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        patrolProblemSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "问题处理率排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        problemDealSort?: 'DESC' | 'ASC';
        @ApiProperty({ description: "平均处理时长排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        problemAvgDealTimeSort?: 'DESC' | 'ASC';
        @ApiProperty({description:'默认json', enum:["csv", "json"]})
        format?: "csv" | 'json'
        @ApiProperty()
        pageNo?: number;
        @ApiProperty()
        pageSize?: number;
    }

    export class StatisticsPatrolRes {
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "智慧工地数" })
        wisdomProject: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "巡检工地数" })
        patrolProject: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "巡检总次数" })
        patrolTotal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "工地平均巡检次数" })
        patrolAvgProjectNum: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "问题工地占比" })
        problemProjectRate: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "巡检问题数" })
        patrolProblemNum: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "问题处理率" })
        problemDealRate: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "平均处理时长（小时）" })
        problemDealTime: CorpReport.DeliveryStatisticsRes;
    }

    export class WisdomProject{
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "工地负责人" })
        projectManagerName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司" })
        linkCompanyName?: string;
        @ApiProperty({ description: "巡检人数" })
        patrolPersonNum: number;
        @ApiProperty({ description: "巡检次数" })
        patrolNum: number;
        @ApiProperty({ description: "发现问题数" })
        problemNum: number;
        @ApiProperty({ description: "问题处理率" })
        problemDealRate: number;
        @ApiProperty({ description: "平均处理时长" })
        problemAvgDealTime: number;
    }

    export class PatrolTotal{
        @ApiProperty({ description: "用户昵称id" })
        managerId?: number;
        @ApiProperty({ description: "用户昵称" })
        managerName?: string;
        projectId?: number;
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "工地负责人" })
        projectManagerName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司" })
        linkCompanyName?: string;
        @ApiProperty({ description: "巡检时间" })
        patrolTime: string;
    }

    export class PatrolProblemNum{
        @ApiProperty({ description: "工地地址" })
        projectAddress?: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId?: number;
        @ApiProperty({ description: "工地负责人" })
        projectManagerName?: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId?: number;
        @ApiProperty({ description: "所属公司" })
        linkCompanyName?: string;
        @ApiProperty({ description: "问题图片" })
        problemPhoto: CorpReport.Img
        @ApiProperty({ description: "问题状态" })
        problemStatus: number;
        @ApiProperty({ description: "问题分类" })
        problemType: string;
        @ApiProperty({ description: "问题描述" })
        problemDescription: string;
        @ApiProperty({ description: "报告人id" })
        reportId: number;
        @ApiProperty({ description: "报告人name" })
        reportName: string;
        @ApiProperty({ description: "报告日期" })
        reportDate: string;
        @ApiProperty({ description: "整改日期" })
        solvedDate: string;
        @ApiProperty({ description: "整改图片" })
        solvedPhoto: CorpReport.Img;
        @ApiProperty({ description: "整改描述" })
        solvedDescription: string;
    }

    export class WisdomProjectRes {
        @ApiProperty({ description: "数据列表", type: [WisdomProject] })
        items: WisdomProject[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

    export class PatrolTotalRes {
        @ApiProperty({ description: "数据列表", type: [PatrolTotal] })
        items: PatrolTotal[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

    export class PatrolProblemNumRes {
        @ApiProperty({ description: "数据列表", type: [PatrolProblemNum] })
        items: PatrolProblemNum[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

}