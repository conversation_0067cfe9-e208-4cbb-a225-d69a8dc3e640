import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "./corp-report.dto";
import { IsNotEmpty } from "class-validator";
import { Detection } from "../../../camera/project-detection/dto/detection.dto";

export namespace CorpReportSecurity {

    export const ModelList = ['securityTotal'];

    export class SecurityType {
        'smoke' = { name: '抽烟', id: 'smoke' };
        'workClothes' = { name: '未穿工服', id: 'workClothes' };
        'slippers' = { name: '穿拖鞋', id: 'slippers' };
        'electroMobiles' = { name: '停放电瓶车', id: 'electroMobiles' };
        constructor() {
            this.smoke = this.smoke;
            this.slippers = this.slippers;
            this.workClothes = this.workClothes;
            this.electroMobiles = this.electroMobiles;
        }
        
        getValueByCode?(code: string) {
            switch (String(code)) {
                case '0': return this.smoke.id;
                case '4': return this.workClothes.id;
                case '5': return this.slippers.id;
                case '7': return this.electroMobiles.id;
            }
        }

        getGroups?() {
            const groups: { [key: string]: { name: string, id: string } } = {};
            // 使用 TypeScript 的反射机制获取类的所有属性
            const properties = Object.getOwnPropertyNames(this);
            // 遍历属性并生成对应对象
            properties.forEach(property => {
                if (typeof this[property] === 'object' && !Array.isArray(this[property])) {
                    groups[property] = { name: this[property].name, id: this[property].id };
                }
            });
            return groups;
        }

        getEnum?() {
            const enumArray = [];
            // 使用 TypeScript 的反射机制获取类的所有属性
            const properties = Object.getOwnPropertyNames(this);
            // 遍历属性并生成对应对象添加到数组中
            properties.forEach(property => {
                if (typeof this[property] === 'object' && !Array.isArray(this[property])) {
                    enumArray.push({ name: this[property].name, id: this[property].id });
                }
            });
            return enumArray;
        }
    }

    export class SecurityDetailReq extends Detection.DetectionDetailReq {
        @ApiProperty({ description: "集团id" })
        deptId?: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期" })
        date: string;
        @IsNotEmpty()
        @ApiProperty({ description: "日期类型:day-天,week-周,month-月", enum: ['day', 'week', 'month'] })
        dayType: 'day' | 'week' | 'month';
        @ApiProperty({ description: "查询部门切片类型(real_time实时、history历史)" })
        deptDateType: 'real_time' | 'history';
    }

    export class SecurityStatisticsRes {
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "问题总数" })
        securityTotal: CorpReport.DeliveryStatisticsRes;
        @ApiProperty({ type: CorpReport.DeliveryStatisticsRes, description: "图表" })
        securityTable: CorpReport.DeliveryStatisticsRes;
    }

    export class SecurityDetailOptionsRes {
        @ApiProperty({ description: "报告类型", type: [CorpReport.InfoDto] })
        securityType: CorpReport.InfoDto[];
        @ApiProperty({ description: "工地负责人", type: [CorpReport.InfoDto] })
        projectManager: CorpReport.InfoDto[];
        @ApiProperty({ description: "所属公司", type: [CorpReport.InfoDto] })
        linkCompany: CorpReport.InfoDto[];
        constructor() {
            this.securityType = [];
            this.projectManager = [];
            this.linkCompany = [];
        }
    }

    export class SecurityDetailRes {
        @ApiProperty({ description: "数据列表", type: [Detection.DetectionDetail] })
        items: Detection.DetectionDetail[];
        @ApiProperty({ description: "数量" })
        total: number;
    }

}