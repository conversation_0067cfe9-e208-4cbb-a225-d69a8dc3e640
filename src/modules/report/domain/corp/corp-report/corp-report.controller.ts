import { Body, Controller, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { MyApiOkResponse, MyDefaultApiOkResponse, MyUser } from "@yqz/nest";
import { CorpReport } from "./dto/corp-report.dto";
import { CorpReportDevice } from "./dto/device.dto";
import { CorpReportPatrol } from "./dto/patrol.dto";
import { CorpReportProject } from "./dto/project.dto";
import { CorpReportSecurity } from "./dto/security.dto";
import { CorpReportSignIn } from "./dto/sign-in.dto";
import { CorpReportTidiness } from "./dto/tidiness.dto";
import { CorpReportService } from "./service/corp-report.service";

@ApiTags("corp-report")
@ApiBearerAuth()
@Controller("corp/report")
export class CorpReportController {
  constructor(
    private readonly corpReportService: CorpReportService
  ) { }

  @ApiTags("device")
  @ApiOperation({ summary: '设备概况' })
  @MyApiOkResponse(CorpReportDevice.DeviceStatisticsRes)
  @Post("device")
  async corpReportDevice(@Body() query: CorpReport.StatisticsReq , @MyUser() user) {
    return await this.corpReportService.corpReportDevice(query, user);
  }

  @ApiTags("device")
  @ApiOperation({ summary: '设备概况详情' })
  @MyApiOkResponse(CorpReportDevice.DeviceDetailRes)
  @Post("device/detail")
  async corpReportDeviceDetail(@Body() query: CorpReportDevice.DeviceDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportDeviceDetail(query, user);
  }

  @ApiTags("device")
  @ApiOperation({ summary: '设备概况详情筛选' })
  @MyApiOkResponse(CorpReportDevice.DeviceDetailOptionsRes)
  @Post("device/detail/options")
  async corpReportDeviceDetailOptions(@Body() query: CorpReportDevice.DeviceDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportDeviceDetailOptions(query, user);
  }

  @ApiTags("project")
  @ApiOperation({ summary: '工地概况' })
  @MyApiOkResponse(CorpReportProject.ProjectStatisticsRes)
  @Post("project")
  async corpReportProject(@Body() query: CorpReport.StatisticsReq , @MyUser() user) {
    return await this.corpReportService.corpReportProject(query, user);
  }

  @ApiTags("project")
  @ApiOperation({ summary: '工地概况详情' })
  @MyApiOkResponse(CorpReportProject.ProjectDetailRes)
  @Post("project/detail")
  async corpReportProjectDetail(@Body() query: CorpReportProject.ProjectDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportProjectDetail(query, user);
  }

  @ApiTags("project")
  @ApiOperation({ summary: '工地概况详情筛选' })
  @MyApiOkResponse(CorpReportProject.ProjectDetailOptionsRes)
  @Post("project/detail/options")
  async corpReportProjectDetailOptions(@Body() query: CorpReportProject.ProjectDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportProjectDetailOptions(query, user);
  }

  @ApiTags("tidiness")
  @ApiOperation({ summary: '工地整洁度' })
  @MyApiOkResponse(CorpReportTidiness.TidinessStatisticsRes)
  @Post("tidiness")
  async corpReportTidiness(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return await this.corpReportService.corpReportTidiness(query, user);
  }

  @ApiTags("tidiness")
  @ApiOperation({ summary: '工地整洁度详情' })
  @MyApiOkResponse(CorpReportTidiness.TidinessDetailRes)
  @Post("tidiness/detail")
  async corpReportTidinessDetail(@Body() query: CorpReportTidiness.TidinessDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportTidinessDetail(query, user);
  }

  @ApiTags("tidiness")
  @ApiOperation({ summary: '工地整洁度详情筛选' })
  @MyApiOkResponse(CorpReportTidiness.TidinessDetailOptionsRes)
  @Post("tidiness/detail/options")
  async corpReportTidinessDetailOptions(@Body() query: CorpReportTidiness.TidinessDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportTidinessDetailOptions(query, user);
  }

  @ApiTags("security")
  @ApiOperation({ summary: '文明施工' })
  @MyApiOkResponse(CorpReportSecurity.SecurityStatisticsRes)
  @Post("security")
  async corpReportSecurity(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return await this.corpReportService.corpReportSecurity(query, user);
  }

  @ApiTags("security")
  @ApiOperation({ summary: '文明施工详情' })
  @MyApiOkResponse(CorpReportSecurity.SecurityDetailRes)
  @Post("security/detail")
  async corpReportSecurityDetail(@Body() query: CorpReportSecurity.SecurityDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportSecurityDetail(query, user);
  }

  @ApiTags("security")
  @ApiOperation({ summary: '文明施工详情筛选' })
  @MyApiOkResponse(CorpReportSecurity.SecurityDetailOptionsRes)
  @Post("security/detail/options")
  async corpReportSecurityDetailOptions(@Body() query: CorpReportSecurity.SecurityDetailReq, @MyUser() user) {
    return await this.corpReportService.corpReportSecurityDetailOptions(query, user);
  }


  @ApiTags("signin")
  @ApiOperation({ summary: '服务签到报表' })
  @MyApiOkResponse(CorpReportSignIn.StatisticsSignInRes)
  @Post("signin")
  async corpReportSignIn(@Body() query: CorpReport.StatisticsReq , @MyUser() user) {
    return this.corpReportService.corpReportSignIn(query, user);
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '签到工地数' })
  @MyApiOkResponse(CorpReportSignIn.SignInProjectRes)
  @Post("signin/project")
  async corpReportSignInProject(@Body() query: CorpReportSignIn.SignInProjectListReq, @MyUser() user) {
    const res = await this.corpReportService.corpReportSignProjectList(query, user);
    // ControllerUtil.toDataOrFile(response, res)
    return res
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '签到工地数下拉列表' })
  @MyApiOkResponse(CorpReportSignIn.SignInProjectListOptionsRes)
  @Post("signin/project/options")
  async corpReportSignInProjectListOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportSignInProjectListOptions(query, user)
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '签到人数' })
  @MyApiOkResponse(CorpReportSignIn.SignInPersonRes)
  @Post("signin/person")
  async corpReportSignInPerson(@Body() query: CorpReportSignIn.SignInPersonListReq, @MyUser() user) {
    const res = await this.corpReportService.corpReportSignPersonList(query, user);
    return res
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '签到人数下拉列表' })
  @MyApiOkResponse(CorpReportSignIn.SignInPersonListOptionsRes)
  @Post("signin/person/options")
  async corpReportSignInPersonListOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportSignInPersonListOptions(query, user)
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '签到次数' })
  @MyApiOkResponse(CorpReportSignIn.SignInTotalRes)
  @Post("signin/total")
  async corpReportSignInTotal(@Body() query: CorpReportSignIn.SignInTotalListReq, @MyUser() user) {
    const res = await this.corpReportService.corpReportSignTotalList(query, user);
    return res
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '签到次数下拉列表' })
  @MyApiOkResponse(CorpReportSignIn.SignInTotalListOptionsRes)
  @Post("signin/total/options")
  async corpReportSignInTotalListOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportSignInTotalListOptions(query, user)
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '≥n天无人签到工地数' })
  @MyApiOkResponse(CorpReportSignIn.SignInNoBodyProjectRes)
  @Post("signin/project/nobody")
  async corpReportSignInNoBodyProject(@Body() query: CorpReportSignIn.SignInNoBodyProjectListReq, @MyUser() user) {
    const res = await this.corpReportService.corpReportSignInNoBodyProjectList(query, user);
    return res
  }

  @ApiTags("signin")
  @ApiOperation({ summary: '无人签到工地数下拉列表' })
  @MyApiOkResponse(CorpReportSignIn.SignInNoBodyProjectListOptionsRes)
  @Post("signin/project/nobody/options")
  async corpReportSignInNoBodyProjectListOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportSignInNoBodyProjectListOptions(query, user)
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '工地巡检报表' })
  @MyApiOkResponse(CorpReportPatrol.StatisticsPatrolRes)
  @Post("patrol")
  async corpReportPatrol(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportPatrol(query, user);
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '智慧工地数' })
  @MyApiOkResponse(CorpReportPatrol.WisdomProjectRes)
  @Post("patrol/wisdom/project")
  async corpReportPatrolWisdomProject(@Body() query: CorpReportPatrol.PatrolWisdomProjectListReq, @MyUser() user) {
    const res = await this.corpReportService.searchPatrolProjectList(query, user);
    return res
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '智慧工地数下拉列表' })
  @MyApiOkResponse(CorpReportPatrol.PatrolWisdomProjectListOptionsRes)
  @Post("patrol/wisdom/project/options")
  async corpReportPatrolWisdomProjectListOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportPatrolWisdomProjectListOptions(query, user);
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '巡检总次数' })
  @MyApiOkResponse(CorpReportPatrol.PatrolTotalRes)
  @Post("patrol/total")
  async corpReportPatrolTotal(@Body() query: CorpReportPatrol.PatrolTotalListReq, @MyUser() user) {
    const res = await this.corpReportService.searchPatrolTotalList(query, user);
    return res
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '巡检总次数下拉列表' })
  @MyApiOkResponse(CorpReportPatrol.PatrolTotalOptionsRes)
  @Post("patrol/total/options")
  async corpReportPatrolTotalOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportPatrolTotalOptions(query, user);
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '巡检问题数' })
  @MyApiOkResponse(CorpReportPatrol.PatrolProblemNumRes)
  @Post("patrol/problem/num")
  async corpReportPatrolProblemNum(@Body() query: CorpReportPatrol.PatrolProblemNumListReq, @MyUser() user) {
    const res = await this.corpReportService.searchPatrolProblemList(query, user);
    return res
  }

  @ApiTags("patrol")
  @ApiOperation({ summary: '巡检问题数下拉列表' })
  @MyApiOkResponse(CorpReportPatrol.PatrolProblemNumListOptionsRes)
  @Post("patrol/problem/num/options")
  async corpReportPatrolProblemNumListOptions(@Body() query: CorpReport.StatisticsReq, @MyUser() user) {
    return this.corpReportService.corpReportPatrolProblemNumListOptions(query, user);
  }

  @ApiOperation({ summary: '地区分布统计' })
  @MyApiOkResponse(CorpReport.SearchOptionsRes)
  @Post("search/options")
  async corpReportSearchOptions(@Body() query: CorpReport.SearchOptionsReq, @MyUser() user) {
    return this.corpReportService.corpReportSearchOptions(query, user)
  }

  @ApiOperation({ summary: '看板配置查询' })
  @MyApiOkResponse(CorpReport.deliverKanbanSetRes)
  @Post("deliver/set/search")
  async corpReportSetSearch(@Body() query: CorpReport.deliverKanbanSetReq,  @MyUser() user) {
    return this.corpReportService.corpReportSetSearch(query, user)
  }

  @ApiOperation({ summary: '看板配置更新' })
  @MyDefaultApiOkResponse()
  @Post("deliver/set/update")
  async corpReportSetUpdate(@Body() query: CorpReport.deliverKanbanSetReq, @MyUser() user) {
    await this.corpReportService.corpReportSetUpdate(query, user)
    return
  }

}