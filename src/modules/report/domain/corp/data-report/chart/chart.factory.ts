import { Injectable } from "@nestjs/common";
import { Chart } from "./chart.type";
import { YqzException } from "@yqz/nest";
import * as _ from "lodash";

/**
 * @description 图表工厂
 */
@Injectable()
export class ChartFactory {

    //处理ChartType和处理方法的映射关系
    private chartHandlers: Readonly<{ [K in Chart.ChartType]: (req: Chart.ModuleDataReq<K>) => Chart.ChartResMapping[K]; }> = {
        [Chart.ChartType.Total]: this.createTotalChart.bind(this), //数值
        [Chart.ChartType.Line]: this.createLineChart.bind(this), //折线图
        [Chart.ChartType.Bar]: this.createLineChart.bind(this), //柱状图
        [Chart.ChartType.MultiLine]: this.createMultiLineChart.bind(this), //多维折线图
        [Chart.ChartType.Ring]: this.createPieChart.bind(this), //环形图
        [Chart.ChartType.Rose]: this.createPieChart.bind(this), //玫瑰图
        [Chart.ChartType.Pie]: this.createPieChart.bind(this), //饼图
        [Chart.ChartType.BarDistribute]: this.createPieChart.bind(this), //柱状图(分布)
    };

    /**
     * 创建图表数据
     * @param req 图表请求数据
     * @returns 对应图表数据
     */
    async createChart<T extends Chart.ChartType>(req: Chart.ModuleDataReq<T>): Promise<Chart.ChartResMapping[T]> {
        const handler = this.chartHandlers[req.chartType];
        if (!handler) throw new YqzException(`Unsupported chart type: ${req.chartType}`);
        return handler(req);
    }

    /**
     * 计算 主数据
     * @param param0 
     */
    private processDescValue<T extends Chart.ChartType>({ chartType, data }: { chartType: T, data: Chart.DataMapping[T] }): number {
        //数值 (本周期数据)
        if (chartType === Chart.ChartType.Total && typeof data === 'number') return data;
        //折线图 || 柱状图 (本周期最后一次数据)
        if ((chartType === Chart.ChartType.Line || chartType === Chart.ChartType.Bar) && Array.isArray(data)) {
            const dataValue = data[data.length - 1];//最后一次数据
            if (typeof dataValue === 'number') return dataValue;//折线图 || 柱状图 为 number
        }
        //多维折线图 (本周期最后一次数据总和)
        if (chartType === Chart.ChartType.MultiLine && Array.isArray(data)) {
            const dataValue = [];//最后一次数据
            data.forEach(res => { if (Array.isArray(res.value)) dataValue.push(res.value[res.value.length - 1]); });
            if (Array.isArray(dataValue)) return _.sum(dataValue.map(value => typeof value === 'number' ? value : 0));//多维折线图 value 为 number[]
        }
        //环形图 || 玫瑰图 || 饼图 (本周期数据总和)
        if ((Chart.TypeOption[Chart.Type.DistributeChart] || []).includes(chartType) && Array.isArray(data)) {
            return _.sum(data.map(res => typeof res.value === 'number' ? res.value : 0)); // 数据总和
        }
        return 0;
    }

    /**
     * 计算 环比
     * @param param0 
     * @returns 
     */
    private calculateRatio<T extends Chart.ChartType>({ chartType, data, preData }: { chartType: T, data: Chart.DataMapping[T], preData: Chart.DataMapping[T] }): string {
        if (preData) {//得有上期数据才行哦！
            //数值 (本周期数据 : 上个周期数据)
            if (chartType === Chart.ChartType.Total && typeof data === 'number' && typeof preData === 'number' && preData !== 0) {
                return (data - preData) / preData * 100 + "%";
            }
            //折线图 || 柱状图 (本周期最后一次数据 : 上个周期最后一次数据)
            if ((chartType === Chart.ChartType.Line || chartType === Chart.ChartType.Bar) && Array.isArray(data) && Array.isArray(preData)) {
                const dataValue = data[data.length - 1];//最后一次数据
                const preDataValue = preData[preData.length - 1];//上个周期最后一次数据
                if (typeof dataValue === 'number' && typeof preDataValue === 'number' && preDataValue !== 0) {//折线图 || 柱状图 为 number
                    return (dataValue - preDataValue) / preDataValue * 100 + "%";
                }
            }
            //多维折线图 (本周期最后一次数据总和 : 上个周期最后一次数据总和)
            if (chartType === Chart.ChartType.MultiLine && Array.isArray(data) && Array.isArray(preData)) {
                const dataValue = [];//最后一次数据
                const preDataValue = [];//上个周期最后一次数据
                data.forEach(res => { if (Array.isArray(res.value)) dataValue.push(res.value[res.value.length - 1]); });
                preData.forEach(res => { if (Array.isArray(res.value)) preDataValue.push(res.value[res.value.length - 1]); });
                if (Array.isArray(dataValue) && Array.isArray(preDataValue) && _.sum(preDataValue) !== 0) {//多维折线图 value 为 number[]
                    const sumDataValue = _.sum(dataValue.map(value => typeof value === 'number' ? value : 0));
                    const sumPreDataValue = _.sum(preDataValue.map(value => typeof value === 'number' ? value : 0));
                    if (sumPreDataValue !== 0) return (sumDataValue - sumPreDataValue) / sumPreDataValue * 100 + "%";
                }
            }
            //环形图 || 玫瑰图 || 饼图 (本周期数据总和 : 上个周期数据总和)
            if ((Chart.TypeOption[Chart.Type.DistributeChart] || []).includes(chartType) && Array.isArray(data) && Array.isArray(preData)) {
                const dataValue = _.sum(data.map(res => typeof res.value === 'number' ? res.value : 0)); // 数据总和
                const preDataValue = _.sum(preData.map(res => typeof res.value === 'number' ? res.value : 0)); // 上个周期数据总和
                if (typeof dataValue === 'number' && typeof preDataValue === 'number' && preDataValue !== 0) {
                    return (dataValue - preDataValue) / preDataValue * 100 + "%";
                }
            }
        }
        return "--";
    }

    /**
     * 预处理数据 返回 header 和 desc
     * @param req 
     * @returns 
     */
    private processData<T extends Chart.ChartType>(req: Chart.ModuleDataReq<T>): { header: Chart.HeaderDataRes, desc: Chart.DescDataRes } {
        const { chartType, title, name, tips, actionType, needRatio, isReal, data, subData, preData } = req;
        const header = {
            title,
            name,
            tips: tips || "",
            actionType: actionType || Chart.ActionType.None,
            chartType
        };
        //计算 主数据
        const value = this.processDescValue<T>({ chartType, data });
        const desc = {
            value,
            isReal,
            subLabel: !_.isEmpty(subData) ? subData[0]?.label : undefined,
            subValue: !_.isEmpty(subData) ? subData[0]?.value : undefined
        }
        //计算 环比
        if ('Y' === needRatio) {
            const ratio = this.calculateRatio<T>({ chartType, data, preData });
            Object.assign(desc, { ratio });
        }
        return { header, desc };
    }

    /**
     * 创建数值数据
     * @param req 数值请求数据
     */
    private createTotalChart(req: Chart.ModuleDataReq<Chart.ChartType.Total>): Chart.ModuleTotalDataRes {
        const { header, desc } = this.processData(req);
        return { header, desc }
    }

    /**
     * 创建折线图/柱状图数据
     * @param req 折线图/柱状图请求数据
     */
    private createLineChart(req: Chart.ModuleDataReq<Chart.ChartType.Line | Chart.ChartType.Bar>): Chart.ModuleLineDataRes {
        const { chartType, data, category } = req;
        const { header, desc } = this.processData(req);
        const config = {
            chartType,
            category,  // x 轴分类
            data: Array.isArray(data) ? data : [],
        }
        return { header, desc, config };
    }

    /**
     * 创建多维折线图数据
     * @param req 多维折线图请求数据
     */
    private createMultiLineChart(req: Chart.ModuleDataReq<Chart.ChartType.MultiLine>): Chart.ModuleMultiLineDataRes {
        const { chartType, data, category } = req;
        const { header, desc } = this.processData(req);
        const config = {
            chartType,
            category, // x 轴分类
            data: Array.isArray(data) ? data : [],
        }
        return { header, desc, config };
    }

    /**
     * 创建环形图/玫瑰图/饼图数据
     * @param req 环形图/玫瑰图/饼图 请求数据
     */
    private createPieChart(req: Chart.ModuleDataReq<Chart.ChartType.Ring | Chart.ChartType.Rose | Chart.ChartType.Pie | Chart.ChartType.BarDistribute>): Chart.ModulePieDataRes {
        const { chartType, data, optionKey } = req;
        const { header, desc } = this.processData(req);
        const config = {
            chartType,
            optionKey,
            data: Array.isArray(data) ? data.map(res => {
                const total = Number(desc?.value || 0);
                const value = Number(res?.value || 0);
                //计算占比
                const rate = total !== 0 ? parseFloat(((value / total) * 100).toFixed(2)) : 0;
                return { ...res, rate }
            }) : [],
        }
        //柱状图默认为降序
        if (chartType === Chart.ChartType.BarDistribute) {
            config.data = _.sortBy(config.data, ['value', 'key']).reverse();
        } else {//其余为升序
            config.data = _.sortBy(config.data, ['value', 'key']);
        }
        return { header, desc, config };
    }
}