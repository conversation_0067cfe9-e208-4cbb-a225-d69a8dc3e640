import { ApiProperty } from "@nestjs/swagger";
import * as _ from "lodash";

export namespace Chart {

    export enum Type {//图表大类
        StatChart = 'stat_chart',//统计图
        DistributeChart = 'distribute_chart',//分布图
        TrendChart = 'trend_chart',//趋势图
        DistributeTrendChart = 'distribute_trend_chart',//分布趋势图
    }

    export enum ActionType {
        None = 'none',//无
        Action = 'action',//可操作
        Setting = 'setting',//可设置
        Form = 'form'//表单
    }

    export enum ChartType {
        Total = 'total',//数值
        Line = 'line',//折线图
        MultiLine = 'multi_line',//多维折线图
        Ring = 'ring',//环形图
        Rose = 'rose',//玫瑰图
        Pie = 'pie',//饼图
        Bar = 'bar',//柱状图(趋势)
        BarDistribute = 'bar_distribute',//柱状图(分布)
    }

    export const ChartTypeName: Readonly<{ [key in ChartType]: string }> = {
        [ChartType.Total]: '数值',
        [ChartType.Line]: '折线图',
        [ChartType.MultiLine]: '多维折线图',
        [ChartType.Bar]: "柱状图",
        [ChartType.Ring]: '环形图',
        [ChartType.Rose]: '玫瑰图',
        [ChartType.Pie]: "饼图",
        [ChartType.BarDistribute]: "柱状图"
    }

    export const TypeOption: Readonly<{ [key in Type]: ChartType[] }> = {
        [Type.StatChart]: [ChartType.Total],
        [Type.DistributeChart]: [ChartType.Ring, ChartType.Rose, ChartType.Pie, ChartType.BarDistribute],
        [Type.TrendChart]: [ChartType.Line, ChartType.Bar],
        [Type.DistributeTrendChart]: [ChartType.MultiLine],
    }

    export const ChartTypeOption = _.mapValues(ChartType, (value) => ({
        value,
        name: ChartTypeName[value as ChartType]
    }));

    export class HeaderDataRes {
        @ApiProperty({ description: '标题' })
        title: string;
        @ApiProperty({ description: '图表名' })
        name: string;
        @ApiProperty({ description: '提示' })
        tips: string;
        @ApiProperty({ description: '图表操作类型  none:无 detail:详情 action:可操作 form:表单, 默认detail' })
        actionType: ActionType;
        @ApiProperty({ description: '图表类型' })
        chartType: ChartType;
    }

    export class DescDataRes {
        @ApiProperty({ description: '环比' })
        ratio?: string;
        @ApiProperty({ description: '主数据' })
        value?: number;
        @ApiProperty({ description: '是否是实时数据' })
        isReal?: 'Y' | 'N';
        @ApiProperty({ description: '副数据标签' })
        subLabel?: string;
        @ApiProperty({ description: '副数据' })
        subValue?: number | string;
    }

    export class ModuleTotalDataRes {//总览模块数据（统计）
        header: HeaderDataRes;
        desc: DescDataRes;
    }

    export class ModuleLineDataRes {//图表模块数据(折线图/柱状图)
        header: HeaderDataRes;
        desc: DescDataRes;
        config: {
            chartType: ChartType.Line | ChartType.Bar;
            category: string[];
            data: DataMapping[ChartType.Line | ChartType.Bar];
        };
    }

    export class ModuleMultiLineDataRes {//图表模块数据(多维折线图)
        header: HeaderDataRes;
        desc: DescDataRes;
        config: {
            chartType: ChartType.MultiLine;
            category: string[];
            data: DataMapping[ChartType.MultiLine];
        };
    }

    export class ModulePieDataRes {//图表模块数据(饼图: 玫瑰图/环形图/饼图)
        header: HeaderDataRes;
        desc: DescDataRes;
        config: {
            chartType: ChartType.Rose | ChartType.Ring | ChartType.Pie | ChartType.BarDistribute;
            optionKey: string;
            data: DataMapping[ChartType.Rose | ChartType.Ring | ChartType.Pie | ChartType.BarDistribute];
        };
    }

    export class ModuleDataReq<T extends Chart.ChartType> {
        @ApiProperty({ description: '图表类型' })
        chartType: T;
        @ApiProperty({ description: '标题（概况展示）' })
        title: string;
        @ApiProperty({ description: '图表名（分析展示）' })
        name: string;
        @ApiProperty({ description: '本周期数据' })
        data: DataMapping[T];
        @ApiProperty({ description: 'x轴数据' })
        category?: string[];
        @ApiProperty({ description: '图表操作类型 none:无、detail:详情、action:可操作、form:表单, 默认none' })
        actionType: ActionType;
        @ApiProperty({ description: '提示' })
        tips?: string;
        @ApiProperty({ description: '是否需要计算环比 默认"N"' })
        needRatio?: 'Y' | 'N';
        @ApiProperty({ description: '是否是实时数据' })
        isReal?: 'Y' | 'N';
        @ApiProperty({ description: '上周期数据' })
        preData?: DataMapping[T];
        @ApiProperty({ description: '副数据' })
        subData?: {
            label: string;
            value: number;
        }[];
        @ApiProperty({ description: '筛选对应字段名' })
        optionKey?: string;
    }

    export type DataMapping = Readonly<{
        [ChartType.Total]: number,
        [ChartType.Line]: number[],//value为y轴数据
        [ChartType.Bar]: number[],//value为y轴数据
        [ChartType.MultiLine]: { key: string, name: string, value: number[] }[],//key表示折线图颜色, name折线数据, value折线数据y轴数据
        [ChartType.Ring]: { key: string, name: string, value: number, rate?: number, ratio?: string }[],
        [ChartType.Rose]: { key: string, name: string, value: number, rate?: number, ratio?: string }[],
        [ChartType.Pie]: { key: string, name: string, value: number, rate?: number, ratio?: string }[],
        [ChartType.BarDistribute]: { key: string, name: string, value: number, rate?: number, ratio?: string }[],
    }>

    export type ChartResMapping = Readonly<{
        [ChartType.Total]: ModuleTotalDataRes,
        [ChartType.Line]: ModuleLineDataRes,
        [ChartType.Bar]: ModuleLineDataRes,
        [ChartType.MultiLine]: ModuleMultiLineDataRes,
        [ChartType.Ring]: ModulePieDataRes,
        [ChartType.Rose]: ModulePieDataRes,
        [ChartType.Pie]: ModulePieDataRes,
        [ChartType.BarDistribute]: ModulePieDataRes,
    }>

    type ValueOf<T> = T[keyof T];

    //类型提取
    export type ChartResDto = ValueOf<ChartResMapping>;

}