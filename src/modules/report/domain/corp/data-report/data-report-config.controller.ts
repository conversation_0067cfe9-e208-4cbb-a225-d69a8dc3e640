import { Body, Controller, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtUserGw, MyApiOkResponse, MyDefaultApiOkResponse, MyUser } from "@yqz/nest";
import { DataReportConfigService } from "./service/data-report-config.service";
import { DateReportConfig } from "./dto/data-report-config.dto";

/**
 * @summary 数据看板配置相关接口
 * @class DataReportConfigController
 */
@ApiTags("data-report-config")
@ApiBearerAuth()
@Controller("data/report/config")
export class DataReportConfigController {
    constructor(
        private readonly dataReportConfigService: DataReportConfigService
    ) { }

    @ApiOperation({ summary: '数据看板菜单配置选项查询' })
    // @MyApiOkResponse(DateReportConfig.Test)
    @Post("menu/option")
    async menuConfigOptionSearch(@Body() body: DateReportConfig.MenuConfigSearchReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body.companyId;
        return await this.dataReportConfigService.menuConfigOptionSearch(body);
    }

    @ApiOperation({ summary: '数据看板菜单配置查询' })
    // @MyApiOkResponse(DateReportConfig.Test)
    @Post("menu/search")
    async menuConfigSearch(@Body() body: DateReportConfig.MenuConfigSearchReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || body.companyId;
        return await this.dataReportConfigService.menuConfigSearch(body);
    }

    @ApiOperation({ summary: '数据看板菜单配置修改' })
    // @MyApiOkResponse(DateReportConfig.Test)
    @Post("menu/update")
    async menuConfigUpdate(@Body() body: DateReportConfig.MenuConfigUpdateReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || "";
        return await this.dataReportConfigService.menuConfigUpdate(body);
    }

    @ApiOperation({ summary: '数据看板数据配置选项查询' })
    // @MyApiOkResponse(DateReportConfig.Test)
    @Post("data/option")
    async dataConfigOptionSearch(@Body() body: DateReportConfig.DataConfigSearchReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || "";
        return await this.dataReportConfigService.dataConfigOptionSearch(body);
    }

    @ApiOperation({ summary: '数据看板数据配置查询' })
    // @MyApiOkResponse(DateReportConfig.Test)
    @Post("data/search")
    async dataConfigSearch(@Body() body: DateReportConfig.DataConfigSearchReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || "";
        return await this.dataReportConfigService.dataConfigSearch(body);
    }

    @ApiOperation({ summary: '数据看板数据配置修改' })
    // @MyApiOkResponse(DateReportConfig.Test)
    @Post("data/update")
    async dataConfigUpdate(@Body() body: DateReportConfig.DataConfigUpdateReq, @MyUser() user: JwtUserGw) {
        body.companyId = user?.targetCompany?.companyId || "";
        return await this.dataReportConfigService.dataConfigUpdate(body);
    }

}