import { Body, Controller, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtUserGw, MyApiOkResponse, MyUser } from "@yqz/nest";
import { DashboardTemplateService } from "./service/dashboard-template.service";
import { DashboardTemplate } from "./dto/dashboard-template.dto";
import { DashboardTemplateTagService } from "./service/dashboard-template-tag.service";

/**
 * 仪表盘模板控制器
 */
@ApiTags("dashboard-template")
@ApiBearerAuth()
@Controller("data/report/dashboard")
export class DashboardTemplateController {
    constructor(
        private readonly dashboardTemplateService: DashboardTemplateService,
        private readonly dashboardTemplateTagService: DashboardTemplateTagService,
    ) { }

    /**
     * 创建仪表盘模板
     */
    @ApiOperation({ summary: '上传当前仪表盘配置为新模板' })
    @MyApiOkResponse(DashboardTemplate.CreateTemplateResponse)
    @Post("templates/save")
    async createTemplate(
        @Body() body: DashboardTemplate.CreateTemplateRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        const companyId = String(user?.targetCompany?.companyId);
        return await this.dashboardTemplateService.createTemplate(body, managerId, companyId);
    }

    /**
     * 获取模板列表
     */
    @ApiOperation({ summary: '获取模板列表' })
    @MyApiOkResponse(DashboardTemplate.ListTemplatesResponse)
    @Post("templates")
    async getTemplateList(
        @Body() body: DashboardTemplate.ListTemplatesRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        const companyId = String(user?.targetCompany?.companyId);
        return await this.dashboardTemplateService.getTemplateList(body, managerId, companyId);
    }

    /**
     * 获取模板详情
     */
    @ApiOperation({ summary: '获取模板详情' })
    @MyApiOkResponse(DashboardTemplate.GetTemplateDetailResponse)
    @Post("templates/detail")
    async getTemplateDetail(
        @Body() body: DashboardTemplate.GetTemplateDetailRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        const companyId = String(user?.targetCompany?.companyId);
        return await this.dashboardTemplateService.getTemplateDetail(body.templateId, managerId, companyId);
    }

    /**
     * 更新模板基本信息
     */
    @ApiOperation({ summary: '修改模板基本信息' })
    @Post("templates/basic-info/update")
    async updateTemplateBasicInfo(
        @Body() body: DashboardTemplate.UpdateTemplateBasicInfoRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        return await this.dashboardTemplateService.updateTemplateBasicInfo(body, managerId);
    }

    /**
     * 更新模板配置
     */
    @ApiOperation({ summary: '修改模板配置内容' })
    @Post("templates/config/update")
    async updateTemplateConfig(
        @Body() body: DashboardTemplate.UpdateTemplateConfigRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        return await this.dashboardTemplateService.updateTemplateConfig(body, managerId);
    }

    /**
     * 删除模板
     */
    @ApiOperation({ summary: '删除模板' })
    @Post("templates/delete")
    async deleteTemplate(
        @Body() body: DashboardTemplate.DeleteTemplateRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        await this.dashboardTemplateService.deleteTemplate(body.templateId, body.version, managerId);
        return { success: true };
    }

    /**
     * 应用模板到我的仪表盘
     */
    @ApiOperation({ summary: '应用模板到我的仪表盘' })
    @Post("my-dashboard/apply-template")
    async applyTemplateToMyDashboard(
        @Body() body: DashboardTemplate.ApplyTemplateRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        const companyId = String(user?.targetCompany?.companyId);
        await this.dashboardTemplateService.applyTemplateToMyDashboard(body.templateId, managerId, companyId);
        return { success: true };
    }

    /**
     * 清除模板更新标记
     */
    @ApiOperation({ summary: '清除模板更新标记' })
    @Post("templates/clear-update-flag")
    async clearTemplateUpdateFlag(
        @Body() body: DashboardTemplate.ClearTemplateUpdateFlagRequest, 
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        const companyId = String(user?.targetCompany?.companyId);
        await this.dashboardTemplateTagService.clearTemplateUpdateFlag(companyId, managerId, body.templateId);
        return { success: true };
    }

    /**
     * 模板红点标记
     */
    @ApiOperation({ summary: '模板红点标记' })
    @Post("templates/get-update-flag")
    async hasUnreadUpdateForSpecificTemplate(
        @MyUser("v2") user: JwtUserGw
    ) {
        const managerId = String(user?.targetCompany?.managerId);
        const companyId = String(user?.targetCompany?.companyId);
        return await this.dashboardTemplateTagService.hasUnreadUpdateForSpecificTemplate(companyId, managerId);
    }
} 