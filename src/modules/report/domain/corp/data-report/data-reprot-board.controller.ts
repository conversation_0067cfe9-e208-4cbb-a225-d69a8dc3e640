import { Body, Controller, Post } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtUserGw, MyApiOkResponse, MyUser } from "@yqz/nest";
import { DataReportBoardService } from "./service/data-report-board.service";
import { DateReportBoard } from "./dto/data-report-board.dto";

/**
 * @summary 数据看板数据布局相关接口
 * @class DataReportBoardController
 */
@ApiTags("data-report-board")
@ApiBearerAuth()
@Controller("data/report/board")
export class DataReportBoardController {
    constructor(
        private readonly dataReportBoardService: DataReportBoardService
    ) { }

    @ApiOperation({ summary: '数据看板数据布局查询' })
    @MyApiOkResponse(DateReportBoard.DataBoardRes)
    @Post("search")
    async dataBoardSearch(@Body() body: DateReportBoard.DataBoardReq, @MyUser("v2") user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportBoardService.dataBoardSearch(body);
    }

    @ApiOperation({ summary: '数据看板数据布局修改' })
    @MyApiOkResponse(DateReportBoard.DataBoardUpdateRes)
    @Post("update")
    async dataBoardUpdate(@Body() body: DateReportBoard.DataBoardUpdateReq, @MyUser("v2") user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportBoardService.dataBoardUpdate(body);
    }

}