import { Body, Controller, Header, Post } from "@nestjs/common";
import { Api<PERSON>earerA<PERSON>, ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtUserGw, MyApiOkArrayResponse, MyApiOkResponse, MyDefaultApiOkResponse, MyUser } from "@yqz/nest";
import { DataReportModuleService } from "./service/data-report-module.service";
import { DateReportModule } from "./dto/data-report-module.dto";
import { DateReportInterface } from "./service/basic/dto/data-report.interface.dto";

/**
 * @summary 数据看板数据模块相关接口
 * @class DataReportModuleController
 */
@ApiTags("data-report-module")
@ApiBearerAuth()
@Controller("data/report/module")
export class DataReportModuleController {
    constructor(
        private readonly dataReportModuleService: DataReportModuleService
    ) { }

    @ApiOperation({ summary: '数据看板添加数据模块 - 筛选项' })
    @MyApiOkArrayResponse(DateReportInterface.SubjectOptionRes)
    @Post("option")
    async dataModuleOption(@Body() body: DateReportModule.SubjectOptionReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        return await this.dataReportModuleService.dataModuleOption(body);
    }

    @ApiOperation({ summary: '数据看板添加数据模块 - 筛默认维度选项' })
    @MyApiOkArrayResponse(DateReportInterface.SubjectOptionRes)
    @Post("default/dimension")
    async defaultDimension(@Body() body: DateReportModule.SubjectDefaultOptionReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        return await this.dataReportModuleService.defaultDimension(body);
    }

    @ApiOperation({ summary: '数据看板添加数据模块 - 数据范围选项' })
    // @MyApiOkResponse()
    @Post("filter/option")
    async dataModuleFilterOption(@Body() body: DateReportModule.FilterOptionReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleFilterOption(body);
    }

    @ApiOperation({ summary: '数据看板添加数据模块 - 编辑时模块配置信息查询' })
    @MyApiOkResponse(DateReportModule.DistributeDataModuleRes)
    @Post("update/search")
    async dataModuleUpdateSearch(@Body() body: { companyId: string, moduleId: string }, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        return await this.dataReportModuleService.dataModuleUpdateSearch(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 数据查询' })
    @MyApiOkResponse(DateReportModule.DistributeDataModuleRes)
    @Post("search")
    async dataModuleSearch(@Body() body: DateReportModule.DataModuleReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleSearch(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 修改' })
    @MyApiOkResponse(DateReportModule.DataModuleUpdateRes)
    @Post("update")
    async dataModuleUpdate(@Body() body: DateReportModule.DataModuleUpdateReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleUpdate(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 复制' })
    @MyApiOkResponse(DateReportModule.DataModuleCopyRes)
    @Post("copy")
    async dataModuleCopy(@Body() body: DateReportModule.DataModuleCopyReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleCopy(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 添加' })
    @MyApiOkResponse(DateReportModule.DataModuleSaveRes)
    @Post("save")
    async dataModuleSave(@Body() body: DateReportModule.DataModuleSaveReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleSave(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 删除' })
    @MyApiOkResponse(DateReportModule.DataModuleDeleteRes)
    @Post("delete")
    async dataModuleDelete(@Body() body: DateReportModule.DataModuleDeleteReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleDelete(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 数据分析筛选项' })
    // @MyApiOkResponse()
    @Post("analysis/option")
    async dataModuleAnalysisOption(@Body() body: DateReportModule.DataModuleAnalysisOptionReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleAnalysisOption(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 数据分析列表' })
    // @MyApiOkResponse()
    @Post("analysis/list")
    async dataModuleAnalysisList(@Body() body: DateReportModule.DataModuleAnalysisListReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleAnalysisList(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 数据分析分布' })
    @MyApiOkResponse(DateReportModule.DataModuleAnalysisDistributeRes)
    @Post("analysis/distribute")
    async dataModuleAnalysisDistribute(@Body() body: DateReportModule.DataModuleAnalysisDistributeReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleAnalysisDistribute(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 数据分析分布详情' })
    // @MyApiOkResponse()
    @Post("analysis/distribute/detail")
    async dataModuleAnalysisDistributeDetail(@Body() body: DateReportModule.DataModuleAnalysisDistributeDetailReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleAnalysisDistributeDetail(body);
    }

    @ApiOperation({ summary: '数据看板数据模块 - 数据分析导出' })
    @Post("analysis/export")
    // @Header('Content-Disposition', `attachment; filename="data_report.xlsx"`)
    // @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    async dataModuleAnalysisExport(@Body() body: DateReportModule.DataModuleAnalysisExportReq, @MyUser() user: JwtUserGw) {
        body.companyId = String(user?.targetCompany?.companyId || body?.companyId);
        body.managerId = String(user?.targetCompany?.managerId || body?.managerId);
        return await this.dataReportModuleService.dataModuleAnalysisExport(body);
    }

}