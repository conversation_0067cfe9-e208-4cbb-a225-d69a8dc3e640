import { Injectable } from "@nestjs/common";
import { DevTimeout, MyLogger, YqzException } from "@yqz/nest";
import { DateReportConfig } from "../dto/data-report-config.dto";
import { DataReportDao } from "../dao/data-report-config.dao";
import * as _ from "lodash";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { DateReport } from "../dto/data-report.dto";
import { DataReportConfigDefault } from "../dto/data-report-config-default.dto";
import { RoleDao } from "../../../bgw/role/dao/role.dao";
import { Sql } from "../../sql-generate/type/sql.type";
import { CommonParameterDao } from "../../../bgw/common/common-parameter.dao";

/**
 * @summary 数据看板 - 配置
 * @class DataReportService
 */
@Injectable()
export class DataReportConfigService {
    private readonly logger = new MyLogger(DataReportConfigService.name);

    constructor(
        private readonly dataReportDao: DataReportDao,
        private readonly companyDao: CompanyDao,
        private readonly roleDao: RoleDao,
        private readonly commonParameterDao: CommonParameterDao,
    ) { }

    // @DevTimeout(100)
    async test11() {
        // const result = await this.dataConfigSearch({
        //     companyId: "65"
        // })
        // console.log(`result: ${JSON.stringify(result)}`)
        await this.dataConfigUpdate({
            companyId: "65",
            data: [{
                "module": "project",
                "todoCompleted": "1180",
                "noworkProject": "70",
                "ownerFocusProjectScope": "all",
                "ownerFocusProjectOneDay": "10",
                "ownerFocusProjectTime": "10"
            }, {
                "module": "device",
                "deviceExpire": "30",
                "inIdleTimeout": "1",
                "outIdleTimeout": "1",
                "exceptionFocusScope": "bindDevice",
                "exceptionOfflineScope": "all",
                "exceptionOfflineCondition": "4",
                "exceptionOfflineConditionUnit": "hour",
                "exceptionOfflineReport": "all",
                "offlineFocusScope": "all",
                "offlineReport": "all",
                "offlineCondition": "7"
            }, {
                "module": "node_overdue",
                "delayOverdue": "Y"
            }, {
                "module": "onsite_patrol",
                "patrolSignStyle": ["device", "app"],
                "roleScope": "all",
                "roles": [],
                "noPatrolProjectShow": "Y"
            }, {
                "module": "project_update",
                "roleScope": "all",
                "roles": [],
                "noPatrolProjectShow": "Y"
            }, {
                "module": "online_patrol",
                "roleScope": "all",
                "roles": [],
                "noPatrolProjectShow": "Y"
            }, {
                "module": "customer_evaluate",
                "badEvaluationScope": "one_of",
                "overallScore": "5",
                "individualScore": "5"
            }]
        })
    }

    async menuConfigOptionSearch(params: DateReportConfig.MenuConfigSearchReq) {
        if (!params.companyId) throw new YqzException("必传参数不能为空");
        const company = await this.companyDao.find({ companyId: params.companyId });
        if (!company) throw new YqzException("公司不存在");
        let columns = Object.values(DateReport.Subject);// 默认配置
        columns = await this.optionData(columns, company.business_category);
        return {
            category: company.business_category,
            columns
        };
    }

    async menuConfigSearch(params: DateReportConfig.MenuConfigSearchReq) {
        if (!params.companyId) throw new YqzException("必传参数不能为空");
        const company = await this.companyDao.find({ companyId: params.companyId });
        if (!company) throw new YqzException("公司不存在");
        const config = await this.dataReportDao.searchMenuConfig({ companyId: params.companyId });
        if (!config || !config.columns) {
            // 没有配置，返回默认配置
            const defColumns = await this.menuConfigOptionSearch({ companyId: params.companyId });
            return {
                columns: defColumns?.columns
            };
        }
        config.columns = await this.optionData(config.columns as DateReport.Subject[], company.business_category);
        return {
            columns: config?.columns || []
        };
    }

    async menuConfigUpdate(params: DateReportConfig.MenuConfigUpdateReq) {
        if (!params.companyId) throw new YqzException("必传参数不能为空");
        return await this.dataReportDao.updateMenuConfig({
            companyId: params.companyId,
            columns: params.columns
        });
    }

    private optionData(columns: DateReport.Subject[], business_category: string) {
        const homeDecorColumns = [
            DateReport.Subject.Evaluate,
            DateReport.Subject.Live,
        ]
        switch (business_category) {
            case "home_decor":
                break;
            case "construction":
                columns = columns.filter(item => !homeDecorColumns.includes(item));
                break;
            default:
                break;
        }
        return columns;
    }


    async dataConfigOptionSearch(params: DateReportConfig.DataConfigSearchReq) {
        if (!params.companyId) throw new YqzException("必传参数不能为空");
        const roleList = await this.roleDao.searchList({ companyIds: [params.companyId] })
        const timeList = [{
            label: "小时",
            value: "hour"
        }, {
            label: "分钟",
            value: "minute"
        }
        ]
        const problemStatus = await this.commonParameterDao.search('PROBLEM_STATUS')
        const problemStatusList = problemStatus.map(item => {
            return {
                label: item.parameterName,
                value: item.parameterCode
            }
        })
        return {
            timeList: timeList,
            problemStatusList,
            roleList: roleList.map(item => {
                return {
                    name: item.roleName,
                    value: item.roleName
                }
            })
        };
    }


    async dataConfigSearch(params: DateReportConfig.DataConfigSearchReq) {
        if (!params.companyId) throw new YqzException("必传参数不能为空");
        const company = await this.companyDao.find({ companyId: params.companyId });
        if (!company) throw new YqzException("公司不存在");
        let configs = await this.dataReportDao.searchDataConfig({ companyId: String(params.companyId), module: params.module });
        if (_.isEmpty(configs)) {
            // 没有配置，返回默认配置
            let defConfig = DataReportConfigDefault.defList
            if (params.module) {
                defConfig = [DataReportConfigDefault.DefaultConfig[params.module]]
            }
            return {
                category: company.business_category,
                configs: defConfig
            };
        }
        // 补充缺失的配置
        if (!params.module) {
            configs = this.supplementMissingConfigs(configs, String(params.companyId));
        }
        // 查询公司菜单配置
        // const {columns} = await this.menuConfigSearch({ companyId: params.companyId })
        const config = await this.dataReportDao.searchMenuConfig({ companyId: params.companyId });
        // 有配置返回配置,没有配置返回默认配置
        const columns = this.optionData(config?.columns ? config.columns as DateReport.Subject[] : Object.values(DateReport.Subject), company.business_category) || [];
        configs = _.filter(configs, obj => _.includes(columns, obj.module));
        configs = _.sortBy(configs, obj => _.indexOf(columns, obj.module));
        return {
            category: company.business_category,
            configs: configs.map(item => {
                delete item._id
                delete item.companyId
                return item
            })
        };
    }

    /**
     * 参数校验，基于默认配置校验数据配置是否合法
     * @param params 
     */
    checkDataConfig(params: DateReportConfig.DataConfigUpdateReq) {
        if (!params.companyId || !(params.data instanceof Array)) throw new YqzException("必传参数不能为空");
        for (const data of params.data) {
            if (!data.module) throw new YqzException("缺少模块参数")
            const defaultConfig = DataReportConfigDefault.DefaultConfig[data?.module];
            if (!defaultConfig) continue;//目前如果没有默认配置就跳过，当然也可以报错强校验，看业务需求
            // 校验：检查每个属性是否存在且不为空 (默认配置属性来作为校验依据)
            for (const key in defaultConfig) {
                if (!defaultConfig.hasOwnProperty(key)) continue;
                if (data[key] !== undefined && data[key] !== null && data[key] !== '') continue;
                throw new Error(`${data?.module}缺少必填参数`);
            }
        }
    }

    async dataConfigUpdate(params: DateReportConfig.DataConfigUpdateReq) {
        if (!params.companyId || !(params.data instanceof Array)) throw new YqzException("必传参数不能为空");
        //校验参数
        this.checkDataConfig(params);
        // // 基于配置插入图表筛选项
        // await this.dataConfigFilterMapping({
        //     companyId: params.companyId,
        //     data: params.data
        // })

        return await this.dataReportDao.updateDataConfig({
            companyId: String(params.companyId),
            configs: params.data
        });
    }

    // @DevTimeout(100)
    async dataConfigFilterMapping(params: {companyId: string, data: any[]}) {
        if (!params.companyId || !(params.data instanceof Array)) throw new YqzException("必传参数不能为空");
        const {companyId, data} = params;
        const columns = this.transformDataToColumns(data)
        await this.dataReportDao.updateDataColumnConfig({
            companyId: companyId,
            columns: columns
        })
    }

    // 处理嵌套字段生成 Column 的函数
    private generateNestedColumns(item: Record<string, any>, nestedKeys: string[], existingKeys: Set<string>): Sql.Column[] {
        return nestedKeys
            .map((nestedKey) => {
                // 如果这个嵌套字段已经添加到主字段中，就跳过
                if (existingKeys.has(nestedKey)) {
                    return null;
                }

                // 否则，创建嵌套字段
                if (item[nestedKey] !== undefined) {
                    return new Sql.Column({
                        key: nestedKey,
                        value: item[nestedKey],
                        operator: Sql.Operator.greater_than_or_equal, // 默认操作符，根据需要调整
                    });
                }
                return null;
            })
            .filter(Boolean) as Sql.Column[];
    }

    // 主转换方法
    private transformDataToColumns(data: Array<Record<string, any>>): Sql.Column[] {
        return data.flatMap((item) => {
            const columns: Sql.Column[] = [];
            const existingKeys = new Set<string>(); // 记录已添加的字段，避免重复

            Object.keys(item).forEach((key) => {
                const mapping = DataReportConfigDefault.PropertyMapping[key];
                if (mapping) {
                    let operator = typeof mapping.operator === 'function'
                        ? mapping.operator(item[key]) // 如果是函数，调用它来获取操作符
                        : mapping.operator;
                    operator = Sql.OperatorMapping(operator);

                    const logic = typeof mapping.logic === 'function'
                        ? mapping.logic(item[key]) // 如果是函数，调用它来获取操作符
                        : mapping.logic;
                    const nestedKeys = mapping.nestedKeys;

                    // 生成嵌套字段的 Column 数组
                    let nestedColumns: Sql.Column[] = [];
                    if (nestedKeys) {
                        nestedColumns = this.generateNestedColumns(item, nestedKeys, existingKeys);
                    }

                    // 创建 Column 实例
                    const column = new Sql.Column({
                        key,
                        operator,
                        value: item[key],
                        logic,
                        nested: nestedColumns.length > 0 ? nestedColumns : undefined,
                    });

                    columns.push(column);
                    existingKeys.add(key); // 将当前字段添加到已存在字段集合中

                    // 将嵌套字段的 key 也添加到集合中，避免嵌套字段重复
                    nestedColumns.forEach((nestedColumn) => {
                        if (nestedColumn) {
                            existingKeys.add(nestedColumn.key);
                        }
                    });
                }
            });

            return columns;
        });
    }

    private supplementMissingConfigs(configs: any[], companyId: string): any[] {
        // 获取默认配置中所有的module
        const defaultModules = DataReportConfigDefault.defList.map(config => config.module);
        
        // 获取当前configs中所有的module
        const existingModules = configs.map(config => config.module);
        
        // 找出缺失的module
        const missingModules = defaultModules.filter(module => !existingModules.includes(module));
        
        // 为缺失的module添加默认配置
        for (const missingModule of missingModules) {
            const defaultConfig = DataReportConfigDefault.DefaultConfig[missingModule];
            if (defaultConfig) {
                configs.push({
                    companyId: companyId,
                    module: missingModule,
                    ...defaultConfig,
                });
            }
        }
        return configs;
    }

}