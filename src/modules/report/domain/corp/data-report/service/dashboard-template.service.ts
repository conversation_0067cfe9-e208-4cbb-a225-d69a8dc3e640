import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ed<PERSON>, YqzException } from "@yqz/nest";
import { DashboardTemplateDao } from "../dao/dashboard-template.dao";
import { DashboardTemplate } from "../dto/dashboard-template.dto";
import * as _ from "lodash";
import { ObjectId } from "mongodb";
import { ManagerDao } from "../../../bgw/manager/dao/manager.dao";
import { DeleteFlag } from "@yqz/nest/lib/types/delete-flag.enum";
import { BaseResp } from "@src/types/report-req.type";
import { DataReportBoardService } from "./data-report-board.service";
import { DataReportModuleService } from "./data-report-module.service";
import { DateReport } from "../dto/data-report.dto";
import * as DateFns from "date-fns";
import { DashboardTemplateTagService } from "./dashboard-template-tag.service";

/**
 * 仪表盘模板服务
 */
@Injectable()
export class DashboardTemplateService {
    private readonly logger = new MyLogger(DashboardTemplateService.name);

    constructor(
        private readonly dashboardTemplateDao: DashboardTemplateDao,
        private readonly managerDao: ManagerDao,
        private readonly dataReportBoardService: DataReportBoardService,
        private readonly dataReportModuleService: DataReportModuleService,
        private readonly dashboardTemplateTagService: DashboardTemplateTagService,
    ) { }

    /**
     * 创建仪表盘模板
     * @param createData 创建数据
     * @param managerId 成员ID
     * @param companyId 公司ID
     * @returns 创建结果
     */
    async createTemplate(
        createData: DashboardTemplate.CreateTemplateRequest,
        managerId: string,
        companyId: string
    ): Promise<DashboardTemplate.CreateTemplateResponse> {
        if (!createData.templateName || !managerId || !companyId) {
            throw new YqzException('必传参数不能为空');
        }

        try {
            // 1. 获取用户当前的仪表盘布局
            const userBoardLayout = await this.dataReportBoardService.dataBoardSearch({
                companyId,
                managerId,
                subject: DateReport.Subject.Delivery
            });

            // 构建模板实体
            const templateEntity: DashboardTemplate.DashboardTemplateEntity = {
                templateName: createData.templateName,
                templateDesc: createData.templateDesc,
                companyId,
                dashboardConfig: [],
                createBy: managerId,
                createTime: new Date(),
                updateBy: managerId,
                updateTime: new Date(),
                lastUpdateBy: managerId,
                lastUpdateTime: new Date(),
                deleteFlag: DeleteFlag.N,
                version: 1
            };

            // 2.保存模板
            const templateId = await this.dashboardTemplateDao.saveTemplate(templateEntity);
        
            // 3. 复制用户所有的仪表盘模块
            const modulePositions: DashboardTemplate.ModulePosition[] = [];
            const moduleBlocks: DashboardTemplate.DashboardBlock[] = [];
            
            if (userBoardLayout?.blocks?.length > 0) {
                for (const block of userBoardLayout.blocks) {
                    try {
                        // 获取模块详情
                        const moduleId = block.moduleId;
                        const moduleDetail = await this.dataReportModuleService.dataModuleUpdateSearch({
                            moduleId
                        });
                        
                        // 复制模块，创建新模块
                        if (moduleDetail) {
                            const copyResult = await this.dataReportModuleService.dataModuleCopy({
                                companyId,
                                managerId,
                                boardId: userBoardLayout?.boardId,
                                saveBoardId: templateId,
                                type: "person",
                                saveType: "template",
                                copyModuleId: moduleId,
                                gridColumn: block.gridColumn
                            });
                            
                            if (copyResult?.moduleId) {
                                modulePositions.push({
                                    moduleId: copyResult.moduleId,
                                    position: { gridColumn: block.gridColumn }
                                });

                                moduleBlocks.push({
                                    moduleId: copyResult.moduleId,
                                    gridColumn: block.gridColumn
                                });
                            }
                        }
                    } catch (error) {
                        this.logger.error(`Error processing module ${block.moduleId}:`, error.stack);
                    }
                }
            }

            await this.dashboardTemplateDao.updateTemplateConfig(
                templateId,
                moduleBlocks,
                1,
                managerId
            );

            return { templateId };
        } catch (error) {
            this.logger.error(`创建模板出错: ${error.message}`, error.stack);
            throw new YqzException('创建模板失败');
        }
    }

    /**
     * 获取模板列表
     * @param queryParams 查询参数
     * @param managerId 成员ID
     * @param companyId 公司ID
     * @returns 模板列表
     */
    async getTemplateList(
        queryParams: DashboardTemplate.ListTemplatesRequest,
        managerId: string,
        companyId?: string
    ): Promise<BaseResp<DashboardTemplate.TemplateListItem>> {
        try {
            // 转换参数
            const searchParams = {
                companyId,
                templateName: queryParams.templateName,
                pageNo: String(queryParams.pageNo || 1),
                pageSize: String(queryParams.pageSize || 10)
            };

            // 查询模板列表
            const templates = await this.dashboardTemplateDao.findTemplates(searchParams);

            // 获取用户当前使用的模板信息
            const userSettings = await this.dashboardTemplateDao.getUserDashboardSettings(managerId, companyId);

            // 获取用户ID到昵称的映射
            const managerIds = _.uniq([
                ...templates.items.map(t => t.createBy),
                ...templates.items.map(t => t.lastUpdateBy).filter(id => !!id)
            ]);
            const managerMap = await this.getUserNicknameMap(managerIds);

            // 从Redis中获取模板更新状态
            const templateIds = templates.items.map(t => t._id.toString());
            const updateStatusMap = await this.dashboardTemplateTagService.getTemplateUpdateStatusFromRedis(companyId, managerId, templateIds);

            // 构建返回数据
            const templateItems = templates.items.map(template => {
                // 判断是否为当前使用的模板
                const isCurrentlyUsed = userSettings?.currentTemplateId === template._id.toString();
                
                // 从Redis中获取更新状态
                const templateId = template._id.toString();
                const hasUpdates = updateStatusMap[templateId] || false;

                return {
                    templateId,
                    templateName: template.templateName,
                    templateDesc: template.templateDesc,
                    isCurrentlyUsed,
                    hasUpdates,
                    creatorNickname: managerMap[template.createBy] || '',
                    createdTime: DateFns.format(new Date(template.createTime),'yyyy-MM-dd HH:mm:ss'),
                    lastUpdaterNickname: template.lastUpdateBy ? (managerMap[template.lastUpdateBy] || '') : '',
                    lastUpdatedTime: DateFns.format(new Date(template.lastUpdateTime || template.updateTime),'yyyy-MM-dd HH:mm:ss'),
                    version: template.version  // 添加版本号到返回结果
                };
            });

            // 将当前使用的模板置顶
            templateItems.sort((a, b) => (b.isCurrentlyUsed ? 1 : 0) - (a.isCurrentlyUsed ? 1 : 0));

            return { items: templateItems, total: templates.total };
        } catch (error) {
            this.logger.error(`获取模板列表出错: ${error.message}`, error.stack);
            throw new YqzException('获取模板列表失败');
        }
    }

    private getTemplateUpdateStatus(isCurrentlyUsed: boolean,lastAppliedTime: Date,lastUpdateTime: Date) {
        return isCurrentlyUsed && 
                lastAppliedTime && 
                lastUpdateTime && 
                lastUpdateTime > lastAppliedTime;
    }

    /**
     * 获取模板详情
     * @param templateId 模板ID
     * @param userId 用户ID
     * @param companyId 公司ID
     * @returns 模板详情
     */
    async getTemplateDetail(
        templateId: string,
        userId: string,
        companyId?: string
    ): Promise<DashboardTemplate.GetTemplateDetailResponse> {
        if (!templateId) {
            throw new YqzException('模板ID不能为空');
        }

        try {
            // 查询模板
            const template = await this.dashboardTemplateDao.findTemplateById(templateId);
            if (!template) {
                throw new YqzException('模板不存在或已删除');
            }

            // 获取用户当前使用的模板信息
            const userSettings = await this.dashboardTemplateDao.getUserDashboardSettings(userId, companyId);

            // 获取用户昵称
            const managerIds = [template.createBy];
            if (template.lastUpdateBy) {
                managerIds.push(template.lastUpdateBy);
            }
            const managerMap = await this.getUserNicknameMap(managerIds);

            // 判断是否为当前使用的模板
            const isCurrentlyUsed = userSettings?.currentTemplateId === template._id.toString();
            
            // 判断模板是否有更新
            const hasUpdates = isCurrentlyUsed && 
                userSettings?.lastAppliedTime && 
                template.lastUpdateTime && 
                template.lastUpdateTime > userSettings.lastAppliedTime;

            // 转换配置中的ObjectId（如果有）
            const dashboardConfig = this.convertDashboardConfig(template.dashboardConfig);

            // 构造返回结果
            return {
                templateId: template._id.toString(),
                templateName: template.templateName,
                templateDesc: template.templateDesc,
                isCurrentlyUsed,
                hasUpdates,
                creatorNickname: managerMap[template.createBy] || template.createBy,
                createdTime: DateFns.format(new Date(template.createTime),'yyyy-MM-dd HH:mm:ss'),
                lastUpdaterNickname: template.lastUpdateBy ? managerMap[template.lastUpdateBy] || template.lastUpdateBy : '',
                lastUpdatedTime: DateFns.format(new Date(template.updateTime || template.createTime),'yyyy-MM-dd HH:mm:ss'),
                dashboardConfig,
                version: template.version
            };
        } catch (error) {
            this.logger.error(`获取模板详情出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('获取模板详情失败');
        }
    }

    /**
     * 更新模板基本信息
     * @param updateData 更新数据
     * @param managerId 成员ID
     * @returns 更新结果
     */
    async updateTemplateBasicInfo(
        updateData: DashboardTemplate.UpdateTemplateBasicInfoRequest,
        managerId: string
    ): Promise<{templateId: string, version: number}> {
        if (!updateData.templateId || !updateData.templateName) {
            throw new YqzException('模板ID和名称不能为空');
        }

        try {
            // 调用DAO层方法，传递版本号
            return await this.dashboardTemplateDao.updateTemplateBasicInfo(
                updateData.templateId, 
                {
                    templateName: updateData.templateName,
                    templateDesc: updateData.templateDesc,
                    version: updateData.version  // 传递版本号用于乐观锁控制
                }, 
                managerId
            );
        } catch (error) {
            this.logger.error(`更新模板基本信息出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('更新模板基本信息失败');
        }
    }

    /**
     * 更新模板配置
     * @param updateData 更新数据
     * @param managerId 成员ID
     * @returns 更新结果
     */
    async updateTemplateConfig(
        updateData: DashboardTemplate.UpdateTemplateConfigRequest,
        managerId: string
    ): Promise<{templateId: string, version: number}> {
        if (!updateData.templateId || !updateData.dashboardConfig) {
            throw new YqzException('模板ID和配置不能为空');
        }

        try {
            // 获取模板当前配置
            const template = await this.dashboardTemplateDao.findTemplateById(updateData.templateId);
            if (!template) {
                throw new YqzException('模板不存在或已删除');
            }

            // 调用DAO层方法，传递版本号
            const result = await this.dashboardTemplateDao.updateTemplateConfig(
                updateData.templateId,
                updateData.dashboardConfig,
                updateData.version,  // 传递版本号用于乐观锁控制
                managerId
            );

            // 如果更新成功，标记所有使用该模板的用户有新的更新
            if (result) {
                await this.dashboardTemplateTagService.markTemplateUpdated(template.companyId, updateData.templateId);
            }

            return result;
        } catch (error) {
            this.logger.error(`更新模板配置出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('更新模板配置失败');
        }
    }

    /**
     * 删除模板
     * @param templateId 模板ID
     * @param version 版本号
     * @param managerId 成员ID
     * @returns 删除结果
     */
    async deleteTemplate(
        templateId: string,
        version: number,
        managerId: string
    ): Promise<boolean> {
        if (!templateId) {
            throw new YqzException('模板ID不能为空');
        }

        try {
            // 调用DAO层方法，传递版本号进行乐观锁控制
            return await this.dashboardTemplateDao.deleteTemplate(templateId, version, managerId);
        } catch (error) {
            this.logger.error(`删除模板出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('删除模板失败');
        }
    }

    /**
     * 应用模板到个人仪表盘
     * @param templateId 模板ID
     * @param managerId 成员ID
     * @param companyId 公司ID
     * @returns 应用结果
     */
    async applyTemplateToMyDashboard(
        templateId: string,
        managerId: string,
        companyId: string
    ): Promise<boolean> {
        if (!templateId || !managerId) {
            throw new YqzException('必传参数不能为空');
        }

        try {
            // 1. 获取模板详情
            const template = await this.dashboardTemplateDao.findTemplateById(templateId);
            if (!template) {
                throw new YqzException('模板不存在或已删除');
            }

            // 2. 获取用户当前的仪表盘布局
            const userBoardLayout = await this.dataReportBoardService.dataBoardSearch({
                companyId,
                managerId,
                subject: DateReport.Subject.Delivery
            });

            // 3. 复制模板中的所有仪表盘模块
            const blocks = [];
            const dashboardConfig = this.convertDashboardConfig(template.dashboardConfig);
            
            if (dashboardConfig?.length > 0) {
                for (const block of dashboardConfig) {
                    // 复制模块到用户的仪表盘
                    const copyResult = await this.dataReportModuleService.dataModuleCopy({
                        companyId,
                        managerId,
                        boardId: templateId,
                        saveBoardId: userBoardLayout?.boardId,
                        type: "template",
                        saveType: "person",
                        copyModuleId: String(block.moduleId),
                        gridColumn: block.gridColumn
                    });
                    
                    if (copyResult?.moduleId) {
                        blocks.push({
                            moduleId: copyResult.moduleId,
                            gridColumn: block.gridColumn
                        });
                    }
                }
            }

            // 4. 更新用户的仪表盘布局
            await this.dataReportBoardService.dataBoardUpdate({
                companyId,
                managerId,
                boardId: userBoardLayout.boardId,
                blocks
            });

            // 5. 更新用户设置，记录应用的模板和时间
            await this.saveUserTemplateSettings(managerId, companyId, templateId);
            
            // 6. 清除当前用户对该模板的更新标记
            await this.dashboardTemplateTagService.clearTemplateUpdateFlag(companyId, managerId, templateId);

            return true;
        } catch (error) {
            this.logger.error(`应用模板出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('应用模板失败');
        }
    }

    /**
     * 保存用户模板设置
     * @param managerId 成员ID
     * @param companyId 公司ID
     * @param templateId 模板ID
     */
    private async saveUserTemplateSettings(managerId: string, companyId: string, templateId: string): Promise<void> {
        try {
            // 查询是否已有用户设置
            const existingSettings = await this.dashboardTemplateDao.getUserDashboardSettings(managerId, companyId);
            
            if (existingSettings) {
                // 更新现有设置
                await this.dashboardTemplateDao.updateUserDashboardSettings(
                    existingSettings._id.toString(),
                    {
                        currentTemplateId: templateId,
                        lastAppliedTime: new Date(),
                        updateBy: managerId,
                        updateTime: new Date()
                    }
                );
            } else {
                // 创建新设置
                const userSettings: DashboardTemplate.UserDashboardSettingsEntity = {
                    managerId,
                    companyId,
                    currentTemplateId: templateId,
                    lastAppliedTime: new Date(),
                    createBy: managerId,
                    createTime: new Date(),
                    updateBy: managerId,
                    updateTime: new Date()
                };
                
                await this.dashboardTemplateDao.saveUserDashboardSettings(userSettings);
            }
        } catch (error) {
            this.logger.error(`保存用户模板设置出错: ${error.message}`, error.stack);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取用户昵称映射
     * @param managerIds 用户ID数组
     * @returns 用户ID到昵称的映射
     */
    private async getUserNicknameMap(managerIds: string[]): Promise<Record<string, string>> {
        if (!managerIds || managerIds.length === 0) {
            return {};
        }

        try {
            // 使用DAO方法获取用户信息
            const userMap = {};
            
            for (const managerId of managerIds) {
                try {
                    const userInfo = await this.managerDao.getUserBasicInfoById(managerId);
                    if (userInfo) {
                        userMap[managerId] = userInfo.nickname || userInfo.name || managerId;
                    }
                } catch (error) {
                    this.logger.warn(`获取用户 ${managerId} 信息失败: ${error.message}`);
                }
            }
            
            return userMap;
        } catch (error) {
            this.logger.error(`获取用户昵称映射出错: ${error.message}`, error.stack);
            return {};
        }
    }

    /**
     * 转换仪表盘配置中的ObjectId为字符串
     * @param config 仪表盘配置
     * @returns 转换后的配置
     */
    private convertDashboardConfig(config: DashboardTemplate.DashboardBlock[]): DashboardTemplate.DashboardBlock[] {
        if (!config) {
            return [];
        }

        const blocks = config.map(block => ({
            moduleId: block.moduleId instanceof ObjectId ? block.moduleId.toString() : block.moduleId,
            gridColumn: block.gridColumn
        }));

        return blocks;
    }

} 