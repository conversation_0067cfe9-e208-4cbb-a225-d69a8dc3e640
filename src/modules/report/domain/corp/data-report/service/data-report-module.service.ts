import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import { DateReportModule } from "../dto/data-report-module.dto";
import { DateReport } from "../dto/data-report.dto";
import { Chart } from "../chart/chart.type";
import { DataReportModuleDao } from "../dao/data-report-module.dao";
import { DataReportBoardDao } from "../dao/data-report-board.dao";
import { DataReportStrategy } from "./basic/data-report.strategy";
import { ObjectId } from "mongodb";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { DateReportInterface } from "./basic/dto/data-report.interface.dto";
import * as _ from "lodash";
import ExcelJS from 'exceljs';
import { parse } from 'csv-parse/sync';
import { ChartDateUtil } from "@src/util/chart-date.util";
import { DataReportModuleFilterDao } from "../dao/data-report-module-filter.dao";
import { DataReportModuleFilter } from "../dto/data-report-module-filter.dto";
import { DashboardTemplateDao } from "../dao/dashboard-template.dao";
import { DashboardTemplateService } from "./dashboard-template.service";
import { DashboardTemplateTagService } from "./dashboard-template-tag.service";
/**
 * @summary 数据看板 - 模块（数据）
 * @class DataReportService
 */
@Injectable()
export class DataReportModuleService {
    private readonly logger = new MyLogger(DataReportModuleService.name);

    constructor(
        private readonly dataReportModuleDao: DataReportModuleDao,
        private readonly dataReportBoardDao: DataReportBoardDao,
        private readonly dataReportStrategy: DataReportStrategy,
        private readonly companyDao: CompanyDao,
        private readonly dataReportModuleFilterDao: DataReportModuleFilterDao,
        private readonly dashboardTemplateDao: DashboardTemplateDao,
        private readonly dashboardTemplateTagService: DashboardTemplateTagService,
    ) { }

    /**
     * 数据看板数据模块筛选项 topic和dimension
     * @param params 
     * @returns 
     */
    async dataModuleOption(params: DateReportModule.SubjectOptionReq) {
        if (!params.subject || !params.companyId) throw new YqzException('必传参数不能为空');
        const company = await this.companyDao.find({ companyId: params.companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(params.subject);
        return await dataReportInstance.getSubjectOption(params.companyId, params.subject, company?.business_category) || [];
    }

    /**
     * 获取topic的默认维度
     * @param params 
     * @returns 
     */
    async defaultDimension(params: DateReportModule.SubjectDefaultOptionReq) {
        if (!params.subject || !params.companyId) throw new YqzException('必传参数不能为空');
        const company = await this.companyDao.find({ companyId: params.companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(params.subject);
        const defaultBoard = dataReportInstance.getDefaultBoard(params.subject, company?.business_category);
        return (defaultBoard?.blocks || []).find((item) => item.subject === params.subject && item.topic === params.topic) || {};
    }

    /**
     * 数据看板数据模块 - 数据范围选项
     * @param params 
     * @returns 
     */
    async dataModuleFilterOption(params: DateReportModule.FilterOptionReq) {
        const { companyId, managerId, departmentId, subject, topic } = params;
        if (!subject || !companyId || !departmentId || !managerId || !topic) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(params.subject);
        return await dataReportInstance.getFilterOption({ ...params, companyType: company.COMPANY_TYPE, businessCategory: company.business_category }) || [];
    }

    /**
     * 校验输入的模块参数是否合法
     * @param param0 
     */
    private async checkDataModule({ companyId, subject, topic, dimension, chartType, filters }: { companyId: string, subject: DateReport.Subject, topic: string, dimension: string, chartType: Chart.ChartType, filters?: DataReportModuleFilter.DataReportModuleFilter[] }) {
        if (!subject || !topic || !dimension || !chartType) throw new YqzException('必传参数不能为空');
        const filterData = await this.dataModuleOption({ companyId, subject });
        // 校验 subject 是否存在
        const subjectData = filterData.find((item) => item.subject.value === subject);
        if (!subjectData) throw new YqzException(`非法的主体`);
        // 校验 topic 是否存在于 subject 中
        const topicData = subjectData.subject.topic.find((item) => item.value === topic);
        if (!topicData) throw new YqzException(`非法的主题`);
        // 校验 dimension 是否存在于 topic 中
        const dimensionData = topicData.dimension.find((item) => item.value === dimension);
        if (!dimensionData) throw new YqzException(`非法的维度`);
        // 校验 chartType 是否存在于 dimension 的 charts 中
        const chartTypeData = dimensionData.charts.find((item) => item.value === chartType);
        if (!chartTypeData) throw new YqzException(`非法的图表类型`);
        if (_.isEmpty(filters)) return true;
        // 校验 filters 是否有重复的filterField
        const filterFieldList = filters.map(item => item.filterField);
        const filterFieldSet = new Set(filterFieldList);
        if (filterFieldList.length !== filterFieldSet.size) throw new YqzException(`筛选配置中存在重复的filterField`);
        // 校验 filters 的 filterValue不能为空
        filters.forEach(item => { if (!item.filterValue) throw new YqzException(`筛选配置中存在filterValue为空的filterField`); });
        // 校验 filters 的 filterType是否合法，只能是FilterControlType
        const filterTypeList = filters.map(item => item.filterType);
        if (filterTypeList.some(item => !Object.values(DataReportModuleFilter.FilterControlType).includes(item))) throw new YqzException(`非法的筛选类型`);
        // 校验 filters 的 filterType 和 filterValue 类型是否匹配
        filters.forEach(item => { if (!DataReportModuleFilter.validateFilterValueByType(item.filterType, item.filterValue)) throw new YqzException(`筛选配置中存在filterValue类型不匹配的filterField`); });
        return true;
    }

    /**
     * 数据看板数据模块编辑时查询
     * @param params 
     * @returns 
     */
    async dataModuleUpdateSearch(params: { moduleId: string }) {
        const { moduleId } = params;
        if (!moduleId) throw new YqzException('必传参数不能为空');
        //查询模块
        const module = await this.dataReportModuleDao.findOne({ moduleId });
        //查询模块筛选配置
        const filters = await this.dataReportModuleFilterDao.find({ moduleId });
        if (!module) throw new YqzException('数据模块不存在');
        return {
            moduleId,
            subject: module.subject,
            topic: module.topic,
            dimension: module.dimension,
            title: module.title || "",
            chartType: module.chartType,
            filters: filters.map(item => ({ filterName: item.filterName, filterField: item.filterField, filterType: item.filterType, filterValue: item.filterValue }))
        }
    }

    /**
     * 数据看板数据模块查询（根据moduleId查询 或 根据主体，主题，维度，图表类型查询）
     * @param params 
     */
    async dataModuleSearch(params: DateReportModule.DataModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { companyId, managerId, departmentId, moduleId, from, to, dataType, subject, topic, dimension, chartType, gridColumn = '1', filters, type = 'person' } = params;
        if (!companyId || !managerId || !departmentId || !from || !to || !dataType) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //根据主体，主题，维度，图表类型查询
        const req: DateReportInterface.ModuleReq = { companyId, from, to, dataType, departmentId, subject, topic, dimension, chartType, gridColumn, businessCategory: company.business_category, filters };
        //根据模块id查询
        let boardSubject = subject;
        if (moduleId) {
            const module = await this.dataReportModuleDao.findOne({ moduleId });
            if (!module) throw new YqzException('数据模块不存在');
            const newType = module?.type || type
            const board = await this.searchBlockByType(module.boardId.toHexString(),moduleId,newType)
            const block = board?.block;
            if (!block) throw new YqzException('数据模块不存在');
            const filters = await this.dataReportModuleFilterDao.find({ moduleId: moduleId });
            boardSubject = board.subject;
            req.subject = module.subject;
            req.topic = module.topic;
            req.dimension = module.dimension;
            req.title = module.title || "";
            req.chartType = module.chartType;
            req.gridColumn = block.gridColumn;
            req.filters = (filters || []).map(item => ({ filterName: item.filterName, filterField: item.filterField, filterType: item.filterType, filterValue: item.filterValue }));
        }
        //获取图表数据
        if (!req.subject || !req.topic || !req.dimension || !req.chartType) throw new YqzException('必传参数不能为空');
        const dataReportInstance = this.dataReportStrategy.getInstance(req.subject);
        let result = await dataReportInstance.getChartData(req);
        //处理tips
        result.data.header.tips = `data:${result.subject}:${result.topic}`;
        //如果是个人仪表盘所有的操作类型都为Action
        if (boardSubject === DateReport.Subject.Delivery) result.data.header.actionType = Chart.ActionType.Action;
        //根据模块id查询
        if (moduleId) {
            const isReal = result?.data?.desc?.isReal;//是否是实时数据(非实时数据都要计算环比)
            if (isReal != 'Y') result.data.desc.ratio = await this.getRatio(req, result);
            return { ...result, moduleId };
        }
        return result;
    }

    async searchBlockByType(boardId: string,moduleId:string,type: string) {
        let subject = DateReport.Subject.Delivery
        let blocks = []
        switch(type) {
            case 'person':
                const board = await this.dataReportBoardDao.findById({ boardId: boardId });
                this.logger.log(`dataModuleCopy board: ${JSON.stringify(board)}`);
                blocks = board?.blocks || []
                blocks = blocks.map(res => {
                    if (res.moduleId && ObjectId.isValid(res.moduleId) && !_.isString(res.moduleId))
                        res.moduleId = res.moduleId.toHexString()
                    return res
                })
                subject = board?.subject
                break;
            case 'template':
                const template = await this.dashboardTemplateDao.findTemplateById(boardId);
                this.logger.log(`dataModuleCopy template: ${JSON.stringify(template)}`);
                blocks = template?.dashboardConfig || [];
                blocks = blocks.map(res => {
                    if (res.moduleId && ObjectId.isValid(res.moduleId) && !_.isString(res.moduleId))
                        res.moduleId = res.moduleId.toHexString()
                    return res
                })
                break;
            default:
                break;
        }
        
        const block = blocks.find((item) => item.moduleId === moduleId);
        return {
            subject: subject || DateReport.Subject.Delivery,
            block
        }
    }

    /**
     * 计算环比(本期数据-上期数据)/上期数据*100% (非实时数据都要计算环比)
     * @param params 
     * @param chartData 
     * @returns 
     */
    private async getRatio(params: DateReportInterface.ModuleReq, chartData: DateReportModule.DistributeDataModuleRes) {
        try {
            const subject = chartData.subject;
            const chartType = chartData.chartType;//图表类型
            let total = 0;
            let beforeTotal = 0;
            //趋势图
            if (chartType === Chart.ChartType.Line || chartType === Chart.ChartType.Bar) {
                const data = chartData.data as Chart.ModuleLineDataRes;
                const dataList = data.config.data;
                total = dataList[dataList.length - 1];
                beforeTotal = dataList[dataList.length - 2];
            }
            //分布趋势图
            if (chartType === Chart.ChartType.MultiLine) {
                const data = chartData.data as Chart.ModuleMultiLineDataRes;
                total = _.sum(data.config.data.map(item => item.value[item.value.length - 1]));
                beforeTotal = _.sum(data.config.data.map(item => item.value[item.value.length - 2]));
            }
            //饼图、玫瑰图、环形图
            if ((Chart.TypeOption[Chart.Type.DistributeChart] || []).includes(chartType)) {
                const data = chartData.data as Chart.ModulePieDataRes;
                //获取上一个周期的开始时间和结束时间
                const timeFrameList = ChartDateUtil.getTimeFrameList({ from: params.from, to: params.to, dataType: params.dataType, cycle: 2 });
                //获取上一个周期的数据
                const dataReportInstance = this.dataReportStrategy.getInstance(subject);
                const beforeResult = await dataReportInstance.getChartData({ ...params, from: timeFrameList[0].startTime, to: timeFrameList[0].endTime });
                const beforeData = beforeResult.data as Chart.ModulePieDataRes;
                total = _.sum(data.config.data.map(item => item.value));
                beforeTotal = _.sum(beforeData.config.data.map(item => item.value));
            }
            //数值
            if (chartType === Chart.ChartType.Total) {
                const data = chartData.data as Chart.ModuleTotalDataRes;
                //获取上一个周期的开始时间和结束时间
                const timeFrameList = ChartDateUtil.getTimeFrameList({ from: params.from, to: params.to, dataType: params.dataType, cycle: 2 });
                //获取上一个周期的数据
                const dataReportInstance = this.dataReportStrategy.getInstance(subject);
                const beforeResult = await dataReportInstance.getChartData({ ...params, from: timeFrameList[0].startTime, to: timeFrameList[0].endTime });
                const beforeData = beforeResult.data as Chart.ModuleTotalDataRes;
                total = data.desc.value;
                beforeTotal = beforeData.desc.value;
            }
            //计算环比
            return beforeTotal !== 0 ? String(Math.round((total - beforeTotal) / beforeTotal * 100)) : '--';
        } catch (e) {
            this.logger.log(`getRatio error: ${e}`);
            return '--';
        }
    }

    /**
     * 数据看板数据模块修改
     * @param params 
     */
    async dataModuleUpdate(params: DateReportModule.DataModuleUpdateReq): Promise<DateReportModule.DataModuleUpdateRes> {
        const { moduleId, companyId, managerId, subject, topic, title, dimension, chartType, filters, type = 'person' } = params;
        if (!moduleId || !companyId || !managerId || !subject || !topic || !dimension || !chartType) throw new YqzException('必传参数不能为空');
        //校验 subject、topic、dimension、chartType
        await this.checkDataModule({ companyId, subject, topic, dimension, chartType, filters });
        const model = await this.dataReportModuleDao.findOne({ moduleId });
        if (!model) throw new YqzException('数据不存在');
        //修改数据模块
        await this.dataReportModuleDao.update(params.moduleId, { title: title || "", subject, topic, dimension, chartType, updateBy: managerId });
        // 布局更新调整
        await this.updateModule(
            {companyId, boardId: model.boardId.toHexString(), updateBy: managerId},
            type
        )
        //修改数据模块筛选配置(先删除，再新增)
        await this.dataReportModuleFilterDao.deleteByModuleId(moduleId);
        await this.saveDataReportModuleFilter(params.moduleId, filters);
        return { moduleId: params.moduleId };
    }

    /**
     * 数据看板数据模块复制
     * @param params 
     */
    async dataModuleCopy(params: DateReportModule.DataModuleCopyReq): Promise<DateReportModule.DataModuleCopyRes> {
        const { companyId, boardId, copyModuleId, managerId, type } = params;
        if (!companyId || !managerId || !boardId || !copyModuleId) throw new YqzException('必传参数不能为空');
        //找到布局中的模块布局信息
        // const board = await this.dataReportBoardDao.findById({ boardId });
        // this.logger.log(`dataModuleCopy board: ${JSON.stringify(board)}`);
        // const copyBlock = (board?.blocks || []).find((item) => item.moduleId.toHexString() === copyModuleId);
        const board = await this.searchBlockByType(boardId,copyModuleId,type)
        const copyBlock = board?.block;
        if (!board || !copyBlock) throw new YqzException('数据不存在');
        //模块查询模块的具体信息
        const copyModule = await this.dataReportModuleDao.findOne({ moduleId: copyModuleId });
        this.logger.log(`dataModuleCopy copyModule: ${JSON.stringify(copyModule)}`);
        if (!copyModule) throw new YqzException('数据不存在');
        //查询模块筛选配置
        const copyModuleFilters = await this.dataReportModuleFilterDao.find({ moduleId: copyModuleId });
        //保存复制的数据模块
        const newModule = await this.dataModuleSave({
            companyId,
            managerId,
            boardId: params?.saveBoardId || boardId,
            type: params?.saveType || type,
            subject: copyModule.subject,
            topic: copyModule.topic,
            title: copyModule.title || "",
            dimension: copyModule.dimension,
            chartType: copyModule.chartType,
            gridColumn: copyBlock?.gridColumn,
            filters: (copyModuleFilters || []).map(item => ({ filterName: item.filterName, filterField: item.filterField, filterType: item.filterType, filterValue: item.filterValue }))
        });
        return { moduleId: newModule.moduleId };
    }

    /**
     * 数据看板数据模块添加
     * @param params 
     */
    async dataModuleSave(params: DateReportModule.DataModuleSaveReq): Promise<DateReportModule.DataModuleSaveRes> {
        const { companyId, managerId, boardId, subject, topic, title, dimension, chartType, gridColumn, filters, type = 'person' } = params;
        if (!companyId || !managerId || !boardId || !subject || !topic || !dimension || !chartType) throw new YqzException('必传参数不能为空');
        //校验 subject、topic、dimension、chartType
        await this.checkDataModule({ companyId, subject, topic, dimension, chartType, filters });
        //保存复制的数据模块
        const newModuleId = await this.dataReportModuleDao.save({ boardId: new ObjectId(boardId), companyId, title: title || "", subject, topic, dimension, chartType, createBy: managerId, type });
        //数据模块添加到布局
        await this.addModule(
            { companyId, boardId: boardId, moduleId: newModuleId.toHexString(), gridColumn: gridColumn || '1', updateBy: managerId }
            ,type
        );
        //新增数据模块筛选配置
        await this.saveDataReportModuleFilter(newModuleId.toHexString(), filters);
        return { moduleId: newModuleId.toHexString() };
    }

    /**
     * 保存数据模块筛选配置
     * @param moduleId 
     * @param filters 
     */
    private async saveDataReportModuleFilter(moduleId: string, filters: DataReportModuleFilter.DataReportModuleFilter[]) {
        if (!moduleId || _.isEmpty(filters)) return;
        const saveList: DataReportModuleFilter.DataReportModuleFilterEntity[] = [];
        for (const filter of filters) {
            const filterValue = DataReportModuleFilter.formatFilterValue(filter);
            saveList.push({ moduleId: new ObjectId(moduleId), filterName: filter.filterName, filterField: filter.filterField, filterType: filter.filterType, filterValue })
        }
        await this.dataReportModuleFilterDao.batchSave(saveList);
    }

    /**
     * 数据看板数据模块添加到指定布局
     * @param params 
     * @param type person-个人|template-仪表盘模版
     */
    async addModule(params: { companyId: string, boardId: string, moduleId: string, gridColumn: string, updateBy?: string }, type: string) {
        const { companyId, boardId, moduleId, gridColumn, updateBy } = params;
        switch(type) {
            case 'person':
                await this.dataReportBoardDao.addModule({ boardId, moduleId: moduleId, gridColumn: gridColumn || '1' });
                break;
            case 'template':
                const result = await this.dashboardTemplateDao.addModule({
                    templateId: String(boardId),
                    moduleId: moduleId,
                    gridColumn: gridColumn || '1',
                    updateBy
                })
                // 如果更新成功，标记所有使用该模板的用户有新的更新
                if (result) {
                    await this.dashboardTemplateTagService.markTemplateUpdated(companyId, String(boardId));
                }
                break;
            default:
                break;
        }
    }

    /**
     * 数据看板数据模块更新到指定布局
     * @param params 
     * @param type person-个人|template-仪表盘模版
     */
    async updateModule(params: { companyId: string, boardId: string, updateBy?: string }, type: string) {
        const { companyId, boardId, updateBy } = params;
        switch(type) {
            case 'person':
                break;
            case 'template':
                const result = await this.dashboardTemplateDao.updateModule({
                    templateId: String(boardId),
                    updateBy
                })
                // 如果更新成功，标记所有使用该模板的用户有新的更新
                if (result) {
                    await this.dashboardTemplateTagService.markTemplateUpdated(companyId, String(boardId));
                }
                break;
            default:
                break;
        }
    }

    /**
     * 数据看板数据模块删除
     * @param params 
     */
    async dataModuleDelete(params: DateReportModule.DataModuleDeleteReq): Promise<DateReportModule.DataModuleDeleteRes> {
        const { companyId, managerId, boardId, moduleId, type = 'person' } = params;
        if (!companyId || !managerId || !boardId || !moduleId) throw new YqzException('必传参数不能为空');
        let board,delBlock;
        //找到布局中的模块布局信息
        switch(type) {
            case 'person':
                board = await this.dataReportBoardDao.findById({ boardId });
                this.logger.log(`dataModuleDelete board: ${JSON.stringify(board)}`);
                delBlock = (board?.blocks || []).find((item) => item.moduleId.toHexString() === moduleId);
                if (!board || !delBlock) throw new YqzException('数据不存在');
                // await this.dataReportModuleDao.delete(params.moduleId);//也可以删除模块，但是考虑用户误操作建议还是保留
                //从布局中删除模块
                await this.dataReportBoardDao.delModule({ boardId, moduleId });
                break;
            case 'template':
                board = await this.dashboardTemplateDao.findTemplateById(boardId);
                this.logger.log(`dataModuleDelete board: ${JSON.stringify(board)}`);
                let blocks = board?.dashboardConfig || [];
                blocks = blocks.map(res => {
                    if (res.moduleId && ObjectId.isValid(res.moduleId) && !_.isString(res.moduleId))
                        res.moduleId = res.moduleId.toHexString()
                    return res
                })
                delBlock = blocks.find((item) => item.moduleId === moduleId);
                if (!board || !delBlock) throw new YqzException('数据不存在');
                // await this.dataReportModuleDao.delete(params.moduleId);//也可以删除模块，但是考虑用户误操作建议还是保留
                //从布局中删除模块
                await this.dashboardTemplateDao.delModule({ boardId, moduleId });
                break;
            default:
                break;
        }
        return { moduleId: params.moduleId };
    }

    /**
     * 数据看板数据模块 - 数据分析筛选项
     * @param params 
     */
    async dataModuleAnalysisOption(params: DateReportModule.DataModuleAnalysisOptionReq) {
        const { companyId, managerId, departmentId, moduleId } = params;
        if (!companyId || !managerId || !departmentId || !moduleId) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //模块查询模块的具体信息
        const module = await this.dataReportModuleDao.findOne({ moduleId });
        this.logger.log(`dataModuleAnalysisOption module: ${JSON.stringify(module)}`);
        if (!module) throw new YqzException('数据不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(module.subject);
        return await dataReportInstance.getAnalysisOption({ ...params, topic: module.topic, companyType: company.COMPANY_TYPE, businessCategory: company.business_category }) || {};
    }

    /**
     * 数据看板数据模块 - 数据分析列表
     * @param params 
     */
    async dataModuleAnalysisList(params: DateReportModule.DataModuleAnalysisListReq) {
        const { companyId, managerId, departmentId, moduleId } = params;
        if (!companyId || !managerId || !departmentId || !moduleId) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //模块查询模块的具体信息
        const module = await this.dataReportModuleDao.findOne({ moduleId });
        this.logger.log(`dataModuleAnalysisList module: ${JSON.stringify(module)}`);
        if (!module) throw new YqzException('数据不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(module.subject);
        return await dataReportInstance.getAnalysisList({ ...params, companyType: company.COMPANY_TYPE, topic: module.topic, businessCategory: company.business_category }) || { total: 0, list: [] };
    }

    /**
     * 数据看板数据模块 - 数据分析分布
     * @param params 
     */
    async dataModuleAnalysisDistribute(params: DateReportModule.DataModuleAnalysisDistributeReq): Promise<DateReportModule.DataModuleAnalysisDistributeRes> {
        const { companyId, managerId, departmentId, moduleId } = params;
        if (!companyId || !managerId || !departmentId || !moduleId) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //模块查询模块的具体信息
        const module = await this.dataReportModuleDao.findOne({ moduleId });
        this.logger.log(`dataModuleAnalysisDistribute module: ${JSON.stringify(module)}`);
        if (!module) throw new YqzException('数据不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(module.subject);
        const distributeList = await dataReportInstance.getAnalysisDistributeCharts({ ...params, subject: module.subject, topic: module.topic, businessCategory: company.business_category }) || [];
        return { moduleId, distributeList };
    }

    /**
     * 数据看板数据模块 - 数据分析分布详情
     * @param params 
     */
    async dataModuleAnalysisDistributeDetail(params: DateReportModule.DataModuleAnalysisDistributeDetailReq) {
        const { companyId, managerId, departmentId, moduleId, dimension } = params;
        if (!companyId || !managerId || !departmentId || !moduleId || !dimension) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //模块查询模块的具体信息
        const module = await this.dataReportModuleDao.findOne({ moduleId });
        this.logger.log(`dataModuleAnalysisDistributeDetail module: ${JSON.stringify(module)}`);
        if (!module) throw new YqzException('数据不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(module.subject);
        return await dataReportInstance.getAnalysisDistributeDetail({ ...params, subject: module.subject, topic: module.topic, businessCategory: company.business_category }) || { dimension, total: 0, items: [] };
    }

    /**
     * 数据看板数据模块 - 数据分析导出
     * <AUTHOR>
     * @param params 
     */
    async dataModuleAnalysisExport(params: DateReportModule.DataModuleAnalysisExportReq) {
        const { companyId, managerId, departmentId, moduleId, dimension } = params;
        if (!companyId || !managerId || !departmentId || !moduleId) throw new YqzException('必传参数不能为空');
        //查询公司类型
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //模块查询模块的具体信息
        const module = await this.dataReportModuleDao.findOne({ moduleId });
        this.logger.log(`dataModuleAnalysisDistribute module: ${JSON.stringify(module)}`);
        if (!module) throw new YqzException('数据不存在');
        const dataReportInstance = this.dataReportStrategy.getInstance(module.subject);
        //查询 所有/单个 维度详情信息，返回csv格式
        const dimensions = await dataReportInstance.getAnalysisFromDistributes({ companyId, topic: module.topic, subject: module.subject, businessCategory: company.business_category, dimension });
        const topicName = await dataReportInstance.getTopicName({ companyId, topic: module.topic, subject: module.subject });
        const promiseList = [];
        const dimensionNames = [];
        for (const dimension of dimensions) {
            dimensionNames.push(`${topicName}-${dimension.name}`);
            promiseList.push(this.dataModuleAnalysisDistributeDetail({ ...params, dimension: dimension.value, format: 'csv' }));
        }
        //查询列表信息，返回csv格式
        if (!dimension) {
            dimensionNames.push(`${topicName}-数据明细`);
            promiseList.push(this.dataModuleAnalysisList({ ...params, format: 'csv' }));
        }
        const csvResults = await Promise.all(promiseList);
        //生成excel文件
        const workbook = new ExcelJS.Workbook();
        csvResults.forEach((csvData, index) => {
            const sheet = workbook.addWorksheet(dimensionNames[index]);
            if (typeof csvData !== 'string') return;
            const records = parse(csvData, { columns: true, skip_empty_lines: true });
            if (records.length > 0) {
                sheet.columns = Object.keys(records[0]).map(key => ({ header: key, key }));
                records.forEach(row => sheet.addRow(row));
            }
        });
        // 直接返回 Excel 文件流
        const buffer = await workbook.xlsx.writeBuffer();
        const subjectName = DateReport.SubjectName[module.subject];
        // 文件名
        const dimensionName = dimension && !_.isEmpty(dimensions) ? dimensions[0].name : '';
        const fileName = !dimension ? `${subjectName}-${topicName}.xlsx` : `${subjectName}-${topicName}-${dimensionName}.xlsx`;
        return { buffer, fileName };
    }

}