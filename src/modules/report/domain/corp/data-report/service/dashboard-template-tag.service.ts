import { Injectable } from "@nestjs/common";
import { MyLogger, MyRedis, YqzException } from "@yqz/nest";
import { DashboardTemplateDao } from "../dao/dashboard-template.dao";

@Injectable()
export class DashboardTemplateTagService {
    private readonly logger = new MyLogger(DashboardTemplateTagService.name);

    constructor(
        private readonly dashboardTemplateDao: DashboardTemplateDao,
    ) { }

    /**
     * 清除模板更新标记
     * 用于用户查看了模板更新后，清除更新标记
     * @param companyId 公司ID
     * @param managerId 成员ID
     * @param templateId 模板ID
     * @returns 清除结果
     */
    async clearTemplateUpdateFlag(
        companyId: string,
        managerId: string,
        templateId: string
    ): Promise<boolean> {
        if (!templateId || !managerId || !companyId) {
            // 保持您自定义的异常类
            throw new YqzException('必传参数不能为空');
        }

        try {
            // Redis键格式: dashboard:template:updates:{companyId}
            const redisKey = `dashboard:template:updates:${companyId}`;
            // 字段格式: {managerId}:{templateId}
            const redisField = `${managerId}:${templateId}`;

            // 使用 MyRedis.hdel 从Redis中删除该字段
            // hdel 会返回删除的字段数量 (0 或 1 在这种情况下)
            // 如果键或字段不存在，它也会成功并返回 0
            await MyRedis.hmdel(redisKey, redisField);

            return true; // 操作成功（即使字段原先不存在也被视为成功清除）
        } catch (error) {
            this.logger.error(`清除模板更新标记出错: ${error.message}`, error.stack);
            return false;
        }
    }

    /**
     * 标记模板已更新
     * 当模板更新时，标记所有使用该模板的用户有新的更新
     * @param companyId 公司ID
     * @param templateId 模板ID
     */
    async markTemplateUpdated(companyId: string, templateId: string): Promise<void> {
        try {
            // 查找使用此模板的所有用户
            const users = await this.dashboardTemplateDao.findUsersWithTemplate(companyId, templateId);
            const now = Date.now().toString();

            if (users && users.length > 0) {
                // Redis键格式: dashboard:template:updates:{companyId}
                const redisKey = `dashboard:template:updates:${companyId}`;

                // 为每个用户设置更新标记
                for (const user of users) {
                    const managerId = user.managerId;
                    // 字段格式: {managerId}:{templateId}
                    const redisField = `${managerId}:${templateId}`;
                    // 使用 MyRedis.hmset 来设置单个哈希字段
                    await MyRedis.hmset(redisKey, { [redisField]: now });
                }

                // // 设置过期时间（例如30天）
                // const expiryInSeconds = 60 * 60 * 24 * 30;
                // await MyRedis.expire(redisKey, expiryInSeconds);
            }
        } catch (error) {
            this.logger.error(`标记模板更新出错: ${error.message}`, error.stack);
        }
    }

    /**
     * 从Redis获取模板更新状态
     * @param companyId 公司ID
     * @param managerId 成员ID
     * @param templateIds 模板ID数组
     * @returns 更新状态映射
     */
    async getTemplateUpdateStatusFromRedis(
        companyId: string,
        managerId: string,
        templateIds: string[]
    ): Promise<Record<string, boolean>> {
        try {
            // Redis键格式: dashboard:template:updates:{companyId}
            const redisKey = `dashboard:template:updates:${companyId}`;
            const updateStatusMap: Record<string, boolean> = {};

            // 串行地为每个模板ID获取更新状态
            for (const templateId of templateIds) {
                // 字段格式: {managerId}:{templateId}
                const redisField = `${managerId}:${templateId}`;

                // 调用 MyRedis 中的 hmget 方法并等待其完成
                const result = await MyRedis.hmget(redisKey, redisField);

                // MyRedis.hmget 在字段存在时返回其值(字符串), 否则返回 null
                // 我们检查结果是否为真值 (非 null/undefined) 来判断状态
                updateStatusMap[templateId] = !!result;
            }

            return updateStatusMap;
        } catch (error) {
            this.logger.error(`获取模板更新状态出错: ${error.message}`, error.stack);
            // 在错误情况下返回空对象或根据需要处理
            return {};
        }
    }

    /**
     * 检查指定管理员的特定模板是否有未读更新。
     * 此方法用于红点提示，前提是调用方已知用户当前活跃的模板ID。
     * 它通过查询Redis中是否存在 `managerId:templateId` 格式的字段来判断。
     *
     * @param companyId 公司ID，用于定位Redis哈希表。
     * @param managerId 成员ID。
     * @returns Promise<boolean> 如果指定模板存在该用户的更新标记，则返回 true；否则返回 false。
     *                         如果发生错误或参数无效，也返回 false 并记录错误。
     */
    async hasUnreadUpdateForSpecificTemplate(
        companyId: string,
        managerId: string
    ): Promise<boolean> {
        if (!companyId || !managerId) {
            this.logger.warn('hasUnreadUpdateForSpecificTemplate: 公司ID、成员ID和模板ID均不能为空');
            return false; 
        }

        // 获取用户当前使用的模板信息
        const userSettings = await this.dashboardTemplateDao.getUserDashboardSettings(managerId, companyId);
        const templateId = userSettings?.currentTemplateId
        if (!userSettings || !templateId) return false

        try {
            // Redis键格式: dashboard:template:updates:{companyId}
            const redisKey = `dashboard:template:updates:${companyId}`;
            // 字段格式: {managerId}:{templateId}
            const redisField = `${managerId}:${templateId}`;

            // MyRedis.hmget(key, field) 在字段存在时返回其值(字符串)，否则返回 null。
            const updateTimestamp = await MyRedis.hmget(redisKey, redisField);

            // 如果 updateTimestamp 不是 null 或 undefined (即字段存在)，则表示有更新。
            return !!updateTimestamp;
        } catch (error) {
            this.logger.error(
                `检查特定模板更新状态出错: companyId=${companyId}, managerId=${managerId}, templateId=${templateId}. Error: ${error.message}`,
                error.stack
            );
            // 在发生任何Redis操作错误或其它意外时，保守地返回false
            return false;
        }
    }
}