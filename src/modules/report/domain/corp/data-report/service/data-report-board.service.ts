import { Injectable } from "@nestjs/common";
import { MyLogger, YqzException } from "@yqz/nest";
import { DateReportBoard } from "../dto/data-report-board.dto";
import { DataReportBoardDao } from "../dao/data-report-board.dao";
import { DateReport } from "../dto/data-report.dto";
import * as _ from "lodash";
import { DataReportStrategy } from "./basic/data-report.strategy";
import { DataReportModuleDao } from "../dao/data-report-module.dao";
import { DateReportModule } from "../dto/data-report-module.dto";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";

/**
 * @summary 数据看板 - 布局
 * @class DataReportService
 */
@Injectable()
export class DataReportBoardService {
    private readonly logger = new MyLogger(DataReportBoardService.name);

    constructor(
        private readonly dataReportBoardDao: DataReportBoardDao,
        private readonly dataReportModuleDao: DataReportModuleDao,
        private readonly dataReportStrategy: DataReportStrategy,
        private readonly companyDao: CompanyDao
    ) { }

    /**
     * 数据看板数据布局查询(没有则创建默认布局返回)
     * @param params 
     * @returns 
     */
    async dataBoardSearch(params: DateReportBoard.DataBoardReq): Promise<DateReportBoard.DataBoardRes> {
        const { companyId, managerId, subject } = params;
        if (!companyId || !subject) throw new YqzException('必传参数不能为空');
        if (subject === DateReport.Subject.Delivery && !managerId) throw new YqzException('必传参数不能为空');
        //布局类型 - 公司看板/个人看板
        const boardType = subject === DateReport.Subject.Delivery ? DateReportBoard.BoardType.Manager : DateReportBoard.BoardType.Company;
        const data = await this.dataReportBoardDao.findOne({ companyId, subject, boardType, managerId });
        //没有布局 - 新建默认布局
        if (!data) return await this.initializeDefaultDataReportBoard({ companyId, subject, boardType, managerId });
        return {
            boardId: data.id.toHexString(),
            subject: data.subject,
            blocks: data.blocks.map(res => ({ ...res, moduleId: res.moduleId.toHexString() }))
        };
    }

    /**
     * 数据看板数据布局修改
     * @param params 
     * @returns 
     */
    async dataBoardUpdate(params: DateReportBoard.DataBoardUpdateReq): Promise<DateReportBoard.DataBoardUpdateRes> {
        const { companyId, managerId, boardId, blocks } = params;
        if (!companyId || !managerId || !boardId) throw new YqzException('必传参数不能为空');
        if (!_.isEmpty(blocks)) blocks.forEach(res => {
            if (!res.moduleId || !res.gridColumn) throw new YqzException('必传参数不能为空');
            const numberValue = Number(res.gridColumn);
            if (isNaN(numberValue) || numberValue <= 0) throw new YqzException('参数错误');
        });
        await this.dataReportBoardDao.update(boardId, { blocks, updateBy: managerId });
        return { boardId };
    }

    /**
     * 初始化默认数据看板数据布局
     * @param params 
     */
    private async initializeDefaultDataReportBoard(params: { boardType: DateReportBoard.BoardType, companyId: string, managerId?: string, subject: DateReport.Subject }): Promise<DateReportBoard.DataBoardRes> {
        const { boardType, companyId, managerId, subject } = params;
        this.logger.log(`initializeDefaultDataReportBoard params: ${JSON.stringify(params)}`);
        const company = await this.companyDao.find({ companyId });
        if (!company?.business_category) throw new YqzException('公司类型不存在');
        //查询看板数据默认布局
        const dataReportInstance = this.dataReportStrategy.getInstance(subject);
        const defaultBoard = dataReportInstance.getDefaultBoard(subject, company.business_category);
        if (!defaultBoard) throw new YqzException('默认布局不存在');
        //查询/初始化数据看板
        const data = await this.dataReportBoardDao.findOne({ companyId, subject, boardType, managerId });
        const dataReportBoardId = data?.id || await this.dataReportBoardDao.save({ boardType, companyId, managerId, subject, blocks: [], createBy: managerId });
        if (!dataReportBoardId) throw new YqzException('数据看板初始化失败');
        //保存数据模块
        const dataReportModules: DateReportModule.DataReportModuleEntity[] = [];
        for (const block of (defaultBoard.blocks || [])) {
            dataReportModules.push({
                boardId: dataReportBoardId,
                companyId,
                subject: block.subject,
                topic: block.topic,
                dimension: block.dimension,
                title: block.title,
                chartType: block.chartType,
                createBy: managerId
            });
        }
        const moduleIds = !_.isEmpty(dataReportModules) ? await this.dataReportModuleDao.batchSave(dataReportModules) : [];
        //修改数据看板 数据模块
        const blocks = moduleIds.map((moduleId, index) => ({ moduleId: moduleId.toHexString(), gridColumn: defaultBoard.blocks[index].gridColumn }));
        await this.dataReportBoardDao.update(dataReportBoardId.toHexString(), { blocks, updateBy: managerId });
        return { boardId: dataReportBoardId.toHexString(), subject, blocks };
    }
}