import { Injectable } from "@nestjs/common";
import { DateReport } from "../../dto/data-report.dto";
import { AiDetectionDataReportService } from "./service/ai-detection-data-report.service";
import { MyLogger, YqzException } from "@yqz/nest";
import { CustomChart } from "../../../custom-charts/abstract/custom-chart.abstract";
import { ProjectDataReportService } from "./service/project-data-report.service";
import { DeviceDataReportService } from "./service/device-data-report.service";
import { AcceptanceReportDataReportService } from "./service/acceptance-report-data-report.service";
import { NodeOverdueDataReportService } from "./service/node-overdue-data-report.service";
import { CustomerEvaluateDataReportService } from "./service/customer-evaluate-data-report.service";
import { ProjectProblemDataReportService } from "./service/project-problem-data-report.service";
import { DeliveryDataReportService } from "./service/delivery-data-report.service";

/**
 * @summary 数据看板 - 策略
 * 根据subject 找到对应的子类
 * @class DataReportStrategy
 */
@Injectable()
export class DataReportStrategy {
    private readonly logger: MyLogger = new MyLogger(DataReportStrategy.name);
    private readonly detectionClassMapping: { [key in DateReport.Subject]: CustomChart };

    constructor(
        readonly aiDetectionDataReportService: AiDetectionDataReportService,
        readonly projectDataReportService: ProjectDataReportService,
        readonly deviceDataReportService: DeviceDataReportService,
        readonly acceptanceReportDataReportService: AcceptanceReportDataReportService,
        readonly nodeOverdueDataReportService: NodeOverdueDataReportService,
        readonly customerEvaluateDataReportService: CustomerEvaluateDataReportService,
        readonly projectProblemDataReportService: ProjectProblemDataReportService,
        readonly deliveryDataReportService: DeliveryDataReportService
    ) {
        //定义检测类型 和 class类的映射 （使用时才会加载到内存）
        this.detectionClassMapping = {
            [DateReport.Subject.Delivery]: this.deliveryDataReportService,
            [DateReport.Subject.Project]: this.projectDataReportService,
            [DateReport.Subject.Device]: this.deviceDataReportService,
            [DateReport.Subject.AiDetection]: this.aiDetectionDataReportService,
            [DateReport.Subject.AcceptanceReport]: this.acceptanceReportDataReportService,
            [DateReport.Subject.NodeOverdue]: this.nodeOverdueDataReportService,
            [DateReport.Subject.Report]: undefined,
            [DateReport.Subject.OnsitePatrol]: undefined,
            [DateReport.Subject.ProjectUpdate]: undefined,
            [DateReport.Subject.OnlinePatrol]: undefined,
            [DateReport.Subject.Dispatch]: undefined,
            [DateReport.Subject.ProjectProblem]: this.projectProblemDataReportService,
            [DateReport.Subject.Evaluate]: this.customerEvaluateDataReportService,
            [DateReport.Subject.Live]: undefined,
        }
    }

    getInstance(subject: DateReport.Subject) {
        this.logger.log(`get data report service for subject: ${subject}`);
        const instance = this.detectionClassMapping[subject];
        if (!instance) throw new YqzException(`Unknown subject: ${subject}`);
        return instance;
    }

}