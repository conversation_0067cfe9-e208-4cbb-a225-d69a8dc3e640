import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>qzException } from "@yqz/nest";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { Injectable } from "@nestjs/common";
import { ChartFactory } from "../../../chart/chart.factory";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { DateReport } from "../../../dto/data-report.dto";
import { Chart } from "../../../chart/chart.type";
import { AcceptanceReportDataReport } from "../dto/acceptance-report-data-report.dto";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import * as DateFns from "date-fns";
import * as _ from "lodash";
import { AcceptanceReportDao } from "@src/modules/report/domain/bgw/acceptance-report/dao/acceptance-report.dao";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";
import { CompanyService } from "@src/modules/report/domain/bgw/company/service/company.service";
import { RoleService } from "@src/modules/report/domain/bgw/role/service/role.service";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { AcceptanceReportService } from "@src/modules/report/domain/bgw/acceptance-report/service/acceptance-report.service";
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import { DataReportConfigService } from "../../data-report-config.service";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";

/**
 * @summary 数据看板 - 线上验收
 * <AUTHOR>
 * @class AcceptanceReportDataReportService
 */
@Injectable()
export class AcceptanceReportDataReportService extends CustomChart {
    public readonly logger = new MyLogger(AcceptanceReportDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly acceptanceReportDao: AcceptanceReportDao,
        private readonly chartFactory: ChartFactory,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly projectService: ProjectService,
        private readonly companyService: CompanyService,
        private readonly roleService: RoleService,
        private readonly acceptanceReportService: AcceptanceReportService,
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
     * 获取报告图表数据
     * @param params 
     */
    async getChartData(params: AcceptanceReportDataReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { dimension } = params;
        //获取图表数据范围筛选条件
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params) as { idList: string[] };
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new Error(`Unsupported dimension: ${dimension}`);
        //获取图表数据
        const result = await method({ ...params, idList });
        //处理辅助数据
        const countProjectRes = await this.acceptanceReportDao.countProjectAcceptance({ ...params, idList });
        result.data.desc.subLabel = "工地数";
        result.data.desc.subValue = Number(countProjectRes?.count || 0);
        return result;
    }

    /**
     * 获取图表必要筛选条件(处理topic筛选和sql筛选的映射关系)
     * @param params 
     */
    async getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string; subject: DateReport.Subject; topic: string; addFilters: Sql.Column[]; }> {
        const { companyId, topic, from, to, departmentId } = params;
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        const subject = DateReport.Subject.AcceptanceReport;
        //查询部门id列表
        const deptList = await this.getDeptIdList({ departmentId, deptDataType, startTime: timeFrameList.startTime, endTime: timeFrameList.endTime });
        const addFilters: Sql.Column[] = [
            { key: "departmentId", value: deptList, operator: Sql.Operator.in },
            { key: "acceptanceDate", value: timeFrameList.startTime, operator: Sql.Operator.greater_than_or_equal },
            { key: "acceptanceDate", value: timeFrameList.endTime, operator: Sql.Operator.less_than }
        ];
        // this.logger.log(`getChartSqlFilters params : ${JSON.stringify(params)}, addFilters: ${JSON.stringify(addFilters)}`);
        return { companyId, subject, topic, addFilters };
    }

    /**
     * 数据看板数据模块 - 数据范围选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return AcceptanceReportDataReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param params 
     */
    async getAnalysisOption(params: AcceptanceReportDataReport.AnalysisOptionReq): Promise<AcceptanceReportDataReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, businessCategory, companyType } = params;
        if (!companyId || !topic) return null;
        const result: AcceptanceReportDataReport.AnalysisOptionRes = {
            projectManager: [],
            acceptanceCriteria: [],
            acceptanceStatus: [],
            rejectedCount: [],
            refuseCount: [],
            projectAddress: ""
        };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        const deviceDepartment: DataReportModuleFilter.SelectFilterValue[] = [];
        //获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        //获取筛选项数据
        const promiseList = [];
        promiseList.push(companyType === Company.CompanyType.Conglomerate ? this.acceptanceReportDao.countAcceptanceGroupCompany({ idList }) : []);//所属公司
        promiseList.push(businessCategory === 'construction' ? this.acceptanceReportDao.countAcceptanceGroupDeviceDepartment({ idList }) : []);//服务厂商
        promiseList.push(this.acceptanceReportDao.countAcceptanceGroupProjectManager({ idList }));//工地负责人
        promiseList.push(this.acceptanceReportDao.countAcceptanceGroupReportCriteria({ idList }));//验收节点
        const [linkCompanyRes, deviceDepartmentRes, projectManagerRes, reportCriteriaRes] = await Promise.all(promiseList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(res => linkCompany.push({ value: res.companyId, name: res.companyName }));
        (deviceDepartmentRes || []).filter(res => !!res.deviceDepartmentId).map(res => deviceDepartment.push({ value: res.deviceDepartmentId, name: res.deviceDepartmentName }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(res => result.projectManager.push({ value: res.projectManagerId, name: res.projectManagerName }));
        (reportCriteriaRes || []).filter(res => !!res.acceptanceCriteriaId).map(res => result.acceptanceCriteria.push({ value: res.acceptanceCriteriaId, name: res.acceptanceCriteriaName }));
        //报告状态
        _.map(AcceptanceReportDataReport.Status, (value) => result.acceptanceStatus.push({ value, name: AcceptanceReportDataReport.StatusName[value] }));
        //审核不通过次数
        _.map(AcceptanceReportDataReport.RejectedCount, (value) => result.rejectedCount.push({ value, name: AcceptanceReportDataReport.RejectedCountName[value] }));
        //业主不通过次数
        _.map(AcceptanceReportDataReport.RefuseCount, (value) => result.refuseCount.push({ value, name: AcceptanceReportDataReport.RefuseCountName[value] }));
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        //营建公司 独有筛选项
        if (businessCategory === 'construction') Object.assign(result, { deviceDepartment });
        return result;
    }

    /**
     * 数据分析列表
     * @param params 
     */
    @CheckFormatCsv(AcceptanceReportDataReport.AnalysisListCsv)
    async getAnalysisList(params: AcceptanceReportDataReport.AnalysisListReq): Promise<{ total: number; items: any[]; }> {
        const { companyType, companyId, topic, from, to, dataType, departmentId, businessCategory } = params;
        const result: { total: number; items: any } = { total: 0, items: [] };
        if (!companyId || !topic) return result;
        //获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        if (_.isEmpty(idList)) return result;
        params.idList = idList;
        const acceptanceRes = await this.acceptanceReportDao.searchList({ ...params });
        //查询其他数据
        const ids = [];
        const projectIds = [];
        const companyIds = [];
        const roleIds = [];
        acceptanceRes.items.forEach(item => {
            ids.push(item.id);
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
            roleIds.push(...(item?.inspectors || "").split(","));
        });
        const promiseList = [];
        promiseList.push(this.acceptanceReportService.searchRejectedInfoMap({ idList: ids }));//审核拒绝信息
        promiseList.push(businessCategory === 'home_decor' ? this.acceptanceReportService.searchRefuseInfoMap({ idList: ids }) : {}); //业主拒绝信息
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        promiseList.push(this.roleService.searchRoleInfoMap({ roleIds }));//角色信息
        const [rejectedInfoMap, refuseInfoMap, projectInfoMap, companyInfoMap, inspectorInfoMap] = await Promise.all(promiseList);
        //组装数据
        result.total = acceptanceRes.total;
        result.items = acceptanceRes.items.map(item => {
            const result = {
                ...item,
                rejectedCount: rejectedInfoMap[item.id]?.rejectedCount || "0",
                rejectedReason: rejectedInfoMap[item.id]?.rejectedReason || "",
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                inspectorNames: (item?.inspectors || "").split(",").map(roleId => inspectorInfoMap[roleId]?.roleName).filter(roleName => !!roleName).join(","),
                acceptanceStatusName: AcceptanceReportDataReport.StatusName[item.acceptanceStatus] || "",
            }
            //集团公司 独有数据
            if (companyType === '4') {
                Object.assign(result, {
                    linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
                });
            }
            //家装公司 独有数据
            if (businessCategory === 'home_decor') {
                Object.assign(result, {
                    refuseCount: refuseInfoMap[item.id]?.refuseCount || "0",
                    refuseReason: refuseInfoMap[item.id]?.refuseReason || "",
                });
            }
            //营建公司 独有数据
            if (businessCategory === 'construction') {
                Object.assign(result, {
                    deviceDepartmentId: projectInfoMap[item.projectId]?.deviceDepartmentId || "",
                    deviceDepartmentName: projectInfoMap[item.projectId]?.deviceDepartmentName || "",
                });
            }
            return result;
        });
        return result;
    }

    /**===================================
                处理图表数据
    ===================================**/

    //维度图表 方法映射
    getDimensionChartDataFunction(dimension: AcceptanceReportDataReport.Dimension) {
        const dimensionChartDataFunction: Readonly<{ [K in AcceptanceReportDataReport.Dimension]: (params: AcceptanceReportDataReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [AcceptanceReportDataReport.Dimension.ProjectDepartmentGroup]: this.getProjectDepartmentGroupChartData.bind(this), // 工地部门
            [AcceptanceReportDataReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [AcceptanceReportDataReport.Dimension.ServiceVendorGroup]: this.getDeviceDepartmentGroupChartData.bind(this), // 服务厂商
            [AcceptanceReportDataReport.Dimension.ReportStatusGroup]: this.getReportStatusGroupChartData.bind(this), // 状态
            [AcceptanceReportDataReport.Dimension.ReportCriteriaGroup]: this.getReportCriteriaGroupChartData.bind(this), // 节点
            [AcceptanceReportDataReport.Dimension.Trend]: this.getTrendChartData.bind(this),//趋势
        };
        return dimensionChartDataFunction[dimension];
    }

    /**
    * 获取工地部门分布图表数据
    * 维度: 工地部门分布
    * 支持的图表结构: 玫瑰图|饼图|环形图
    * @param params 
    * @returns 
    */
    private async getProjectDepartmentGroupChartData(params: AcceptanceReportDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, departmentId } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.acceptanceReportDao.countAcceptanceGroupDepartment(params);
        //处理图表结构: 工地部门分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const entity = await this.companyDecorationDao.search(params.companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.from, data: projectData });
        const title = params?.title || AcceptanceReportDataReport.TopicName[topic];
        const name = AcceptanceReportDataReport.DimensionName[AcceptanceReportDataReport.Dimension.ProjectDepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = AcceptanceReportDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.AcceptanceReport,
            dimension: AcceptanceReportDataReport.Dimension.ProjectDepartmentGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取数值图表数据
     * 维度: 数值
     * 支持的图表结构: 数值
     * @param params 
     */
    private async getNumberChartData(params: AcceptanceReportDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const detectionData = await this.acceptanceReportDao.countAcceptance(params);
        //处理图表结构: 总数统计 & 数值
        const title = params?.title || AcceptanceReportDataReport.TopicName[topic];
        const name = AcceptanceReportDataReport.DimensionName[AcceptanceReportDataReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = AcceptanceReportDataReport.RealTopic.includes(topic) ? "Y" : "N";
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({ chartType: Chart.ChartType.Total, title, name, data: Number(detectionData?.count || 0), actionType, isReal });
        return {
            subject: DateReport.Subject.AcceptanceReport,
            dimension: AcceptanceReportDataReport.Dimension.Number,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取服务厂商分布图表数据
     * 维度: 服务厂商分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getDeviceDepartmentGroupChartData(params: AcceptanceReportDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.acceptanceReportDao.countAcceptanceGroupDeviceDepartment(params);
        //处理图表结构: 服务厂商分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => {
            data.push({ key: item.deviceDepartmentId || "", name: item.deviceDepartmentName || "无服务厂商", value: Number(item?.count || 0) });
        });
        const title = params?.title || AcceptanceReportDataReport.TopicName[topic];
        const name = AcceptanceReportDataReport.DimensionName[AcceptanceReportDataReport.Dimension.ServiceVendorGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = AcceptanceReportDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.AcceptanceReport,
            dimension: AcceptanceReportDataReport.Dimension.ServiceVendorGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取报告状态分布图表数据
     * 维度: 报告状态分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getReportStatusGroupChartData(params: AcceptanceReportDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.acceptanceReportDao.countAcceptanceGroupReportStatus(params);
        //处理图表结构: 报告状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => {
            data.push({ key: item.reportStatus || "", name: AcceptanceReportDataReport.StatusName[item.reportStatus] || "其他状态", value: Number(item?.count || 0) });
        });
        const title = params?.title || AcceptanceReportDataReport.TopicName[topic];
        const name = AcceptanceReportDataReport.DimensionName[AcceptanceReportDataReport.Dimension.ReportStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = AcceptanceReportDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'acceptanceStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'acceptanceStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'acceptanceStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'acceptanceStatus', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.AcceptanceReport,
            dimension: AcceptanceReportDataReport.Dimension.ReportStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取验收节点分布图表数据
     * 维度: 验收节点分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getReportCriteriaGroupChartData(params: AcceptanceReportDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.acceptanceReportDao.countAcceptanceGroupReportCriteria(params);
        //处理图表结构: 验收节点分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.acceptanceCriteriaId || "", name: item.acceptanceCriteriaName || "无验收节点", value: Number(item?.count || 0) }); });
        const title = params?.title || AcceptanceReportDataReport.TopicName[topic];
        const name = AcceptanceReportDataReport.DimensionName[AcceptanceReportDataReport.Dimension.ReportCriteriaGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = AcceptanceReportDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'acceptanceCriteriaId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'acceptanceCriteriaId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'acceptanceCriteriaId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'acceptanceCriteriaId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.AcceptanceReport,
            dimension: AcceptanceReportDataReport.Dimension.ReportCriteriaGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 查询周期数据
     * 用于: 分布趋势图表数据
     * @param params 
     * @returns 
     */
    private async getGroupTrendData(params: AcceptanceReportDataReport.AnalysisDistributeReq): Promise<{ category: string, value: number }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return null;
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType: "day", departmentId }) as { idList: string[] };
        params.idList = idList;
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        const res = await this.acceptanceReportDao.countAcceptance(params);
        return { category, value: Number(res?.count || 0) };
    }

    /**
     * 获取趋势图表数据
     * 维度: 趋势
     * 支持的图表结构: 折线图|柱状图
     * @param params 
     */
    private async getTrendChartData(params: AcceptanceReportDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { companyId, topic, from, to, dataType, chartType } = params;
        if (!companyId || !topic || !from || !to || !dataType || !chartType) throw new YqzException("必传参数不能为空");
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        //查询周期数据
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, value: number }[] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const data = [];//y轴数据
        promiseResult.forEach(res => {
            category.push(res?.category || "");
            data.push(res?.value || 0);
        });
        //处理图表结构: 趋势 & 折线图/柱状图
        const title = params?.title || `${AcceptanceReportDataReport.TopicName[topic]}`;
        const name = AcceptanceReportDataReport.DimensionName[AcceptanceReportDataReport.Dimension.Trend];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = AcceptanceReportDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Line:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Line>({ chartType: Chart.ChartType.Line, title, name, category, data, actionType, isReal });
                break;
            case Chart.ChartType.Bar:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Bar>({ chartType: Chart.ChartType.Bar, title, name, category, data, actionType, isReal });
                break;
        }
        return {
            subject: DateReport.Subject.AcceptanceReport,
            dimension: AcceptanceReportDataReport.Dimension.Trend,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

}