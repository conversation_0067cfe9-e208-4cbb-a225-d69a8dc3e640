import { Injectable } from "@nestjs/common";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { DateReport } from "../../../dto/data-report.dto";
import { <PERSON><PERSON><PERSON>at<PERSON><PERSON>, MyLogger } from "@yqz/nest";
import { ProjectProblemDataReport } from "../dto/project-problem-data-report.dto";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { Chart } from "../../../chart/chart.type";
import { ChartFactory } from "../../../chart/chart.factory";
import { ProjectProblemDao } from "@src/modules/report/domain/bgw/project-problem/dao/project-problem.dao";
import * as _ from "lodash";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { ProjectDao } from "@src/modules/report/domain/bgw/project/dao/project.dao";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";
import { CompanyService } from "@src/modules/report/domain/bgw/company/service/company.service";
import { MediaService } from "@src/modules/report/domain/bgw/media/service/media.service";
import { CommonParameterService } from "@src/modules/report/domain/bgw/common/common-paramet.serervice";
import { durationByMinute } from "@src/util/datetime.util";
import * as DateFns from "date-fns";
import { ObjectId } from "mongodb";
import { ManagerService } from "@src/modules/report/domain/bgw/manager/service/manager.service";
import { DataReportConfigService } from "../../data-report-config.service";
import { CommonParameterDao } from "@src/modules/report/domain/bgw/common/common-parameter.dao";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";

/**
 * @summary 数据看板 - 工地问题
 * <AUTHOR>
 * @class ProjectProblemDataReportService
 */
@Injectable()
export class ProjectProblemDataReportService extends CustomChart {
    public readonly logger = new MyLogger(ProjectProblemDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly projectProblemDao: ProjectProblemDao,
        private readonly chartFactory: ChartFactory,
        private readonly projectDao: ProjectDao,
        private readonly projectService: ProjectService,
        private readonly companyService: CompanyService,
        private readonly mediaService: MediaService,
        private readonly commonParameterService: CommonParameterService,
        private readonly managerService: ManagerService,
        private readonly commonParameterDao: CommonParameterDao
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
     * 获取报告图表数据
     * @param params 
     * @returns 
     */
    async getChartData(params: ProjectProblemDataReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { dimension, topic } = params;
        //处理图表数据参数
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params) as { idList: string[] };
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new Error(`Unsupported dimension: ${dimension}`);
        //获取图表数据
        const result = await method({ ...params, idList });
        //处理辅助数据
        const countProjectRes = await this.getProjectIdsByProblemIds({ ...params, idList });
        result.data.desc.subLabel = "工地数"
        result.data.desc.subValue = Number(countProjectRes.length || 0);
        result.data.desc.isReal = ProjectProblemDataReport.RealTopic.includes(topic) ? "Y" : "N";
        return result;
    }

    /**
     * 获取工地id列表
     * @param params 
     */
    private async getProjectIdsByProblemIds(params: ProjectProblemDataReport.GetProjectIdsReq): Promise<string[]> {
        if (_.isEmpty(params.idList)) return [];
        const projectProblemRes = await this.projectProblemDao.searchProjectProblemProjectList({ ...params });
        return projectProblemRes.map(item => item.projectId);
    }

    /**
     * 获取图表必要筛选条件(处理topic筛选和sql筛选的映射关系)
     * @param params 
     * @returns 
     */
    async getChartSqlFilters(params: any): Promise<{ companyId: string, subject: DateReport.Subject, topic: string, addFilters: any[] }> {
        const { companyId, topic, from, to, departmentId } = params;
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        const subject = DateReport.Subject.ProjectProblem;
        const dataConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.ProjectProblem });
        let projectIds;
        if ([ProjectProblemDataReport.Topic.ProjectProblemTotal, ProjectProblemDataReport.Topic.NorectifiedProblemNow, ProjectProblemDataReport.Topic.NorectifiedOverdueNow, ProjectProblemDataReport.Topic.RectifiedProblemNow, ProjectProblemDataReport.Topic.OverdueRectifiedNow].includes(topic)) {
            //查询部门id列表
            const deptList = await this.getDeptIdList({ departmentId, deptDataType: 'real_time', startTime: timeFrame.startTime, endTime: timeFrame.endTime });
            //查询部门下的工地id列表
            const projectInfoList = !_.isEmpty(deptList) ? await this.projectDao.searchProjectIdsByDept({ departmentIds: deptList }) : [];
            projectIds = projectInfoList.map(item => item.projectId);
        } else {
            //查询报表的部门数据类型(根据用户所登陆的公司)
            const entity = await this.companyDecorationDao.search(companyId);
            const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
            //根据部门id列表查询部门数据切片的工地id列表
            const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.ProjectIdList] });
            projectIds = etlDateList?.projectIdList || [];
        }
        let addFilters = [
            { key: "projectIds", value: projectIds, operator: Sql.Operator.in },
        ];
        if (![ProjectProblemDataReport.Topic.ProjectProblemTotal, ProjectProblemDataReport.Topic.NorectifiedProblemNow, ProjectProblemDataReport.Topic.NorectifiedOverdueNow, ProjectProblemDataReport.Topic.RectifiedProblemNow, ProjectProblemDataReport.Topic.OverdueRectifiedNow].includes(topic)) {
            addFilters = [
                ...addFilters,
                { key: "from", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal },
                { key: "to", value: timeFrame.endTime, operator: Sql.Operator.less_than },
            ]

        }
        if (dataConfig?.configs?.[0]?.problemStatus === 'part_of') addFilters.push({ key: "statusList", value: dataConfig?.configs?.[0]?.conditions, operator: Sql.Operator.in });
        return { companyId, subject, topic, addFilters };
    }

    /**
     * 获取数据分析筛选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return ProjectProblemDataReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param params 
     */
    async getAnalysisOption(params: ProjectProblemDataReport.AnalysisOptionReq): Promise<ProjectProblemDataReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, businessCategory, companyType } = params;
        if (!companyId || !topic) return null;
        const result: ProjectProblemDataReport.AnalysisOptionRes = { problemStatus: [], problemSource: [], problemType: [], projectManager: [], problemOverdueStatus: [], projectAddress: '', problemTime: { from: '', to: '' }, problemDeadline: { from: '', to: '' }, problemSolvedTime: { from: '', to: '' } };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        const deviceDepartment: DataReportModuleFilter.SelectFilterValue[] = [];
        const decorationModel: DataReportModuleFilter.SelectFilterValue[] = [];
        //获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        //获取筛选项数据
        const promiseList = [];
        promiseList.push(companyType === Company.CompanyType.Conglomerate ? this.projectDao.countProjectProblemGroupCompany({ projectProblemIds: idList }) : []);//所属公司
        promiseList.push(businessCategory === 'construction' ? this.projectProblemDao.countProblemGroupDeviceDepartment({ idList }) : []);//服务厂商
        promiseList.push(businessCategory === 'construction' ? this.commonParameterDao.search("contract") : []);//装修方式
        promiseList.push(this.commonParameterDao.search('PROBLEM_STATUS'));//问题状态
        promiseList.push(this.commonParameterDao.search('PROBLEM_SOURCE'));//问题来源
        promiseList.push(this.commonParameterDao.search('PROBLEM_TYPE'));//问题类型
        promiseList.push(this.projectProblemDao.countProjectManagerGroupType({ idList }));//工地负责人
        const [linkCompanyRes, deviceDepartmentRes, decorationModelRes, problemStatusRes, problemSourceRes, problemTypeRes, projectManagerRes] = await Promise.all(promiseList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(res => linkCompany.push({ value: res.companyId, name: res.companyName }));
        (deviceDepartmentRes || []).filter(res => !!res.deviceDepartmentId).map(res => deviceDepartment.push({ value: res.deviceDepartmentId, name: res.deviceDepartmentName }));
        (decorationModelRes || []).filter(res => !!res.parameterCode).map(res => decorationModel.push({ value: res.parameterCode, name: res.parameterName }));
        (problemStatusRes || []).filter(res => !!res.parameterCode).map(res => result.problemStatus.push({ value: res.parameterCode, name: res.parameterName }));
        (problemSourceRes || []).filter(res => !!res.parameterCode).map(res => result.problemSource.push({ value: res.parameterCode, name: res.parameterName }));
        (problemTypeRes || []).filter(res => !!res.parameterCode).map(res => result.problemType.push({ value: res.parameterCode, name: res.parameterName }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(res => result.projectManager.push({ value: res.projectManagerId, name: res.projectManagerName }));
        //公共基础筛选项
        result.problemOverdueStatus = [
            { value: "deadlineUnsolved", name: "超时未整改" },
            { value: "deadlineSolved", name: "超时整改" },
            { name: '按时整改', value: 'solved' },
            { name: '未超时', value: 'noDeadline' },
        ];
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        //营建公司 独有筛选项
        if (businessCategory === 'construction') Object.assign(result, { deviceDepartment, decorationModel });
        return result;
    }

    /**
     * 数据分析列表
     * @param params 
     */
    @CheckFormatCsv(ProjectProblemDataReport.AnalysisListCsv)
    async getAnalysisList(params: ProjectProblemDataReport.AnalysisListReq): Promise<{ total: number; items: any[]; }> {
        const { companyType, companyId, topic, from, to, dataType, departmentId, businessCategory } = params;
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        if (_.isEmpty(idList)) return { total: 0, items: [] };
        params.idList = idList;
        const projectProblemRes = await this.projectProblemDao.searchList({ ...params });
        //查询其他数据
        const projectIds = [];
        const companyIds = [];
        const mediaIds = [];
        const managerFaceIds = []
        const wrongdoerFaceIds = [];
        projectProblemRes.items.forEach(item => {
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
            const problemImageIdList = (item?.problemImageIds || "").split(',');
            if (!_.isEmpty(problemImageIdList)) mediaIds.push(...problemImageIdList);
            const solvedImageIdList = (item?.solvedImageIds || "").split(',');
            if (!_.isEmpty(solvedImageIdList)) mediaIds.push(...solvedImageIdList);
            if (item?.wrongdoerFaceId) {
                if (ObjectId.isValid(item.wrongdoerFaceId)) wrongdoerFaceIds.push(item.wrongdoerFaceId);
                else managerFaceIds.push(item.wrongdoerFaceId);
            }
        });
        const promiseList = [];
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory: params.businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        promiseList.push(businessCategory === 'construction' ? this.commonParameterService.searchCommonInfoMap({ parameterType: 'contract' }) : {});//装修方式
        promiseList.push(this.commonParameterService.searchCommonInfoMap({ parameterType: 'PROBLEM_STATUS' }));//问题状态
        promiseList.push(this.commonParameterService.searchCommonInfoMap({ parameterType: 'PROBLEM_SOURCE' }));//问题来源
        promiseList.push(this.commonParameterService.searchCommonInfoMap({ parameterType: 'PROBLEM_TYPE' }));//问题类型
        promiseList.push(this.mediaService.searchParadigm({ mediaIdList: mediaIds }));
        promiseList.push(this.managerService.searchManagerInfoMap({ managerIdList: managerFaceIds }));
        promiseList.push(this.managerService.searchFaceInfoMap({ faceIdList: wrongdoerFaceIds }));
        const [projectInfoMap, companyInfoMap, decorationModelMap, problemStatusMap, problemSourceMap, problemTypeMap, mediaMap, managerMap, wrongdoerFaceMap] = await Promise.all(promiseList);
        //组装数据
        projectProblemRes.items = projectProblemRes.items.map(item => {
            const problemImageIds = (item?.problemImageIds || "").split(',');
            const solvedImageIds = (item?.solvedImageIds || "").split(',');
            const problemImage = [];
            const solvedImage = [];
            problemImageIds.map(id => { if (mediaMap[id]) problemImage.push(mediaMap[id]); });
            solvedImageIds.map(id => { if (mediaMap[id]) solvedImage.push(mediaMap[id]); });
            if (item.solveDeadlineHours) {
                const hours = Number(item.solveDeadlineHours);
                const hoursStr = hours > 24 ?
                    hours % 24 === 0 ?
                        `${Math.floor(hours / 24)}天` : `${Math.floor(hours / 24)}天${hours % 24}小时`
                    : `${hours}小时`;
                item.solveDeadlineHours = hoursStr;
            }
            const result = {
                ...item,
                statusName: problemStatusMap[item.status]?.parameterName || "",
                problemSource: problemSourceMap[item.problemSource]?.parameterName || "",
                problemType: problemTypeMap[item.problemType]?.parameterName || "",
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                problemImage: problemImage,
                solvedImage: solvedImage,
                solvedHours: item.solveDuration ? `${durationByMinute(item.solveDuration)}` : '',
                overdueName: ProjectProblemDataReport.OverdueStatusName[item?.overdueStatus] || "",
                wrongdoerFaceName: ObjectId.isValid(item.wrongdoerFaceId) ? wrongdoerFaceMap[item.wrongdoerFaceId]?.nickName || "" : managerMap[item.wrongdoerFaceId]?.nickName || "",
                problemTime: item.problemTime ? DateFns.format(new Date(item.problemTime), 'yyyy-MM-dd HH:mm') : "",
                solvedTime: item.solvedTime ? DateFns.format(new Date(item.solvedTime), 'yyyy-MM-dd HH:mm') : "",
                solveDeadlineTime: item.solveDeadlineTime ? DateFns.format(new Date(item.solveDeadlineTime), 'yyyy-MM-dd HH:mm') : "",
            };
            //集团公司 独有数据
            if (companyType === '4') {
                Object.assign(result, {
                    linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
                });
            }
            //营建公司 独有数据
            if (businessCategory === 'construction') {
                Object.assign(result, {
                    deviceDepartmentId: projectInfoMap[item.projectId]?.deviceDepartmentId || "",
                    deviceDepartmentName: projectInfoMap[item.projectId]?.deviceDepartmentName || "",
                    decorationModelName: decorationModelMap[item.decorationModel]?.parameterName || "",
                });
            }
            return result;
        });
        return { total: projectProblemRes.total, items: projectProblemRes.items };
    }

    /**===================================
                处理图表数据
    ===================================**/

    //维度图表 方法映射
    getDimensionChartDataFunction(dimension: ProjectProblemDataReport.Dimension) {
        // 维度图表 方法映射
        const dimensionChartDataFunction: Readonly<{ [K in ProjectProblemDataReport.Dimension]: (params: ProjectProblemDataReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [ProjectProblemDataReport.Dimension.DepartmentGroup]: this.getDepartmentGroupChartData.bind(this), // 工地部门
            [ProjectProblemDataReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [ProjectProblemDataReport.Dimension.Trend]: this.getGroupTrendChartData.bind(this), // 分布趋势
            [ProjectProblemDataReport.Dimension.ServiceVendorGroup]: this.getDeviceDepartmentGroupChartData.bind(this), // 服务厂商
            [ProjectProblemDataReport.Dimension.DecorationModelGroup]: this.getDecorationModelGroupChartData.bind(this), // 装修方式
            [ProjectProblemDataReport.Dimension.ProblemSourceGroup]: this.getSourceGroupChartData.bind(this), // 问题来源
            [ProjectProblemDataReport.Dimension.ProblemTypeGroup]: this.getTypeGroupChartData.bind(this), // 问题类型
            [ProjectProblemDataReport.Dimension.ProblemStatusGroup]: this.getStatusGroupChartData.bind(this), // 问题状态
            [ProjectProblemDataReport.Dimension.OverdueStatusGroup]: this.getOverdueStatusGroupChartData.bind(this), // 超时状态
            [ProjectProblemDataReport.Dimension.ProjectManagerGroup]: this.getProjectManagerGroupChartData.bind(this), // 工地负责人
        };
        return dimensionChartDataFunction[dimension];
    }

    // 部门分布数据
    private async getDepartmentGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType, departmentId } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.DepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;

        const deptData = await this.projectProblemDao.countDepartmentGroupType(params);
        //处理图表结构: 工地部门分布
        const entity = await this.companyDecorationDao.search(params.companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.to, data: deptData });

        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'departmentId', data, actionType });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.DepartmentGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }
    private async getDeviceDepartmentGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.ServiceVendorGroup];
        const actionType = params?.actionType || Chart.ActionType.None;

        const deviceDepartmentData = await this.projectProblemDao.countDeviceDepartmentGroupType(params);
        const data = deviceDepartmentData.map(item => {
            return {
                key: item.deviceDepartmentId || "",
                name: item.deviceDepartmentName || "无服务厂商",
                value: Number(item?.count || 0)
            };
        });
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.ServiceVendorGroup,
            topic, title, chartType, data: chartData, gridColumn
        }


    }
    // 当前问题总数
    private async getNumberChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        const res = await this.projectProblemDao.countProjectProblem(params);
        const total = Number(res?.count || 0);
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({
            chartType: Chart.ChartType.Total,
            title,
            name,
            optionKey: 'total',
            data: total,
            actionType
        });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.Number,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    // 问题分类分布数据
    private async getTypeGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.ProblemTypeGroup];
        const actionType = params?.actionType || Chart.ActionType.None;

        const typeData = await this.projectProblemDao.countTypeGroupType(params);
        const problemTypeMap = await this.commonParameterService.searchCommonInfoMap({ parameterType: 'PROBLEM_TYPE' });
        // to-do
        const data = typeData.map(item => ({
            key: item.problemType,
            name: problemTypeMap[item.problemType]?.parameterName || "未知类型",
            value: Number(item?.count || 0)
        }));

        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'problemType',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.ProblemTypeGroup,
            topic, title, chartType, data: chartData, gridColumn
        };
    }

    // 问题状态分布数据
    private async getStatusGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.ProblemStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;

        const statusData = await this.projectProblemDao.countStatusGroupType(params);
        const problemStatusMap = await this.commonParameterService.searchCommonInfoMap({ parameterType: 'PROBLEM_STATUS' });
        const data = statusData.map(item => ({
            key: item.problemStatus,
            name: problemStatusMap[item.problemStatus]?.parameterName || "未知状态",
            value: Number(item?.count || 0)
        }));

        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'problemStatus',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.ProblemStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        };
    }

    // 问题来源分布数据
    private async getSourceGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.ProblemSourceGroup];
        const actionType = params?.actionType || Chart.ActionType.None;

        const sourceData = await this.projectProblemDao.countSourceGroupType(params);
        const problemSourceMap = await this.commonParameterService.searchCommonInfoMap({ parameterType: 'PROBLEM_SOURCE' });
        const data = sourceData.map(item => ({
            key: item.problemSource,
            name: problemSourceMap[item.problemSource]?.parameterName || "未知来源",
            value: Number(item?.count || 0)
        }));

        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'problemSource',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.ProblemSourceGroup,
            topic, title, chartType, data: chartData, gridColumn
        };
    }

    // 装修方式分布数据
    private async getDecorationModelGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.DecorationModelGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const decoData = await this.projectProblemDao.countDecorationModelGroupType(params);
        const data = decoData.map(item => ({
            key: item.decorationModel || "",
            name: item.decorationModelName || "无装修方式",
            value: Number(item?.count || 0)
        }));
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'decorationModel',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.DecorationModelGroup,
            topic, title, chartType, data: chartData, gridColumn
        };
    }

    // 工地负责人分布数据
    private async getProjectManagerGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.ProjectManagerGroup];
        const actionType = params?.actionType || Chart.ActionType.None;

        const managerData = await this.projectProblemDao.countProjectManagerGroupType(params);
        const data = managerData.map(item => ({
            key: item.projectManagerId || "",
            name: item.projectManagerName || '未知负责人',
            value: Number(item?.count || 0)
        }));

        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'projectManagerId',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.ProjectManagerGroup,
            topic, title, chartType, data: chartData, gridColumn
        };
    }

    // 问题超时状态分布数据
    private async getOverdueStatusGroupChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const overdueData = await this.projectProblemDao.countOverdueStatusGroupType(params);
        const data = overdueData.map(item => {
            return {
                key: item.overdueStatus || "",
                name: ProjectProblemDataReport.OverdueStatusName[item.overdueStatus] || '无超时状态',
                value: Number(item?.count || 0)
            }
        });
        //创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.OverdueStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'problemOverdueStatus',
            data,
            actionType
        });
        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.OverdueStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        };
    }

    /**
     * 创建分布图表
     * @param params 图表参数
     */
    private async createDistributionChart<T extends Chart.ChartType>(params: {
        chartType: T,
        title: string,
        name: string,
        optionKey: string,
        data: { key: string, name: string, value: number }[],
        actionType: Chart.ActionType
    }): Promise<any> {
        const { chartType, title, name, optionKey, data, actionType } = params;

        switch (chartType) {
            case Chart.ChartType.Pie:
                return await this.chartFactory.createChart<Chart.ChartType.Pie>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.Rose:
                return await this.chartFactory.createChart<Chart.ChartType.Rose>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.Ring:
                return await this.chartFactory.createChart<Chart.ChartType.Ring>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.BarDistribute:
                return await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({
                    chartType, title, name, optionKey, data, actionType
                });
            default:
                throw new Error(`Unsupported chartType: ${chartType}`);
        }
    }

    /**
     * 获取趋势图表数据
     */
    private async getGroupTrendChartData(params: ProjectProblemDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to, dataType } = params;
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, value: number }[] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const data = [];//y轴数据
        promiseResult.forEach(res => {
            category.push(res?.category || "");
            data.push(res?.value || 0);
        });
        // 创建折线图
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || ProjectProblemDataReport.TopicName[topic];
        const name = ProjectProblemDataReport.DimensionName[ProjectProblemDataReport.Dimension.Trend];
        const actionType = params?.actionType || Chart.ActionType.None;
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Line:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Line>({ chartType: Chart.ChartType.Line, title, name, category, data, actionType });
                break;
            case Chart.ChartType.Bar:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Bar>({ chartType: Chart.ChartType.Bar, title, name, category, data, actionType });
                break;
        }
        return {
            subject: DateReport.Subject.ProjectProblem,
            dimension: ProjectProblemDataReport.Dimension.Trend,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取趋势数据
     */
    private async getGroupTrendData(params: ProjectProblemDataReport.DimensionReq): Promise<{ category: string, value: number }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return null;
        const { idList } = await this.getTopicIdList({ ...params, companyId, topic, from, to, dataType: "day", departmentId }) as { idList: string[] };
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        const res = await this.projectProblemDao.countProjectProblem({ ...params, idList });
        return { category, value: Number(res?.count || 0) };
    }

}