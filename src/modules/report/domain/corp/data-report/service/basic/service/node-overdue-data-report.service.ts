import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON>le<PERSON><PERSON>, CheckFormatCsv, Common, <PERSON>Logger, YqzException } from "@yqz/nest";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { ChartFactory } from "../../../chart/chart.factory";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { DateReport } from "../../../dto/data-report.dto";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { NodeOverdueDataReport } from "../dto/node-overdue-data-report.dto";
import { Chart } from "../../../chart/chart.type";
import { ProjectDao } from "@src/modules/report/domain/bgw/project/dao/project.dao";
import * as _ from "lodash";
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { ProjectNodeDao } from "@src/modules/report/domain/bgw/project/dao/project-node.dao";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";
import { CompanyService } from "@src/modules/report/domain/bgw/company/service/company.service";
import { CompanyDao } from "@src/modules/report/domain/bgw/company/dao/company.dao";
import { DataReportConfigService } from "../../data-report-config.service";
import { NodeOverdueEventDao } from "@src/modules/report/domain/bgw/node-overdue-event/dao/node-overdue-event.dao";
import * as DateFns from "date-fns";
import { ProjectUpdateBoardDao } from "../../../../data-old-report/dao/project-update.dao";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";

/**
 * @summary 数据看板 - 节点逾期
 * <AUTHOR>
 * @class NodeOverdueDataReportService
 */
@Injectable()
export class NodeOverdueDataReportService extends CustomChart {
    public readonly logger = new MyLogger(NodeOverdueDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly chartFactory: ChartFactory,
        private readonly projectDao: ProjectDao,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly projectNodeDao: ProjectNodeDao,
        private readonly companyDao: CompanyDao,
        private readonly projectService: ProjectService,
        private readonly companyService: CompanyService,
        private readonly projectUpdateDao: ProjectUpdateBoardDao,
        private readonly nodeOverdueEventDao: NodeOverdueEventDao,
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
     * 获取报告图表数据
     * @param params 
     */
    async getChartData(params: NodeOverdueDataReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { dimension } = params;
        //处理图表数据参数
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params);
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new Error(`Unsupported dimension: ${dimension}`);
        //获取图表数据
        const result = await method({ ...params, idList });
        //处理辅助数据
        const projectIds = await this.getProjectIdsByNodeIds({ ...params, idList });
        result.data.desc.subLabel = "工地数"
        result.data.desc.subValue = Number(projectIds.length || 0);
        return result;
    }

    /**
     * 获取工地数
     * @param params 
     */
    private async getProjectIdsByNodeIds(params: NodeOverdueDataReport.GetProjectIdsReq): Promise<string[]> {
        if (_.isEmpty(params.idList)) return [];
        const projectNodeRes = await this.projectNodeDao.searchProjectNodeProjectList({ ...params, nodeIdList: params.idList });
        return projectNodeRes.map(res => res.projectId);
    }

    /**
     * 获取图表必要筛选条件
     * @param params 
     */
    async getChartSqlFilters(params: NodeOverdueDataReport.GetTopicIdListReq): Promise<{ companyId: string; subject: DateReport.Subject; topic: string; addFilters: any[] }> {
        const { companyId, topic } = params;
        const subject = DateReport.Subject.NodeOverdue;
        let addFilters: Sql.Column[] = [];
        addFilters = await this.getProjectNodeChartFilters(params);
        return { companyId, subject, topic, addFilters };
    }

    async getTopicIdList(params: NodeOverdueDataReport.GetTopicIdListReq): Promise<{ idList: string[] }> {
        let { idList } = await super.getTopicIdList(params) as { idList: string[] };
        //处理公共筛选项
        if (params.overdueStatus) {//是否逾期
            const overdue = await super.getTopicIdList({ ...params, topic: NodeOverdueDataReport.Topic.OverdueNode }) as { idList: string[] };
            if (params.overdueStatus === 'Y') idList = _.intersection(idList, overdue.idList);
            if (params.overdueStatus === 'N') idList = _.difference(idList, overdue.idList);
        }
        return { idList };
    }

    /**
     * 获取节点工地图表筛选条件
     * @param params 
     * @returns 
     */
    async getProjectChartFilters(params: DateReportInterface.GetTopicIdListReq): Promise<Sql.Column[]> {
        const { companyId, topic, from, to, departmentId } = params;
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        //根据部门id列表查询部门数据切片的工地id列表
        const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.ProjectIdList] });
        let projectIds = etlDateList?.projectIdList || [];
        // const nodeConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        // const delayOverdue = nodeConfig?.configs[0]?.delayOverdue;
        const addFilters = [
            { key: "projectId", value: projectIds, operator: Sql.Operator.in },
            { key: "time", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal },
            { key: "time", value: timeFrame.endTime, operator: Sql.Operator.less_than },
        ];
        return addFilters;
    }

    /**
     * 获取节点图表筛选条件
     * @param params 
     * @returns 
     */
    async getProjectNodeChartFilters(params: DateReportInterface.GetTopicIdListReq): Promise<Sql.Column[]> {
        const { companyId, topic, from, to, departmentId } = params;
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        //根据部门id列表查询部门数据切片的工地id列表
        const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.ProjectIdList] });
        const projectIds = etlDateList?.projectIdList || [];
        let projectNodeIds = [];
        const nodeConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        let delayOverdue = nodeConfig?.configs[0]?.delayOverdue;
        if (topic === NodeOverdueDataReport.Topic.ControlsNode) {
            delayOverdue = 'Y';
        }
        if (topic === NodeOverdueDataReport.Topic.OverdueNode) {//逾期节点
            const res = await this.nodeOverdueEventDao.searchOverdueProjectNodeIdListByOverdueTimeV2({ projectIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime, delayOverdue, overdueTime: '1' });
            projectNodeIds = _.uniq((res || []).map(res => res.projectNodeId));
        } else {
            projectNodeIds = await this.projectNodeDao.searchProjectNodeIdList({ projectIdList: projectIds });
        }
        const addFilters: Sql.Column[] = [
            { key: "projectNodeId", value: projectNodeIds, operator: Sql.Operator.in },
            // { key: "time", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal },
            // { key: "time", value: timeFrame.endTime, operator: Sql.Operator.less_than },
        ];
        if (Common.Flag.Y === delayOverdue) {
            addFilters.push({ key: "originTime", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal });
            addFilters.push({ key: "originTime", value: timeFrame.endTime, operator: Sql.Operator.less_than });
        } else {
            addFilters.push({ key: "time", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal });
            addFilters.push({ key: "time", value: timeFrame.endTime, operator: Sql.Operator.less_than });
        }
        return addFilters;
    }

    /**
     * 获取数据分析筛选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return NodeOverdueDataReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param params 
     */
    async getAnalysisOption(params: NodeOverdueDataReport.AnalysisOptionReq): Promise<NodeOverdueDataReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, businessCategory, companyType } = params;
        if (!companyId || !topic) return null;
        const result: NodeOverdueDataReport.AnalysisOptionRes = { projectDirector: [], nodeTemplateName: [], stage: [], nodeName: [], role: [], isCompleted: [], overdueStatus: [], isScheduleModify: [], scheduleType: [], reasonType: [], projectAddress: "", projectTime: { from: '', to: '' }, deadlineTime: { from: '', to: '' } };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        // 获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId });
        const projectIds = await this.projectNodeDao.getProjectIdsByNodeIds(idList);
        const nodeIds = idList;
        const promiseList = [];
        // 工地公司
        promiseList.push(companyType === Company.CompanyType.Conglomerate ? this.projectDao.countProjectGroupCompany({ projectIdList: projectIds }) : []);
        // 工地负责人
        promiseList.push(this.projectDao.countProjectGroupProjectManager({ projectIdList: projectIds }));
        // 服务厂商
        promiseList.push(businessCategory === 'construction' ? this.projectDao.countProjectGroupDeviceDepartment({ projectIdList: projectIds }) : []);
        // 节点模板名称
        promiseList.push(this.projectDao.countNodeTemplateGroup({ projectIds }));
        // 节点阶段
        promiseList.push(this.projectNodeDao.countNodeGroupStage({ nodeIdList: nodeIds }));
        // 节点名称
        promiseList.push(this.projectNodeDao.countGroupNodeTitle({ nodeIdList: nodeIds }));
        // 对应岗位
        promiseList.push(this.projectNodeDao.getNodeRoleIds({ nodeIdList: nodeIds }));
        // 原因分类
        promiseList.push(this.projectDao.countNodeModifiedReasonTypeGroup({ nodeIds }));
        const [linkCompanyRes, projectManagerRes, deviceDepartmentRes, templateRes, stageRes, nodeRes, roleIdsRes, reasonTypeRes] = await Promise.all(promiseList);
        const roleIdsList = (roleIdsRes.roleIds || "").split(",").filter(roleId => !!roleId);
        const roleRes = await this.companyDao.getCompanyMemberRoleByRoleIds(roleIdsList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(item => linkCompany.push({ name: item.companyName, value: item.companyId }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(item => result.projectDirector.push({ name: item.projectManagerName, value: item.projectManagerId }));
        (templateRes || []).filter(res => !!res.templateName).map(item => result.nodeTemplateName.push({ name: item.templateName, value: item.templateName }));
        (stageRes || []).filter(res => !!res.stageName).map(item => result.stage.push({ name: item.stageName, value: item.stageName }));
        (nodeRes || []).filter(res => !!res.nodeName).map(item => result.nodeName.push({ name: item.nodeName, value: item.nodeName }));
        (roleRes || []).filter(res => !!res.roleId).map(item => result.role.push({ name: item.roleName, value: item.roleId }));
        (reasonTypeRes || []).filter(res => !!res.reasonType).map(item => result.reasonType.push({ name: item.reasonName, value: item.reasonType }));
        result.isCompleted = [{ name: '已完成', value: 'Y' }, { name: '未完成', value: 'N' },];
        result.overdueStatus = [{ name: '已逾期', value: 'Y' }, { name: '未逾期', value: 'N' },];
        result.isScheduleModify = [{ name: '是', value: 'Y' }, { name: '否', value: 'N' },];
        result.scheduleType = [{ name: '延期', value: 'delay' }, { name: '提前', value: 'ahead' },];
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        //营建公司 独有筛选项
        if (businessCategory === 'construction') Object.assign(result, { deviceDepartment: deviceDepartmentRes.filter(res => !!res.deviceDepartmentId).map(item => ({ name: item.deviceDepartmentName, value: item.deviceDepartmentId })) });
        return result;
    }

    /**
     * 数据分析列表
     * @param params 
     */
    @CheckFormatCsv(NodeOverdueDataReport.AnalysisListCsv)
    async getAnalysisList(params: NodeOverdueDataReport.AnalysisListReq): Promise<{ total: number; items: any[]; }> {
        const { companyType, companyId, topic, from, to, dataType, departmentId, businessCategory } = params;
        const { idList } = await this.getTopicIdList({ ...params, companyId, topic, from, to, dataType, departmentId })
        if (_.isEmpty(idList)) return { total: 0, items: [] };
        const projectNodeRes = await this.projectNodeDao.searchProjectNodeList({ ...params, nodeIdList: idList });
        //查询其他数据
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        const nodeConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        const delayOverdue = nodeConfig?.configs[0]?.delayOverdue;
        const projectIds = [];
        const companyIds = [];
        const roleIdList = [];
        const projectNodeIds = [];
        projectNodeRes.items.forEach(item => {
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
            if (item.roleIdStr) roleIdList.push(...item.roleIdStr.split(','));
            if (item.projectNodeId) projectNodeIds.push(item.projectNodeId);
        });
        const promiseList = [];
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        promiseList.push(this.companyDao.getCompanyMemberRoleByRoleIds(_.uniq(roleIdList)));//角色列表
        promiseList.push(this.projectNodeDao.getProjectScheduleModifiedInfo({ projectNodeIds }));//节点调整信息
        promiseList.push(this.nodeOverdueEventDao.searchNodeOverdueInfo({ projectNodeIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime, delayOverdue, overdueTime: '1' }));//节点逾期信息(后续可以优化可考虑在代码层做计算)
        promiseList.push(this.projectUpdateDao.searchProjectNodeProjectUpdateInfo({ projectNodeIds }));//节点更新信息
        //节点更新信息
        const [projectInfoMap, companyInfoMap, roleList, projectScheduleModifiedInfo, overdueInfoList, nodeProjectUpdateInfoList] = await Promise.all(promiseList);
        const roleMap = _.keyBy(roleList, 'roleId');
        const projectScheduleModifiedInfoMap = _.keyBy(projectScheduleModifiedInfo, 'projectNodeId');
        const overdueInfoMap = _.keyBy(overdueInfoList, 'projectNodeId');
        const nodeProjectUpdateInfoListMap = _.keyBy(nodeProjectUpdateInfoList, 'projectNodeId');
        //组装数据
        projectNodeRes.items = projectNodeRes.items.map(item => {
            const scheduleModifyDay = DateFns.differenceInDays(new Date(item.deadlineTime), new Date(item.beforeDeadlineTime));
            const result = {
                ...item,
                scheduleModifyDay: Math.abs(scheduleModifyDay).toString(),
                role: (item?.roleIdStr || "").split(',')?.map(i => roleMap[i]?.roleName).filter(i => !!i).join('、'),
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                projectDirectorId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectDirector: projectInfoMap[item.projectId]?.projectManagerName || "",
                scheduleModifyNum: projectScheduleModifiedInfoMap[item.projectNodeId]?.scheduleModifyNum || "0",
                scheduleType: scheduleModifyDay > 0 ? "延期" : scheduleModifyDay === 0 ? null : "提前",
                reasonType: projectScheduleModifiedInfoMap[item.projectNodeId]?.reasonType || "",
                reasonDescription: projectScheduleModifiedInfoMap[item.projectNodeId]?.reasonDescription || "",
                isScheduleModify: Number(projectScheduleModifiedInfoMap[item.projectNodeId]?.scheduleModifyNum || 0) > 0 ? "Y" : "N",
                overdueStatus: Number(overdueInfoMap[item.projectNodeId]?.overdueTime || 0) > 0 ? `节点逾期${overdueInfoMap[item.projectNodeId]?.overdueTime}天` : "未逾期",
                projectUpdator: nodeProjectUpdateInfoListMap[item.projectNodeId]?.projectUpdator || "",
                projectUpdateRole: nodeProjectUpdateInfoListMap[item.projectNodeId]?.projectUpdateRole || "",
                projectUpdateNum: nodeProjectUpdateInfoListMap[item.projectNodeId]?.projectUpdateNum || "",
                projectUpdateImgNum: nodeProjectUpdateInfoListMap[item.projectNodeId]?.projectUpdateImgNum || "",
                projectStartTime: DateFns.format(new Date(item.startTime), 'yyyy-MM-dd') || "",
                startTime: DateFns.format(new Date(item.startTime), 'yyyy-MM-dd') || "",
                deadlineTime: DateFns.format(new Date(item.deadlineTime), 'yyyy-MM-dd') || "",
                beforeDeadlineTime: DateFns.format(new Date(item.beforeDeadlineTime), 'yyyy-MM-dd') || "",
                completedTime: item.completedTime ? DateFns.format(new Date(item.completedTime), 'yyyy-MM-dd') : "",
            }
            //集团公司 独有数据
            if (companyType === '4') Object.assign(result, {
                linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
            });
            //营建公司 独有数据
            if (businessCategory === 'construction') Object.assign(result, {
                deviceDepartmentId: projectInfoMap[item.projectId]?.deviceDepartmentId || "",
                deviceDepartmentName: projectInfoMap[item.projectId]?.deviceDepartmentName || "",
            });
            return result;
        })
        return { total: projectNodeRes.total, items: projectNodeRes.items };
    }

    /**===================================
                处理图表数据
    ===================================**/

    //维度图表 方法映射
    getDimensionChartDataFunction(dimension: NodeOverdueDataReport.Dimension) {
        // 维度图表 方法映射
        const dimensionChartDataFunction: Readonly<{ [K in NodeOverdueDataReport.Dimension]: (params: NodeOverdueDataReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [NodeOverdueDataReport.Dimension.ProjectDepartmentGroup]: this.getProjectDepartmentGroupChartData.bind(this), // 工地部门分布
            [NodeOverdueDataReport.Dimension.ServiceVendorGroup]: this.getDeviceDepartmentGroupChartData.bind(this), // 服务厂商
            [NodeOverdueDataReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [NodeOverdueDataReport.Dimension.Trend]: this.getGroupTrendChartData.bind(this), // 趋势
            [NodeOverdueDataReport.Dimension.ProjectManagerGroup]: this.getProjectManagerGroupChartData.bind(this), // 工地负责人分布
            [NodeOverdueDataReport.Dimension.StageGroup]: this.getStageGroupChartData.bind(this), // 节点阶段分布
            [NodeOverdueDataReport.Dimension.NodeGroup]: this.getNodeGroupChartData.bind(this), // 节点分布
            [NodeOverdueDataReport.Dimension.MemberGroup]: this.getMemberGroupChartData.bind(this), // 人员分布
            [NodeOverdueDataReport.Dimension.RoleGroup]: this.getRoleGroupChartData.bind(this), // 岗位分布
            [NodeOverdueDataReport.Dimension.ProjectGroup]: this.getProjectGroupChartData.bind(this), // 工地分布
        };
        return dimensionChartDataFunction[dimension];
    }

    /**
     * 获取工地部门分布图表数据
     */
    private async getProjectDepartmentGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType, departmentId } = params;
        // 获取部门分布数据
        const departmentData = await this.projectNodeDao.countNodeDepartmentGroupType({ ...params, nodeIdList: idList });
        //处理图表结构: 工地部门分布
        const entity = await this.companyDecorationDao.search(params.companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.to, data: departmentData });
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.ProjectDepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'departmentId', data, actionType });
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.ProjectDepartmentGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取服务厂商分布图表数据
     * 维度: 服务厂商分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getDeviceDepartmentGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        // 获取部门分布数据
        const departmentData = await this.projectNodeDao.countNodeDeviceDepartmentGroupType({ ...params, nodeIdList: idList });
        //处理图表结构: 服务厂商分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        departmentData.map(item => { data.push({ key: item.deviceDepartmentId || "", name: item.deviceDepartmentName || "无服务厂商", value: Number(item?.count || 0) }); });
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.ServiceVendorGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.ServiceVendorGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取数值图表数据
     */
    private async getNumberChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const res = await this.projectNodeDao.countProjectNode({ ...params, nodeIdList: idList });
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        // 创建数值图表
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({ chartType: Chart.ChartType.Total, title, name, optionKey: 'total', data: Number(res?.count || 0), actionType });
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.Number,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取趋势图表数据
     */
    private async getGroupTrendChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to, dataType } = params;
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, value: number }[] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const data = [];//y轴数据
        promiseResult.forEach(res => {
            category.push(res?.category || "");
            data.push(res?.value || 0);
        });
        // 创建折线图
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.Trend];
        const actionType = params?.actionType || Chart.ActionType.None;
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Line:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Line>({ chartType: Chart.ChartType.Line, title, name, category, data, actionType });
                break;
            case Chart.ChartType.Bar:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Bar>({ chartType: Chart.ChartType.Bar, title, name, category, data, actionType });
                break;
        }
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.Trend,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取趋势数据
     */
    private async getGroupTrendData(params: NodeOverdueDataReport.AnalysisDistributeReq): Promise<{ category: string, value: number }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return null;
        const { idList } = await this.getTopicIdList({ ...params, companyId, topic, from, to, dataType: "day", departmentId });
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        const res = await this.projectNodeDao.countProjectNode({ ...params, nodeIdList: idList });
        return { category, value: Number(res?.count || 0) };
    }

    /**
     * 获取工地负责人分布图表数据
     */
    private async getProjectManagerGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        // 获取工地负责人分布数据
        const managerData = await this.projectNodeDao.countNodeGroupProjectManager({ ...params, nodeIdList: idList });
        const data = managerData.map(item => ({
            key: item.projectManagerId,
            name: item.projectManagerName || '未知负责人',
            value: Number(item?.count || 0)
        }));
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.ProjectManagerGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'projectDirectorId', data, actionType });
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.ProjectManagerGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取阶段分布图表数据
     */
    private async getStageGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        // 获取阶段分布数据
        const stageData = await this.projectNodeDao.countNodeGroupStage({ ...params, nodeIdList: idList })
        const data = stageData.map(item => ({ key: item.stageName || '未知阶段', name: item.stageName || '未知阶段', value: Number(item?.count || 0) }));
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.StageGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'stage', data, actionType });
        return {
            topic, title, chartType, gridColumn,
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.StageGroup,
            data: chartData,
        };
    }

    /**
     * 创建分布图表
     */
    private async createDistributionChart<T extends Chart.ChartType>(params: {
        chartType: T, title: string, name: string, optionKey: string, data: { key: string, name: string, value: number }[],
        actionType: Chart.ActionType
    }): Promise<any> {
        const { chartType, title, name, optionKey, data, actionType } = params;
        switch (chartType) {
            case Chart.ChartType.Pie:
                return await this.chartFactory.createChart<Chart.ChartType.Pie>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.Rose:
                return await this.chartFactory.createChart<Chart.ChartType.Rose>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.Ring:
                return await this.chartFactory.createChart<Chart.ChartType.Ring>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.BarDistribute:
                return await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({
                    chartType, title, name, optionKey, data, actionType
                });
            default:
                throw new Error(`Unsupported chartType: ${chartType}`);
        }
    }

    /**
     * 获取节点分布图表数据
     */
    private async getNodeGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        // 获取节点分布数据
        const nodeData = await this.projectNodeDao.countGroupNodeTitle({ ...params, nodeIdList: idList });
        const data = nodeData.map(item => ({ key: item.nodeName || '未知节点', name: item.nodeName || '未知节点', value: Number(item?.count || 0) }));
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.NodeGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'nodeName', data, actionType });
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.NodeGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    //这里感觉需求有问题 by ch 
    private async getMemberGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.MemberGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        // 获取人员分布数据
        const memberData = await this.projectDao.countProjectGroupMember({ idList });
        const data = memberData.map(item => ({
            key: item.memberId,
            name: item.memberName || '未知人员',
            value: Number(item?.count || 0)
        }));
        // 创建图表
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'member',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.MemberGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    private async getRoleGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        // 获取岗位分布数据
        const roleIdsRes = await this.projectNodeDao.getNodeRoleIds({ ...params, nodeIdList: idList })
        const roleIdsList = (roleIdsRes.roleIds || "").split(",").filter(roleId => !!roleId);
        const roleIdCountMap = _.countBy(roleIdsList);// 使用 lodash 的 countBy 方法统计每个 roleId 的出现次数
        const roleList = await this.companyDao.getCompanyMemberRoleByRoleIds(roleIdsList);
        const data = roleList.map(item => { return { key: item.roleId || "", name: item.roleName || '未知岗位', value: roleIdCountMap[item.roleId] || 0 } });
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.RoleGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'roleId', data, actionType });
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.RoleGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    private async getProjectGroupChartData(params: NodeOverdueDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || NodeOverdueDataReport.TopicName[topic];
        const name = NodeOverdueDataReport.DimensionName[NodeOverdueDataReport.Dimension.ProjectGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const projectData = await this.projectNodeDao.countNodeGroupProject({ ...params, nodeIdList: idList });
        const data = projectData.map(item => ({ key: item.projectId, name: item.projectName || '未知工地', value: Number(item?.count || 0) }));
        const chartData = await this.createDistributionChart({ chartType, title, name, optionKey: 'projectId', data, actionType });
        return {
            subject: DateReport.Subject.NodeOverdue,
            dimension: NodeOverdueDataReport.Dimension.ProjectGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }
}
