import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { Chart } from "../../../chart/chart.type"
import { DateReportInterface } from "./data-report.interface.dto"
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace AcceptanceReportDataReport {

    export enum Topic {
        AcceptanceReportTotal = 'acceptance_report_total',//验收报告总数
        NewAcceptanceReport = 'new_acceptance_report',//新增验收报告
        RejectedAcceptanceReport = 'rejected_acceptance_report',//审核不通过验收报告
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.AcceptanceReportTotal]: "验收报告总数",
        [Topic.NewAcceptanceReport]: "新增验收报告",
        [Topic.RejectedAcceptanceReport]: "审核不通过验收报告",
    }

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.AcceptanceReportTotal]: { name: TopicName[Topic.AcceptanceReportTotal], value: Topic.AcceptanceReportTotal },
        [Topic.NewAcceptanceReport]: { name: TopicName[Topic.NewAcceptanceReport], value: Topic.NewAcceptanceReport },
        [Topic.RejectedAcceptanceReport]: { name: TopicName[Topic.RejectedAcceptanceReport], value: Topic.RejectedAcceptanceReport }
    }

    //实时数据的主题
    export const RealTopic: Readonly<Topic[]> = [Topic.AcceptanceReportTotal];

    //维度
    export enum Dimension {
        ProjectDepartmentGroup = 'project_department_group',//工地部门分布
        Number = 'number',//数值
        ServiceVendorGroup = 'service_vendor_group',//服务厂商分布
        ReportStatusGroup = 'report_status_group',//状态分布
        ReportCriteriaGroup = 'report_criteria_group',//节点分布
        Trend = 'trend'//趋势
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.ProjectDepartmentGroup]: "工地部门分布",
        [Dimension.Number]: "数值",
        [Dimension.ServiceVendorGroup]: "服务厂商分布",
        [Dimension.ReportStatusGroup]: "状态分布",
        [Dimension.ReportCriteriaGroup]: "节点分布",
        [Dimension.Trend]: "趋势"
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.ProjectDepartmentGroup]: { name: DimensionName[Dimension.ProjectDepartmentGroup], value: Dimension.ProjectDepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.ServiceVendorGroup]: { name: DimensionName[Dimension.ServiceVendorGroup], value: Dimension.ServiceVendorGroup, gridColumn: '1' },
        [Dimension.ReportStatusGroup]: { name: DimensionName[Dimension.ReportStatusGroup], value: Dimension.ReportStatusGroup, gridColumn: '1' },
        [Dimension.ReportCriteriaGroup]: { name: DimensionName[Dimension.ReportCriteriaGroup], value: Dimension.ReportCriteriaGroup, gridColumn: '1' },
        [Dimension.Trend]: { name: DimensionName[Dimension.Trend], value: Dimension.Trend, gridColumn: '1' },
    }

    //主题可筛选的维度
    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.AcceptanceReportTotal]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.ReportStatusGroup],
            Dimensions[Dimension.ReportCriteriaGroup]
        ],
        [Topic.NewAcceptanceReport]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.ReportStatusGroup],
            Dimensions[Dimension.ReportCriteriaGroup]
        ],
        [Topic.RejectedAcceptanceReport]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.ReportStatusGroup],
            Dimensions[Dimension.ReportCriteriaGroup]

        ],
    }

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.AcceptanceReportTotal]: Dimensions[Dimension.ReportStatusGroup],
        [Topic.NewAcceptanceReport]: Dimensions[Dimension.Trend],
        [Topic.RejectedAcceptanceReport]: Dimensions[Dimension.ReportStatusGroup],
    }

    //维度可筛选的图表类型
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.ProjectDepartmentGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.Number]: [
            Chart.ChartTypeOption.Total
        ],
        [Dimension.ServiceVendorGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.ReportStatusGroup]: [
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.ReportCriteriaGroup]: [
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.Trend]: [
            Chart.ChartTypeOption.Line,
            Chart.ChartTypeOption.Bar
        ]
    }

    export enum Status {
        InApproval = 'in_approval',// 待审核
        Pending = 'pending',// 待业主验收
        Refuse = 'refuse',// 业主拒绝验收
        Rejected = 'rejected',// 审核拒绝
        Agree = 'agree'//同意验收
    }

    export const StatusName: Readonly<{ [key in Status]: string }> = {
        [Status.InApproval]: "待审核",
        [Status.Pending]: "待业主验收",
        [Status.Refuse]: "业主拒绝验收",
        [Status.Rejected]: "审核不通过",
        [Status.Agree]: "验收完成"
    }

    export enum RejectedCount {//选项：0次、1次、2次、3次、3次以上
        None = 'none',            // 0 次
        Once = 'once',            // 1 次
        Twice = 'twice',          // 2 次
        Three = 'three',          // 3 次
        ThreePlus = 'three_plus'  // 3 次以上
    }

    export const RejectedCountName: Readonly<{ [key in RejectedCount]: string }> = {
        [RejectedCount.None]: "0次",
        [RejectedCount.Once]: "1次",
        [RejectedCount.Twice]: "2次",
        [RejectedCount.Three]: "3次",
        [RejectedCount.ThreePlus]: "3次以上"
    }

    export const RejectedCountSqlExpressMap: Readonly<{ [key in RejectedCount]: string }> = {
        [RejectedCount.None]: '= 0',
        [RejectedCount.Once]: '= 1',
        [RejectedCount.Twice]: '= 2',
        [RejectedCount.Three]: "= 3",
        [RejectedCount.ThreePlus]: "> 3"
    }

    export enum RefuseCount {//选项：0次、1次、2次、3次、3次以上
        None = 'none',            // 0 次
        Once = 'once',            // 1 次
        Twice = 'twice',          // 2 次
        Three = 'three',          // 3 次
        ThreePlus = 'three_plus'  // 3 次以上
    }

    export const RefuseCountName: Readonly<{ [key in RefuseCount]: string }> = {
        [RefuseCount.None]: "0次",
        [RefuseCount.Once]: "1次",
        [RefuseCount.Twice]: "2次",
        [RefuseCount.Three]: "3次",
        [RefuseCount.ThreePlus]: "3次以上"
    }

    export const RefuseCountSqlExpressMap: Readonly<{ [key in RefuseCount]: string }> = {
        [RefuseCount.None]: '= 0',
        [RefuseCount.Once]: '= 1',
        [RefuseCount.Twice]: '= 2',
        [RefuseCount.Three]: "= 3",
        [RefuseCount.ThreePlus]: "> 3"
    }

    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: Topic;//主题
        dimension: Dimension;//维度
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;//工地负责人
        acceptanceCriteriaId?: string;//验收节点
        acceptanceStatus?: Status;//报告状态
        projectAddress?: string;//工地地址
        rejectedCount?: RejectedCount[];//审核不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        refuseCount?: RefuseCount[];//业主不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        topic: Topic;//主题
        idList: string[];
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;//工地负责人
        acceptanceCriteriaId?: string;//验收节点
        acceptanceStatus?: Status;//报告状态
        projectAddress?: string;//工地地址
        rejectedCount?: RejectedCount[];//审核不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        refuseCount?: RefuseCount[];//业主不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
    }

    export class AnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;//工地负责人
        acceptanceCriteriaId?: string;//验收节点
        acceptanceStatus?: Status;//报告状态
        projectAddress?: string;//工地地址
        rejectedCount?: RejectedCount[];//审核不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        refuseCount?: RefuseCount[];//业主不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: Topic;//主题
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        projectManager: DataReportModuleFilter.SelectFilterValue[];
        acceptanceCriteria: DataReportModuleFilter.SelectFilterValue[];
        acceptanceStatus: DataReportModuleFilter.SelectFilterValue[];
        rejectedCount: DataReportModuleFilter.SelectFilterValue[];
        refuseCount: DataReportModuleFilter.SelectFilterValue[];
        projectAddress: string;
        department?: any;
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
        deviceDepartment?: DataReportModuleFilter.SelectFilterValue[];
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "工地归属",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        deviceDepartment: {
            filterName: "服务厂商",
            filterField: "deviceDepartmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        projectManager: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        acceptanceCriteria: {
            filterName: "验收节点",
            filterField: "acceptanceCriteriaId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        acceptanceStatus: {
            filterName: "报告状态",
            filterField: "acceptanceStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        rejectedCount: {
            filterName: "审核不通过次数",
            filterField: "rejectedCount",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        refuseCount: {
            filterName: "业主不通过次数",
            filterField: "refuseCount",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        }
    };

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        topic: Topic;//主题
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;//工地负责人
        acceptanceCriteriaId?: string;//验收节点
        acceptanceStatus?: Status;//报告状态
        projectAddress?: string;//工地地址
        rejectedCount?: RejectedCount[];//审核不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        refuseCount?: RefuseCount[];//业主不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;//工地负责人
        acceptanceCriteriaId?: string;//验收节点
        acceptanceStatus?: Status;//报告状态
        projectAddress?: string;//工地地址
        rejectedCount?: RejectedCount[];//审核不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        refuseCount?: RefuseCount[];//业主不通过次数 选项：0次、1次、2次、3次、3次以上，可多选
        dimension: Dimension;//维度
    }

    export const AnalysisListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "工地归属" },
        { key: "deviceDepartmentName", value: "服务厂商" },
        { key: "projectAddress", value: "工地地址" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "acceptanceReportName", value: "验收节点" },
        { key: "acceptanceStatusName", value: "报告状态" },
        { key: "inspectorNames", value: "验收人员" },
        { key: "acceptanceDate", value: "验收日期" },
        { key: "rejectedCount", value: "审核不通过次数" },
        { key: "rejectedReason", value: "审核不通过原因" },
        { key: "refuseCount", value: "业主不通过次数" },
        { key: "refuseReason", value: "业主不通过原因" },
    ];

}