import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>qzEx<PERSON> } from "@yqz/nest";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { Injectable } from "@nestjs/common";
import { ChartFactory } from "../../../chart/chart.factory";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { DateReport } from "../../../dto/data-report.dto";
import { Chart } from "../../../chart/chart.type";
import { DeviceDataReport } from "../dto/device-data-report.dto";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import * as _ from "lodash";
import { DeviceDao } from "@src/modules/report/domain/camera/device/dao/device.dao";
import * as DateFns from "date-fns";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { Device } from "@src/modules/report/domain/camera/device/types/device.type";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";
import { CompanyService } from "@src/modules/report/domain/bgw/company/service/company.service";
import { dateDifference, generateDateList } from "@src/util/datetime.util";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { DataReportConfigService } from "../../data-report-config.service";
import { DimDeviceDao } from "@src/modules/report/domain/etl/dao/dim-device.dao";
import { DeviceService } from "@src/modules/report/domain/camera/device/service/device.service";
import { TimeUtil } from "@src/util";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";

/**
 * @summary 数据看板 - 设备概况
 * <AUTHOR>
 * @class DeviceDataReportService
 */
@Injectable()
export class DeviceDataReportService extends CustomChart {
    public readonly logger = new MyLogger(DeviceDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly deviceDao: DeviceDao,
        private readonly chartFactory: ChartFactory,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly projectService: ProjectService,
        private readonly deviceService: DeviceService,
        private readonly companyService: CompanyService,
        private readonly dimDeviceDao: DimDeviceDao
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
    * 获取报告图表数据
    * @param params 
    */
    async getChartData(params: DeviceDataReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { companyId, subject, topic, dimension } = params;
        //处理图表数据参数
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { deviceIdList, companyIdList } = await this.getTopicIdList(params);
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new YqzException(`维度类型不支持`);
        //获取图表数据
        const result = await method({ ...params, deviceIdList, companyIdList });
        if (DeviceDataReport.ActionTypeTopic.includes(topic)) result.data.header.actionType = Chart.ActionType.Setting;
        //表单配置图表类型: 设备离线>=N天工地要把n替换为公司设置的值
        if (DeviceDataReport.Topic.OfflineDeviceByDay === topic || DeviceDataReport.Topic.CurrentOfflineDeviceByDay === topic) {
            const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: subject });
            if (!_.isEmpty(config?.configs) && config?.configs[0]?.offlineCondition) {
                result.title = result.title.replace('N天', `${config?.configs[0]?.offlineCondition}天`);
                result.data.header.title = result.data.header.title.replace('N天', `${config?.configs[0]?.offlineCondition}天`);
            }
        }
         //处理辅助数据（实时）
         if (topic === DeviceDataReport.Topic.CurrentInternalIdleDevice) {//当前库内闲置设备: 环比 = 当前库内闲置设备 / 当前设备总数
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.DeviceTotal, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "闲置率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
         if (topic === DeviceDataReport.Topic.CurrentExternalIdleDevice) {//当前库外闲置设备: 环比 = 当前库外闲置设备 / 当前已领用设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.CurrentReviceDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "闲置率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
         if (topic === DeviceDataReport.Topic.CurrentReviceDevice) {//当前已领用设备: 环比 = 当前已领用设备 / 当前设备总数
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.DeviceTotal, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "领用率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
         if (topic === DeviceDataReport.Topic.CurrentInstallDevice) {//当前已安装设备: 环比 = 当前已安装设备 / 当前已领用设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.CurrentReviceDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "安装率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
         if (topic === DeviceDataReport.Topic.CurrentOnlineDevice) {//当前在线设备: 环比 = 当前在线设备 / 当前已安装设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.CurrentInstallDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "在线率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
         if (topic === DeviceDataReport.Topic.CurrentOfflineDevice) {//当前离线设备: 环比 = 当前离线设备 / 当前已安装设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.CurrentInstallDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "离线率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
         if (topic === DeviceDataReport.Topic.CurrentOfflineDeviceByDay) {//当前离线大于N天设备: 环比 = 当前离线大于N天设备 / 当前已安装设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.CurrentInstallDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "离线率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
         }
        //处理辅助数据（切片）
        if (topic === DeviceDataReport.Topic.InternalIdleDevice) {//库内闲置设备: 环比 = 库内闲置设备 / 设备总数
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.DeviceCount, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "闲置率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
        }
        if (topic === DeviceDataReport.Topic.ExternalIdleDevice) {//库外闲置设备: 环比 = 库外闲置设备 / 已领用设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.ReviceDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "闲置率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
        }
        if (topic === DeviceDataReport.Topic.ReviceDevice) {//已领用设备: 环比 = 已领用设备 / 设备总数
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.DeviceCount, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "领用率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
        }
        if (topic === DeviceDataReport.Topic.InstallDevice) {//已安装设备: 环比 = 已安装设备 / 已领用设备
            const total = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.ReviceDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "安装率"
            result.data.desc.subValue = total.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / total.data.desc.value * 100)}%` : '--';
        }
        if (topic === DeviceDataReport.Topic.OfflineDevice) {//异常离线设备: 环比 = 异常离线设备 / 已安装设备
            const instalTotal = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.InstallDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "离线率"
            result.data.desc.subValue = instalTotal.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / instalTotal.data.desc.value * 100)}%` : '--';
        }
        if (topic === DeviceDataReport.Topic.OnlineDevice) {//正常在线设备: 环比 = 正常在线设备 / 已安装设备
            const instalTotal = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.InstallDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "在线率"
            result.data.desc.subValue = instalTotal.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / instalTotal.data.desc.value * 100)}%` : '--';
        }
        if (topic === DeviceDataReport.Topic.OfflineDeviceByDay) {//离线大于N天设备: 环比 = 离线大于N天设备 / 已安装设备
            const instalTotal = await this.getChartData({ ...params, topic: DeviceDataReport.Topic.InstallDevice, dimension: DeviceDataReport.Dimension.Number });
            result.data.desc.subLabel = "离线率"
            result.data.desc.subValue = instalTotal.data.desc.value !== 0 ? `${Math.round(result.data.desc.value / instalTotal.data.desc.value * 100)}%` : '--';
        }
        return result;
    }

    /**
     * 获取图表必要筛选条件(处理topic筛选和sql筛选的映射关系)
     * @param params 
     */
    async getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string; subject: DateReport.Subject; topic: string; addFilters: Sql.Column[]; }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) throw new YqzException("必传参数不能为空");
        const subject = DateReport.Subject.Device;
        let addFilters: Sql.Column[] = [];
        //实时数据
        if (DeviceDataReport.RealTopic.includes(topic as DeviceDataReport.Topic)) addFilters = await this.getRealChartFilters(params);
        //切片数据
        if (DeviceDataReport.EtlTopic.includes(topic as DeviceDataReport.Topic)) addFilters = await this.getEtlChartFilters(params);
        //查询切片数据获取时间
        // this.logger.log(`getChartSqlFilters params : ${JSON.stringify(params)}, addFilters: ${JSON.stringify(addFilters)}`);
        return { companyId, subject, topic, addFilters };
    }

    /**
     * 获取实时数据必要筛选条件
     * @param params 
     */
    private async getRealChartFilters(params: DateReportInterface.GetTopicIdListReq): Promise<Sql.Column[]> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) throw new YqzException("必传参数不能为空");
        const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
        const deviceExpire = config?.configs[0]?.deviceExpire;
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const addFilters: Sql.Column[] = [];
        //查询部门id列表
        const deptList = await this.getDeptIdList({ departmentId, deptDataType: 'real_time', startTime: timeFrameList.startTime, endTime: timeFrameList.endTime });
        //查询部门下的设备id列表
        let deviceIdList = await this.deviceDao.searchDeviceIdsByDept({ departmentIds: deptList });
        //即将过期设备
        if (topic === DeviceDataReport.Topic.SoonExpiredDevice) deviceIdList = await this.getSoonExpiredDeviceIds({ deviceExpire, deviceIdList });
        //安装异常设备
        if (topic === DeviceDataReport.Topic.InstallErrorDevice) deviceIdList = await this.getInstallErrorDevice({ deviceIdList });
        //当前离线>=N天设备
        if (topic === DeviceDataReport.Topic.CurrentOfflineDeviceByDay) {
            const deviceConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
            const offlineDay = deviceConfig?.configs[0]?.offlineCondition || '7';
            addFilters.push({ key: "offlineDay", value: offlineDay, operator: Sql.Operator.greater_than_or_equal });
        }
        addFilters.push({ key: "yqzDeviceId", value: deviceIdList, operator: Sql.Operator.in });
        return addFilters;
    }

    /**
     * 获取切片数据必要筛选条件，含缓存机制
     * @param params 
     * @returns 
     */
    private async getEtlChartFilters(params: DateReportInterface.GetTopicIdListReq): Promise<Sql.Column[]> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) throw new YqzException("必传参数不能为空");
        const deviceConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
        const exceptionOfflineScope = deviceConfig?.configs[0]?.exceptionOfflineScope;
        const exceptionOfflineCondition = deviceConfig?.configs[0]?.exceptionOfflineCondition;
        const exceptionOfflineConditionUnit = deviceConfig?.configs[0]?.exceptionOfflineConditionUnit;
        const exceptionOfflineReport = deviceConfig?.configs[0]?.exceptionOfflineReport;
        const inIdleTimeout = deviceConfig?.configs[0]?.inIdleTimeout;
        const offlineCondition = deviceConfig?.configs[0]?.offlineCondition;
        const offlineFocusScope = deviceConfig?.configs[0]?.offlineFocusScope;
        const offlineReport = deviceConfig?.configs[0]?.offlineReport;
        const outIdleTimeout = deviceConfig?.configs[0]?.outIdleTimeout;
        // // 生成缓存键
        let cacheKey = `getEtlChartFilters:${DateReport.Subject.Device}:${topic}:${companyId}:${departmentId}:${from}:${to}`;//默认
        if (topic === DeviceDataReport.Topic.InternalIdleDevice) cacheKey += `:inIdleTimeout:${inIdleTimeout}`;//库内闲置
        if (topic === DeviceDataReport.Topic.ExternalIdleDevice) cacheKey += `:outIdleTimeout:${outIdleTimeout}`;//库外闲置
        if (topic === DeviceDataReport.Topic.OfflineDevice) cacheKey += `:exceptionOfflineScope:${exceptionOfflineScope}:exceptionOfflineReport:${exceptionOfflineReport}:exceptionOfflineCondition:${exceptionOfflineCondition}:exceptionOfflineConditionUnit:${exceptionOfflineConditionUnit}`;//异常离线
        if (topic === DeviceDataReport.Topic.OnlineDevice) cacheKey += `:exceptionOfflineScope:${exceptionOfflineScope}:exceptionOfflineReport:${exceptionOfflineReport}:exceptionOfflineCondition:${exceptionOfflineCondition}:exceptionOfflineConditionUnit:${exceptionOfflineConditionUnit}`;//正常在线
        if (topic === DeviceDataReport.Topic.OfflineDeviceByDay) cacheKey += `:offlineFocusScope:${offlineFocusScope}:offlineReport:${offlineReport}:offlineCondition:${offlineCondition}`;//离线大于N天
        // // 检查缓存
        const cachedResult = await MyRedis.get(cacheKey);
        if (cachedResult) return JSON.parse(cachedResult);
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        const addFilters: Sql.Column[] = [];
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        //根据部门id列表查询部门数据切片的设备id列表
        const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.DeviceIdList] });
        let req = { deviceIdList: etlDateList?.deviceIdList || [], companyIdList: etlDateList?.companyIdList || [] };
        //动态逻辑: 库内闲置
        if (topic === DeviceDataReport.Topic.InternalIdleDevice) req = await this.getInternalIdleDevice({ deviceIdList: req.deviceIdList, companyIdList: req.companyIdList, startTime: timeFrame.startTime, endTime: timeFrame.endTime, inIdleTimeout });
        //动态逻辑: 库外闲置
        if (topic === DeviceDataReport.Topic.ExternalIdleDevice) req = await this.getExternalIdleDevice({ deviceIdList: req.deviceIdList, companyIdList: req.companyIdList, startTime: timeFrame.startTime, endTime: timeFrame.endTime, outIdleTimeout });
        //动态逻辑: 异常离线
        if (topic === DeviceDataReport.Topic.OfflineDevice) req = await this.getOfflineDevice({ deviceIdList: req.deviceIdList, companyIdList: req.companyIdList, startTime: timeFrame.startTime, endTime: timeFrame.endTime, exceptionOfflineScope, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport });
        //动态逻辑: 正常在线
        if (topic === DeviceDataReport.Topic.OnlineDevice) req = await this.getUnOfflineDevice({ deviceIdList: req.deviceIdList, companyIdList: req.companyIdList, startTime: timeFrame.startTime, endTime: timeFrame.endTime, exceptionOfflineScope, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport });
        //动态逻辑: 离线大于N天
        if (topic === DeviceDataReport.Topic.OfflineDeviceByDay) req = await this.getOfflineDayDevice({ deviceIdList: req.deviceIdList, companyIdList: req.companyIdList, startTime: timeFrame.startTime, endTime: timeFrame.endTime, offlineFocusScope, offlineReport, offlineCondition });
        addFilters.push({ key: "yqzDeviceId", value: req.deviceIdList, operator: Sql.Operator.in });
        addFilters.push({ key: "companyId", value: req.companyIdList, operator: Sql.Operator.in });
        const timeList = generateDateList({ startTime: DateFns.format(new Date(timeFrame.startTime), 'yyyyMMdd'), endTime: DateFns.format(new Date(timeFrame.endTime), 'yyyyMMdd') });
        addFilters.push({ key: "time", value: timeList, operator: Sql.Operator.in });
        // 存入缓存，设置过期时间为 10 分钟
        await MyRedis.nxset(cacheKey, JSON.stringify(addFilters), 10 * 60);
        return addFilters;
    }

    /**
     * 获取即将过期设备idList
     * @param param0 
     * @returns 
     */
    private async getSoonExpiredDeviceIds({ deviceExpire, deviceIdList }: { deviceExpire: string, deviceIdList: string[] }) {
        if (!deviceExpire || _.isEmpty(deviceIdList)) return [];
        return await this.deviceDao.searchSoonExpiredDeviceIds({ deviceIdList, deviceExpire });
    }

    /**
     * 获取安装异常设备idList
     * @param param0 
     * @returns 
     */
    private async getInstallErrorDevice({ deviceIdList }: { deviceIdList: string[] }) {
        if (_.isEmpty(deviceIdList)) return [];
        return await this.deviceDao.searchInstallErrorDevice({ deviceIdList });
    }

    /**
     * 获取库内闲置设备idList
     * @param param0 
     * @returns 
     */
    private async getInternalIdleDevice({ deviceIdList, companyIdList, startTime, endTime, inIdleTimeout }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, inIdleTimeout: string }): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime || !inIdleTimeout) return { deviceIdList: [], companyIdList: [] };
        const res = await this.dimDeviceDao.findIdleDeviceWithDaysContinuity({ deviceIdList, companyIdList, startTime, endTime, consecutiveDays: inIdleTimeout, type: 'internal' });
        const deviceIds = [];
        const companyIds = [];
        res.forEach(item => {
            deviceIds.push(item.deviceId);
            companyIds.push(item.companyId);
        });
        return { deviceIdList: deviceIds, companyIdList: companyIds };
    }

    /**
     * 获取库外闲置设备idList
     * @param param0 
     * @returns
     */
    private async getExternalIdleDevice({ deviceIdList, companyIdList, startTime, endTime, outIdleTimeout }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, outIdleTimeout: string }): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime || !outIdleTimeout) return { deviceIdList: [], companyIdList: [] };
        const res = await this.dimDeviceDao.findIdleDeviceWithDaysContinuity({ deviceIdList, companyIdList, startTime, endTime, consecutiveDays: outIdleTimeout, type: 'external' });
        const deviceIds = [];
        const companyIds = [];
        res.forEach(item => {
            deviceIds.push(item.deviceId);
            companyIds.push(item.companyId);
        });
        return { deviceIdList: deviceIds, companyIdList: companyIds };
    }

    /**
     * 获取异常离线设备idList
     * @param param0 
     * @returns 
     */
    private async getOfflineDevice({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition = '4', exceptionOfflineConditionUnit = 'hour', exceptionOfflineReport }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, exceptionOfflineScope: string, exceptionOfflineCondition: string, exceptionOfflineConditionUnit: string, exceptionOfflineReport: string }): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return { deviceIdList: [], companyIdList: [] };
        const res = await this.deviceService.getOfflineDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport });
        const deviceIds = [];
        const companyIds = [];
        res.forEach(item => {
            deviceIds.push(item.yqzDeviceId);
            companyIds.push(item.companyId);
        });
        return { deviceIdList: deviceIds, companyIdList: companyIds };
    }

    /**
     * 获取未异常离线设备idList
     * @param param0 
     * @returns 
     */
    private async getUnOfflineDevice({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, exceptionOfflineScope: string, exceptionOfflineCondition: string, exceptionOfflineConditionUnit: string, exceptionOfflineReport: string }): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return { deviceIdList: [], companyIdList: [] };
        const res = await this.deviceService.getUnOfflineDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport });
        const deviceIds = [];
        const companyIds = [];
        res.forEach(item => {
            deviceIds.push(item.yqzDeviceId);
            companyIds.push(item.companyId);
        });
        return { deviceIdList: deviceIds, companyIdList: companyIds };
    }

    /**
     * 获取异常离线N天设备idList
     * @param param0 
     * @returns 
     */
    private async getOfflineDayDevice({ deviceIdList, companyIdList, startTime, endTime, offlineFocusScope, offlineReport, offlineCondition }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, offlineFocusScope: string, offlineReport: 'all' | 'no_report', offlineCondition: string }): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return { deviceIdList: [], companyIdList: [] };
        const res = await this.deviceService.getOfflineDayDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, offlineFocusScope, offlineReport, offlineCondition });
        const deviceIds = [];
        const companyIds = [];
        res.forEach(item => {
            deviceIds.push(item.yqzDeviceId);
            companyIds.push(item.companyId);
        });
        return { deviceIdList: deviceIds, companyIdList: companyIds };
    }

    //获取图表主题id列表(含时间、部门等筛选)
    async getTopicIdList(params: DeviceDataReport.GetTopicIdListReq): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        const { companyId, topic, from, to, dataType, departmentId } = params;
        if (!companyId || !topic || !from || !to || !dataType || !departmentId) throw new YqzException("必传参数不能为空");
        const deviceIdList = [];
        const companyIdList = [];
        //查询图表筛选条件
        const filters = await this.getChartSqlFilters(params);
        //传了值，但是为空数组或undefined直接返回
        const values = (filters?.addFilters || []).map(item => item.value);
        if (values.some(value => value === undefined || (Array.isArray(value) && _.isEmpty(value)))) return { deviceIdList, companyIdList };
        //创建sql
        const data = await this.sqlFactory.createSqlData(filters);
        //执行sql
        const result = await this.sqlFactory.executeSql(data.sql, data.params);
        result.map(item => {
            deviceIdList.push(item.deviceId)
            companyIdList.push(item.companyId)
        });
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList)) return { deviceIdList, companyIdList };
        //执行公共筛选
        const time = ChartDateUtil.getTimeFrame({ from, to });
        return await this.commonFilter({ ...params, deviceIdList, companyIdList, startTime: time.startTime, endTime: time.endTime });
    }

    /**
     * 公共筛选过滤
     * @param params 
     * @returns 
     */
    private async commonFilter(params: DeviceDataReport.CommonFilterReq): Promise<{ deviceIdList: string[], companyIdList: string[] }> {
        const { topic, companyIdList, deviceIdList, isOffline } = params;
        if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList)) return { deviceIdList: [], companyIdList: [] };
        const result = { deviceIdList: params.deviceIdList, companyIdList: params.companyIdList };
        //异常离线（切片数据）
        if (isOffline && DeviceDataReport.EtlTopic.includes(topic)) {
            if (isOffline === DeviceDataReport.IsOffline.Offline) {
                const offlineList = await this.deviceService.getOfflineDeviceByCompany(params);
                const deviceIdList = [];
                const companyIdList = [];
                offlineList.map(item => {
                    deviceIdList.push(item.yqzDeviceId);
                    companyIdList.push(item.companyId);
                });
                result.deviceIdList = deviceIdList;
                result.companyIdList = companyIdList;
            }
            if (isOffline === DeviceDataReport.IsOffline.UnOffline) {
                const unOfflineList = await this.deviceService.getUnOfflineDeviceByCompany(params);
                const deviceIdList = [];
                const companyIdList = [];
                unOfflineList.map(item => {
                    deviceIdList.push(item.yqzDeviceId);
                    companyIdList.push(item.companyId);
                });
                result.deviceIdList = deviceIdList;
                result.companyIdList = companyIdList;
            }
        }
        result.deviceIdList = _.uniq(result.deviceIdList);
        result.companyIdList = _.uniq(result.companyIdList);
        return result;
    }

    /**
     * 获取数据分析筛选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return DeviceDataReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param params 
     */
    async getAnalysisOption(params: DeviceDataReport.AnalysisOptionReq): Promise<DeviceDataReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, businessCategory, companyType } = params;
        if (!companyId || !topic) return null;
        const result: DeviceDataReport.AnalysisOptionRes = { reviceManager: [], projectManager: [], deviceSerial: "", projectAddress: "" };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        //获取数据点id列表
        const { deviceIdList, companyIdList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId });
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        const promiseList = [];
        promiseList.push(companyType === Company.CompanyType.Conglomerate ? DeviceDataReport.RealTopic.includes(topic) ? this.deviceDao.countDeviceGroupCompany({ deviceIdList }) : this.deviceDao.countEtlDeviceGroupCompany({ deviceIdList, companyIdList, startTime, endTime }) : []);//所属公司
        promiseList.push(DeviceDataReport.RealTopic.includes(topic) ? this.deviceDao.countDeviceGroupReviceManager({ deviceIdList }) : this.deviceDao.countEtlDeviceGroupReviceManager({ deviceIdList, companyIdList, startTime, endTime }));//领用人
        promiseList.push(DeviceDataReport.RealTopic.includes(topic) ? this.deviceDao.countDeviceGroupProjectManager({ deviceIdList }) : this.deviceDao.countEtlDeviceGroupProjectManager({ deviceIdList, companyIdList, startTime, endTime }));//工地负责人
        const [linkCompanyRes, reviceManagerRes, projectManagerRes] = await Promise.all(promiseList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(res => linkCompany.push({ value: res.companyId, name: res.companyName }));
        (reviceManagerRes || []).filter(res => !!res.reviceManagerId).map(res => result.reviceManager.push({ value: res.reviceManagerId, name: res.reviceManagerName }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(res => result.projectManager.push({ value: res.projectManagerId, name: res.projectManagerName }));
        if (DeviceDataReport.RealTopic.includes(topic)) Object.assign(result, { isOnline: _.map(DeviceDataReport.IsOnline, (value) => { return { value, name: DeviceDataReport.IsOnlineName[value] } }) });//在线状态
        if (DeviceDataReport.EtlTopic.includes(topic)) Object.assign(result, { isOffline: _.map(DeviceDataReport.IsOffline, (value) => { return { value, name: DeviceDataReport.IsOfflineName[value] } }) });//异常离线状态
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        return result;
    }

    /**
     * 数据分析列表
     * @param params 
     */
    async getAnalysisList(params: DeviceDataReport.AnalysisListReq): Promise<{ total: number; items: any[]; }> {
        const { companyId, topic, from, to } = params;
        const result: { total: number; items: any } = { total: 0, items: [] };
        if (!companyId || !topic) return result;
        //获取数据点id列表
        const { deviceIdList, companyIdList } = await this.getTopicIdList(params);
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        params.deviceIdList = deviceIdList;
        params.companyIdList = companyIdList;
        params.startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        params.endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询列表
        return DeviceDataReport.RealTopic.includes(topic) ? await this.getRealDeviceList(params) : await this.getEtlDeviceList(params);
    }

    @CheckFormatCsv(DeviceDataReport.AnalysisRealListCsv)
    async getRealDeviceList(params: Device.SearchRealDeviceListReq) {
        const { companyType } = params;
        const result = await this.deviceDao.searchRealDeviceList(params);
        //查询其他数据
        const projectIds = [];
        const companyIds = [];
        result.items.forEach(item => {
            projectIds.push(item.projectId);
            companyIds.push(item.companyId);
        });
        const promiseList = [];
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory: params.businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        const [projectInfoMap, companyInfoMap] = await Promise.all(promiseList);
        result.items = result.items.map(item => {
            const result = {
                ...item,
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                lastOnlineTime: DateFns.format(new Date(item.lastOnlineTime), 'yyyy-MM-dd HH:mm:ss') || "",
                offLineTime: item.lastOnlineTime ? dateDifference(new Date(), new Date(item.lastOnlineTime)) : ""
            }
            //集团公司 独有数据
            if (companyType === '4') Object.assign(result, {
                linkCompanyName: companyInfoMap[item.companyId]?.companyName || "",
            });
            return result;
        })
        return result;
    }

    @CheckFormatCsv(DeviceDataReport.AnalysisEtlListCsv)
    async getEtlDeviceList(params: Device.SearchEtlDeviceListReq) {
        const { companyType } = params;
        const result = await this.deviceDao.searchEtlDeviceList(params);
        //查询设备其他数据
        const deviceCompanyIdList = [];
        const deviceIdList = []
        result.items.forEach(item => {
            deviceCompanyIdList.push(item.companyId);
            deviceIdList.push(item.yqzDeviceId);
        });
        const devicePromiseList = [];
        devicePromiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds: deviceCompanyIdList }) : {});//公司信息
        devicePromiseList.push(this.deviceService.getOfflineDeviceByCompanyMap({ companyId: params.companyId, companyIdList: deviceCompanyIdList, deviceIdList, startTime: params.startTime, endTime: params.endTime }));//离线时长
        devicePromiseList.push(this.deviceService.searchInternalIdleDeviceMap({ companyId: params.companyId, deviceIdList, companyIdList: deviceCompanyIdList, startTime: params.startTime, endTime: params.endTime }));//库内闲置时长
        devicePromiseList.push(this.deviceService.searchExternalIdleDeviceMap({ companyId: params.companyId, deviceIdList, companyIdList: deviceCompanyIdList, startTime: params.startTime, endTime: params.endTime }));//库外闲置时长
        devicePromiseList.push(this.deviceService.searchBindDeviceMap({ companyId: params.companyId, deviceIdList, companyIdList: deviceCompanyIdList, startTime: params.startTime, endTime: params.endTime }));//绑定时长
        devicePromiseList.push(this.deviceService.searchDeviceOfflineTotalInTimeRangeMap({ deviceIdList, companyIdList: deviceCompanyIdList, startTime: params.startTime, endTime: params.endTime }));//查询设备在时间段内的离线时长(开始和结束都在时间范围内)
        const [companyInfoMap, offlineEventMap, internalIdleDeviceMap, externalIdleDeviceMap, bindDeviceMap, offlineTotalInTimeRangeMap] = await Promise.all(devicePromiseList);
        //查询工地信息
        const projectIdList = [];
        Object.values(bindDeviceMap).forEach((item: { deviceId: string; companyId: string; projectId: string; consecutiveDays: string }) => {
            if (item?.projectId) projectIdList.push(item.projectId);
        });
        const projectPromiseList = [];
        projectPromiseList.push(this.projectService.searchProjectInfoMap({ projectIds: projectIdList, businessCategory: params.businessCategory }));//工地信息
        const [projectInfoMap] = await Promise.all(projectPromiseList);
        result.items = result.items.map(item => {
            const projectId = bindDeviceMap[item.yqzDeviceId]?.projectId || item.projectId;
            const offLineTime = TimeUtil.formatSecondsDuration(offlineEventMap[item.yqzDeviceId]?.offLineSec || 0);//单次离线最久时长
            const totalOffLineSec = offlineTotalInTimeRangeMap[item.yqzDeviceId]?.offLineSec || 0;
            const bindSec = (bindDeviceMap[item.yqzDeviceId]?.consecutiveDays || 0) * 24 * 60 * 60;
            const diffSec = bindSec - totalOffLineSec;
            const onlineTime = TimeUtil.formatSecondsDuration(diffSec > 0 ? diffSec : 0);//在线时长（所选时间周期内: 绑定时长 - 离线时长)
            //在线时长（绑定时长 - 周期内离线时长, 如果小于0即为0)
            const result = {
                ...item,
                projectId,
                projectAddress: projectInfoMap[projectId]?.projectAddress || "",
                projectManagerId: projectInfoMap[projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[projectId]?.projectManagerName || "",
                isOffline: offlineEventMap[item.yqzDeviceId] ? 'Y' : 'N',
                offLineTime: offLineTime,//单次离线时长最久的离线时长
                onlineTime: onlineTime,//在线时长（绑定时长 - 周期内离线总时长, 如果小于0即为0)
                bindingTime: bindDeviceMap[item.yqzDeviceId]?.consecutiveDays ? `${bindDeviceMap[item.yqzDeviceId]?.consecutiveDays}天` : "--",//绑定时长（设备只有每天的切片，没有具体绑定时间的切片，无法精确到时分秒）
                internalIdleTime: internalIdleDeviceMap[item.yqzDeviceId]?.consecutiveDays ? `${internalIdleDeviceMap[item.yqzDeviceId]?.consecutiveDays}天` : "--",//库内闲置时长（设备只有每天的切片，没有具体绑定时间的切片，无法精确到时分秒）
                externalIdleTime: externalIdleDeviceMap[item.yqzDeviceId]?.consecutiveDays ? `${externalIdleDeviceMap[item.yqzDeviceId]?.consecutiveDays}天` : "--"//库外闲置时长（设备只有每天的切片，没有具体绑定时间的切片，无法精确到时分秒）
            }
            //集团公司 独有数据
            if (companyType === '4') Object.assign(result, {
                linkCompanyName: companyInfoMap[item.companyId]?.companyName || "",
            });
            return result;
        })
        return result;
    }

    /**===================================
                处理图表数据
    ===================================**/

    //维度图表 方法映射
    getDimensionChartDataFunction(dimension: DeviceDataReport.Dimension) {
        //维度图表 方法映射
        const dimensionChartDataFunction: Readonly<{ [K in DeviceDataReport.Dimension]: (params: DeviceDataReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [DeviceDataReport.Dimension.DeviceDepartmentGroup]: this.getDeviceDepartmentGroupChartData.bind(this), // 设备部门
            [DeviceDataReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [DeviceDataReport.Dimension.ReviceStatusGroup]: this.getReviceStatusGroupChartData.bind(this), // 领用状态
            [DeviceDataReport.Dimension.BindStatusGroup]: this.getBindStatusGroupChartData.bind(this), // 绑定状态
            [DeviceDataReport.Dimension.OnlineStatusGroup]: this.getOnlineStatusGroupChartData.bind(this), // 在线状态
            [DeviceDataReport.Dimension.OfflineStatusGroup]: this.getOfflineStatusGroupChartData.bind(this), // 异常离线状态
            [DeviceDataReport.Dimension.DeviceStatusGroup]: this.getDeviceStatusGroupChartData.bind(this), // 设备状态
            [DeviceDataReport.Dimension.ReviceManagerGroup]: this.getReviceManagerGroupChartData.bind(this), // 领用负责人
            [DeviceDataReport.Dimension.Trend]: this.getTrendChartData.bind(this),//趋势
        };
        return dimensionChartDataFunction[dimension];
    }

    /**
     * 获取设备部门分布图表数据
     * 维度: 设备部门分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getDeviceDepartmentGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, departmentId, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询数据
        const departmentData = DeviceDataReport.RealTopic.includes(topic) ? await this.deviceDao.countDeviceGroupDepartment(params) : await this.deviceDao.countEtlDeviceGroupDepartment({ ...params, startTime, endTime });
        //处理图表结构: 工地部门分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        let deptDataType = CustomChartType.DeptDataType.RealTime;
        if (DeviceDataReport.EtlTopic.includes(topic)) {
            const entity = await this.companyDecorationDao.search(params.companyId);
            deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        }
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.to, data: departmentData });
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.DeviceDepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.DeviceDepartmentGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取数值图表数据
     * 维度: 数值
     * 支持的图表结构: 数值
     * @param params 
     */
    private async getNumberChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询数据
        const detectionData = DeviceDataReport.RealTopic.includes(topic) ? await this.deviceDao.countDevice(params) : await this.deviceDao.countEtlDevice({ ...params, startTime, endTime });
        //处理图表结构: 总数统计 & 数值
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({ chartType: Chart.ChartType.Total, title, name, data: Number(detectionData?.count || 0), actionType, isReal });
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.Number,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取设备领用状态分布图表数据
     * 维度: 设备领用状态分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getReviceStatusGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询数据
        const departmentData = DeviceDataReport.RealTopic.includes(topic) ? await this.deviceDao.countDeviceGroupReviceStatus(params) : await this.deviceDao.countEtlDeviceGroupReviceStatus({ ...params, startTime, endTime });
        //处理图表结构: 领用状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        departmentData.map(item => { data.push({ key: item.isRevice, name: DeviceDataReport.IsReviceName[item.isRevice], value: Number(item?.count || 0) }); });
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.ReviceStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'isRevice', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'isRevice', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'isRevice', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'isRevice', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.ReviceStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取设备绑定状态分布图表数据
     * 维度: 设备绑定状态分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getBindStatusGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询数据
        const departmentData = DeviceDataReport.RealTopic.includes(topic) ? await this.deviceDao.countDeviceGroupBindStatus(params) : await this.deviceDao.countEtlDeviceGroupBindStatus({ ...params, startTime, endTime });
        //处理图表结构: 绑定状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        departmentData.map(item => { data.push({ key: item.isBind, name: DeviceDataReport.IsBindName[item.isBind], value: Number(item?.count || 0) }); });
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.BindStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'isBind', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'isBind', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'isBind', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'isBind', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.BindStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取设备在线状态分布图表数据
     * 维度: 设备在线状态分布（实时数据特有）
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getOnlineStatusGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        if (!DeviceDataReport.RealTopic.includes(topic)) throw new YqzException("周期数据暂不支持此维度");
        //查询数据
        const data: { key: string, name: string, value: number }[] = [];
        const departmentData = await this.deviceDao.countDeviceGroupOnlineStatus(params);
        departmentData.map(item => { data.push({ key: item.isOnline, name: DeviceDataReport.IsOnlineName[item.isOnline], value: Number(item?.count || 0) }); });
        //处理图表结构: 在线状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.OnlineStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'isOnline', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'isOnline', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'isOnline', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'isOnline', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.OnlineStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取设备异常离线状态分布图表数据
     * 维度: 设备异常离线状态分布（切片数据特有）
    * 支持的图表结构: 玫瑰图|饼图|环形图
    * @param params 
    * @returns 
    */
    private async getOfflineStatusGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        if (!DeviceDataReport.EtlTopic.includes(topic)) throw new YqzException("实时数据暂不支持此维度");
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询数据
        const data: { key: string, name: string, value: number }[] = [];
        const departmentData = await this.deviceService.countEtlDeviceGroupOnlineStatus({ ...params, startTime, endTime });
        departmentData.map(item => { data.push({ key: item.isOffline, name: DeviceDataReport.IsOfflineName[item.isOffline], value: Number(item?.count || 0) }); });
        //处理图表结构: 在线状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.OfflineStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'isOffline', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'isOffline', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'isOffline', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'isOffline', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.OfflineStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取设备领用人分布图表数据
     * 维度: 领用人分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getReviceManagerGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //获取筛选项数据
        const timeFrameList = ChartDateUtil.getTimeFrame({ from, to });
        const startTime = DateFns.format(new Date(timeFrameList.startTime), 'yyyyMMdd');
        const endTime = DateFns.format(new Date(timeFrameList.endTime), 'yyyyMMdd');
        //查询数据
        const departmentData = DeviceDataReport.RealTopic.includes(topic) ? await this.deviceDao.countDeviceGroupReviceManager(params) : await this.deviceDao.countEtlDeviceGroupReviceManager({ ...params, startTime, endTime });
        //处理图表结构: 领用人分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        departmentData.map(item => { data.push({ key: item.reviceManagerId || "", name: item.reviceManagerName || "无领用人", value: Number(item?.count || 0) }); });
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.ReviceManagerGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'reviceManagerId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'reviceManagerId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'reviceManagerId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'reviceManagerId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.ReviceManagerGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取设备状态分布图表数据
     * 维度: 设备状态分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getDeviceStatusGroupChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        if (!DeviceDataReport.RealTopic.includes(topic)) throw new YqzException("周期数据暂不支持此维度");
        //查询数据
        const departmentData = await this.deviceDao.countDeviceGroupDeviceStatus(params);
        //处理图表结构: 设备状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        departmentData.map(item => { data.push({ key: item.status || "", name: DeviceDataReport.StatusName[item.status] || "其他状态", value: Number(item?.count || 0) }); });
        const title = params?.title || DeviceDataReport.TopicName[topic];
        const name = DeviceDataReport.DimensionName[DeviceDataReport.Dimension.DeviceStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'deviceStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'deviceStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'deviceStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'deviceStatus', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.DeviceStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 查询周期数据
     * 用于: 分布趋势图表数据
     * @param params 
     * @returns 
     */
    private async getGroupTrendData(params: DeviceDataReport.AnalysisReq): Promise<{ category: string, value: number }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return null;
        const { deviceIdList, companyIdList } = await this.getTopicIdList({ ...params, from, to, dataType: "day" });
        params.deviceIdList = deviceIdList;
        params.companyIdList = companyIdList;
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        //图表展示的时间是 左闭右闭 所以这边结束时间要减一天
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        const time = ChartDateUtil.getTimeFrame({ from, to });
        const res = DeviceDataReport.RealTopic.includes(topic) ? await this.deviceDao.countDevice(params) : await this.deviceDao.countEtlDevice({ ...params, startTime: time.startTime, endTime: time.endTime });
        return { category, value: Number(res?.count || 0) };
    }

    /**
     * 获取趋势图表数据
     * 维度: 趋势
     * 支持的图表结构: 折线图|柱状图
     * @param params 
     */
    private async getTrendChartData(params: DeviceDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { companyId, topic, from, to, dataType, chartType } = params;
        if (!companyId || !topic || !from || !to || !dataType || !chartType) throw new YqzException("必传参数不能为空");
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        //查询周期数据
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, value: number }[] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const data = [];//y轴数据
        promiseResult.forEach(res => {
            category.push(res?.category || "");
            data.push(res?.value || 0);
        });
        //处理图表结构: 趋势 & 折线图/柱状图
        const title = params?.title || `${DeviceDataReport.TopicName[topic]}`;
        const name = `${DeviceDataReport.DimensionName[DeviceDataReport.Dimension.Trend]}`;
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = DeviceDataReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Line:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Line>({ chartType: Chart.ChartType.Line, title, name, category, data, actionType, isReal });
                break;
            case Chart.ChartType.Bar:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Bar>({ chartType: Chart.ChartType.Bar, title, name, category, data, actionType, isReal });
                break;
        }
        return {
            subject: DateReport.Subject.Device,
            dimension: DeviceDataReport.Dimension.Trend,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

}