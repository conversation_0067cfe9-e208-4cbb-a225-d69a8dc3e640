import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { Chart } from "../../../chart/chart.type";
import { DateReportInterface } from "./data-report.interface.dto";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace ProjectDateReport {

    export enum Topic {
        ProjectTotal = 'project_total',//当前工地总数(实时)
        ProjectInWork = 'project_in_work',//当前在建工地(实时)
        ProblemProject = 'problem_project',//当前问题工地(实时)
        TodayNoWorkProject = 'today_no_work_project',//今日停工工地(实时)
        TodayNoWorkProjectByDay = 'today_no_work_project_by_day',//今日停工>=N天工地(实时)
        NoCompletedProject = 'no_completed_project',//待设完工工地(实时)
        ProjectCount = 'project_count',//工地数
        NewStartProject = 'new_start_project',//新开工工地
        NewCompletedProject = 'new_completed_project',//新完工工地
        NewProblemProject = 'new_problem_project',//新增问题工地
        DelayProject = 'delay_project',//延期工地
        OverdueProject = 'overdue_project',//逾期工地
        InactiveProject = 'inactive_project',//停工工地
        InactiveProjectByDay = 'inactive_project_by_day',//停工>=N天工地
        OwnerFocusProject = 'owner_focus_project',//业主特别关注工地
        OwnerNegativeReviewProject = 'owner_negative_review_project',//差评工地
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.ProjectTotal]: "当前工地总数",
        [Topic.ProjectInWork]: "当前在建工地",
        [Topic.ProblemProject]: "当前问题工地",
        [Topic.TodayNoWorkProject]: "今日停工工地",
        [Topic.TodayNoWorkProjectByDay]: "今日停工>=N天工地",
        [Topic.NoCompletedProject]: "待设完工工地",
        [Topic.ProjectCount]: "工地数",
        [Topic.NewStartProject]: "新开工工地",
        [Topic.NewCompletedProject]: "新完工工地",
        [Topic.NewProblemProject]: "新增问题工地",
        [Topic.DelayProject]: "延期工地",
        [Topic.OverdueProject]: "逾期工地",
        [Topic.InactiveProject]: "停工工地",
        [Topic.InactiveProjectByDay]: "停工>=N天工地",
        [Topic.OwnerFocusProject]: "业主特别关注工地",
        [Topic.OwnerNegativeReviewProject]: "差评工地"
    }

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.ProjectTotal]: { name: TopicName[Topic.ProjectTotal], value: Topic.ProjectTotal },
        [Topic.ProjectInWork]: { name: TopicName[Topic.ProjectInWork], value: Topic.ProjectInWork },
        [Topic.ProblemProject]: { name: TopicName[Topic.ProblemProject], value: Topic.ProblemProject },
        [Topic.TodayNoWorkProject]: { name: TopicName[Topic.TodayNoWorkProject], value: Topic.TodayNoWorkProject },
        [Topic.TodayNoWorkProjectByDay]: { name: TopicName[Topic.TodayNoWorkProjectByDay], value: Topic.TodayNoWorkProjectByDay },
        [Topic.NoCompletedProject]: { name: TopicName[Topic.NoCompletedProject], value: Topic.NoCompletedProject },
        [Topic.ProjectCount]: { name: TopicName[Topic.ProjectCount], value: Topic.ProjectCount },
        [Topic.NewStartProject]: { name: TopicName[Topic.NewStartProject], value: Topic.NewStartProject },
        [Topic.NewCompletedProject]: { name: TopicName[Topic.NewCompletedProject], value: Topic.NewCompletedProject },
        [Topic.NewProblemProject]: { name: TopicName[Topic.NewProblemProject], value: Topic.NewProblemProject },
        [Topic.DelayProject]: { name: TopicName[Topic.DelayProject], value: Topic.DelayProject },
        [Topic.OverdueProject]: { name: TopicName[Topic.OverdueProject], value: Topic.OverdueProject },
        [Topic.InactiveProject]: { name: TopicName[Topic.InactiveProject], value: Topic.InactiveProject },
        [Topic.InactiveProjectByDay]: { name: TopicName[Topic.InactiveProjectByDay], value: Topic.InactiveProjectByDay },
        [Topic.OwnerFocusProject]: { name: TopicName[Topic.OwnerFocusProject], value: Topic.OwnerFocusProject },
        [Topic.OwnerNegativeReviewProject]: { name: TopicName[Topic.OwnerNegativeReviewProject], value: Topic.OwnerNegativeReviewProject },
    }

    //可以定义逻辑的主题
    export const ActionTypeTopic: Readonly<Topic[]> = [Topic.NoCompletedProject, Topic.InactiveProjectByDay, Topic.OwnerFocusProject, Topic.TodayNoWorkProjectByDay];

    //实时数据的主题
    export const RealTopic: Readonly<Topic[]> = [Topic.ProjectTotal, Topic.ProjectInWork, Topic.ProblemProject, Topic.NoCompletedProject, Topic.TodayNoWorkProject, Topic.TodayNoWorkProjectByDay];

    //切片数据的主题
    export const EtlTopic: Readonly<Topic[]> = Object.values(Topic).filter((topic) => !RealTopic.includes(topic));

    export class GetTopicIdListReq extends DateReportInterface.GetTopicIdListReq {
        topic: Topic;
        projectType?: string;//工地类型
        deviceDepartmentId?: string;//服务厂商
        workStatus?: ProjectDateReport.WorkStatus;//是否施工（实时数据特有）
        delayStatus?: ProjectDateReport.DelayStatus;//是否延期（实时数据特有）
        overdueStatus?: ProjectDateReport.OverdueStatus;//逾期状态（切片数据特有）
    }

    //维度
    export enum Dimension {
        ProjectDepartmentGroup = 'project_department_group',//工地部门分布
        Number = 'number',//数值
        ServiceVendorGroup = 'service_vendor_group',//服务厂商分布
        DecorationModelGroup = 'decoration_model_group',//装修方式分布
        ProjectTypeGroup = 'project_type_group',//工地类型分布
        ProjectStatusGroup = 'project_status_group',//工地状态分布
        StageGroup = 'stage_group',//阶段分布
        ProjectManagerGroup = 'project_manager_group',//负责人分布
        Trend = 'trend',//趋势
        CustomKeyPosition1Group = 'custom_keyPosition_1_group',//关键岗位1分布
        CustomKeyPosition2Group = 'custom_keyPosition_2_group',//关键岗位2分布
        CustomKeyPosition3Group = 'custom_keyPosition_3_group',//关键岗位3分布
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.ProjectDepartmentGroup]: "工地部门分布",
        [Dimension.Number]: "数值",
        [Dimension.ServiceVendorGroup]: "服务厂商分布",
        [Dimension.DecorationModelGroup]: "装修方式分布",
        [Dimension.ProjectTypeGroup]: "工地类型分布",
        [Dimension.ProjectStatusGroup]: "工地状态分布",
        [Dimension.StageGroup]: "阶段分布",
        [Dimension.ProjectManagerGroup]: "负责人分布",
        [Dimension.Trend]: "趋势",
        [Dimension.CustomKeyPosition1Group]: "关键岗位1分布",
        [Dimension.CustomKeyPosition2Group]: "关键岗位2分布",
        [Dimension.CustomKeyPosition3Group]: "关键岗位3分布"
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.ProjectDepartmentGroup]: { name: DimensionName[Dimension.ProjectDepartmentGroup], value: Dimension.ProjectDepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.ServiceVendorGroup]: { name: DimensionName[Dimension.ServiceVendorGroup], value: Dimension.ServiceVendorGroup, gridColumn: '1' },
        [Dimension.DecorationModelGroup]: { name: DimensionName[Dimension.DecorationModelGroup], value: Dimension.DecorationModelGroup, gridColumn: '1' },
        [Dimension.ProjectTypeGroup]: { name: DimensionName[Dimension.ProjectTypeGroup], value: Dimension.ProjectTypeGroup, gridColumn: '1' },
        [Dimension.ProjectStatusGroup]: { name: DimensionName[Dimension.ProjectStatusGroup], value: Dimension.ProjectStatusGroup, gridColumn: '1' },
        [Dimension.StageGroup]: { name: DimensionName[Dimension.StageGroup], value: Dimension.StageGroup, gridColumn: '1' },
        [Dimension.ProjectManagerGroup]: { name: DimensionName[Dimension.ProjectManagerGroup], value: Dimension.ProjectManagerGroup, gridColumn: '1' },
        [Dimension.Trend]: { name: DimensionName[Dimension.Trend], value: Dimension.Trend, gridColumn: '1' },
        [Dimension.CustomKeyPosition1Group]: { name: DimensionName[Dimension.CustomKeyPosition1Group], value: Dimension.CustomKeyPosition1Group, gridColumn: '1' },
        [Dimension.CustomKeyPosition2Group]: { name: DimensionName[Dimension.CustomKeyPosition2Group], value: Dimension.CustomKeyPosition2Group, gridColumn: '1' },
        [Dimension.CustomKeyPosition3Group]: { name: DimensionName[Dimension.CustomKeyPosition3Group], value: Dimension.CustomKeyPosition3Group, gridColumn: '1' },
    }

    //维度可筛选的图表类型
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.ProjectDepartmentGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.Number]: [Chart.ChartTypeOption.Total],
        [Dimension.ServiceVendorGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.DecorationModelGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProjectTypeGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProjectStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.StageGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProjectManagerGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.Trend]: [Chart.ChartTypeOption.Line, Chart.ChartTypeOption.Bar],
        [Dimension.CustomKeyPosition1Group]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.CustomKeyPosition2Group]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.CustomKeyPosition3Group]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute]
    }

    //主题可筛选的维度
    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.ProjectTotal]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.ProjectInWork]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.ProblemProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.TodayNoWorkProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.TodayNoWorkProjectByDay]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.NoCompletedProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.ProjectCount]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.ProjectStatusGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.NewStartProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.NewCompletedProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.NewProblemProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.DelayProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.OverdueProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.InactiveProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.InactiveProjectByDay]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.OwnerFocusProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ],
        [Topic.OwnerNegativeReviewProject]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.DecorationModelGroup],
            Dimensions[Dimension.ProjectTypeGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
            Dimensions[Dimension.CustomKeyPosition1Group],
            Dimensions[Dimension.CustomKeyPosition2Group],
            Dimensions[Dimension.CustomKeyPosition3Group]
        ]
    };

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.ProjectTotal]: Dimensions[Dimension.Number],
        [Topic.ProjectInWork]: Dimensions[Dimension.Number],
        [Topic.ProblemProject]: Dimensions[Dimension.ProjectDepartmentGroup],
        [Topic.TodayNoWorkProject]: Dimensions[Dimension.ProjectDepartmentGroup],
        [Topic.TodayNoWorkProjectByDay]: Dimensions[Dimension.ProjectDepartmentGroup],
        [Topic.NoCompletedProject]: Dimensions[Dimension.ProjectDepartmentGroup],
        [Topic.ProjectCount]: Dimensions[Dimension.Number],
        [Topic.NewStartProject]: Dimensions[Dimension.Trend],
        [Topic.NewCompletedProject]: Dimensions[Dimension.Trend],
        [Topic.NewProblemProject]: Dimensions[Dimension.Trend],
        [Topic.DelayProject]: Dimensions[Dimension.Trend],
        [Topic.OverdueProject]: Dimensions[Dimension.Trend],
        [Topic.InactiveProject]: Dimensions[Dimension.Trend],
        [Topic.InactiveProjectByDay]: Dimensions[Dimension.Trend],
        [Topic.OwnerFocusProject]: Dimensions[Dimension.Trend],
        [Topic.OwnerNegativeReviewProject]: Dimensions[Dimension.Trend],
    }

    export enum ProjectType {
        WisdomProject = 'wisdom_project',//智慧工地
        CommonProject = 'common_project',//普通工地
    }

    export const ProjectTypeName: Readonly<{ [key in ProjectType]: string }> = {
        [ProjectType.WisdomProject]: "智慧工地",
        [ProjectType.CommonProject]: "普通工地"
    }

    export enum ProjectStatus {
        NoWork = 'no_work',//未开工
        NoFinish = 'no_finish',//在建
        Finish = 'finish',//完工
    }

    export const ProjectStatusName: Readonly<{ [key in ProjectStatus]: string }> = {
        [ProjectStatus.NoWork]: "未开工",
        [ProjectStatus.NoFinish]: "在建",
        [ProjectStatus.Finish]: "完工"
    }

    export enum OverdueStatus {
        Overdue = 'Y',//逾期
        Normal = 'N',//正常
    }

    export const OverdueStatusName: Readonly<{ [key in OverdueStatus]: string }> = {
        [OverdueStatus.Overdue]: "逾期",
        [OverdueStatus.Normal]: "未逾期"
    }

    export enum DelayStatus {
        Delay = 'Y',//延期
        Normal = 'N',//正常
    }

    export const DelayStatusName: Readonly<{ [key in DelayStatus]: string }> = {
        [DelayStatus.Delay]: "延期",
        [DelayStatus.Normal]: "未延期"
    }

    export enum WorkStatus {
        Work = 'Y',//施工
        UnWork = 'N',//未施工
    }

    export const WorkStatusName: Readonly<{ [key in WorkStatus]: string }> = {
        [WorkStatus.Work]: "施工",
        [WorkStatus.UnWork]: "未施工"
    }

    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: Topic;//主题
        dimension: Dimension;//维度
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        deviceDepartmentId?: string;//服务厂商
        decorationModel?: string;//装修方式
        projectType?: string;//工地类型
        projectStatus?: string;//工地状态
        stageList?: string[];//当前阶段
        projectAddress?: string;//工地地址
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        idList: string[];
        topic: Topic;//主题
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        deviceDepartmentId?: string;//服务厂商
        decorationModel?: string;//装修方式
        projectType?: string;//工地类型
        projectStatus?: string;//工地状态
        stageList?: string[];//当前阶段
        projectAddress?: string;//工地地址
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: Topic;//主题
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
        projectManager: DataReportModuleFilter.SelectFilterValue[];
        projectType: DataReportModuleFilter.SelectFilterValue[];
        projectStatus: DataReportModuleFilter.SelectFilterValue[];
        stage: DataReportModuleFilter.SelectFilterValue[];
        projectAddress: string;
        overdueStatus?: DataReportModuleFilter.SelectFilterValue[];
        workStatus?: DataReportModuleFilter.SelectFilterValue[];
        delayStatus?: DataReportModuleFilter.SelectFilterValue[];
        deviceDepartment?: DataReportModuleFilter.SelectFilterValue[];
        decorationModel?: DataReportModuleFilter.SelectFilterValue[];
        department?: any;
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "工地归属",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        deviceDepartment: {
            filterName: "服务厂商",
            filterField: "deviceDepartmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        projectManager: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectType: {
            filterName: "工地类型",
            filterField: "projectType",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectStatus: {
            filterName: "工地状态",
            filterField: "projectStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        stage: {
            filterName: "当前阶段",
            filterField: "stageList",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_MULTIPLE
        },
        overdueStatus: {
            filterName: "逾期状态",
            filterField: "overdueStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        workStatus: {
            filterName: "施工状态",
            filterField: "workStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        delayStatus: {
            filterName: "延期状态",
            filterField: "delayStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        decorationModel: {
            filterName: "装修方式",
            filterField: "decorationModel",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        }
    }

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        topic: Topic;//主题
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        deviceDepartmentId?: string;//服务厂商
        decorationModel?: string;//装修方式
        projectType?: string;//工地类型
        projectStatus?: string;//工地状态
        stageList?: string[];//当前阶段
        projectAddress?: string;//工地地址
    }

    export class AnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        deviceDepartmentId?: string;//服务厂商
        decorationModel?: string;//装修方式
        projectType?: string;//工地类型
        projectStatus?: string;//工地状态
        stageList?: string[];//当前阶段
        projectAddress?: string;//工地地址
    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        idList: string[];//报告ID列表
        linkCompanyId?: string;//所属公司
        projectManagerId?: string;//工地负责人
        deviceDepartmentId?: string;//服务厂商
        decorationModel?: string;//装修方式
        projectType?: string;//工地类型
        projectStatus?: string;//工地状态
        stageList?: string[];//当前阶段
        projectAddress?: string;//工地地址
        dimension: Dimension;//维度
    }

    export class ProjectRealDetail {//实时数据工地列表
        projectId: string;//项目id
        linkCompanyId: string;//所属公司id
        linkCompanyName: string;//所属公司
        departmentId: string;//工地归属id
        departmentName: string;//工地归属
        deviceDepartmentId: string;//服务厂商（设备归属）
        deviceDepartmentName: string;//服务厂商（设备归属）
        projectNumber: string;//项目编号
        projectAddress: string;//工地地址
        projectManagerId: string;//工地负责人id
        projectManagerName: string;//工地负责人
        createDate: string;//创建时间
        startDate: string;//开工时间
        completedDate: string;//完工时间
        decorationModel: string;//装修方式
        projectType: string;//工地类型
        noRectifiedProblemCount: string;//未整改问题数
        toCompleted: string;//待设完工
        stageId: string;//当前阶段id
        stageName: string;//当前阶段
    }

    export class ProjectEtlDetail {//切片数据工地列表
        projectId: string;//项目id
        linkCompanyId: string;//所属公司id
        linkCompanyName: string;//所属公司
        departmentId: string;//工地归属id
        departmentName: string;//工地归属
        deviceDepartmentId: string;//服务厂商（设备归属）
        deviceDepartmentName: string;//服务厂商（设备归属）
        projectNumber: string;//项目编号
        projectAddress: string;//工地地址
        projectManagerId: string;//工地负责人id
        projectManagerName: string;//工地负责人
        createDate: string;//创建时间
        startDate: string;//开工时间
        completedDate: string;//完工时间
        decorationModel: string;//装修方式
        projectType: string;//工地类型
        workStatus: string;//施工状态
        noWorkDay: string;//未施工天数(异常停工天数)
        delayStatus: string;//延期状态
        overdueStatus: string;//逾期状态
        overdueDay: string;//逾期时间(天)
        newProblemCount: string;//新增问题数量
        ownerNegativeReviewCount: string;//业主差评数量
        ownerFocus: string;//业主关注
    }

    export const AnalysisRealListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "工地归属" },
        { key: "deviceDepartmentName", value: "服务厂商" },
        { key: "projectNumber", value: "编号" },
        { key: "projectAddress", value: "工地地址" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "custom_keyPosition_1.name", value: "{{custom_keyPosition_1.columnName}}" },
        { key: "custom_keyPosition_2.name", value: "{{custom_keyPosition_2.columnName}}" },
        { key: "custom_keyPosition_3.name", value: "{{custom_keyPosition_3.columnName}}" },
        { key: "decorationModelName", value: "装修方式" },
        { key: "projectType", value: "工地类型", options: { values: [{ key: ProjectType.CommonProject, value: ProjectTypeName[ProjectType.CommonProject] }, { key: ProjectType.WisdomProject, value: ProjectTypeName[ProjectType.WisdomProject] }] } },
        { key: "stageName", value: "当前阶段" },
        { key: "createDate", value: "创建时间" },
        { key: "startDate", value: "开工时间" },
        { key: "completedDate", value: "完工时间" },
        { key: "noRectifiedProblemCount", value: "未整改问题数" },
        { key: "toCompleted", value: "待设完工", options: { values: [{ key: "Y", value: "是" }, { key: "N", value: "否" }] } },
    ];

    export const AnalysisEtlListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "工地归属" },
        { key: "deviceDepartmentName", value: "服务厂商" },
        { key: "projectNumber", value: "编号" },
        { key: "projectAddress", value: "工地地址" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "custom_keyPosition_1.name", value: "{{custom_keyPosition_1.columnName}}" },
        { key: "custom_keyPosition_2.name", value: "{{custom_keyPosition_2.columnName}}" },
        { key: "custom_keyPosition_3.name", value: "{{custom_keyPosition_3.columnName}}" },
        { key: "createDate", value: "创建时间" },
        { key: "startDate", value: "开工时间" },
        { key: "completedDate", value: "完工时间" },
        { key: "decorationModelName", value: "装修方式" },
        { key: "projectType", value: "工地类型", options: { values: [{ key: ProjectType.CommonProject, value: ProjectTypeName[ProjectType.CommonProject] }, { key: ProjectType.WisdomProject, value: ProjectTypeName[ProjectType.WisdomProject] }] } },
        { key: "stageName", value: "当前阶段" },
        { key: "workStatus", value: "施工状态", options: { values: [{ key: WorkStatus.Work, value: WorkStatusName[WorkStatus.Work] }, { key: WorkStatus.UnWork, value: WorkStatusName[WorkStatus.UnWork] }] } },
        { key: "noWorkDay", value: "异常停工天数" },
        { key: "delayStatus", value: "延期状态", options: { values: [{ key: DelayStatus.Delay, value: DelayStatusName[DelayStatus.Delay] }, { key: DelayStatus.Normal, value: DelayStatusName[DelayStatus.Normal] }] } },
        { key: "overdueStatus", value: "逾期状态", options: { values: [{ key: OverdueStatus.Overdue, value: OverdueStatusName[OverdueStatus.Overdue] }, { key: OverdueStatus.Normal, value: OverdueStatusName[OverdueStatus.Normal] }] } },
        { key: "overdueDay", value: "逾期时间(天)" },
        { key: "newProblemCount", value: "新增问题数" },
        { key: "ownerNegativeReviewCount", value: "业主差评数" },
        { key: "ownerFocus", value: "业主关注", options: { values: [{ key: "Y", value: "是" }, { key: "N", value: "否" }] } },
    ];
}