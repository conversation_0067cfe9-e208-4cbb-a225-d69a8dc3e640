import _ from "lodash";
import { Chart } from "../../../chart/chart.type";
import { DateReportInterface } from "./data-report.interface.dto";
import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace NodeOverdueDataReport {
    // 主题
    export enum Topic {
        ControlsNode = 'controls_node',        // 管控节点数
        DelayNode = 'delay_node',             // 延期节点数
        OverdueNode = 'overdue_node',         // 逾期节点数
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.ControlsNode]: "管控节点数",
        [Topic.DelayNode]: "延期节点数",
        [Topic.OverdueNode]: "逾期节点数",
    }

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.ControlsNode]: { name: TopicName[Topic.ControlsNode], value: Topic.ControlsNode },
        [Topic.DelayNode]: { name: TopicName[Topic.DelayNode], value: Topic.DelayNode },
        [Topic.OverdueNode]: { name: TopicName[Topic.OverdueNode], value: Topic.OverdueNode }
    }

    // 维度
    export enum Dimension {
        ProjectDepartmentGroup = 'project_department_group',  // 工地所属部门分布
        ServiceVendorGroup = 'service_vendor_group',  // 服务厂商分布
        Number = 'number',                                    // 数值
        Trend = 'trend',                                     // 趋势
        ProjectManagerGroup = 'project_manager_group',        // 工地负责人分布
        StageGroup = 'stage_group',                         // 当前阶段分布
        NodeGroup = 'node_group',                           // 节点分布
        MemberGroup = 'member_group',                        // 人员分布
        RoleGroup = 'role_group',                        // 岗位分布
        ProjectGroup = 'project_group',                    // 工地分布
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.ProjectDepartmentGroup]: "工地部门分布",
        [Dimension.ServiceVendorGroup]: "服务厂商分布",
        [Dimension.Number]: "数值",
        [Dimension.Trend]: "趋势",
        [Dimension.ProjectManagerGroup]: "工地负责人分布",
        [Dimension.StageGroup]: "阶段分布",
        [Dimension.NodeGroup]: "节点分布",
        [Dimension.RoleGroup]: "岗位分布",
        [Dimension.MemberGroup]: "人员分布",
        [Dimension.ProjectGroup]: "工地分布",
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.ProjectDepartmentGroup]: { name: DimensionName[Dimension.ProjectDepartmentGroup], value: Dimension.ProjectDepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.ServiceVendorGroup]: { name: DimensionName[Dimension.ServiceVendorGroup], value: Dimension.ServiceVendorGroup, gridColumn: '1' },
        [Dimension.Trend]: { name: DimensionName[Dimension.Trend], value: Dimension.Trend, gridColumn: '1' },
        [Dimension.ProjectManagerGroup]: { name: DimensionName[Dimension.ProjectManagerGroup], value: Dimension.ProjectManagerGroup, gridColumn: '1' },
        [Dimension.StageGroup]: { name: DimensionName[Dimension.StageGroup], value: Dimension.StageGroup, gridColumn: '1' },
        [Dimension.NodeGroup]: { name: DimensionName[Dimension.NodeGroup], value: Dimension.NodeGroup, gridColumn: '1' },
        [Dimension.RoleGroup]: { name: DimensionName[Dimension.RoleGroup], value: Dimension.RoleGroup, gridColumn: '1' },
        [Dimension.MemberGroup]: { name: DimensionName[Dimension.MemberGroup], value: Dimension.MemberGroup, gridColumn: '1' },
        [Dimension.ProjectGroup]: { name: DimensionName[Dimension.ProjectGroup], value: Dimension.ProjectGroup, gridColumn: '1' },
    }

    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.ControlsNode]: [Dimensions[Dimension.ProjectDepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.StageGroup], Dimensions[Dimension.NodeGroup], Dimensions[Dimension.RoleGroup], Dimensions[Dimension.ProjectManagerGroup], Dimensions[Dimension.ProjectGroup]],
        [Topic.DelayNode]: [Dimensions[Dimension.ProjectDepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.StageGroup], Dimensions[Dimension.NodeGroup], Dimensions[Dimension.RoleGroup], Dimensions[Dimension.ProjectManagerGroup], Dimensions[Dimension.ProjectGroup]],
        [Topic.OverdueNode]: [Dimensions[Dimension.ProjectDepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.StageGroup], Dimensions[Dimension.NodeGroup], Dimensions[Dimension.RoleGroup], Dimensions[Dimension.ProjectManagerGroup], Dimensions[Dimension.ProjectGroup]],
    };

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.ControlsNode]: Dimensions[Dimension.Trend],
        [Topic.DelayNode]: Dimensions[Dimension.Trend],
        [Topic.OverdueNode]: Dimensions[Dimension.Trend],
    }

    // 维度可筛选的图表类型
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.ProjectDepartmentGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.ServiceVendorGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.Number]: [Chart.ChartTypeOption.Total],
        [Dimension.Trend]: [Chart.ChartTypeOption.Line, Chart.ChartTypeOption.Bar],
        [Dimension.ProjectManagerGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.StageGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.NodeGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.MemberGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.RoleGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.ProjectGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
    }

    export class GetTopicIdListReq extends DateReportInterface.GetTopicIdListReq {
        topic: NodeOverdueDataReport.Topic;
        overdueStatus?: 'Y' | 'N'
    }

    // 请求参数类
    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: NodeOverdueDataReport.Topic;
        dimension: Dimension;//维度
        linkCompanyId?: string;
        projectAddress?: string;
        projectManagerId?: string;
    }

    export class GetProjectIdsReq {
        idList: string[];
        projectDirectorId?: string;//工地负责人
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectTimeStart?: string;//开工开始时间
        projectTimeEnd?: string;//开工结束时间
        nodeTemplateName?: string;//节点模版名称
        stage?: string;//节点阶段
        nodeName?: string;//节点名称
        role?: string;//节点对应岗位
        deadlineStartTime?: string;//节点截止日期开始时间
        deadlineEndTime?: string;//节点截止日期结束时间
        isCompleted?: 'Y' | 'N';//是否完成
        overdueStatus?: 'Y' | 'N';//是否逾期
        isScheduleModify?: 'Y' | 'N';//是否调整工期
        scheduleType?: string;//工期调整类型
        reasonType?: string;//工期调整原因类型
        deviceDepartmentId?: string;//服务厂商
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        idList: string[];
        topic: NodeOverdueDataReport.Topic;
        linkCompanyId?: string;
        projectAddress?: string;
        projectManagerId?: string;
        deviceDepartmentId?: string;//服务厂商
        chartType: Chart.ChartType;
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: NodeOverdueDataReport.Topic;
        idList: string[];
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        department?: any;
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
        projectDirector: DataReportModuleFilter.SelectFilterValue[];
        nodeTemplateName: DataReportModuleFilter.SelectFilterValue[];
        stage: DataReportModuleFilter.SelectFilterValue[];
        nodeName: DataReportModuleFilter.SelectFilterValue[];
        role: DataReportModuleFilter.SelectFilterValue[];
        isCompleted: DataReportModuleFilter.SelectFilterValue[];
        overdueStatus: DataReportModuleFilter.SelectFilterValue[];
        isScheduleModify: DataReportModuleFilter.SelectFilterValue[];
        scheduleType: DataReportModuleFilter.SelectFilterValue[];
        reasonType: DataReportModuleFilter.SelectFilterValue[];
        projectAddress: string;
        projectTime: DataReportModuleFilter.DateRangeFilterValue;
        deadlineTime: DataReportModuleFilter.DateRangeFilterValue;
        deviceDepartment?: DataReportModuleFilter.SelectFilterValue[];
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "工地归属",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        deviceDepartment: {
            filterName: "服务厂商",
            filterField: "deviceDepartmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        projectDirector: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        nodeTemplateName: {
            filterName: "模板名称",
            filterField: "nodeTemplateName",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        stage: {
            filterName: "节点阶段",
            filterField: "stage",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        nodeName: {
            filterName: "节点名称",
            filterField: "nodeName",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        role: {
            filterName: "对应岗位",
            filterField: "role",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectTime: {
            filterName: "开工时间",
            filterField: "projectTime",
            filterType: DataReportModuleFilter.FilterControlType.DATE_RANGE_PICKER
        },
        deadlineTime: {
            filterName: "截止时间",
            filterField: "deadlineTime",
            filterType: DataReportModuleFilter.FilterControlType.DATE_RANGE_PICKER
        },
        isCompleted: {
            filterName: "是否完工",
            filterField: "isCompleted",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        overdueStatus: {
            filterName: "逾期情况",
            filterField: "overdueStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        isScheduleModify: {
            filterName: "是否工期调整",
            filterField: "isScheduleModify",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        scheduleType: {
            filterName: "截止日期调整类型",
            filterField: "scheduleType",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        reasonType: {
            filterName: "原因分类",
            filterField: "reasonType",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
    }

    // 新增 AnalysisDistributeReq
    export class AnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        topic: NodeOverdueDataReport.Topic;
        companyId: string;
        projectManagerId?: string;
        linkCompanyId?: string;
        projectAddress?: string;
        deviceDepartmentId?: string;//服务厂商
        idList: string[];

    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: NodeOverdueDataReport.Topic;
        companyId: string;
        projectManagerId?: string;
        linkCompanyId?: string;
        projectAddress?: string;
        deviceDepartmentId?: string;//服务厂商
        idList: string[];
        dimension: Dimension;
    }

    export class AnalysisReq extends DateReportInterface.AnalysisReq {
        topic: NodeOverdueDataReport.Topic;
        idList: string[];
    }

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        topic: NodeOverdueDataReport.Topic;
        idList: string[];
    }

    export class NodeStatusReport {
        static readonly types = new Map<string, DataReportModuleFilter.SelectFilterValue>([
            ['1', { name: '逾期', value: 'overdue' }],
            ['2', { name: '未逾期', value: 'not_overdue' }],
            ['3', { name: '今日到期', value: 'due_today' }],
            ['4', { name: '节点倒计时1天', value: 'countdown_one' }],
        ]);
        private static readonly enumKeys = Array.from(NodeStatusReport.types.keys());
        private static readonly enumValues = Array.from(NodeStatusReport.types.values());

        static getNameByCode(code: string): string | undefined {
            return this.types.get(String(code))?.name;
        }

        static getValueByCode(code: string): string | undefined {
            return this.types.get(String(code))?.value;
        }

        static getCodeByValue(value: string): string | undefined {
            const entry = _.find([...this.types.entries()], ([, option]) => option.value === value);
            return entry ? entry[0] : undefined;
        }

        static getGroups(): Record<string, DataReportModuleFilter.SelectFilterValue> {
            const groups: Record<string, DataReportModuleFilter.SelectFilterValue> = {};
            this.types.forEach((value) => { groups[value.value] = value; });
            return groups;
        }

        static getEnums(): string[] {
            return this.enumKeys;
        }

        static getOption(): DataReportModuleFilter.SelectFilterValue[] {
            return this.enumValues;
        }
    }

    export const AnalysisListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "工地归属" },
        { key: "deviceDepartmentName", value: "服务厂商" },
        { key: "projectAddress", value: "工地地址" },
        { key: "stageName", value: "节点阶段" },
        { key: "nodeName", value: "节点名称" },
        { key: "role", value: "对应岗位" },
        { key: "nodeStatus", value: "节点状态", options: { values: [{ key: "Y", value: "已完成" }, { key: "N", value: "待完成" }] } },
        { key: "overdueStatus", value: "逾期情况" },
        { key: "startTime", value: "开始日期" },
        { key: "deadlineTime", value: "现截止日期" },
        { key: "beforeDeadlineTime", value: "原截止日期" },
        { key: "completedTime", value: "完成时间" },
        { key: "isScheduleModify", value: "是否工期调整", options: { values: [{ key: "Y", value: "是" }, { key: "N", value: "否" }] } },
        { key: "scheduleModifyNum", value: "调整次数" },
        { key: "scheduleType", value: "调整类型" },
        { key: "scheduleModifyDay", value: "调整天数" },
        { key: "reasonType", value: "原因分类" },
        { key: "reasonDescription", value: "调整原因" },
        { key: "projectUpdator", value: "更新人" },
        { key: "projectUpdateRole", value: "更新人岗位" },
        { key: "projectUpdateNum", value: "更新次数" },
        { key: "projectUpdateImgNum", value: "更新图片数" },
        { key: "projectDirector", value: "工地负责人" },
        { key: "nodeTemplateName", value: "节点模板名称" },

    ];

}