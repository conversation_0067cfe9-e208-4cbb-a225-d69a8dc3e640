import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckFormat<PERSON>v, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, MyRedis, YqzException } from "@yqz/nest";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { Injectable } from "@nestjs/common";
import { ChartFactory } from "../../../chart/chart.factory";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { ProjectDateReport } from "../dto/project-data-report.dto";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { DateReport } from "../../../dto/data-report.dto";
import * as DateFns from "date-fns";
import { Chart } from "../../../chart/chart.type";
import { ProjectDao } from "@src/modules/report/domain/bgw/project/dao/project.dao";
import * as _ from "lodash";
import { CommonParameterDao } from "@src/modules/report/domain/bgw/common/common-parameter.dao";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { Project } from "@src/modules/report/domain/bgw/project/dto/project.dto";
import { CompanyService } from "@src/modules/report/domain/bgw/company/service/company.service";
import { CommonParameterService } from "@src/modules/report/domain/bgw/common/common-paramet.serervice";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { DataReportConfigService } from "../../data-report-config.service";
import { DeviceDao } from "@src/modules/report/domain/camera/device/dao/device.dao";
import { ProjectInactiveRecordDao } from "@src/modules/report/domain/camera/project-inactive-record/dao/project-inactive-record.dao";
import { NodeOverdueEventDao } from "@src/modules/report/domain/bgw/node-overdue-event/dao/node-overdue-event.dao";
import { Timeout } from "@nestjs/schedule";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";
import { ManagerDao } from "@src/modules/report/domain/bgw/manager/dao/manager.dao";

/**
 * @summary 数据看板 - 工地概况
 * <AUTHOR>
 * @class ProjectDataReportService
 */
@Injectable()
export class ProjectDataReportService extends CustomChart {
    public readonly logger = new MyLogger(ProjectDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly projectDao: ProjectDao,
        private readonly deviceDao: DeviceDao,
        private readonly chartFactory: ChartFactory,
        private readonly commonParameterDao: CommonParameterDao,
        private readonly projectService: ProjectService,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly companyService: CompanyService,
        private readonly commonParameterService: CommonParameterService,
        private readonly projectInactiveRecordDao: ProjectInactiveRecordDao,
        private readonly nodeOverdueEventDao: NodeOverdueEventDao,
        private readonly managerDao: ManagerDao
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
     * 获取报告图表数据
     * @param params 
     */
    async getChartData(params: ProjectDateReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { dimension, topic, companyId, subject } = params;
        //处理图表数据参数
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params);
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new Error(`Unsupported dimension: ${dimension}`);
        //获取图表数据
        const result = await method({ ...params, idList });
        //可配置的图表类型
        if (ProjectDateReport.ActionTypeTopic.includes(topic)) result.data.header.actionType = Chart.ActionType.Setting;
        //表单配置图表类型: 停工工地>=N天工地要把n替换为公司设置的值
        if (ProjectDateReport.Topic.InactiveProjectByDay === topic || ProjectDateReport.Topic.TodayNoWorkProjectByDay === topic) {
            const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: subject });
            if (!_.isEmpty(config?.configs) && config?.configs[0]?.noworkProject) {
                result.title = result.title.replace('N天', `${config?.configs[0]?.noworkProject}天`);
                result.data.header.title = result.data.header.title.replace('N天', `${config?.configs[0]?.noworkProject}天`);
            }
        }
        return result;
    }

    /**
     * 获取图表必要筛选条件(处理topic筛选和sql筛选的映射关系)
     * @param params 
     * @returns 
     */
    async getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string; subject: DateReport.Subject; topic: string; addFilters: Sql.Column[]; }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) throw new YqzException("必传参数不能为空");
        const subject = DateReport.Subject.Project;
        let addFilters: Sql.Column[] = [];
        //实时数据
        if (ProjectDateReport.RealTopic.includes(topic as ProjectDateReport.Topic)) addFilters = await this.getRealChartFilters(params);
        //切片数据
        if (ProjectDateReport.EtlTopic.includes(topic as ProjectDateReport.Topic)) addFilters = await this.getEtlChartFilters(params);
        //查询切片数据获取时间
        // this.logger.log(`getChartSqlFilters params : ${JSON.stringify(params)}, addFilters: ${JSON.stringify(addFilters)}`);
        return { companyId, subject, topic, addFilters };
    }

    /**
     * 获取实时数据必要筛选条件
     * @param params 
     * @returns 
     */
    private async getRealChartFilters(params: DateReportInterface.GetTopicIdListReq): Promise<Sql.Column[]> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) throw new YqzException("必传参数不能为空");
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        const addFilters: Sql.Column[] = [];
        //查询部门id列表
        const deptList = await this.getDeptIdList({ departmentId, deptDataType: 'real_time', startTime: timeFrame.startTime, endTime: timeFrame.endTime });
        //查询部门下的工地id列表
        const projectInfoList = !_.isEmpty(deptList) ? await this.projectDao.searchProjectIdsByDept({ departmentIds: deptList }) : [];
        let realProjectIds = projectInfoList.map(item => item.projectId);
        //动态逻辑: 查询今日停工>=N天工地
        if (topic === ProjectDateReport.Topic.TodayNoWorkProjectByDay) {
            const projectConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Project });
            const inactiveDay = projectConfig?.configs[0]?.noworkProject;
            addFilters.push({ key: "noWorkDays", value: inactiveDay, operator: Sql.Operator.greater_than_or_equal });
        }
        //动态逻辑: 查询待完工工地idList
        if (topic === ProjectDateReport.Topic.NoCompletedProject) realProjectIds = await this.getNoCompletedProjectIds({ companyId, projectIds: realProjectIds });
        addFilters.push({ key: "projectId", value: realProjectIds, operator: Sql.Operator.in });
        return addFilters;
    }

    /**
     * 获取切片数据必要筛选条件，含缓存机制
     * @param params 
     * @returns 
     */
    private async getEtlChartFilters(params: DateReportInterface.GetTopicIdListReq): Promise<Sql.Column[]> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) throw new YqzException("必传参数不能为空");
        //查询项目配置信息(停工工地、业主特别关注)
        const projectConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Project });
        const inactiveDay = projectConfig?.configs[0]?.noworkProject;
        const ownerFocusProjectScope = projectConfig?.configs[0]?.ownerFocusProjectScope as 'one_of' | 'all';
        const ownerFocusProjectOneDay = projectConfig?.configs[0]?.ownerFocusProjectOneDay;
        const ownerFocusProjectTime = projectConfig?.configs[0]?.ownerFocusProjectTime;
        //查询评价配置信息(业主差评工地)
        const evaluateConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Evaluate });
        const badEvaluationScope = evaluateConfig?.configs[0]?.badEvaluationScope as 'one_of' | 'all';
        const overallScore = evaluateConfig?.configs[0]?.overallScore;
        const individualScore = evaluateConfig?.configs[0]?.individualScore;
        //查询逾期配置信息(逾期工地)
        const overdueConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        const delayOverdue = overdueConfig?.configs[0]?.delayOverdue;
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const delayOverdue = matterSet?.isNodeDelayOverdue;
        // 生成缓存键
        let cacheKey = `getEtlChartFilters:${DateReport.Subject.Project}:${topic}:${companyId}:${departmentId}:${from}:${to}`;//默认
        if (topic === ProjectDateReport.Topic.OverdueProject) cacheKey += `:delayOverdue:${delayOverdue}`;//逾期工地
        if (topic === ProjectDateReport.Topic.InactiveProjectByDay) cacheKey += `:inactiveDay:${inactiveDay}`;//未施工N天
        if (topic === ProjectDateReport.Topic.OwnerFocusProject) cacheKey += `:ownerFocusProjectScope:${ownerFocusProjectScope}:ownerFocusProjectOneDay:${ownerFocusProjectOneDay}:ownerFocusProjectTime:${ownerFocusProjectTime}`;//业主特别关注
        if (topic === ProjectDateReport.Topic.OwnerNegativeReviewProject) cacheKey += `:badEvaluationScope:${badEvaluationScope}:overallScore:${overallScore}:individualScore:${individualScore}`;//业主差评
        // 检查缓存
        const cachedResult = await MyRedis.get(cacheKey);
        if (cachedResult) return JSON.parse(cachedResult);
        // 缓存未命中，执行查询
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        const addFilters: Sql.Column[] = [];
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        //根据部门id列表查询部门数据切片的工地id列表
        const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.ProjectIdList] });
        let etlProjectIds = etlDateList?.projectIdList || [];
        //复杂逻辑: 逾期的工地idList
        if (topic === ProjectDateReport.Topic.OverdueProject) etlProjectIds = await this.getOverdueProjectIds({ projectIds: etlProjectIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime, delayOverdue });
        //复杂逻辑: 未施工的工地idList
        if (topic === ProjectDateReport.Topic.InactiveProject) etlProjectIds = await this.getNoWorkProjectIds({ projectIds: etlProjectIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime });
        //动态逻辑: 查询满足连续未施工N天的工地idList
        if (topic === ProjectDateReport.Topic.InactiveProjectByDay) etlProjectIds = await this.getNoWorkByDayProjectIds({ projectIds: etlProjectIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime, inactiveDay });
        //动态逻辑: 查询业主特别关注工地
        if (topic === ProjectDateReport.Topic.OwnerFocusProject) etlProjectIds = await this.getOwnerFocusProjectIds({ projectIds: etlProjectIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime, ownerFocusProjectScope, ownerFocusProjectOneDay, ownerFocusProjectTime });
        //动态逻辑: 查询业主差评工地
        if (topic === ProjectDateReport.Topic.OwnerNegativeReviewProject) etlProjectIds = await this.getOwnerNegativeReviewProjectIds({ projectIds: etlProjectIds, startTime: timeFrame.startTime, endTime: timeFrame.endTime, badEvaluationScope, overallScore, individualScore });
        addFilters.push({ key: "projectId", value: etlProjectIds, operator: Sql.Operator.in });
        // 基于节点延期配置，筛选工地
        if (topic === ProjectDateReport.Topic.DelayProject && Common.Flag.Y === delayOverdue) {
            addFilters.push({ key: "originTime", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal });
            addFilters.push({ key: "originTime", value: timeFrame.endTime, operator: Sql.Operator.less_than });
        } else {
            addFilters.push({ key: "time", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal });
            addFilters.push({ key: "time", value: timeFrame.endTime, operator: Sql.Operator.less_than });
        }
        // 存入缓存，设置过期时间为 10 分钟
        await MyRedis.nxset(cacheKey, JSON.stringify(addFilters), 10 * 60);
        return addFilters;
    }

    //获取图表主题id列表(含时间、部门等复杂筛选)
    async getTopicIdList(params: ProjectDateReport.GetTopicIdListReq): Promise<{ idList: string[] }> {
        const { companyId, topic, from, to, dataType, departmentId } = params;
        if (!companyId || !topic || !from || !to || !dataType || !departmentId) throw new YqzException("必传参数不能为空");
        const projectIdList = [];
        //查询图表筛选条件
        const filters = await this.getChartSqlFilters(params);
        //传了值，但是为空数组或undefined直接返回
        const values = (filters?.addFilters || []).map(item => item.value);
        if (values.some(value => value === undefined || (Array.isArray(value) && _.isEmpty(value)))) return { idList: [] };
        //创建sql
        const data = await this.sqlFactory.createSqlData(filters);
        //执行sql
        const result = await this.sqlFactory.executeSql(data.sql, data.params);
        result.map(item => { projectIdList.push(item.id) });
        if (_.isEmpty(projectIdList)) return { idList: [] };
        // 获取配置信息
        const overdueConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        const isNodeDelayOverdue = overdueConfig?.configs[0]?.delayOverdue;
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const isNodeDelayOverdue = matterSet?.isNodeDelayOverdue;
        //执行公共筛选
        const time = ChartDateUtil.getTimeFrame({ from, to });
        const idList = await this.commonFilter({ ...params, projectIdList, startTime: time.startTime, endTime: time.endTime, isNodeDelayOverdue });
        return { idList };
    }

    /**
     * 公共筛选过滤
     * @param params 
     * @returns 
     */
    private async commonFilter(params: Project.CommonFilterReq): Promise<string[]> {
        const { topic, startTime, endTime, deviceDepartmentId, projectIdList, workStatus, delayStatus, overdueStatus, projectType, isNodeDelayOverdue, custom_keyPosition_1, custom_keyPosition_2, custom_keyPosition_3 } = params;
        if (_.isEmpty(projectIdList)) return projectIdList;
        let result = projectIdList;
        //智慧工地筛选逻辑
        if (projectType && !_.isEmpty(result)) {
            let aiProjectIdList = [];
            if (ProjectDateReport.RealTopic.includes(topic)) {//实时数据的智慧工地
                aiProjectIdList = await this.deviceDao.searchAiProjectIds({ projectIds: projectIdList });
            }
            if (ProjectDateReport.EtlTopic.includes(topic)) {//切片数据的智慧工地
                aiProjectIdList = await this.deviceDao.searchEtlAiProjectIds({ projectIds: projectIdList, startTime, endTime });
            }
            if (projectType === 'wisdom_project') result = result.filter(projectId => (aiProjectIdList || []).includes(projectId));
            if (projectType === 'common_project') result = result.filter(projectId => !(aiProjectIdList || []).includes(projectId));
        }
        //施工状态（切片数据特有）
        if (workStatus && !_.isEmpty(result)) {
            const inactiveInfoMap = await this.projectService.searchInactiveInfoMap({ projectIds: result, startTime, endTime });
            if (workStatus === ProjectDateReport.WorkStatus.UnWork) result = result.filter(projectId => Number(inactiveInfoMap[projectId]?.inactiveDay || 0) > 0);
            if (workStatus === ProjectDateReport.WorkStatus.Work) result = result.filter(projectId => !(Number(inactiveInfoMap[projectId]?.inactiveDay || 0) > 0));
        }
        //延期状态（切片数据特有）
        if (delayStatus && !_.isEmpty(result)) {
            const delayProjectIds = await this.projectDao.getDelayProjectIdList({ projectIds: result, startTime, endTime, isNodeDelayOverdue });
            if (delayStatus === ProjectDateReport.DelayStatus.Delay) result = result.filter(projectId => delayProjectIds.includes(projectId));
            if (delayStatus === ProjectDateReport.DelayStatus.Normal) result = result.filter(projectId => !delayProjectIds.includes(projectId));
        }
        //逾期状态（切片数据特有）
        if (overdueStatus && !_.isEmpty(result)) {
            const overdueInfoMap = await this.projectService.searchOverdueInfoMap({ companyId: params.companyId, projectIds: result, startTime, endTime });
            if (overdueStatus === ProjectDateReport.OverdueStatus.Overdue) result = result.filter(projectId => Number(overdueInfoMap[projectId]?.overdueTime || 0) > 0);
            if (overdueStatus === ProjectDateReport.OverdueStatus.Normal) result = result.filter(projectId => !(Number(overdueInfoMap[projectId]?.overdueTime || 0) > 0));
        }
        //服务厂商
        if (deviceDepartmentId && !_.isEmpty(result)) {
            const deviceDepartmentProjectIds = await this.projectDao.getProjectIdsByDeviceDepartmentId({ projectIds: result, deviceDepartmentId });
            result = result.filter(projectId => deviceDepartmentProjectIds.includes(projectId));
        }
        //关键岗位
        if (custom_keyPosition_1 && !_.isEmpty(result)) {
            const customKeyPositionInfo = await this.getCustomKeyPositionInfo({ companyId: params.companyId, keyPositionNo: 1 });
            const customKeyPositionProjects = await this.projectDao.getProjectIdsByCustomKeyPosition({ companyId: params.companyId, projectIdList: result, customKeyPosition: custom_keyPosition_1, roleIdList: customKeyPositionInfo?.roleIds });
            const customKeyPositionProjectIds = customKeyPositionProjects.map(item => item.projectId);
            result = result.filter(projectId => customKeyPositionProjectIds.includes(projectId));
        }
        if (custom_keyPosition_2 && !_.isEmpty(result)) {
            const customKeyPositionInfo = await this.getCustomKeyPositionInfo({ companyId: params.companyId, keyPositionNo: 2 });
            const customKeyPositionProjects = await this.projectDao.getProjectIdsByCustomKeyPosition({ companyId: params.companyId, projectIdList: result, customKeyPosition: custom_keyPosition_2, roleIdList: customKeyPositionInfo?.roleIds });
            const customKeyPositionProjectIds = customKeyPositionProjects.map(item => item.projectId);
            result = result.filter(projectId => customKeyPositionProjectIds.includes(projectId));
        }
        if (custom_keyPosition_3 && !_.isEmpty(result)) {
            const customKeyPositionInfo = await this.getCustomKeyPositionInfo({ companyId: params.companyId, keyPositionNo: 3 });
            const customKeyPositionProjects = await this.projectDao.getProjectIdsByCustomKeyPosition({ companyId: params.companyId, projectIdList: result, customKeyPosition: custom_keyPosition_3, roleIdList: customKeyPositionInfo?.roleIds });
            const customKeyPositionProjectIds = customKeyPositionProjects.map(item => item.projectId);
            result = result.filter(projectId => customKeyPositionProjectIds.includes(projectId));
        }
        return result;
    }

    /**
     * 获取数据分析筛选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return ProjectDateReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param params 
     */
    async getAnalysisOption(params: ProjectDateReport.AnalysisOptionReq): Promise<ProjectDateReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, businessCategory, companyType } = params;
        if (!companyId || !topic) return null;
        const result: ProjectDateReport.AnalysisOptionRes = { projectManager: [], projectType: [], projectStatus: [], stage: [], projectAddress: "" };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        const overdueStatus: DataReportModuleFilter.SelectFilterValue[] = [];
        const workStatus: DataReportModuleFilter.SelectFilterValue[] = [];
        const delayStatus: DataReportModuleFilter.SelectFilterValue[] = [];
        const deviceDepartment: DataReportModuleFilter.SelectFilterValue[] = [];
        const decorationModel: DataReportModuleFilter.SelectFilterValue[] = [];
        //获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId });
        //获取筛选项数据
        const promiseList = [];
        promiseList.push(companyType === Company.CompanyType.Conglomerate ? this.projectDao.countProjectGroupCompany({ projectIdList: idList }) : []);//所属公司
        promiseList.push(businessCategory === 'construction' ? this.projectDao.countProjectGroupDeviceDepartment({ projectIdList: idList }) : []);//服务厂商
        promiseList.push(this.projectDao.countProjectGroupProjectManager({ projectIdList: idList }));//工地负责人
        promiseList.push(this.projectDao.countProjectGroupStage({ projectIdList: idList }));//阶段
        promiseList.push(businessCategory === 'construction' ? this.commonParameterDao.search("contract") : []);//装修方式
        promiseList.push(this.getProjectCustomKeyPositionOption({ companyId }));//工地关键岗位
        const [linkCompanyRes, deviceDepartmentRes, projectManagerRes, stageRes, decorationModelRes, customKeyPositionRes] = await Promise.all(promiseList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(res => linkCompany.push({ value: res.companyId, name: res.companyName }));
        (deviceDepartmentRes || []).filter(res => !!res.deviceDepartmentId).map(res => deviceDepartment.push({ value: res.deviceDepartmentId, name: res.deviceDepartmentName }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(res => result.projectManager.push({ value: res.projectManagerId, name: res.projectManagerName }));
        (stageRes || []).filter(res => !!res.stageName).map(res => result.stage.push({ value: res.stageName, name: res.stageName }));
        (decorationModelRes || []).filter(res => !!res.parameterCode).map(res => decorationModel.push({ value: res.parameterCode, name: res.parameterName }));
        //逾期状态
        if (ProjectDateReport.EtlTopic.includes(topic)) _.map(ProjectDateReport.OverdueStatus, (value) => overdueStatus.push({ value, name: ProjectDateReport.OverdueStatusName[value] }));
        //延期状态
        if (ProjectDateReport.EtlTopic.includes(topic)) _.map(ProjectDateReport.DelayStatus, (value) => delayStatus.push({ value, name: ProjectDateReport.DelayStatusName[value] }));
        //施工状态
        if (ProjectDateReport.EtlTopic.includes(topic)) _.map(ProjectDateReport.WorkStatus, (value) => workStatus.push({ value, name: ProjectDateReport.WorkStatusName[value] }));
        //工地类型
        _.map(ProjectDateReport.ProjectType, (value) => result.projectType.push({ value, name: ProjectDateReport.ProjectTypeName[value] }));
        //工地状态
        _.map(ProjectDateReport.ProjectStatus, (value) => result.projectStatus.push({ value, name: ProjectDateReport.ProjectStatusName[value] }));
        //周期数据 独有筛选项
        if (ProjectDateReport.EtlTopic.includes(topic)) Object.assign(result, { overdueStatus, workStatus, delayStatus });
        //营建公司 独有筛选项
        if (businessCategory === 'construction') Object.assign(result, { deviceDepartment, decorationModel });
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        //工地关键岗位
        if (customKeyPositionRes?.custom_keyPosition_1) Object.assign(result, { custom_keyPosition_1: customKeyPositionRes.custom_keyPosition_1 });
        if (customKeyPositionRes?.custom_keyPosition_2) Object.assign(result, { custom_keyPosition_2: customKeyPositionRes.custom_keyPosition_2 });
        if (customKeyPositionRes?.custom_keyPosition_3) Object.assign(result, { custom_keyPosition_3: customKeyPositionRes.custom_keyPosition_3 });
        return result;
    }

    /**
     * 数据分析列表
     * @param params 
     */
    async getAnalysisList(params: ProjectDateReport.AnalysisListReq): Promise<{ total: number; items: any[]; }> {
        const { companyId, topic, from, to } = params;
        const result: { total: number; items: any } = { total: 0, items: [] };
        if (!companyId || !topic) return result;
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params);
        if (_.isEmpty(idList)) return result;
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        //查询列表
        return ProjectDateReport.RealTopic.includes(topic) ? await this.getRealProjectList({ ...params, projectIdList: idList }) : await this.getEtlProjectList({ ...params, projectIdList: idList, startTime: timeFrame.startTime, endTime: timeFrame.endTime });
    }

    @CheckFormatCsv(ProjectDateReport.AnalysisRealListCsv)
    async getRealProjectList(params: Project.SearchRealProjectReq) {
        const { companyType, businessCategory } = params;
        const result = await this.projectDao.searchRealProjectList(params);
        //查询其他数据
        const projectIds = [];
        const companyIds = [];
        result.items.forEach(item => {
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
        });
        const promiseList = [];
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory: params.businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        promiseList.push(businessCategory === 'construction' ? this.commonParameterService.searchCommonInfoMap({ parameterType: 'contract' }) : {});//装修方式
        promiseList.push(this.getNoCompletedProjectIds({ companyId: params.companyId, projectIds }));//待完工工地id列表
        promiseList.push(this.deviceDao.searchAiProjectIds({ projectIds }));//获取AI工地详情
        promiseList.push(this.getProjectCustomKeyPositionMap({ companyId: params.companyId, projectIdList: projectIds }));//获取工地关键岗位信息
        const [projectInfoMap, companyInfoMap, decorationModelMap, noCompletedProjectIds, aiProjectIds, customKeyPositionMap] = await Promise.all(promiseList);
        //组装数据
        result.items = result.items.map(item => {
            const result = {
                ...item,
                projectType: aiProjectIds.includes(item.projectId) ? ProjectDateReport.ProjectType.WisdomProject : ProjectDateReport.ProjectType.CommonProject,
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                toCompleted: noCompletedProjectIds.includes(item.projectId) ? "Y" : "N",
                custom_keyPosition_1: customKeyPositionMap[item.projectId]?.custom_keyPosition_1,
                custom_keyPosition_2: customKeyPositionMap[item.projectId]?.custom_keyPosition_2,
                custom_keyPosition_3: customKeyPositionMap[item.projectId]?.custom_keyPosition_3,
            }
            //集团公司 独有数据
            if (companyType === '4') {
                Object.assign(result, {
                    linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
                });
            }
            //营建公司 独有数据
            if (businessCategory === 'construction') {
                Object.assign(result, {
                    deviceDepartmentId: projectInfoMap[item.projectId]?.deviceDepartmentId || "",
                    deviceDepartmentName: projectInfoMap[item.projectId]?.deviceDepartmentName || "",
                    decorationModelName: decorationModelMap[item.decorationModel]?.parameterName || "",
                });
            }
            return result;
        });
        return result;
    }

    @CheckFormatCsv(ProjectDateReport.AnalysisEtlListCsv)
    async getEtlProjectList(params: Project.SearchEtlProjectReq) {
        const { companyType, businessCategory, companyId } = params;
        const result = await this.projectDao.searchEtlProjectList(params);
        // 获取配置信息
        const overdueConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.NodeOverdue });
        const isNodeDelayOverdue = overdueConfig?.configs[0]?.delayOverdue;
        // const matterSet = await AgileApi.searchMatterSet({companyId});
        // const isNodeDelayOverdue = matterSet?.isNodeDelayOverdue;
        //查询其他数据
        const projectIds = [];
        const companyIds = [];
        result.items.forEach(item => {
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
        });
        const promiseList = [];
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory: params.businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        promiseList.push(businessCategory === 'construction' ? this.commonParameterService.searchCommonInfoMap({ parameterType: 'contract' }) : {});//装修方式
        promiseList.push(this.projectService.searchInactiveInfoMap({ projectIds, startTime: params.startTime, endTime: params.endTime }))//获取发生过停工的工地详情
        promiseList.push(this.projectService.searchOverdueInfoMap({ companyId: params.companyId, projectIds, startTime: params.startTime, endTime: params.endTime }))//获取发生过逾期的工地详情
        promiseList.push(this.projectService.searchProblemInfoMap({ projectIds, startTime: params.startTime, endTime: params.endTime }))//获取发生过问题的工地详情
        promiseList.push(businessCategory === 'home_decor' ? this.projectService.getOwnerFocusProjectIds({ companyId: params.companyId, projectIds, startTime: params.startTime, endTime: params.endTime }) : [])//获取业主关注的工地详情
        promiseList.push(this.projectDao.getDelayProjectIdList({ projectIds, startTime: params.startTime, endTime: params.endTime, isNodeDelayOverdue }))//获取发生过延期的工地详情
        promiseList.push(businessCategory === 'home_decor' ? this.projectService.getOwnerNegativeReviewProjectInfoMap({ companyId: params.companyId, projectIds, startTime: params.startTime, endTime: params.endTime }) : [])//获取发生过业主差评的工地详情
        promiseList.push(this.deviceDao.searchEtlAiProjectIds({ projectIds, startTime: params.startTime, endTime: params.endTime }));//获取AI工地详情
        promiseList.push(this.getProjectCustomKeyPositionMap({ companyId: params.companyId, projectIdList: projectIds }));//获取工地关键岗位信息
        const [projectInfoMap, companyInfoMap, decorationModelMap, inactiveInfoMap, overdueInfoMap, problemInfoMap, ownerFocusProjectIds, delayProjectIds, ownerNegativeMap, aiProjectIds, customKeyPositionMap] = await Promise.all(promiseList);
        //组装数据
        result.items = result.items.map(item => {
            const result = {
                ...item,
                projectType: aiProjectIds.includes(item.projectId) ? ProjectDateReport.ProjectType.WisdomProject : ProjectDateReport.ProjectType.CommonProject,
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                workStatus: Number(inactiveInfoMap[item.projectId]?.inactiveDay || 0) > 0 ? "N" : "Y",//施工状态
                noWorkDay: String(inactiveInfoMap[item.projectId]?.inactiveDay || ""),//未施工天数(异常停工天数)
                delayStatus: (delayProjectIds || []).includes(item.projectId) ? "Y" : "N",//延期状态
                overdueStatus: Number(overdueInfoMap[item.projectId]?.overdueTime || 0) > 0 ? "Y" : "N",//逾期状态
                overdueDay: String(overdueInfoMap[item.projectId]?.overdueTime >= 0 ? overdueInfoMap[item.projectId]?.overdueTime : 0 || ""),//逾期时间(天)
                newProblemCount: String(problemInfoMap[item.projectId]?.count || ""),//新增问题数量
                custom_keyPosition_1: customKeyPositionMap[item.projectId]?.custom_keyPosition_1,
                custom_keyPosition_2: customKeyPositionMap[item.projectId]?.custom_keyPosition_2,
                custom_keyPosition_3: customKeyPositionMap[item.projectId]?.custom_keyPosition_3,
            }
            //集团公司 独有数据
            if (companyType === '4') {
                Object.assign(result, {
                    linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
                });
            }
            //营建公司 独有数据
            if (businessCategory === 'construction') {
                Object.assign(result, {
                    decorationModelName: decorationModelMap[item.decorationModel]?.parameterName || "",
                    deviceDepartmentId: projectInfoMap[item.projectId]?.deviceDepartmentId || "",
                    deviceDepartmentName: projectInfoMap[item.projectId]?.deviceDepartmentName || "",
                });
            }
            //家装公司 独有数据
            if (businessCategory === 'home_decor') {
                Object.assign(result, {
                    ownerNegativeReviewCount: String((ownerNegativeMap[item.projectId] || [])?.length || ""),//业主差评数量
                    ownerFocus: (ownerFocusProjectIds || []).includes(item.projectId) ? "Y" : "N"//业主关注
                });
            }
            return result;
        });
        return result;
    }

    /**
     * 获取custom_keyPosition_*关键岗位配置信息
     * @param params 
     * @returns 
     */
    private async getCustomKeyPositionInfo({ companyId, keyPositionNo }: { companyId: string, keyPositionNo: 1 | 2 | 3 }) {
        if (!companyId || !keyPositionNo) return null;
        const matterSet = await AgileApi.searchMatterSet({ companyId });
        const projectKeyPositions = matterSet?.projectKeyPositions || [];
        if (_.isEmpty(projectKeyPositions)) return null;
        const projectKeyPosition = projectKeyPositions.find(item => item.sort == keyPositionNo);
        return projectKeyPosition;
    }

    /**
     * 获取工地关键岗位筛选信息
     * @param params 
     * @returns 
     */
    private async getProjectCustomKeyPositionOption({ companyId }: { companyId: string }): Promise<{ custom_keyPosition_1?: DataReportModuleFilter.SelectFilterValue[], custom_keyPosition_2?: DataReportModuleFilter.SelectFilterValue[], custom_keyPosition_3?: DataReportModuleFilter.SelectFilterValue[] }> {
        const result: { custom_keyPosition_1?: DataReportModuleFilter.SelectFilterValue[], custom_keyPosition_2?: DataReportModuleFilter.SelectFilterValue[], custom_keyPosition_3?: DataReportModuleFilter.SelectFilterValue[] } = {};
        if (!companyId) return result;
        //查询工地事项配置
        const matterSet = await AgileApi.searchMatterSet({ companyId });
        const projectKeyPositions = matterSet?.projectKeyPositions || [];
        if (_.isEmpty(projectKeyPositions)) return result;
        //查询岗位下的成员信息
        const roleIds = [];
        projectKeyPositions.map(item => { if (!_.isEmpty(item?.roleIds)) roleIds.push(...item.roleIds); });
        const managerList = !_.isEmpty(roleIds) ? await this.managerDao.getProjectManagerInfoByRoleIds({ companyId, roleIds }) : [];
        const managerInfoMap = _.groupBy(managerList, 'roleId');
        //组装数据
        _.sortBy(projectKeyPositions, 'sort').map(item => {
            const options: DataReportModuleFilter.SelectFilterValue[] = [];
            (item?.roleIds || []).forEach(roleId => {
                const managerList = managerInfoMap[roleId] || [];
                return managerList.map(item => {
                    options.push({
                        name: item.nickName,
                        value: item.managerId
                    });
                });
            });
            result[`custom_keyPosition_${item?.sort}`] = { name: item.name, options: options };
        });
        return result;
    }

    /**
     * 获取工地关键岗位信息
     * 返回结构：projectId: { 
     *              custom_keyPosition_1?: { columnName: string, managerId: string, name: string } | null | undefined, 
     *              custom_keyPosition_2?: { columnName: string, managerId: string, name: string } | null | undefined, 
     *              custom_keyPosition_3?: { columnName: string, managerId: string, name: string } | null | undefined
     *           }
     * 1、custom_keyPosition_*: null 表示公司未开启该关键岗位
     * 2、custom_keyPosition_*: { columnName: string, managerId: null, name: null } 表示公司开启了该关键岗位，工地没有符合该关键岗位的成员
     * 3、custom_keyPosition_*: { columnName: string, managerId: string, name: string } 表示公司开启了该关键岗位，工地有符合该关键岗位的成员
     * @param params 
     * @returns 
     */
    private async getProjectCustomKeyPositionMap({ companyId, projectIdList }: { companyId: string, projectIdList: string[] }) {
        let result: { [key: string]: { custom_keyPosition_1?: { columnName: string, managerId: string, name: string }, custom_keyPosition_2?: { columnName: string, managerId: string, name: string }, custom_keyPosition_3?: { columnName: string, managerId: string, name: string } } } = {};
        if (!companyId || _.isEmpty(projectIdList)) return result;
        //查询工地事项配置
        const matterSet = await AgileApi.searchMatterSet({ companyId });
        const projectKeyPositions = matterSet?.projectKeyPositions || [];
        if (_.isEmpty(projectKeyPositions)) return result;
        //查询工地custom_keyPosition_*关键岗位信息
        const promiseList = [];
        projectKeyPositions.forEach(item => {
            promiseList.push(this.getProjectCustomKeyPosition({ companyId, projectIdList, roleIdList: item?.roleIds, keyPositionNo: item.sort, columnName: item.name }));
        });
        const items: { [key: string]: { [key: string]: { projectId: string, managerId: string, name: string } } }[] = await Promise.all(promiseList);
        const customKeyPositionMap = {};
        items.forEach(item => { Object.assign(customKeyPositionMap, item); });
        //处理工地关键岗位信息：根据工地id分组
        for (const projectId of projectIdList) {
            result[projectId] = {
                custom_keyPosition_1: customKeyPositionMap?.['custom_keyPosition_1'] ?
                    {
                        name: customKeyPositionMap?.['custom_keyPosition_1']?.[projectId]?.name || null,
                        managerId: customKeyPositionMap?.['custom_keyPosition_1']?.[projectId]?.managerId || null,
                        columnName: customKeyPositionMap?.['custom_keyPosition_1']?.columnName || null,
                    }
                    : null,
                custom_keyPosition_2: customKeyPositionMap?.['custom_keyPosition_2'] ?
                    {
                        name: customKeyPositionMap?.['custom_keyPosition_2']?.[projectId]?.name || null,
                        managerId: customKeyPositionMap?.['custom_keyPosition_2']?.[projectId]?.managerId || null,
                        columnName: customKeyPositionMap?.['custom_keyPosition_2']?.columnName || null,
                    }
                    : null,
                custom_keyPosition_3: customKeyPositionMap?.['custom_keyPosition_3'] ?
                    {
                        name: customKeyPositionMap?.['custom_keyPosition_3']?.[projectId]?.name || null,
                        managerId: customKeyPositionMap?.['custom_keyPosition_3']?.[projectId]?.managerId || null,
                        columnName: customKeyPositionMap?.['custom_keyPosition_3']?.columnName || null,
                    }
                    : null,
            }
        }
        return result;
    }

    /**
     * 获取工地custom_keyPosition_*关键岗位信息
     * @param params 
     * @returns 
     */
    private async getProjectCustomKeyPosition({ companyId, projectIdList, roleIdList, keyPositionNo, columnName }: { companyId: string, projectIdList: string[], roleIdList: string[], keyPositionNo: 1 | 2 | 3, columnName: string }) {
        if (!companyId || _.isEmpty(projectIdList) || _.isEmpty(roleIdList) || !keyPositionNo) return {};
        const result = await this.projectDao.getProjectCustomKeyPosition({ companyId, projectIdList, roleIdList });
        const key = `custom_keyPosition_${keyPositionNo}`;
        return { [key]: { ..._.keyBy(result, 'projectId'), columnName } };
    }

    /**===================================
                处理图表数据
    ===================================**/
    //维度图表 方法映射
    getDimensionChartDataFunction(dimension: ProjectDateReport.Dimension) {
        //维度图表 方法映射
        const dimensionChartDataFunction: Readonly<{ [K in ProjectDateReport.Dimension]: (params: ProjectDateReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [ProjectDateReport.Dimension.ProjectDepartmentGroup]: this.getProjectDepartmentGroupChartData.bind(this), // 工地部门
            [ProjectDateReport.Dimension.ServiceVendorGroup]: this.getDeviceDepartmentGroupChartData.bind(this), // 服务厂商
            [ProjectDateReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [ProjectDateReport.Dimension.DecorationModelGroup]: this.getDecorationModelGroupChartData.bind(this), // 装修方式
            [ProjectDateReport.Dimension.ProjectTypeGroup]: this.getProjectTypeGroupChartData.bind(this), // 工地类型
            [ProjectDateReport.Dimension.ProjectStatusGroup]: this.getProjectStatusGroupChartData.bind(this), // 工地状态
            [ProjectDateReport.Dimension.StageGroup]: this.getStageGroupChartData.bind(this), // 阶段
            [ProjectDateReport.Dimension.ProjectManagerGroup]: this.getProjectManagerGroupChartData.bind(this), // 负责人
            [ProjectDateReport.Dimension.Trend]: this.getTrendChartData.bind(this),//趋势
            [ProjectDateReport.Dimension.CustomKeyPosition1Group]: this.getCustomKeyPosition1GroupChartData.bind(this),//关键岗位1分布
            [ProjectDateReport.Dimension.CustomKeyPosition2Group]: this.getCustomKeyPosition2GroupChartData.bind(this),//关键岗位2分布
            [ProjectDateReport.Dimension.CustomKeyPosition3Group]: this.getCustomKeyPosition3GroupChartData.bind(this),//关键岗位3分布
        };
        return dimensionChartDataFunction[dimension];
    }


    /**
     * 获取工地部门分布图表数据
     * 维度: 工地部门分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getProjectDepartmentGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList, departmentId } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.projectDao.countProjectGroupDepartment({ ...params, projectIdList: idList });
        //处理图表结构: 工地部门分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const entity = await this.companyDecorationDao.search(params.companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.to, data: projectData });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.ProjectDepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'departmentId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.ProjectDepartmentGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取数值图表数据
     * 维度: 数值
     * 支持的图表结构: 数值
     * @param params 
     */
    private async getNumberChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const detectionData = await this.projectDao.countProject({ ...params, projectIdList: idList });
        //处理图表结构: 总数统计 & 数值
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({ chartType: Chart.ChartType.Total, title, name, data: Number(detectionData?.count || 0), actionType, isReal });
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.Number,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取服务厂商分布图表数据
     * 维度: 服务厂商分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getDeviceDepartmentGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.projectDao.countProjectGroupDeviceDepartment({ ...params, projectIdList: idList });
        //处理图表结构: 服务厂商分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.deviceDepartmentId || "", name: item.deviceDepartmentName || "无服务厂商", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.ServiceVendorGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.ServiceVendorGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取装修方式分布图表数据
     * 维度: 装修方式分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getDecorationModelGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.projectDao.countProjectGroupDecorationModel({ ...params, projectIdList: idList });
        //处理图表结构: 装修方式分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.decorationModel || "", name: item.decorationModelName || "无装修方式", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.DecorationModelGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'decorationModel', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'decorationModel', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'decorationModel', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'decorationModel', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.DecorationModelGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取工地类型分布图表数据
     * 维度: 工地类型分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getProjectTypeGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList, from, to } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        let aiProjectIdList = [];
        if (ProjectDateReport.RealTopic.includes(topic as ProjectDateReport.Topic)) {//实时数据的智慧工地
            aiProjectIdList = await this.deviceDao.searchAiProjectIds({ projectIds: idList });
        }
        if (ProjectDateReport.EtlTopic.includes(topic as ProjectDateReport.Topic)) {//切片数据的智慧工地
            aiProjectIdList = await this.deviceDao.searchEtlAiProjectIds({ projectIds: idList, startTime: timeFrame.startTime, endTime: timeFrame.endTime });
        }
        //查询数据
        const projectData = await this.projectDao.countProjectGroupProjectType({ ...params, projectIdList: idList, aiProjectIdList });
        //处理图表结构: 工地类型分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.projectType, name: ProjectDateReport.ProjectTypeName[item.projectType] || "", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.ProjectTypeGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'projectType', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'projectType', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'projectType', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'projectType', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.ProjectTypeGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取工地状态分布图表数据
     * 维度: 工地状态分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getProjectStatusGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.projectDao.countProjectGroupProjectStatus({ ...params, projectIdList: idList });
        //处理图表结构: 工地状态分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.projectStatus || "", name: ProjectDateReport.ProjectStatusName[item.projectStatus] || "其他状态", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.ProjectStatusGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'projectStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'projectStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'projectStatus', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'projectStatus', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.ProjectStatusGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取工地阶段分布图表数据
     * 维度: 工地阶段分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getStageGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.projectDao.countProjectGroupStage({ ...params, projectIdList: idList });
        //处理图表结构: 工地阶段分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.stageName || "", name: item.stageName || "未知阶段", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.StageGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'stageList', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'stageList', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'stageList', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'stageList', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.StageGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取工地负责人分布图表数据
     * 维度: 工地负责人分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getProjectManagerGroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        const projectData = await this.projectDao.countProjectGroupProjectManager({ ...params, projectIdList: idList });
        //处理图表结构: 工地负责人分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.projectManagerId || "", name: item.projectManagerName || "未知负责人", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = ProjectDateReport.DimensionName[ProjectDateReport.Dimension.ProjectManagerGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'projectManagerId', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'projectManagerId', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'projectManagerId', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'projectManagerId', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.ProjectManagerGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 查询周期数据
     * 用于: 分布趋势图表数据
     * @param params 
     * @returns
     */
    private async getGroupTrendData(params: ProjectDateReport.AnalysisDistributeReq): Promise<{ category: string, value: number }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return null;
        const { idList } = await this.getTopicIdList({ ...params, companyId, topic, from, to, dataType: "day", departmentId });
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        const res = await this.projectDao.countProject({ ...params, projectIdList: idList });
        return { category, value: Number(res?.count || 0) };
    }

    /**
     * 获取趋势图表数据
     * 维度: 趋势
     * 支持的图表结构: 折线图|柱状图
     * @param params 
     */
    private async getTrendChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { companyId, topic, from, to, dataType, chartType } = params;
        if (!companyId || !topic || !from || !to || !dataType || !chartType) throw new YqzException("必传参数不能为空");
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        //查询周期数据
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, value: number }[] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const data = [];//y轴数据
        promiseResult.forEach(res => {
            category.push(res?.category || "");
            data.push(res?.value || 0);
        });
        //处理图表结构: 趋势 & 折线图/柱状图
        const title = params?.title || `${ProjectDateReport.TopicName[topic]}`;
        const name = `${ProjectDateReport.DimensionName[ProjectDateReport.Dimension.Trend]}`;
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Line:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Line>({ chartType: Chart.ChartType.Line, title, name, category, data, actionType, isReal });
                break;
            case Chart.ChartType.Bar:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Bar>({ chartType: Chart.ChartType.Bar, title, name, category, data, actionType, isReal });
                break;
        }
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.Trend,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取关键岗位1分布图表数据
     * 维度: 关键岗位1分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     */
    private async getCustomKeyPosition1GroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList, companyId } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询公司事项配置 关键岗位1的配置信息
        const projectKeyPositions_1 = await this.getCustomKeyPositionInfo({ companyId, keyPositionNo: 1 });
        if (!projectKeyPositions_1) return null;
        const roleIdList = projectKeyPositions_1?.roleIds || [];
        //查询数据
        const projectData = await this.projectDao.countProjectGroupCustomKeyPosition({ ...params, projectIdList: idList, customKeyPositionRoleIds: roleIdList });
        //处理图表结构: 工地负责人分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.managerId || "", name: item.nickName || "其他", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = `${projectKeyPositions_1?.name}分布` || ProjectDateReport.DimensionName[ProjectDateReport.Dimension.CustomKeyPosition1Group];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'custom_keyPosition_1', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'custom_keyPosition_1', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'custom_keyPosition_1', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'custom_keyPosition_1', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.CustomKeyPosition1Group,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取关键岗位2分布图表数据
     * 维度: 关键岗位2分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getCustomKeyPosition2GroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList, companyId } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询公司事项配置 关键岗位2的配置信息
        const projectKeyPositions_2 = await this.getCustomKeyPositionInfo({ companyId, keyPositionNo: 2 });
        if (!projectKeyPositions_2) return null;
        const roleIdList = projectKeyPositions_2?.roleIds || [];
        //查询数据
        const projectData = await this.projectDao.countProjectGroupCustomKeyPosition({ ...params, projectIdList: idList, customKeyPositionRoleIds: roleIdList });
        //处理图表结构: 工地负责人分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.managerId || "", name: item.nickName || "其他", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = `${projectKeyPositions_2?.name}分布` || ProjectDateReport.DimensionName[ProjectDateReport.Dimension.CustomKeyPosition2Group];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'custom_keyPosition_2', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'custom_keyPosition_2', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'custom_keyPosition_2', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'custom_keyPosition_2', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.CustomKeyPosition2Group,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取关键岗位3分布图表数据
     * 维度: 关键岗位3分布
     * 支持的图表结构: 玫瑰图|饼图|环形图|柱状图(分布)
     * @param params 
     * @returns 
     */
    private async getCustomKeyPosition3GroupChartData(params: ProjectDateReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList, companyId } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询公司事项配置 关键岗位3的配置信息
        const projectKeyPositions_3 = await this.getCustomKeyPositionInfo({ companyId, keyPositionNo: 3 });
        if (!projectKeyPositions_3) return null;
        const roleIdList = projectKeyPositions_3?.roleIds || [];
        //查询数据
        const projectData = await this.projectDao.countProjectGroupCustomKeyPosition({ ...params, projectIdList: idList, customKeyPositionRoleIds: roleIdList });
        //处理图表结构: 工地负责人分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        projectData.map(item => { data.push({ key: item.managerId || "", name: item.nickName || "其他", value: Number(item?.count || 0) }); });
        const title = params?.title || ProjectDateReport.TopicName[topic];
        const name = `${projectKeyPositions_3?.name}分布` || ProjectDateReport.DimensionName[ProjectDateReport.Dimension.CustomKeyPosition3Group];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const isReal = ProjectDateReport.RealTopic.includes(topic) ? "Y" : "N";
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'custom_keyPosition_3', data, actionType, isReal });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'custom_keyPosition_3', data, actionType, isReal });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'custom_keyPosition_3', data, actionType, isReal });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'custom_keyPosition_3', data, actionType, isReal });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.Project,
            dimension: ProjectDateReport.Dimension.CustomKeyPosition3Group,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取待完工工地idList
     * @param companyId 当前登录的公司
     * @param projectIds 需要过滤的工地
     * @returns 
     */
    private async getNoCompletedProjectIds({ companyId, projectIds }: { companyId: string, projectIds: string[] }) {
        if (!companyId || _.isEmpty(projectIds)) return [];
        const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Project });
        const todoCompleted = config?.configs[0]?.todoCompleted;
        if (!todoCompleted) return [];
        return await this.projectDao.searchProjectIdListByTodoCompleted({ projectIds, todoCompleted });
    }

    /**
     * 获取逾期的工地idList
     * @param projectIds 需要过滤的工地
     * @returns 
     */
    private async getOverdueProjectIds({ startTime, endTime, projectIds, delayOverdue }: { startTime: string, endTime: string, projectIds: string[], delayOverdue: 'Y' | 'N' }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
        const res = await this.nodeOverdueEventDao.searchOverdueProjectNodeIdListByOverdueTimeV2({ projectIds, startTime, endTime, delayOverdue, overdueTime: '1' });
        const projectIdList = _.uniq((res || []).map(res => res.projectId));
        return projectIdList;
    }

    /**
     * 获取未施工的工地idList
     * @param projectIds 需要过滤的工地
     * @returns 
     */
    private async getNoWorkProjectIds({ startTime, endTime, projectIds }: { startTime: string, endTime: string, projectIds: string[] }) {
        if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
        return await this.projectInactiveRecordDao.searchProjectIdListByInactiveDay({ projectIds, startTime, endTime, inactiveDay: '1' });
    }

    /**
     * 获取满足连续未施工N天的工地idList
     * @param companyId 当前登录的公司
     * @param projectIds 需要过滤的工地
     * @returns 
     */
    private async getNoWorkByDayProjectIds({ inactiveDay, startTime, endTime, projectIds }: { inactiveDay: string, startTime: string, endTime: string, projectIds: string[] }) {
        if (!inactiveDay || _.isEmpty(projectIds) || !startTime || !endTime) return [];
        return await this.projectInactiveRecordDao.searchProjectIdListByInactiveDay({ projectIds, startTime, endTime, inactiveDay });
    }

    /**
     * 获取满足业主关注工地的工地idList
     * @param companyId 当前登录的公司
     * @param projectIds 需要过滤的工地
     */
    private async getOwnerFocusProjectIds({ ownerFocusProjectScope, ownerFocusProjectOneDay, ownerFocusProjectTime, startTime, endTime, projectIds }: { ownerFocusProjectScope: string, ownerFocusProjectOneDay: string, ownerFocusProjectTime: string, startTime: string, endTime: string, projectIds: string[] }): Promise<string[]> {
        if (!ownerFocusProjectScope || !ownerFocusProjectOneDay || !ownerFocusProjectTime || _.isEmpty(projectIds) || !startTime || !endTime) return [];
        const promiseList = [];
        //查询单日内查看直播超过N次的工地idList
        promiseList.push(this.projectDao.searchProjectIdListByOwnerFocusOneDay({ projectIds, ownerFocusProjectOneDay, startTime, endTime }));
        //查询所选时间段内查看直播超过N次的工地idList
        promiseList.push(this.projectDao.searchProjectIdListByOwnerFocusTime({ projectIds, ownerFocusProjectTime, startTime, endTime }));
        const [ownerFocusOneDayProjectIds, ownerFocusTimeProjectIds] = await Promise.all(promiseList);
        //取交集
        if (ownerFocusProjectScope === 'all') return _.uniq(_.intersection(ownerFocusOneDayProjectIds, ownerFocusTimeProjectIds));
        //取并集
        if (ownerFocusProjectScope === 'one_of') return _.uniq(_.union(ownerFocusOneDayProjectIds, ownerFocusTimeProjectIds));
        return [];
    }

    /**
     * 获取满足业主差评工地的工地idList
     * @param companyId 当前登录的公司
     * @param projectIds 需要过滤的工地
     * @returns 
     */
    private async getOwnerNegativeReviewProjectIds({ badEvaluationScope, overallScore, individualScore, projectIds, startTime, endTime }: { badEvaluationScope: string, overallScore: string, individualScore: string, startTime: string, endTime: string, projectIds: string[] }): Promise<string[]> {
        if (!badEvaluationScope || !overallScore || !individualScore || _.isEmpty(projectIds) || !startTime || !endTime) return [];
        const promiseList = [];
        //综合评分差评工地idList
        promiseList.push(this.projectDao.searchProjectListByOverallScore({ projectIds, overallScore, startTime, endTime }));
        //单项评分差评工地idList
        promiseList.push(this.projectDao.searchProjectListByIndividualScore({ projectIds, individualScore, startTime, endTime }));
        const [overallScoreProjectRes, individualScoreProjectRes] = await Promise.all(promiseList);
        const overallScoreProjectIds = overallScoreProjectRes.map(res => res.projectId);
        const individualScoreProjectIds = individualScoreProjectRes.map(res => res.projectId);
        //取交集
        if (badEvaluationScope === 'all') return _.uniq(_.intersection(overallScoreProjectIds, individualScoreProjectIds));
        //取并集
        if (badEvaluationScope === 'one_of') return _.uniq(_.union(overallScoreProjectIds, individualScoreProjectIds));
        return [];
    }

}