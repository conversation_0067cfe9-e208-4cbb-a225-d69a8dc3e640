import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>og<PERSON> } from "@yqz/nest";
import { SqlFactory } from '../../../../sql-generate/factory/sql-factory';
import { DepartmentDao } from '@src/modules/report/domain/bgw/department/dao/department.dao';
import { ProjectDao } from '@src/modules/report/domain/bgw/project/dao/project.dao';
import { ChartFactory } from '../../../chart/chart.factory';
import { DateReport } from '../../../dto/data-report.dto';
import { CustomerEvaluateDataReport } from '../dto/customer-evaluate-data-report.dto';
import { DateReportModule } from '../../../dto/data-report-module.dto';
import { DateReportInterface } from '../dto/data-report.interface.dto';
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { Chart } from '../../../chart/chart.type';
import * as _ from 'lodash';
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { Sql } from '../../../../sql-generate/type/sql.type';
import { ChartDateUtil } from '@src/util/chart-date.util';
import { ProjectService } from '@src/modules/report/domain/bgw/project/service/project.service';
import { CompanyService } from '@src/modules/report/domain/bgw/company/service/company.service';
import * as DateFns from "date-fns";
import { DataReportConfigService } from '../../data-report-config.service';
import { MediaService } from '@src/modules/report/domain/bgw/media/service/media.service';
import { DataReportModuleFilter } from '../../../dto/data-report-module-filter.dto';
import { Company } from '@src/modules/report/domain/bgw/company/dto/company.dto';

/**
 * @summary 数据看板 - 业主评价
 * <AUTHOR>
 * @class CustomerEvaluateDataReportService
 */
@Injectable()
export class CustomerEvaluateDataReportService extends CustomChart {
    public readonly logger = new MyLogger(CustomerEvaluateDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly chartFactory: ChartFactory,
        private readonly projectDao: ProjectDao,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly projectService: ProjectService,
        private readonly companyService: CompanyService,
        private readonly mediaService: MediaService,
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
     * 获取报告图表数据
     */
    async getChartData(params: CustomerEvaluateDataReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { dimension, topic, companyId } = params;
        //处理图表数据参数
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params) as { idList: string[] };
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new Error(`Unsupported dimension: ${dimension}`);
        //获取图表数据
        const result = await method({ ...params, idList });
        //处理辅助数据
        const countProjectRes = await this.getProjectIdsByEvaluateIds({ ...params, companyId, idList });
        result.data.desc.subLabel = "工地数"
        result.data.desc.subValue = Number(countProjectRes.length || 0);
        result.data.desc.isReal = CustomerEvaluateDataReport.RealTopic.includes(topic) ? "Y" : "N";
        return result;
    }

    /**
     * 获取工地数
     * @param params 
     */
    private async getProjectIdsByEvaluateIds(params: CustomerEvaluateDataReport.GetProjectIdsReq): Promise<string[]> {
        if (_.isEmpty(params.idList)) return [];
        //筛选好评差评
        if (params.evaluateType) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList: params.idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        if (_.isEmpty(params.idList)) return [];
        //查询工地id列表
        const projectList = await this.projectDao.searchProjectEvaluateProjectList(params);
        return projectList.map(res => res.projectId);
    }

    /**
     * 获取图表必要筛选条件
     */
    async getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string; subject: DateReport.Subject; topic: string; addFilters: any[] }> {
        const { companyId, topic, from, to, departmentId } = params;
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        const subject = DateReport.Subject.Evaluate;
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        //根据部门id列表查询部门数据切片的工地id列表
        const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.ProjectIdList] });
        const projectIds = etlDateList?.projectIdList || [];
        let evaluateIdList = await this.projectDao.searchEvaluateIdList({ projectIds });
        //新增好评/差评
        if (topic === CustomerEvaluateDataReport.Topic.EvaluateGoodNew || topic === CustomerEvaluateDataReport.Topic.EvaluateBadNew) {
            //查询差评idList
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList: evaluateIdList });
            evaluateIdList = topic === CustomerEvaluateDataReport.Topic.EvaluateGoodNew ? _.difference(evaluateIdList, ownerNegativeIdList) : ownerNegativeIdList;
        }
        const addFilters = [
            { key: "evaluateId", value: evaluateIdList, operator: Sql.Operator.in },
            { key: "from", value: timeFrame.startTime, operator: Sql.Operator.greater_than },
            { key: "to", value: timeFrame.endTime, operator: Sql.Operator.less_than },
        ];
        // this.logger.log(`getChartSqlFilters params : ${JSON.stringify(params)}, addFilters: ${JSON.stringify(addFilters)}`);
        return { companyId, subject, topic, addFilters };
    }

    /**
     * 获取数据分析筛选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return CustomerEvaluateDataReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param params 
     */
    async getAnalysisOption(params: CustomerEvaluateDataReport.AnalysisOptionReq): Promise<CustomerEvaluateDataReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, companyType } = params;
        if (!companyId || !topic) return null;
        const result: CustomerEvaluateDataReport.AnalysisOptionRes = { projectManager: [], evaluateStage: [], evaluateType: [], score: [], projectAddress: "" };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        // 获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        const projectIdList = await this.projectDao.getProjectIdsByEvaluateIds({ evaluateIds: idList });
        const promiseList = [];
        // 工地公司
        promiseList.push(companyType === Company.CompanyType.Conglomerate ? this.projectDao.countProjectGroupCompany({ projectIdList }) : []);
        // 工地负责人
        promiseList.push(this.projectDao.countProjectGroupProjectManager({ projectIdList }));
        // 评价阶段
        promiseList.push(this.projectDao.countEvaluateProjectStageGroup({ idList }));
        const [linkCompanyRes, projectManagerRes, stageRes] = await Promise.all(promiseList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(item => linkCompany.push({
            name: item.companyName,
            value: item.companyId
        }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(item => result.projectManager.push({
            name: item.projectManagerName,
            value: item.projectManagerId
        }));
        (stageRes || []).filter(res => !!res.stageLabel).map(item => result.evaluateStage.push({
            name: item.stageLabel,
            value: item.stageLabel
        }));
        result.evaluateType = [
            { name: '好评', value: 'good' },
            { name: '差评', value: 'bad' },
        ];
        result.score = [
            { name: '1星', value: '1' },
            { name: '2星', value: '2' },
            { name: '3星', value: '3' },
            { name: '4星', value: '4' },
            { name: '5星', value: '5' },
        ];
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        return result;
    }

    /**
     * 获取分析列表
     */
    @CheckFormatCsv(CustomerEvaluateDataReport.AnalysisListCsv)
    async getAnalysisList(params: CustomerEvaluateDataReport.AnalysisListReq): Promise<any> {
        const { companyType, companyId, topic, from, to, dataType, departmentId } = params;
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        params.idList = idList;
        //筛选好评差评
        if (params.evaluateType && !_.isEmpty(params.idList)) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList: params.idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        if (_.isEmpty(params.idList)) return { total: 0, items: [] };
        const evaluateRes = await this.projectDao.searchProjectEvaluateList(params);
        //查询其他数据
        const evaluateIds = [];
        const projectIds = [];
        const companyIds = [];
        const mediaIds = [];
        evaluateRes.items.forEach(item => {
            evaluateIds.push(item.evaluateId);
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
            const evaluateImgIdList = (item?.evaluateImgIds || "").split(',');
            if (!_.isEmpty(evaluateImgIdList)) mediaIds.push(...evaluateImgIdList);
        });
        const promiseList = [];
        promiseList.push(this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList: evaluateIds }));//业主差评id列表
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory: params.businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        promiseList.push(this.mediaService.searchParadigm({ mediaIdList: mediaIds }));
        const [ownerNegativeReviewIds, projectInfoMap, companyInfoMap, mediaMap] = await Promise.all(promiseList);
        //组装数据
        evaluateRes.items = evaluateRes.items.map(item => {
            const evaluateImgIdList = (item?.evaluateImgIds || "").split(',');
            const image = [];
            evaluateImgIdList.map(id => { if (mediaMap[id]) image.push(mediaMap[id]); });
            const result = {
                ...item,
                type: ownerNegativeReviewIds.includes(item.evaluateId) ? 'bad' : 'good',
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                image,
                time: DateFns.format(new Date(item.time), 'yyyy-MM-dd HH:mm:ss') || ""
            }
            //集团公司 独有数据
            if (companyType === '4') {
                Object.assign(result, {
                    linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
                });
            }
            return result;
        })
        return { total: evaluateRes.total, items: evaluateRes.items };
    }

    /**===================================
                处理图表数据
    ===================================**/

    //维度图表 方法映射
    getDimensionChartDataFunction(dimension: CustomerEvaluateDataReport.Dimension) {
        const dimensionChartDataFunction: Readonly<{ [K in CustomerEvaluateDataReport.Dimension]: (params: CustomerEvaluateDataReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [CustomerEvaluateDataReport.Dimension.ProjectDepartmentGroup]: this.getProjectDepartmentGroupChartData.bind(this), // 工地部门分布
            [CustomerEvaluateDataReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [CustomerEvaluateDataReport.Dimension.Trend]: this.getGroupTrendChartData.bind(this), // 趋势
            [CustomerEvaluateDataReport.Dimension.StageGroup]: this.getStageGroupChartData.bind(this), // 阶段分布
            [CustomerEvaluateDataReport.Dimension.ProjectManagerGroup]: this.getProjectManagerGroupChartData.bind(this), // 工地负责人分布
            [CustomerEvaluateDataReport.Dimension.GoodBadGroup]: this.getGoodBadGroupChartData.bind(this), // 评价类型分布
        };
        return dimensionChartDataFunction[dimension];
    }

    /**
     * 获取满足业主差评的idList
     * @param companyId 当前登录的公司
     * @param idList 需要过滤的评价ids
     * @param startTime 开始时间
     * @param endTime 结束时间
     * 时间范围左闭右开
     * @returns 
     */
    private async getOwnerNegativeReviewIds({ companyId, idList }: { companyId: string, idList: string[] }): Promise<string[]> {
        if (!companyId || _.isEmpty(idList)) return [];
        const evaluateConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Evaluate });
        const badEvaluationScope = evaluateConfig?.configs[0]?.badEvaluationScope as 'one_of' | 'all';
        const overallScore = evaluateConfig?.configs[0]?.overallScore;
        const individualScore = evaluateConfig?.configs[0]?.individualScore;
        //评分idList的评分
        const scoreRes = await this.projectDao.searchEvaluateListScore({ idList });
        const overallScoreIds = [];
        const individualScoreIds = [];
        scoreRes.map(res => {
            if (res.averageValue < overallScore) overallScoreIds.push(res.evaluateId);
            if (res.constructValue < individualScore || res.desighValue < individualScore || res.constValue < individualScore) individualScoreIds.push(res.evaluateId);
        });
        //取交集
        if (badEvaluationScope === 'all') return _.uniq(_.intersection(overallScoreIds, individualScoreIds));
        //取并集
        if (badEvaluationScope === 'one_of') return _.uniq(_.union(overallScoreIds, individualScoreIds));
        return [];
    }

    /**
     * 获取工地部门分布图表数据
     * 维度: 工地部门分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getProjectDepartmentGroupChartData(params: CustomerEvaluateDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType, departmentId } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || CustomerEvaluateDataReport.TopicName[topic];
        const name = CustomerEvaluateDataReport.DimensionName[CustomerEvaluateDataReport.Dimension.ProjectDepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        //筛选好评差评
        if (params.evaluateType) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        // 获取部门分布数据
        const departmentData = await this.projectDao.countEvaluateProjectDepartmentGroup(params);
        //处理图表结构: 工地部门分布
        const entity = await this.companyDecorationDao.search(params.companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.to, data: departmentData });
        // 创建图表
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'departmentId',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.Evaluate,
            dimension: CustomerEvaluateDataReport.Dimension.ProjectDepartmentGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取数值图表数据
     * 维度: 数值
     * 支持的图表结构: 数值
     * @param params 
     */
    private async getNumberChartData(params: CustomerEvaluateDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || CustomerEvaluateDataReport.TopicName[topic];
        const name = CustomerEvaluateDataReport.DimensionName[CustomerEvaluateDataReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        //筛选好评差评
        if (params.evaluateType) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        const res = await this.projectDao.countEvaluate(params);
        // 创建数值图表
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({
            chartType: Chart.ChartType.Total,
            title,
            name,
            optionKey: 'number',
            data: Number(res?.count || 0),
            actionType
        });
        return {
            subject: DateReport.Subject.Evaluate,
            dimension: CustomerEvaluateDataReport.Dimension.Number,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取趋势图表数据
     * 维度: 趋势
     * 支持的图表结构: 折线图|柱状图
     * @param params 
     */
    private async getGroupTrendChartData(params: CustomerEvaluateDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, from, to, dataType } = params;
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, value: number }[] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const data = [];//y轴数据
        promiseResult.forEach(res => {
            category.push(res?.category || "");
            data.push(res?.value || 0);
        });
        // 创建折线图/柱状图
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || CustomerEvaluateDataReport.TopicName[topic];
        const name = CustomerEvaluateDataReport.DimensionName[CustomerEvaluateDataReport.Dimension.Trend];
        const actionType = params?.actionType || Chart.ActionType.None;
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Line:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Line>({ chartType: Chart.ChartType.Line, title, name, category, data, actionType });
                break;
            case Chart.ChartType.Bar:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Bar>({ chartType: Chart.ChartType.Bar, title, name, category, data, actionType });
                break;
        }
        return {
            subject: DateReport.Subject.Evaluate,
            dimension: CustomerEvaluateDataReport.Dimension.Trend,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 查询周期数据
     * 用于: 分布趋势图表数据
     * @param params 
     * @returns 
     */
    private async getGroupTrendData(params: CustomerEvaluateDataReport.DimensionReq): Promise<{ category: string, value: number }> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return null;
        const { idList } = await this.getTopicIdList({ ...params, companyId, topic, from, to, dataType: "day", departmentId }) as { idList: string[] };
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        //筛选好评差评
        if (params.evaluateType) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        const res = await this.projectDao.countEvaluate({ ...params, idList });
        return { category, value: Number(res?.count || 0) };
    }

    /**
     * 获取阶段分布图表数据
     * 维度: 阶段分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getStageGroupChartData(params: CustomerEvaluateDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        //筛选好评差评
        if (params.evaluateType) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        // 获取阶段分布数据
        const stageData = await this.projectDao.countEvaluateProjectStageGroup(params);
        const data = stageData.map(item => ({
            key: `${item.stageLabel}`,
            name: item.stageLabel || '未知阶段',
            value: Number(item?.count || 0)
        }));
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || CustomerEvaluateDataReport.TopicName[topic];
        const name = CustomerEvaluateDataReport.DimensionName[CustomerEvaluateDataReport.Dimension.StageGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'stage',
            data,
            actionType
        });
        return {
            subject: DateReport.Subject.Evaluate,
            dimension: CustomerEvaluateDataReport.Dimension.StageGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取工地负责人分布图表数据
     * 维度: 工地负责人分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getProjectManagerGroupChartData(params: CustomerEvaluateDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        //筛选好评差评
        if (params.evaluateType) {
            const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList });
            if (params.evaluateType === 'good') params.idList = _.difference(params.idList, ownerNegativeIdList);
            if (params.evaluateType === 'bad') params.idList = ownerNegativeIdList;
        }
        // 获取工地负责人分布数据
        const managerData = await this.projectDao.countEvaluateProjectManagerGroup(params);
        const data = managerData.map(item => ({
            key: item.projectManagerId,
            name: item.projectManagerName || '未知负责人',
            value: Number(item?.count || 0)
        }));
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || CustomerEvaluateDataReport.TopicName[topic];
        const name = CustomerEvaluateDataReport.DimensionName[CustomerEvaluateDataReport.Dimension.ProjectManagerGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'projectManagerId',
            data,
            actionType
        });
        return {
            subject: DateReport.Subject.Evaluate,
            dimension: CustomerEvaluateDataReport.Dimension.ProjectManagerGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 获取评价类型分布图表数据
     * 维度: 评价类型分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getGoodBadGroupChartData(params: CustomerEvaluateDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { idList, topic, chartType } = params;
        const ownerNegativeIdList = await this.getOwnerNegativeReviewIds({ companyId: params.companyId, idList });
        // 获取评价类型分布数据
        let goodBadData = await this.projectDao.countEvaluateProjectGoodBadGroup({ ...params, ownerNegativeIdList });
        //筛选好评差评
        if (params?.evaluateType === 'good') goodBadData = goodBadData.filter(item => item.type === 'good');
        if (params?.evaluateType === 'bad') goodBadData = goodBadData.filter(item => item.type === 'bad');
        const data = goodBadData.map(item => ({
            key: item.type,
            name: item.name,
            value: Number(item?.count || 0)
        }));
        // 创建图表
        const gridColumn = params?.gridColumn || '1';
        const title = params?.title || CustomerEvaluateDataReport.TopicName[topic];
        const name = CustomerEvaluateDataReport.DimensionName[CustomerEvaluateDataReport.Dimension.GoodBadGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const chartData = await this.createDistributionChart({
            chartType,
            title,
            name,
            optionKey: 'evaluateType',
            data,
            actionType
        });

        return {
            subject: DateReport.Subject.Evaluate,
            dimension: CustomerEvaluateDataReport.Dimension.GoodBadGroup,
            topic,
            title,
            chartType,
            data: chartData,
            gridColumn
        };
    }

    /**
     * 创建分布图表
     */
    private async createDistributionChart<T extends Chart.ChartType>(params: {
        chartType: T,
        name: string,
        title: string,
        optionKey: string,
        data: { key: string, name: string, value: number }[],
        actionType: Chart.ActionType
    }): Promise<any> {
        const { chartType, title, name, optionKey, data, actionType } = params;

        switch (chartType) {
            case Chart.ChartType.Pie:
                return await this.chartFactory.createChart<Chart.ChartType.Pie>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.Rose:
                return await this.chartFactory.createChart<Chart.ChartType.Rose>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.Ring:
                return await this.chartFactory.createChart<Chart.ChartType.Ring>({
                    chartType, title, name, optionKey, data, actionType
                });
            case Chart.ChartType.BarDistribute:
                return await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({
                    chartType, title, name, optionKey, data, actionType
                });
            default:
                throw new Error(`Unsupported chartType: ${chartType}`);
        }
    }

}