import { Chart } from "../../../chart/chart.type";
import * as _ from "lodash";
import { DateReportInterface } from "./data-report.interface.dto";
import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace ProjectProblemDataReport {

    //主题
    export enum Topic {
        ProjectProblemTotal = 'project_problem_total',    // 当前问题总数
        ProjectProblemNew = 'project_problem_new',        // 新增问题
        NorectifiedProblemNew = 'norectified_problem_new',// 新增未整改
        NorectifiedProblemNow = 'norectified_problem_now',// 当前未整改
        NorectifiedOverdueNew = 'norectified_overdue_new',// 新增超时未整改
        NorectifiedOverdueNow = 'norectified_overdue_now',// 当前超时未整改
        RectifiedProblemNew = 'rectified_problem_new',    // 新增已整改
        RectifiedProblemNow = 'rectified_problem_now',    // 当前已整改
        OverdueRectifiedNew = 'overdue_rectified_new',  // 新增超时整改
        OverdueRectifiedNow = 'overdue_rectified_now',  // 当前超时整改
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.ProjectProblemTotal]: "当前问题总数",
        [Topic.ProjectProblemNew]: "新增问题",
        [Topic.NorectifiedProblemNew]: "新增未整改",
        [Topic.NorectifiedProblemNow]: "当前未整改",
        [Topic.NorectifiedOverdueNew]: "新增超时未整改",
        [Topic.NorectifiedOverdueNow]: "当前超时未整改",
        [Topic.RectifiedProblemNew]: "新增已整改",
        [Topic.RectifiedProblemNow]: "当前已整改",
        [Topic.OverdueRectifiedNew]: "新增超时整改",
        [Topic.OverdueRectifiedNow]: "当前超时整改",
    }

    //实时数据的主题
    export const RealTopic: Readonly<Topic[]> = [Topic.ProjectProblemTotal, Topic.NorectifiedProblemNow, Topic.NorectifiedOverdueNow, Topic.RectifiedProblemNow, Topic.OverdueRectifiedNow];

    //切片数据的主题
    export const EtlTopic: Readonly<Topic[]> = Object.values(Topic).filter((topic) => !RealTopic.includes(topic));

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.ProjectProblemTotal]: { name: TopicName[Topic.ProjectProblemTotal], value: Topic.ProjectProblemTotal },
        [Topic.NorectifiedProblemNow]: { name: TopicName[Topic.NorectifiedProblemNow], value: Topic.NorectifiedProblemNow },
        [Topic.NorectifiedOverdueNow]: { name: TopicName[Topic.NorectifiedOverdueNow], value: Topic.NorectifiedOverdueNow },
        [Topic.RectifiedProblemNow]: { name: TopicName[Topic.RectifiedProblemNow], value: Topic.RectifiedProblemNow },
        [Topic.OverdueRectifiedNow]: { name: TopicName[Topic.OverdueRectifiedNow], value: Topic.OverdueRectifiedNow },
        [Topic.ProjectProblemNew]: { name: TopicName[Topic.ProjectProblemNew], value: Topic.ProjectProblemNew },
        [Topic.NorectifiedProblemNew]: { name: TopicName[Topic.NorectifiedProblemNew], value: Topic.NorectifiedProblemNew },
        [Topic.NorectifiedOverdueNew]: { name: TopicName[Topic.NorectifiedOverdueNew], value: Topic.NorectifiedOverdueNew },
        [Topic.RectifiedProblemNew]: { name: TopicName[Topic.RectifiedProblemNew], value: Topic.RectifiedProblemNew },
        [Topic.OverdueRectifiedNew]: { name: TopicName[Topic.OverdueRectifiedNew], value: Topic.OverdueRectifiedNew },
    }

    //维度
    export enum Dimension {
        DepartmentGroup = 'department_group',//部门分布
        Number = 'number',//数值
        Trend = 'trend',//趋势
        ServiceVendorGroup = 'service_vendor_group',//服务厂商分布
        DecorationModelGroup = 'decoration_model_group',//装修方式分布
        ProblemSourceGroup = 'problem_source_group',//问题来源分布
        ProblemTypeGroup = 'problem_type_group',//问题分类分布
        ProblemStatusGroup = 'problem_status_group',//问题状态分布
        OverdueStatusGroup = 'overdue_status_group',//超时状态分布
        ProjectManagerGroup = 'project_manager_group',//工地负责人分布
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.DepartmentGroup]: "工地部门分布",
        [Dimension.Number]: "数值",
        [Dimension.Trend]: "趋势",
        [Dimension.ServiceVendorGroup]: "服务厂商分布",
        [Dimension.DecorationModelGroup]: "装修方式分布",
        [Dimension.ProblemStatusGroup]: "问题状态分布",
        [Dimension.ProblemSourceGroup]: "问题来源分布",
        [Dimension.ProblemTypeGroup]: "问题分类分布",
        [Dimension.OverdueStatusGroup]: "超时状态分布",
        [Dimension.ProjectManagerGroup]: "工地负责人分布",
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.DepartmentGroup]: { name: DimensionName[Dimension.DepartmentGroup], value: Dimension.DepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.Trend]: { name: DimensionName[Dimension.Trend], value: Dimension.Trend, gridColumn: '1' },
        [Dimension.ServiceVendorGroup]: { name: DimensionName[Dimension.ServiceVendorGroup], value: Dimension.ServiceVendorGroup, gridColumn: '1' },
        [Dimension.DecorationModelGroup]: { name: DimensionName[Dimension.DecorationModelGroup], value: Dimension.DecorationModelGroup, gridColumn: '1' },
        [Dimension.ProblemSourceGroup]: { name: DimensionName[Dimension.ProblemSourceGroup], value: Dimension.ProblemSourceGroup, gridColumn: '1' },
        [Dimension.ProblemTypeGroup]: { name: DimensionName[Dimension.ProblemTypeGroup], value: Dimension.ProblemTypeGroup, gridColumn: '1' },
        [Dimension.ProblemStatusGroup]: { name: DimensionName[Dimension.ProblemStatusGroup], value: Dimension.ProblemStatusGroup, gridColumn: '1' },
        [Dimension.OverdueStatusGroup]: { name: DimensionName[Dimension.OverdueStatusGroup], value: Dimension.OverdueStatusGroup, gridColumn: '1' },
        [Dimension.ProjectManagerGroup]: { name: DimensionName[Dimension.ProjectManagerGroup], value: Dimension.ProjectManagerGroup, gridColumn: '1' },
    }

    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.ProjectProblemTotal]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.ProjectProblemNew]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.NorectifiedProblemNew]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.NorectifiedOverdueNew]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.RectifiedProblemNew]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.OverdueRectifiedNew]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.Trend], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.NorectifiedProblemNow]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.NorectifiedOverdueNow]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.RectifiedProblemNow]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
        [Topic.OverdueRectifiedNow]: [Dimensions[Dimension.DepartmentGroup], Dimensions[Dimension.Number], Dimensions[Dimension.ServiceVendorGroup], Dimensions[Dimension.DecorationModelGroup], Dimensions[Dimension.ProblemStatusGroup], Dimensions[Dimension.ProblemSourceGroup], Dimensions[Dimension.ProblemTypeGroup], Dimensions[Dimension.OverdueStatusGroup], Dimensions[Dimension.ProjectManagerGroup]],
    };

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.ProjectProblemTotal]: Dimensions[Dimension.Number],
        [Topic.ProjectProblemNew]: Dimensions[Dimension.Trend],
        [Topic.NorectifiedProblemNew]: Dimensions[Dimension.Trend],
        [Topic.NorectifiedOverdueNew]: Dimensions[Dimension.Trend],
        [Topic.RectifiedProblemNew]: Dimensions[Dimension.Trend],
        [Topic.OverdueRectifiedNew]: Dimensions[Dimension.Trend],
        [Topic.NorectifiedProblemNow]: Dimensions[Dimension.DepartmentGroup],
        [Topic.NorectifiedOverdueNow]: Dimensions[Dimension.DepartmentGroup],
        [Topic.RectifiedProblemNow]: Dimensions[Dimension.DepartmentGroup],
        [Topic.OverdueRectifiedNow]: Dimensions[Dimension.DepartmentGroup],
    }

    //维度可筛选的图表类型
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.DepartmentGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.Number]: [Chart.ChartTypeOption.Total],
        [Dimension.Trend]: [Chart.ChartTypeOption.Line, Chart.ChartTypeOption.Bar],
        [Dimension.ServiceVendorGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.DecorationModelGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProblemSourceGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProblemTypeGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProblemStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.OverdueStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ProjectManagerGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
    }

    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: ProjectProblemDataReport.Topic;//主题
        dimension: Dimension;//维度
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string;//报告类型
        problemIdList?: string[];//工地问题ID列表
    }

    export class GetProjectIdsReq {
        idList: string[];//工地问题ID列表
        linkCompanyId?: string;
        projectAddress?: string;//工地地址
        deviceDepartmentId?: string;//服务厂商-设备部门id
        decorationModel?: string;//装修方式
        problemStatus?: string;//问题状态
        problemOverdueStatus?: string;//问题逾期状态
        problemType?: string;//问题类型
        problemSource?: string;//问题来源
        problemTimeFrom?: string;//问题发生时间开始
        problemTimeTo?: string;//问题发生时间结束
        problemDeadlineFrom?: string;//问题截止时间开始
        problemDeadlineTo?: string;//问题截止时间结束
        problemSolvedTimeFrom?: string;//问题解决时间开始
        problemSolvedTimeTo?: string;//问题解决时间结束
        overdueStatus?: string;//问题超时状态
        projectManagerId?: string;//工地负责人
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        idList: string[];
        topic: ProjectProblemDataReport.Topic;//主题
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string;//报告类型
        chartType: Chart.ChartType;
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: ProjectProblemDataReport.Topic;//主题
        idList: string[];//报告ID列表
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "工地归属",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        deviceDepartment: {
            filterName: "服务厂商",
            filterField: "deviceDepartmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        problemStatus: {
            filterName: "问题状态",
            filterField: "problemStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        problemOverdueStatus: {
            filterName: "超时状态",
            filterField: "problemOverdueStatus",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        problemType: {
            filterName: "问题分类",
            filterField: "problemType",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        problemSource: {
            filterName: "问题来源",
            filterField: "problemSource",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        projectManager: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        decorationModel: {
            filterName: "装修方式",
            filterField: "decorationModel",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        problemTime: {
            filterName: "发生时间",
            filterField: "problemTime",
            filterType: DataReportModuleFilter.FilterControlType.DATE_RANGE_PICKER
        },
        problemDeadline: {
            filterName: "截止时间",
            filterField: "problemDeadline",
            filterType: DataReportModuleFilter.FilterControlType.DATE_RANGE_PICKER
        },
        problemSolvedTime: {
            filterName: "整改时间",
            filterField: "problemSolvedTime",
            filterType: DataReportModuleFilter.FilterControlType.DATE_RANGE_PICKER
        }
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
        department?: any;
        problemStatus: DataReportModuleFilter.SelectFilterValue[];
        problemSource: DataReportModuleFilter.SelectFilterValue[];
        problemType: DataReportModuleFilter.SelectFilterValue[];
        projectManager: DataReportModuleFilter.SelectFilterValue[];
        problemOverdueStatus: DataReportModuleFilter.SelectFilterValue[];
        projectAddress: string;
        problemTime: DataReportModuleFilter.DateRangeFilterValue;
        problemDeadline: DataReportModuleFilter.DateRangeFilterValue;
        problemSolvedTime: DataReportModuleFilter.DateRangeFilterValue;
        deviceDepartment?: DataReportModuleFilter.SelectFilterValue[];
        decorationModel?: DataReportModuleFilter.SelectFilterValue[];
    }

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        idList: string[];
        decorationModel?: string;//装修方式
        problemStatus?: string;//问题状态
        problemOverdueStatus?: string;//问题逾期状态
        problemType?: string;//问题类型
        problemSource?: string;//问题来源
        problemTimeFrom?: string;//问题发生时间开始
        problemTimeTo?: string;//问题发生时间结束
        problemDeadlineFrom?: string;//问题截止时间开始
        problemDeadlineTo?: string;//问题截止时间结束
        problemSolvedTimeFrom?: string;//问题解决时间开始
        problemSolvedTimeTo?: string;//问题解决时间结束
    }

    export class AnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        topic: ProjectProblemDataReport.Topic;//主题
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string;//报告类型
        problemIdList?: string[];//工地问题ID列表
        idList: string[];//报告ID列表
    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: ProjectProblemDataReport.Topic;//主题
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string;//报告类型
        problemIdList?: string[];//工地问题ID列表
        idList: string[];//报告ID列表
        dimension: string;
    }

    export enum OverdueStatus {
        DeadlineUnsolved = 'deadlineUnsolved',
        DeadlineSolved = 'deadlineSolved',
        Solved = 'solved',
        NoDeadline = 'noDeadline'
    }

    export const OverdueStatusName: Readonly<Record<OverdueStatus, string>> = {
        [OverdueStatus.DeadlineUnsolved]: '超时未整改',
        [OverdueStatus.DeadlineSolved]: '超时已整改',
        [OverdueStatus.Solved]: '按时整改',
        [OverdueStatus.NoDeadline]: '未超时'
    };

    export const AnalysisListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "工地归属" },
        { key: "deviceDepartmentName", value: "服务厂商" },
        { key: "decorationModelName", value: "装修方式" },
        { key: "projectAddress", value: "工地地址" },
        { key: "statusName", value: "问题状态" },
        { key: "overdueName", value: "超时状态" },
        { key: "description", value: "问题描述" },
        { key: "problemImage[*].mediaResourceUri", value: "问题图片" },
        { key: "problemType", value: "问题分类" },
        { key: "problemSource", value: "问题来源" },
        { key: "reportPerson", value: "报告人" },
        { key: "problemTime", value: "发生时间" },
        { key: "solveDeadlineTime", value: "截止时间" },
        { key: "solvedTime", value: "整改时间" },
        { key: "solveDeadlineHours", value: "整改限时" },
        { key: "solvedHours", value: "整改时长" },
        { key: "solvedDescription", value: "整改描述" },
        { key: "solvedImage[*].mediaResourceUri", value: "整改图片" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "wrongdoerFaceName", value: "标记用户" },
    ];
}