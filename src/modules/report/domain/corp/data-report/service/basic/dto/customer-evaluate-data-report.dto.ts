import _ from "lodash";
import { Chart } from "../../../chart/chart.type";
import { DateReportInterface } from "./data-report.interface.dto";
import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace CustomerEvaluateDataReport {
    // 主题
    export enum Topic {
        EvaluateTotal = 'evaluate_total',          // 评价总数
        EvaluateNew = 'evaluate_new',              // 新增评价数
        EvaluateGoodNew = 'evaluate_good_new',     // 新增好评数
        EvaluateBadNew = 'evaluate_bad_new',       // 新增差评数
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.EvaluateTotal]: "评价总数",
        [Topic.EvaluateNew]: "新增评价数",
        [Topic.EvaluateGoodNew]: "新增好评数",
        [Topic.EvaluateBadNew]: "新增差评数",
    }

    //实时数据的主题
    export const RealTopic: Readonly<Topic[]> = [Topic.EvaluateTotal];

    //切片数据的主题
    export const EtlTopic: Readonly<Topic[]> = Object.values(Topic).filter((topic) => !RealTopic.includes(topic));

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.EvaluateTotal]: { name: TopicName[Topic.EvaluateTotal], value: Topic.EvaluateTotal },
        [Topic.EvaluateNew]: { name: TopicName[Topic.EvaluateNew], value: Topic.EvaluateNew },
        [Topic.EvaluateGoodNew]: { name: TopicName[Topic.EvaluateGoodNew], value: Topic.EvaluateGoodNew },
        [Topic.EvaluateBadNew]: { name: TopicName[Topic.EvaluateBadNew], value: Topic.EvaluateBadNew }
    }

    // 维度
    export enum Dimension {
        ProjectDepartmentGroup = 'project_department_group',  // 工地部门分布
        Number = 'number',                                    // 数值
        Trend = 'trend',                                     // 趋势
        StageGroup = 'stage_group',                          // 阶段分布
        ProjectManagerGroup = 'project_manager_group',        // 工地负责人分布
        GoodBadGroup = 'good_bad_group',                     // 评价类型分布
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.ProjectDepartmentGroup]: "工地部门分布",
        [Dimension.Number]: "数值",
        [Dimension.Trend]: "趋势",
        [Dimension.StageGroup]: "阶段分布",
        [Dimension.ProjectManagerGroup]: "工地负责人分布",
        [Dimension.GoodBadGroup]: "评价类型分布",
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.ProjectDepartmentGroup]: { name: DimensionName[Dimension.ProjectDepartmentGroup], value: Dimension.ProjectDepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.StageGroup]: { name: DimensionName[Dimension.StageGroup], value: Dimension.StageGroup, gridColumn: '1' },
        [Dimension.ProjectManagerGroup]: { name: DimensionName[Dimension.ProjectManagerGroup], value: Dimension.ProjectManagerGroup, gridColumn: '1' },
        [Dimension.GoodBadGroup]: { name: DimensionName[Dimension.GoodBadGroup], value: Dimension.GoodBadGroup, gridColumn: '1' },
        [Dimension.Trend]: { name: DimensionName[Dimension.Trend], value: Dimension.Trend, gridColumn: '1' }
    }

    // 主题对应的维度
    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.EvaluateTotal]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.GoodBadGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
        ],
        [Topic.EvaluateNew]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.GoodBadGroup],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
        ],
        [Topic.EvaluateGoodNew]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
        ],
        [Topic.EvaluateBadNew]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.StageGroup],
            Dimensions[Dimension.ProjectManagerGroup],
        ],
    }

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.EvaluateTotal]: Dimensions[Dimension.Number],
        [Topic.EvaluateNew]: Dimensions[Dimension.GoodBadGroup],
        [Topic.EvaluateGoodNew]: Dimensions[Dimension.Trend],
        [Topic.EvaluateBadNew]: Dimensions[Dimension.Trend],
    }

    export class EvaluateTypeReport {
        static readonly types = new Map<string, DataReportModuleFilter.SelectFilterValue>([
            ['1', { name: '好评', value: 'good' }],
            ['2', { name: '差评', value: 'bad' }],
        ])
        private static readonly enumKeys = Array.from(EvaluateTypeReport.types.keys());
        private static readonly enumValues = Array.from(EvaluateTypeReport.types.values());

        static getNameByCode(code: string): string | undefined {
            return this.types.get(String(code))?.name;
        }

        static getValueByCode(code: string): string | undefined {
            return this.types.get(String(code))?.value;
        }

        static getCodeByValue(value: string): string | undefined {
            const entry = _.find([...this.types.entries()], ([, option]) => option.value === value);
            return entry ? entry[0] : undefined;
        }

        static getGroups(): Record<string, DataReportModuleFilter.SelectFilterValue> {
            const groups: Record<string, DataReportModuleFilter.SelectFilterValue> = {};
            this.types.forEach((value) => { groups[value.value] = value; });
            return groups;
        }

        static getEnums(): string[] {
            return this.enumKeys;
        }

        static getOption(): DataReportModuleFilter.SelectFilterValue[] {
            return this.enumValues;
        }
    }
    // 维度可筛选的图表类型
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.ProjectDepartmentGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.Number]: [
            Chart.ChartTypeOption.Total
        ],
        [Dimension.Trend]: [
            Chart.ChartTypeOption.Line,
            Chart.ChartTypeOption.Bar
        ],
        [Dimension.StageGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.ProjectManagerGroup]: [
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.BarDistribute
        ],
        [Dimension.GoodBadGroup]: [
            Chart.ChartTypeOption.Ring,
            Chart.ChartTypeOption.Pie,
            Chart.ChartTypeOption.Rose,
            Chart.ChartTypeOption.BarDistribute
        ],
    }

    // 请求参数类
    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: CustomerEvaluateDataReport.Topic;
        dimension: Dimension;//维度
        linkCompanyId?: string;
        projectAddress?: string;
        projectManagerId?: string;
        score?: string;
        stage?: string;
        evaluateType?: string;
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        idList: string[];
        topic: CustomerEvaluateDataReport.Topic;
        linkCompanyId?: string;
        projectAddress?: string;
        projectManagerId?: string;
        score?: string;
        stage?: string;
        evaluateType?: string;
        chartType: Chart.ChartType;
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: CustomerEvaluateDataReport.Topic;
        idList: string[];
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        projectManager: DataReportModuleFilter.SelectFilterValue[];
        evaluateStage: DataReportModuleFilter.SelectFilterValue[];
        evaluateType: DataReportModuleFilter.SelectFilterValue[];
        score: DataReportModuleFilter.SelectFilterValue[];
        projectAddress: string;
        department?: any;
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "工地归属",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        projectManager: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        evaluateStage: {
            filterName: "评价阶段",
            filterField: "stage",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        evaluateType: {
            filterName: "评价类型",
            filterField: "evaluateType",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        score: {
            filterName: "评价星级",
            filterField: "score",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
    }

    export class GetProjectIdsReq {
        idList: string[];
        companyId: string;
        projectManagerId?: string;
        linkCompanyId?: string;
        projectAddress?: string;
        score?: string;
        stage?: string;
        evaluateType?: string;
    }

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        topic: CustomerEvaluateDataReport.Topic;
        idList: string[];
        companyId: string;
        projectManagerId?: string;
        linkCompanyId?: string;
        projectAddress?: string;
        score?: string;
        stage?: string;
        evaluateType?: string;
    }

    export class AnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        topic: CustomerEvaluateDataReport.Topic;
        companyId: string;
        projectManagerId?: string;
        linkCompanyId?: string;
        projectAddress?: string;
        score?: string;
        stage?: string;
        evaluateType?: string;
        idList: string[];
    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: CustomerEvaluateDataReport.Topic;
        companyId: string;
        projectManagerId?: string;
        linkCompanyId?: string;
        projectAddress?: string;
        score?: string;
        stage?: string;
        evaluateType?: string;
        idList: string[];
        dimension: Dimension;
    }

    export const AnalysisListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "工地归属" },
        { key: "projectAddress", value: "工地地址" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "stage", value: "评价阶段" },
        { key: "type", value: "评价类型", options: { values: [{ key: "good", value: "好评" }, { key: "bad", value: "差评" }] } },
        { key: "constructValue", value: "施工分" },
        { key: "desighValue", value: "设计分" },
        { key: "costValue", value: "服务分" },
        { key: "score", value: "综合评分" },
        { key: "description", value: "评价描述" },
        { key: "image[*].mediaResourceUri", value: "图片" },
        { key: "owner", value: "评价业主" },
        { key: "time", value: "评价时间" },
    ];
}