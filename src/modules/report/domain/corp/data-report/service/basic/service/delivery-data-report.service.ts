import { <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import { Injectable } from "@nestjs/common";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { DateReport } from "../../../dto/data-report.dto";
import * as _ from "lodash";
import { AcceptanceReportDataReportService } from "./acceptance-report-data-report.service";
import { AiDetectionDataReportService } from "./ai-detection-data-report.service";
import { ProjectDataReportService } from "./project-data-report.service";
import { DeviceDataReportService } from "./device-data-report.service";
import { NodeOverdueDataReportService } from "./node-overdue-data-report.service";
import { CustomerEvaluateDataReportService } from "./customer-evaluate-data-report.service";
import { ProjectProblemDataReportService } from "./project-problem-data-report.service";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { ProjectProblemDataReport } from "../dto/project-problem-data-report.dto";
import { Chart } from "../../../chart/chart.type";
import { ProjectDateReport } from "../dto/project-data-report.dto";
import { AiDetectionDataReport } from "../dto/ai-detection-data-report.dto";
import { DeviceDataReport } from "../dto/device-data-report.dto";
import { DataReportConfigService } from "../../data-report-config.service";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

/**
 * @summary 数据看板 - 个人仪表盘
 * <AUTHOR>
 * @class DeliveryDataReportService
 */
@Injectable()
export class DeliveryDataReportService extends CustomChart {

    public readonly logger = new MyLogger(DeliveryDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        readonly aiDetectionDataReportService: AiDetectionDataReportService,
        readonly projectDataReportService: ProjectDataReportService,
        readonly deviceDataReportService: DeviceDataReportService,
        readonly acceptanceReportDataReportService: AcceptanceReportDataReportService,
        readonly nodeOverdueDataReportService: NodeOverdueDataReportService,
        readonly customerEvaluateDataReportService: CustomerEvaluateDataReportService,
        readonly projectProblemDataReportService: ProjectProblemDataReportService,
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    getDimensionChartDataFunction(dimension: string): Function {
        return null;
    }

    async getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string; subject: DateReport.Subject; topic: string; addFilters: Sql.Column[]; }> {
        return null;
    }
    async getChartData(params: DateReportInterface.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        return null;
    }

    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return {};
    }

    async getAnalysisOption(params: DateReportInterface.AnalysisOptionReq): Promise<{ [key in string]: DataReportModuleFilter.SelectFilterValue[] | string; }> {
        return {};
    }
    async getAnalysisList(params: DateReportInterface.AnalysisReq): Promise<{ total: number; items: any[]; }> {
        return { total: 0, items: [] };
    }

    /**
     * 默认看板布局
     * @returns
     */
    getDefaultBoard(subject: DateReport.Subject, businessCategory: 'home_decor' | 'construction'): DateReportInterface.DefaultBoardRes {
        const DefaultBoard: Readonly<DateReportInterface.DefaultBoardRes> = {
            subject: DateReport.Subject.Delivery,
            blocks: [
                {
                    subject: DateReport.Subject.ProjectProblem,
                    gridColumn: "1",
                    topic: ProjectProblemDataReport.Topic.ProjectProblemTotal,
                    dimension: ProjectProblemDataReport.Dimension.Number,
                    title: "",
                    chartType: Chart.ChartType.Total,
                },
                {
                    subject: DateReport.Subject.ProjectProblem,
                    gridColumn: "1",
                    topic: ProjectProblemDataReport.Topic.ProjectProblemNew,
                    dimension: ProjectProblemDataReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line,
                },
                {
                    subject: DateReport.Subject.Project,
                    gridColumn: "1",
                    topic: ProjectDateReport.Topic.ProjectInWork,//在建工地数
                    dimension: ProjectDateReport.Dimension.Number,
                    title: "",
                    chartType: Chart.ChartType.Total
                },
                {
                    subject: DateReport.Subject.Project,
                    gridColumn: "1",
                    topic: ProjectDateReport.Topic.ProblemProject,//问题工地(实时)
                    dimension: ProjectDateReport.Dimension.ProjectDepartmentGroup,
                    title: "",
                    chartType: Chart.ChartType.Ring
                },
                // {
                //     subject: DateReport.Subject.Project,
                //     gridColumn: "1",
                //     topic: ProjectDateReport.Topic.ProjectCount,//工地数
                //     dimension: ProjectDateReport.Dimension.Number,
                //     title: "",
                //     chartType: Chart.ChartType.Total
                // },
                {
                    subject: DateReport.Subject.Project,
                    gridColumn: "1",
                    topic: ProjectDateReport.Topic.NewStartProject,//新开工工地
                    dimension: ProjectDateReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line
                },
                {
                    subject: DateReport.Subject.Project,
                    gridColumn: "1",
                    topic: ProjectDateReport.Topic.NewProblemProject,//新增问题工地
                    dimension: ProjectDateReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line
                },
                {
                    subject: DateReport.Subject.AiDetection,
                    gridColumn: "2",
                    topic: AiDetectionDataReport.Topic.TidinessDetection,//整洁度检测
                    dimension: AiDetectionDataReport.Dimension.GroupTrend,
                    title: "",
                    chartType: Chart.ChartType.MultiLine
                },
                {
                    subject: DateReport.Subject.AiDetection,
                    gridColumn: "2",
                    topic: AiDetectionDataReport.Topic.ActionDetection,//行为识别
                    dimension: AiDetectionDataReport.Dimension.GroupTrend,
                    title: "",
                    chartType: Chart.ChartType.MultiLine
                },
                // {
                //     subject: DateReport.Subject.AiDetection,
                //     gridColumn: "2",
                //     topic: AiDetectionDataReport.Topic.BodyDetection,//物体识别
                //     dimension: AiDetectionDataReport.Dimension.GroupTrend,
                //     title: AiDetectionDataReport.TopicName[AiDetectionDataReport.Topic.BodyDetection],
                //     chartType: Chart.ChartType.MultiLine
                // },
                {
                    subject: DateReport.Subject.Device,
                    gridColumn: "1",
                    topic: DeviceDataReport.Topic.DeviceCount,//设备数
                    dimension: DeviceDataReport.Dimension.Number,
                    title: "",
                    chartType: Chart.ChartType.Total
                },
                {
                    subject: DateReport.Subject.Device,
                    gridColumn: "1",
                    topic: DeviceDataReport.Topic.InternalIdleDevice,//库内闲置设备
                    dimension: DeviceDataReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line
                },
                {
                    subject: DateReport.Subject.Device,
                    gridColumn: "1",
                    topic: DeviceDataReport.Topic.ExternalIdleDevice,//库外闲置设备
                    dimension: DeviceDataReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line
                },
                {
                    subject: DateReport.Subject.Device,
                    gridColumn: "1",
                    topic: DeviceDataReport.Topic.ReviceDevice,//已领用设备
                    dimension: DeviceDataReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line
                },
                {
                    subject: DateReport.Subject.Device,
                    gridColumn: "1",
                    topic: DeviceDataReport.Topic.InstallDevice,//已安装设备
                    dimension: DeviceDataReport.Dimension.Trend,
                    title: "",
                    chartType: Chart.ChartType.Line
                },
            ]
        }
        return DefaultBoard;
    }

    /**
     * 数据点筛选项
     * @returns
     */
    async getSubjectOption(companyId: string, subject: DateReport.Subject, businessCategory: 'home_decor' | 'construction'): Promise<DateReportInterface.SubjectOptionRes[]> {
        const result = [];
        const [project] = await super.getSubjectOption(companyId, DateReport.Subject.Project, businessCategory);
        const [device] = await super.getSubjectOption(companyId, DateReport.Subject.Device, businessCategory);
        const [projectProblem] = await super.getSubjectOption(companyId, DateReport.Subject.ProjectProblem, businessCategory);
        const [aiDetection] = await super.getSubjectOption(companyId, DateReport.Subject.AiDetection, businessCategory);
        const [acceptance] = await super.getSubjectOption(companyId, DateReport.Subject.AcceptanceReport, businessCategory);
        const [node] = await super.getSubjectOption(companyId, DateReport.Subject.NodeOverdue, businessCategory);
        result.push(...[project, device, projectProblem, aiDetection, acceptance, node].filter(item => !!item));
        if (businessCategory === 'construction') return result;
        const [evaluate] = await super.getSubjectOption(companyId, DateReport.Subject.Evaluate, businessCategory);
        if (evaluate) result.push(evaluate);
        return result;
    }

}