import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { Chart } from "../../../chart/chart.type";
import { DateReportInterface } from "./data-report.interface.dto";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace DeviceDataReport {

    export enum Topic {
        DeviceTotal = 'device_total',//当前设备总数(实时)
        CurrentInternalIdleDevice = 'current_internal_idle_device',//当前库内闲置设备(实时)
        CurrentExternalIdleDevice = 'current_external_idle_device',//当前库外闲置设备(实时)
        CurrentReviceDevice = 'current_revice_device',//当前已领用设备(实时)
        CurrentInstallDevice = 'current_install_device',//当前已安装设备(实时)
        CurrentOnlineDevice = 'current_online_device',//当前在线设备(实时)
        CurrentOfflineDevice = 'current_offline_device',//当前离线设备(实时)
        CurrentOfflineDeviceByDay = 'current_offline_device_by_day',//当前离线>=N天设备(实时)
        InstallErrorDevice = 'install_error_device',//当前安装异常设备(实时)
        ExpiredDevice = 'expired_device',//当前过期设备(实时)
        SoonExpiredDevice = 'soon_expired_device',//即将过期设备(实时)
        DeviceCount = 'device_count',//设备数
        InternalIdleDevice = 'internal_idle_device',//库内闲置设备
        ExternalIdleDevice = 'external_idle_device',//库外闲置设备
        ReviceDevice = 'revice_device',//已领用设备
        InstallDevice = 'install_device',//已安装设备
        OfflineDevice = 'offline_device',//异常离线设备
        OnlineDevice = 'online_device',//正常在线设备
        OfflineDeviceByDay = 'offline_device_by_day',//离线>=N天设备
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.DeviceTotal]: "当前设备总数",
        [Topic.CurrentInternalIdleDevice]: "当前库内闲置设备",
        [Topic.CurrentExternalIdleDevice]: "当前库外闲置设备",
        [Topic.CurrentReviceDevice]: "当前已领用设备",
        [Topic.CurrentInstallDevice]: "当前已安装设备",
        [Topic.CurrentOnlineDevice]: "当前在线设备",
        [Topic.CurrentOfflineDevice]: "当前离线设备",
        [Topic.CurrentOfflineDeviceByDay]: "当前离线>=N天设备",
        [Topic.InstallErrorDevice]: "当前安装异常设备",
        [Topic.ExpiredDevice]: "当前过期设备",
        [Topic.SoonExpiredDevice]: "即将过期设备",
        [Topic.DeviceCount]: "设备数",
        [Topic.InternalIdleDevice]: "库内闲置设备",
        [Topic.ExternalIdleDevice]: "库外闲置设备",
        [Topic.ReviceDevice]: "已领用设备",
        [Topic.InstallDevice]: "已安装设备",
        [Topic.OfflineDevice]: "异常离线设备",
        [Topic.OnlineDevice]: "正常在线设备",
        [Topic.OfflineDeviceByDay]: "离线>=N天设备"
    }

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.DeviceTotal]: { name: TopicName[Topic.DeviceTotal], value: Topic.DeviceTotal },
        [Topic.CurrentInternalIdleDevice]: { name: TopicName[Topic.CurrentInternalIdleDevice], value: Topic.CurrentInternalIdleDevice },
        [Topic.CurrentExternalIdleDevice]: { name: TopicName[Topic.CurrentExternalIdleDevice], value: Topic.CurrentExternalIdleDevice },
        [Topic.CurrentReviceDevice]: { name: TopicName[Topic.CurrentReviceDevice], value: Topic.CurrentReviceDevice },
        [Topic.CurrentInstallDevice]: { name: TopicName[Topic.CurrentInstallDevice], value: Topic.CurrentInstallDevice },
        [Topic.CurrentOnlineDevice]: { name: TopicName[Topic.CurrentOnlineDevice], value: Topic.CurrentOnlineDevice },
        [Topic.CurrentOfflineDevice]: { name: TopicName[Topic.CurrentOfflineDevice], value: Topic.CurrentOfflineDevice },
        [Topic.CurrentOfflineDeviceByDay]: { name: TopicName[Topic.CurrentOfflineDeviceByDay], value: Topic.CurrentOfflineDeviceByDay },
        [Topic.InstallErrorDevice]: { name: TopicName[Topic.InstallErrorDevice], value: Topic.InstallErrorDevice },
        [Topic.ExpiredDevice]: { name: TopicName[Topic.ExpiredDevice], value: Topic.ExpiredDevice },
        [Topic.SoonExpiredDevice]: { name: TopicName[Topic.SoonExpiredDevice], value: Topic.SoonExpiredDevice },
        [Topic.DeviceCount]: { name: TopicName[Topic.DeviceCount], value: Topic.DeviceCount },
        [Topic.InternalIdleDevice]: { name: TopicName[Topic.InternalIdleDevice], value: Topic.InternalIdleDevice },
        [Topic.ExternalIdleDevice]: { name: TopicName[Topic.ExternalIdleDevice], value: Topic.ExternalIdleDevice },
        [Topic.ReviceDevice]: { name: TopicName[Topic.ReviceDevice], value: Topic.ReviceDevice },
        [Topic.InstallDevice]: { name: TopicName[Topic.InstallDevice], value: Topic.InstallDevice },
        [Topic.OfflineDevice]: { name: TopicName[Topic.OfflineDevice], value: Topic.OfflineDevice },
        [Topic.OnlineDevice]: { name: TopicName[Topic.OnlineDevice], value: Topic.OnlineDevice },
        [Topic.OfflineDeviceByDay]: { name: TopicName[Topic.OfflineDeviceByDay], value: Topic.OfflineDeviceByDay }
    }

    //可以定义逻辑的主题
    export const ActionTypeTopic: Readonly<Topic[]> = [Topic.SoonExpiredDevice, Topic.InternalIdleDevice, Topic.ExternalIdleDevice, Topic.OfflineDevice, Topic.OfflineDeviceByDay, Topic.CurrentOfflineDeviceByDay];

    //实时数据的主题
    export const RealTopic: Readonly<Topic[]> = [Topic.DeviceTotal, Topic.ExpiredDevice, Topic.SoonExpiredDevice, Topic.InstallErrorDevice, Topic.CurrentInternalIdleDevice, Topic.CurrentExternalIdleDevice, Topic.CurrentReviceDevice, Topic.CurrentInstallDevice, Topic.CurrentOnlineDevice, Topic.CurrentOfflineDevice, Topic.CurrentOfflineDeviceByDay];

    //切片数据的主题
    export const EtlTopic: Readonly<Topic[]> = Object.values(Topic).filter((topic) => !RealTopic.includes(topic));

    //维度
    export enum Dimension {
        DeviceDepartmentGroup = 'device_department_group',//设备部门分布
        Number = 'number',//数值
        ReviceStatusGroup = 'revice_status_group',//领用状态分布
        ReviceManagerGroup = 'revice_manager_group',//领用人分布
        BindStatusGroup = 'bind_status_group',//绑定状态分布
        OnlineStatusGroup = 'online_status_group',//在线状态分布
        OfflineStatusGroup = 'offline_status_group',//异常离线状态分布
        DeviceStatusGroup = 'device_status_group',//设备状态分布
        Trend = 'trend'//趋势
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.DeviceDepartmentGroup]: "设备部门分布",
        [Dimension.Number]: "数值",
        [Dimension.ReviceStatusGroup]: "领用状态分布",
        [Dimension.ReviceManagerGroup]: "领用人分布",
        [Dimension.BindStatusGroup]: "绑定状态分布",
        [Dimension.OnlineStatusGroup]: "在线状态分布",
        [Dimension.OfflineStatusGroup]: "异常离线状态分布",
        [Dimension.DeviceStatusGroup]: "设备状态分布",
        [Dimension.Trend]: "趋势"
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.DeviceDepartmentGroup]: { name: DimensionName[Dimension.DeviceDepartmentGroup], value: Dimension.DeviceDepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.ReviceStatusGroup]: { name: DimensionName[Dimension.ReviceStatusGroup], value: Dimension.ReviceStatusGroup, gridColumn: '1' },
        [Dimension.ReviceManagerGroup]: { name: DimensionName[Dimension.ReviceManagerGroup], value: Dimension.ReviceManagerGroup, gridColumn: '1' },
        [Dimension.BindStatusGroup]: { name: DimensionName[Dimension.BindStatusGroup], value: Dimension.BindStatusGroup, gridColumn: '1' },
        [Dimension.OnlineStatusGroup]: { name: DimensionName[Dimension.OnlineStatusGroup], value: Dimension.OnlineStatusGroup, gridColumn: '1' },
        [Dimension.OfflineStatusGroup]: { name: DimensionName[Dimension.OfflineStatusGroup], value: Dimension.OfflineStatusGroup, gridColumn: '1' },
        [Dimension.DeviceStatusGroup]: { name: DimensionName[Dimension.DeviceStatusGroup], value: Dimension.DeviceStatusGroup, gridColumn: '1' },
        [Dimension.Trend]: { name: DimensionName[Dimension.Trend], value: Dimension.Trend, gridColumn: '1' }
    }

    //维度可筛选的图表类型(第一个为默认图表类型)
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.DeviceDepartmentGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.Number]: [Chart.ChartTypeOption.Total],
        [Dimension.ReviceStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ReviceManagerGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.BindStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.OnlineStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.OfflineStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.DeviceStatusGroup]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
        [Dimension.Trend]: [Chart.ChartTypeOption.Line, Chart.ChartTypeOption.Bar],
    }

    //主题可筛选的维度(顺序为分析维度顺序展示，维度不可重复选择)
    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.DeviceTotal]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup],
            Dimensions[Dimension.DeviceStatusGroup],
        ],
        [Topic.CurrentInternalIdleDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.CurrentExternalIdleDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.CurrentReviceDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.CurrentInstallDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.CurrentOnlineDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.CurrentOfflineDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.CurrentOfflineDeviceByDay]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.ExpiredDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.SoonExpiredDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.InstallErrorDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OnlineStatusGroup]
        ],
        [Topic.DeviceCount]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceStatusGroup],
            Dimensions[Dimension.BindStatusGroup],
            Dimensions[Dimension.OfflineStatusGroup]

        ],
        [Topic.InternalIdleDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend]

        ],
        [Topic.ExternalIdleDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceManagerGroup]

        ],
        [Topic.ReviceDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceManagerGroup]

        ],
        [Topic.InstallDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceManagerGroup]

        ],
        [Topic.OfflineDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceManagerGroup]

        ],
        [Topic.OnlineDevice]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceManagerGroup]

        ],
        [Topic.OfflineDeviceByDay]: [
            Dimensions[Dimension.DeviceDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.Trend],
            Dimensions[Dimension.ReviceManagerGroup]
        ],
    }

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.DeviceTotal]: Dimensions[Dimension.Number],
        [Topic.CurrentInternalIdleDevice]: Dimensions[Dimension.Number],
        [Topic.CurrentExternalIdleDevice]: Dimensions[Dimension.Number],
        [Topic.CurrentReviceDevice]: Dimensions[Dimension.Number],
        [Topic.CurrentInstallDevice]: Dimensions[Dimension.Number],
        [Topic.CurrentOnlineDevice]: Dimensions[Dimension.Number],
        [Topic.CurrentOfflineDevice]: Dimensions[Dimension.Number],
        [Topic.CurrentOfflineDeviceByDay]: Dimensions[Dimension.Number],
        [Topic.ExpiredDevice]: Dimensions[Dimension.Number],
        [Topic.SoonExpiredDevice]: Dimensions[Dimension.Number],
        [Topic.InstallErrorDevice]: Dimensions[Dimension.Number],
        [Topic.DeviceCount]: Dimensions[Dimension.Number],
        [Topic.InternalIdleDevice]: Dimensions[Dimension.Trend],
        [Topic.ExternalIdleDevice]: Dimensions[Dimension.Trend],
        [Topic.ReviceDevice]: Dimensions[Dimension.Trend],
        [Topic.InstallDevice]: Dimensions[Dimension.Trend],
        [Topic.OfflineDevice]: Dimensions[Dimension.Trend],
        [Topic.OnlineDevice]: Dimensions[Dimension.Trend],
        [Topic.OfflineDeviceByDay]: Dimensions[Dimension.Trend],
    }

    export class CommonFilterReq {
        companyId: string;
        topic: Topic;
        deviceIdList: string[];
        companyIdList: string[];
        //周期范围
        startTime: string;
        endTime: string;
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: Topic;//主题
        dimension: Dimension;//维度
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline: IsOffline;//异常离线
        isOnline: IsOnline;//在线状态
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: Topic;//主题
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
        department?: any;
        reviceManager: DataReportModuleFilter.SelectFilterValue[];
        projectManager: DataReportModuleFilter.SelectFilterValue[];
        deviceSerial: string;
        projectAddress: string;
        isOnline?: DataReportModuleFilter.SelectFilterValue[];
        isOffline?: DataReportModuleFilter.SelectFilterValue[];
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "所属部门",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        deviceSerial: {
            filterName: "设备序列号",
            filterField: "deviceSerial",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        reviceManager: {
            filterName: "领用人",
            filterField: "reviceManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        isOnline: {
            filterName: "在线状态",
            filterField: "isOnline",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        isOffline: {
            filterName: "异常离线",
            filterField: "isOffline",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectManager: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
    }

    export enum IsOffline {
        Offline = 'Y',//异常离线
        UnOffline = 'N',//未异常离线
    }

    export const IsOfflineName: Readonly<{ [key in IsOffline]: string }> = {
        [IsOffline.Offline]: "是",
        [IsOffline.UnOffline]: "否"
    }


    export enum IsOnline {
        Online = 'Y',//在线
        Offline = 'N',//离线
    }

    export const IsOnlineName: Readonly<{ [key in IsOnline]: string }> = {
        [IsOnline.Online]: "在线",
        [IsOnline.Offline]: "离线"
    }

    export enum IsRevice {
        Revice = 'Y',//已领用
        UnRevice = 'N',//未领用
    }

    export const IsReviceName: Readonly<{ [key in IsRevice]: string }> = {
        [IsRevice.Revice]: "已领用",
        [IsRevice.UnRevice]: "未领用"
    }


    export enum IsBind {
        Bind = 'Y',//已绑定
        UnBind = 'N',//未绑定
    }

    export const IsBindName: Readonly<{ [key in IsBind]: string }> = {
        [IsBind.Bind]: "已绑定",
        [IsBind.UnBind]: "未绑定"
    }

    export enum Status {
        UnActive = '0',//未激活
        Transit = '1',//在途
        Normal = '2',//正常
        Expired = '3',//到期
        Repair = '4',//维修
        Damage = '5',//损坏
        Lost = '6',//遗失
    }

    export const StatusName: Readonly<{ [key in Status]: string }> = {
        [Status.UnActive]: "未激活",
        [Status.Transit]: "在途",
        [Status.Normal]: "正常",
        [Status.Expired]: "到期",
        [Status.Repair]: "维修",
        [Status.Damage]: "损坏",
        [Status.Lost]: "遗失"
    }

    export class GetTopicIdListReq extends DateReportInterface.GetTopicIdListReq {
        topic: Topic;
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        topic: Topic;//主题
        deviceIdList: string[];
        companyIdList: string[];
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export class AnalysisReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        deviceIdList: string[];
        companyIdList: string[];
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export class AnalysisDistributeReq extends DateReportInterface.AnalysisDistributeReq {
        topic: Topic;//主题
        deviceIdList: string[];
        companyIdList: string[];
        startTime: string;
        endTime: string;
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        deviceIdList: string[];
        companyIdList: string[];
        startTime: string;
        endTime: string;
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        dimension: Dimension;//维度
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        topic: Topic;//主题
        deviceIdList: string[];
        companyIdList: string[];
        startTime: string;
        endTime: string;
        linkCompanyId?: string;//所属公司
        deviceSerial?: string;//设备序列号
        reviceManagerId?: string;//领用人
        projectAddress?: string;//工地地址（绑定工地）
        projectManagerId?: string;//工地负责人
        isOffline?: IsOffline;//异常离线
        isOnline?: IsOnline;//在线状态
    }

    export const AnalysisRealListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "所属部门" },
        { key: "deviceSerial", value: "序列号" },
        { key: "reviceManagerName", value: "领用人" },
        { key: "reviceDate", value: "领用时间" },
        { key: "projectAddress", value: "绑定工地" },
        { key: "bindingDate", value: "绑定时间" },
        { key: "isOnline", value: "在线状态", options: { values: [{ key: IsOnline.Online, value: IsOnlineName[IsOnline.Online] }, { key: IsOnline.Offline, value: IsOnlineName[IsOnline.Offline] }] } },
        { key: "offLineTime", value: "离线时长" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "startDate", value: "开始时间" },
        { key: "endDate", value: "到期时间" },
    ];

    export const AnalysisEtlListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "所属部门" },
        { key: "deviceSerial", value: "序列号" },
        { key: "reviceManagerName", value: "领用人" },
        { key: "reviceDate", value: "领用时间" },
        { key: "projectAddress", value: "绑定工地" },
        { key: "bindingDate", value: "绑定时间" },
        { key: "isOffline", value: "是否异常离线", options: { values: [{ key: IsOffline.Offline, value: IsOfflineName[IsOffline.Offline] }, { key: IsOffline.UnOffline, value: IsOfflineName[IsOffline.UnOffline] }] } },
        { key: "offLineTime", value: "异常离线时长" },
        { key: "projectManagerName", value: "工地负责人" },
        { key: "bindingTime", value: "绑定时长" },
        { key: "onlineTime", value: "绑定后在线总时长" },
        { key: "internalIdleTime", value: "库内闲置时长" },
        { key: "externalIdleTime", value: "库外闲置时长" },
    ];

}