import { Chart } from "../../../chart/chart.type";
import * as _ from "lodash";
import { DateReportInterface } from "./data-report.interface.dto";
import { UtilType } from "@src/modules/report/domain/bgw/types/util.type";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";

export namespace AiDetectionDataReport {

    //主题
    export enum Topic {
        TidinessDetection = 'tidiness_detection',//工地整洁度
        ActionDetection = 'action_detection',//行为识别
        BodyDetection = 'body_detection',//物体识别
    }

    export const TopicName: Readonly<{ [key in Topic]: string }> = {
        [Topic.TidinessDetection]: "工地整洁度",
        [Topic.ActionDetection]: "行为识别",
        [Topic.BodyDetection]: "物体识别",
    }

    export const Topics: Readonly<{ [key in Topic]: { name: string, value: Topic } }> = {
        [Topic.TidinessDetection]: { name: TopicName[Topic.TidinessDetection], value: Topic.TidinessDetection },
        [Topic.ActionDetection]: { name: TopicName[Topic.ActionDetection], value: Topic.ActionDetection },
        [Topic.BodyDetection]: { name: TopicName[Topic.BodyDetection], value: Topic.BodyDetection }
    }

    //维度
    export enum Dimension {
        ProjectDepartmentGroup = 'project_department_group',//工地部门分布
        ServiceVendorGroup = 'service_vendor_group',//服务厂商分布
        Number = 'number',//数值
        Group = 'group',//检测类型分布
        GroupTrend = 'group_trend'//检测类型分布趋势
    }

    export const DimensionName: Readonly<{ [key in Dimension]: string }> = {
        [Dimension.ProjectDepartmentGroup]: "工地部门分布",
        [Dimension.ServiceVendorGroup]: "服务厂商分布",
        [Dimension.Number]: "数值",
        [Dimension.Group]: "分布",
        [Dimension.GroupTrend]: "分布趋势"
    }

    export const Dimensions: Readonly<{ [key in Dimension]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Dimension.ProjectDepartmentGroup]: { name: DimensionName[Dimension.ProjectDepartmentGroup], value: Dimension.ProjectDepartmentGroup, gridColumn: '1' },
        [Dimension.Number]: { name: DimensionName[Dimension.Number], value: Dimension.Number, gridColumn: '1' },
        [Dimension.ServiceVendorGroup]: { name: DimensionName[Dimension.ServiceVendorGroup], value: Dimension.ServiceVendorGroup, gridColumn: '1' },
        [Dimension.Group]: { name: DimensionName[Dimension.Group], value: Dimension.Group, gridColumn: '1' },
        [Dimension.GroupTrend]: { name: DimensionName[Dimension.GroupTrend], value: Dimension.GroupTrend, gridColumn: '1' },
    }

    //维度可筛选的图表类型
    export const DimensionCharts: Readonly<{ [key in Dimension]: { name: string, value: Chart.ChartType }[] }> = {
        [Dimension.ProjectDepartmentGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.ServiceVendorGroup]: [Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.BarDistribute],
        [Dimension.Number]: [Chart.ChartTypeOption.Total],
        [Dimension.GroupTrend]: [Chart.ChartTypeOption.MultiLine],
        [Dimension.Group]: [Chart.ChartTypeOption.Ring, Chart.ChartTypeOption.Pie, Chart.ChartTypeOption.Rose, Chart.ChartTypeOption.BarDistribute],
    }

    //主题默认维度
    export const TopicDefaultDimension: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string } }> = {
        [Topic.TidinessDetection]: { ...Dimensions[Dimension.GroupTrend], gridColumn: '2' },
        [Topic.ActionDetection]: { ...Dimensions[Dimension.GroupTrend], gridColumn: '2' },
        [Topic.BodyDetection]: { ...Dimensions[Dimension.GroupTrend], gridColumn: '2' },
    }

    //主题可筛选的维度
    export const TopicDimensions: Readonly<{ [key in Topic]: { name: string, value: Dimension, gridColumn: string }[] }> = {
        [Topic.TidinessDetection]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.GroupTrend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.Group]
        ],
        [Topic.ActionDetection]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.GroupTrend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.Group]
        ],
        [Topic.BodyDetection]: [
            Dimensions[Dimension.ProjectDepartmentGroup],
            Dimensions[Dimension.Number],
            Dimensions[Dimension.GroupTrend],
            Dimensions[Dimension.ServiceVendorGroup],
            Dimensions[Dimension.Group]
        ],
    }

    export class TidinessReportType {
        static readonly types = new Map<string, DataReportModuleFilter.SelectFilterValue>([
            ['1', { name: '优', value: 'good' }],
            ['2', { name: '良', value: 'middle' }],
            ['3', { name: '差', value: 'bad' }],
            ['4', { name: '未知', value: 'unknown' }],
        ]);
        private static readonly enumKeys = Array.from(this.types.keys());
        private static readonly enumValues = Array.from(this.types.values());
        //根据编码获取对应的名称
        static getNameByCode(code: string): string | undefined {
            return this.types.get(String(code))?.name;
        }
        //根据编码获取对应的值
        static getValueByCode(code: string): string | undefined {
            return this.types.get(String(code))?.value;
        }
        // 根据值获取对应的编码
        static getCodeByValue(value: string): string | undefined {
            const entry = _.find([...this.types.entries()], ([, option]) => option.value === value);
            return entry ? entry[0] : undefined; // 返回匹配的 code 或 undefined
        }
        //获取所有分组数据
        static getGroups(): Record<string, DataReportModuleFilter.SelectFilterValue> {
            const groups: Record<string, DataReportModuleFilter.SelectFilterValue> = {};
            this.types.forEach((value) => { groups[value.value] = value; });
            return groups;
        }
        //获取所有枚举类型数组
        static getEnums(): string[] {
            return this.enumKeys;
        }
        //获取所有枚举类型数组
        static getOption(): DataReportModuleFilter.SelectFilterValue[] {
            return this.enumValues;
        }
    }

    export class BodyReportType {
        static readonly types = new Map<string, DataReportModuleFilter.SelectFilterValue>([
            ['7', { name: '停放电瓶车', value: 'electro_mobile' }],
            ['10', { name: '角磨机', value: 'angle_grinder' }],
            ['11', { name: '切割机', value: 'cutting_machine' }],
            ['12', { name: '灭火器', value: 'fire_extinguisher' }],
            ['13', { name: '临时用电', value: 'temporary_electricity' }],
            ['1', { name: '明火', value: 'flame' }],
            ['2', { name: '浓烟', value: 'thick_smoke' }],
            ['15', { name: '脚手架未装防护网', value: 'safety_mesh' }],
            ['16', { name: '临边洞口无防护', value: 'safety_hole' }],
            ['17', { name: '楼梯无防护', value: 'safety_stairs' }],
        ]);
        static readonly enumKeys = Array.from(this.types.keys());
        static readonly enumValues = Array.from(this.types.values());
        //根据编码获取对应的名称
        static getNameByCode(code: string): string | undefined {
            return this.types.get(String(code))?.name;
        }
        //根据编码获取对应的值
        static getValueByCode(code: string): string | undefined {
            return this.types.get(String(code))?.value;
        }
        // 根据值获取对应的编码
        static getCodeByValue(value: string): string | undefined {
            const entry = _.find([...this.types.entries()], ([, option]) => option.value === value);
            return entry ? entry[0] : undefined; // 返回匹配的 code 或 undefined
        }
        //获取所有分组数据
        static getGroups(): Record<string, DataReportModuleFilter.SelectFilterValue> {
            const groups: Record<string, DataReportModuleFilter.SelectFilterValue> = {};
            this.types.forEach((value) => { groups[value.value] = value; });
            return groups;
        }
        //获取所有枚举类型数组
        static getEnums(): string[] {
            return this.enumKeys;
        }
        //获取所有枚举类型数组
        static getOption(): DataReportModuleFilter.SelectFilterValue[] {
            return this.enumValues;
        }
    }

    export class ActionReportType {
        static readonly types = new Map<string, DataReportModuleFilter.SelectFilterValue>([
            ['0', { name: '抽烟', value: 'smoke' }],
            ['4', { name: '未穿工服', value: 'not_work_clothes' }],
            ['5', { name: '穿拖鞋', value: 'slippers' }],
            ['9', { name: '登高', value: 'climb_work' }],
            ['8', { name: '安全帽', value: 'safety_helmet' }],
            ['14', { name: '动火', value: 'active_fire' }],
            ['6', { name: '非工作人员', value: 'non_worker' }]
        ]);
        static readonly enumKeys = Array.from(this.types.keys());
        static readonly enumValues = Array.from(this.types.values());
        //根据编码获取对应的名称
        static getNameByCode(code: string): string | undefined {
            return this.types.get(String(code))?.name;
        }
        //根据编码获取对应的值
        static getValueByCode(code: string): string | undefined {
            return this.types.get(String(code))?.value;
        }
        // 根据值获取对应的编码
        static getCodeByValue(value: string): string | undefined {
            const entry = _.find([...this.types.entries()], ([, option]) => option.value === value);
            return entry ? entry[0] : undefined; // 返回匹配的 code 或 undefined
        }
        //获取所有分组数据
        static getGroups(): Record<string, DataReportModuleFilter.SelectFilterValue> {
            const groups: Record<string, DataReportModuleFilter.SelectFilterValue> = {};
            this.types.forEach((value) => { groups[value.value] = value; });
            return groups;
        }
        //获取所有枚举类型数组
        static getEnums(): string[] {
            return this.enumKeys;
        }
        //获取所有枚举类型数组
        static getOption(): DataReportModuleFilter.SelectFilterValue[] {
            return this.enumValues;
        }
    }

    export class AnalysisOptionReq extends DateReportInterface.AnalysisOptionReq {
        topic: Topic;//主题
    }

    export class AnalysisOptionRes extends DateReportInterface.AnalysisOptionRes {
        projectManager: DataReportModuleFilter.SelectFilterValue[];
        reportType: DataReportModuleFilter.SelectFilterValue[];
        projectAddress: string;
        department?: any;
        linkCompany?: DataReportModuleFilter.SelectFilterValue[];
        deviceDepartment?: DataReportModuleFilter.SelectFilterValue[];
    }

    export const FilterMap: Readonly<{ [key in keyof AnalysisOptionRes]?: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } }> = {
        linkCompany: {
            filterName: "所属公司",
            filterField: "linkCompanyId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        department: {
            filterName: "工地归属",
            filterField: "departmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_TREE
        },
        deviceDepartment: {
            filterName: "服务厂商",
            filterField: "deviceDepartmentId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        projectAddress: {
            filterName: "工地地址",
            filterField: "projectAddress",
            filterType: DataReportModuleFilter.FilterControlType.TEXT_INPUT
        },
        projectManager: {
            filterName: "工地负责人",
            filterField: "projectManagerId",
            filterType: DataReportModuleFilter.FilterControlType.SELECT
        },
        reportType: {
            filterName: "报告类型",
            filterField: "reportType",
            filterType: DataReportModuleFilter.FilterControlType.SELECT_MULTIPLE
        },
    }

    export class AnalysisListReq extends DateReportInterface.AnalysisListReq {
        topic: Topic;//主题
        linkCompanyId?: string;//所属公司
        deviceDepartmentId?: string;//服务厂商
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string[];//报告类型
        projectIdList?: string[];//工地ID列表
        idList: string[];//报告ID列表
    }

    export class AnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string[];//报告类型
        projectIdList?: string[];//工地ID列表
        idList: string[];//报告ID列表
        deviceDepartmentId?: string;//服务厂商
    }

    export class AnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        topic: Topic;//主题
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string[];//报告类型
        projectIdList?: string[];//工地ID列表
        idList: string[];//报告ID列表
        dimension: Dimension;//维度
        deviceDepartmentId?: string;//服务厂商
    }

    export class ModuleReq extends DateReportInterface.ModuleReq {
        topic: Topic;//主题
        dimension: Dimension;//维度
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string[];//报告类型
        projectIdList?: string[];//工地ID列表
        deviceDepartmentId?: string;//服务厂商
    }

    export class DimensionReq extends DateReportInterface.DimensionReq {
        topic: Topic;//主题
        idList: string[];
        linkCompanyId?: string;//所属公司
        projectAddress?: string;//工地地址
        projectManagerId?: string;//工地负责人
        reportType?: string[];//报告类型
        projectIdList?: string[];//工地Id列表
        deviceDepartmentId?: string;//服务厂商
    }

    export const AnalysisListCsv: UtilType.propertyCsv[] = [
        { key: "linkCompanyName", value: "所属公司" },
        { key: "departmentName", value: "所属部门" },
        { key: "deviceDepartmentName", value: "服务厂商" },
        { key: "projectAddress", value: "工地地址" },
        { key: "reportType", value: "报告类型", options: { formatter: (value: any) => `${value.join(',')}` } },
        { key: "photo.mediaResourceUri", value: "图片" },
        { key: "detectionDate", value: "监测时间" },
        { key: "projectManagerName", value: "工地负责人" },
    ];

}