import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";
import { Chart } from "../../../chart/chart.type";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { DateReport } from "../../../dto/data-report.dto";

export namespace DateReportInterface {

    export class GetTopicIdListReq {
        companyId: string;
        topic: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
    }

    export class ModuleReq {
        companyId: string;
        subject: DateReport.Subject;
        topic: string;
        dimension: string;
        chartType: Chart.ChartType;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        title?: string;
        actionType?: Chart.ActionType;
        gridColumn?: string;
        isRatio?: 'Y' | 'N';//计算环比
        isProjectCount?: 'Y' | 'N';//计算工地数量
        isRate?: 'Y' | 'N';//计算占比
        businessCategory: 'home_decor' | 'construction';
        filters?: DataReportModuleFilter.DataReportModuleFilter[];//数据范围
    }

    export class DimensionReq {
        companyId: string;
        topic: string;
        chartType: Chart.ChartType;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        title?: string;
        actionType?: Chart.ActionType;
        gridColumn?: string;
        isRatio?: 'Y' | 'N';//计算环比
        isProjectCount?: 'Y' | 'N';//计算工地数量
        isRate?: 'Y' | 'N';//计算占比
        businessCategory: 'home_decor' | 'construction';
    }

    export class FilterOptionReq {
        companyId: string;
        managerId: string;
        companyType: Company.CompanyType;//公司类型
        topic: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        businessCategory: 'home_decor' | 'construction';
    }

    export class FilterOptionRes {
        filterName: string;
        filterField: string;
        filterType: DataReportModuleFilter.FilterControlType;
        filterValue: string | DataReportModuleFilter.SelectFilterValue[] | DataReportModuleFilter.DateRangeFilterValue | DataReportModuleFilter.SelectTreeFilterValue;
    }

    export class AnalysisReq {
        companyId: string;
        topic: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        businessCategory: 'home_decor' | 'construction';
    }

    export class AnalysisOptionReq {
        companyId: string;
        companyType: Company.CompanyType;//公司类型
        topic: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        businessCategory: 'home_decor' | 'construction';
    }

    export class AnalysisOptionRes {
    }

    export class AnalysisListReq {
        companyId: string;
        companyType: string;//公司类型
        topic: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        businessCategory: 'home_decor' | 'construction';
        format?: "csv" | 'json';
        pageNo?: number;
        pageSize?: number;
    }

    export class AnalysisDistributeReq {
        companyId: string;
        subject: DateReport.Subject;
        topic: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        businessCategory: 'home_decor' | 'construction';
    }

    export class AnalysisDetailReq {
        companyId: string;
        subject: DateReport.Subject;
        topic: string;
        dimension: string;
        from: string;//开始时间
        to: string;//结束时间
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        departmentId: string;//工地归属（归属部门）
        businessCategory: 'home_decor' | 'construction';
    }

    export class SubjectOptionRes {
        subject: {
            name: string;
            value: DateReport.Subject;
            topic: {
                name: string;
                value: string;
                dimension: {
                    name: string;
                    value: string;
                    charts: {
                        name: string;
                        value: Chart.ChartType;
                    }[]
                }[]
            }[]
        }
    }

    export class DefaultBoardRes {
        subject: DateReport.Subject;
        blocks: {
            subject: DateReport.Subject;
            gridColumn: string;
            topic: string;
            dimension: string;
            title: string;
            chartType: Chart.ChartType;
        }[]
    }

}