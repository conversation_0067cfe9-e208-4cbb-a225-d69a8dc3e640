import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty } from "class-validator";
import { ObjectId } from "mongodb";
import * as _ from 'lodash';

export namespace DataReportModuleFilter {

    export enum FilterControlType {
        TEXT_INPUT = 'text_input',//文本输入
        SELECT = 'select',//下拉选择
        SELECT_TREE = 'select_tree',//下拉选择（树状结构）
        SELECT_MULTIPLE = 'select_multiple',//多选下拉
        DATE_RANGE_PICKER = 'date_range_picker',//日期范围选择
    }

    export class SelectTreeFilterValue {
        name: string;//选项名
        value: string;//选项值id
        adminAccess: 'Y' | 'N';//是否可以选择
        children?: SelectTreeFilterValue[];//子选项
    }

    export class SelectFilterValue {
        name: string;//选项名
        value: string;//选项值
    }

    export class DateRangeFilterValue {
        from: string;//开始时间
        to: string;//结束时间
    }

    //配合泛型推断(推断不同的filterType 对应的传入的 filterValue的类型)
    export interface FilterValueFormat {
        [FilterControlType.TEXT_INPUT]: string;
        [FilterControlType.SELECT]: SelectFilterValue;
        [FilterControlType.SELECT_TREE]: SelectFilterValue;
        [FilterControlType.SELECT_MULTIPLE]: SelectFilterValue[];
        [FilterControlType.DATE_RANGE_PICKER]: DateRangeFilterValue;
    }

    // 定义一个辅助函数，验证 filterValue 和 filterType 是否匹配
    export function validateFilterValueByType(filterType: FilterControlType, filterValue: any): boolean {
        switch (filterType) {
            case FilterControlType.TEXT_INPUT://文本输入
                return typeof filterValue === 'string';
            case FilterControlType.SELECT://下拉选择（单选）
                return filterValue && typeof filterValue.name === 'string' && typeof filterValue.value === 'string';
            case FilterControlType.SELECT_TREE://下拉树选择
                return filterValue && typeof filterValue.name === 'string' && typeof filterValue.value === 'string';
            case FilterControlType.SELECT_MULTIPLE://下拉选择（多选）
                return Array.isArray(filterValue) && filterValue.every(item => typeof item.name === 'string' && typeof item.value === 'string');
            case FilterControlType.DATE_RANGE_PICKER://日期范围选择
                return filterValue && typeof filterValue.from === 'string' && typeof filterValue.to === 'string';
        }
    }

    // 定义一个辅助函数，转换过滤条件，处理模块筛选字段的映射关系
    export function getChartModuleFilters(filters: DataReportModuleFilter[]): { [key in string]: any } {
        const result: { [key in string]: any } = {};
        if (_.isEmpty(filters)) return result;
        for (const filter of filters) {
            switch (filter.filterType) {
                case FilterControlType.TEXT_INPUT://文本输入
                    result[filter.filterField] = filter?.filterValue as string;
                    break;
                case FilterControlType.SELECT://下拉选择（单选）
                    result[filter.filterField] = (filter?.filterValue as SelectFilterValue)?.value;
                    break;
                case FilterControlType.SELECT_TREE://下拉树选择
                    result[filter.filterField] = (filter?.filterValue as SelectFilterValue)?.value;
                    break;
                case FilterControlType.SELECT_MULTIPLE://下拉选择（多选）
                    result[filter.filterField] = (filter?.filterValue as SelectFilterValue[])?.map(item => item.value);
                    break;
                case FilterControlType.DATE_RANGE_PICKER://日期范围选择
                    result[filter.filterField] = (filter?.filterValue as DateRangeFilterValue);
                    break;
            }
        }
        return result;
    }

    // 定义一个辅助函数，格式化筛选值, 防止前端乱传参数
    export function formatFilterValue(filter: DataReportModuleFilter): FilterValueFormat[FilterControlType] {
        let filterValue = null;
        switch (filter.filterType) {
            case FilterControlType.TEXT_INPUT://文本输入
                const textInputValue = filter.filterValue as string;
                filterValue = textInputValue;
                break;
            case FilterControlType.SELECT://下拉选择（单选）
                const selectValue = filter.filterValue as SelectFilterValue;
                filterValue = { name: selectValue.name, value: selectValue.value };
                break;
            case FilterControlType.SELECT_TREE://下拉树选择
                const selectTreeValue = filter.filterValue as SelectFilterValue;
                filterValue = { name: selectTreeValue.name, value: selectTreeValue.value };
                break;
            case FilterControlType.SELECT_MULTIPLE://下拉选择（多选）
                const selectMultipleValue = filter.filterValue as SelectFilterValue[];
                filterValue = selectMultipleValue.map(item => ({ name: item.name, value: item.value }));
                break;
            case FilterControlType.DATE_RANGE_PICKER://日期范围选择
                const dateRangeValue = filter.filterValue as DateRangeFilterValue;
                filterValue = { from: dateRangeValue.from, to: dateRangeValue.to };
                break;
        }
        return filterValue;
    }

    //数据模块筛选配置
    export class DataReportModuleFilter {
        @IsNotEmpty({ message: '筛选字段描述不能为空' })
        @ApiProperty({ description: '筛选字段描述' })
        filterName: string;//筛选字段描述
        @IsNotEmpty({ message: '筛选字段不能为空' })
        @ApiProperty({ description: '筛选字段' })
        filterField: string;//筛选字段
        @IsNotEmpty({ message: '筛选类型不能为空' })
        @IsEnum(FilterControlType, { message: '筛选类型错误' })
        @ApiProperty({ description: '筛选类型' })
        filterType: FilterControlType;//筛选类型
        @IsNotEmpty({ message: '筛选值不能为空' })
        @ApiProperty({ description: '筛选值' })
        filterValue: FilterValueFormat[FilterControlType]; //筛选值
    }

    // 再定义 Entity，filterValue 根据 filterType 自动关联
    export class DataReportModuleFilterEntity<T extends FilterControlType = FilterControlType> {
        id?: ObjectId;//用于映射 MongoDB 的 `_id`
        moduleId: ObjectId;//模块id
        filterName: string;//筛选字段描述
        filterField: string;//筛选字段
        filterType: T;//筛选类型
        filterValue: FilterValueFormat[T]; // 根据 T 自动推断类型！
        createBy?: string;//创建人
        createTime?: Date;//创建时间
        updateBy?: string;//更新人
        updateTime?: Date;//更新时间
    }

}
