import { ApiProperty } from "@nestjs/swagger";

export namespace DateReportConfig {

    export class MenuConfigSearchReq {
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        @ApiProperty({ description: '成员id' })
        managerId?: string;
    }

    export class MenuConfigUpdateReq {
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        @ApiProperty({ description: '成员id' })
        managerId?: string;
        @ApiProperty({ description: '菜单模块list' })
        columns?: string[];
    }

    export class DataConfigSearchReq {
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        @ApiProperty({ description: '成员id' })
        managerId?: string;
        @ApiProperty({ description: '模块' })
        module?: string;
    }

    export class DataConfigUpdateReq {
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        @ApiProperty({ description: '成员id' })
        managerId?: string;
        @ApiProperty({ description: '模块' })
        module?: string;
        @ApiProperty({ description: '配置' })
        data?: any[];
    }

    export class MenuConfig {
        companyId: string;
        columns: string[];
    }

    export class DataConfig {
        companyId: string;
        module: string;
    }

}