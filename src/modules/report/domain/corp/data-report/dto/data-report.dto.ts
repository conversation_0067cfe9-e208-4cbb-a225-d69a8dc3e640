import { Chart } from "../chart/chart.type";
import * as _ from "lodash";
import { ProjectDateReport } from "../service/basic/dto/project-data-report.dto";
import { ProjectProblemDataReport } from "../service/basic/dto/project-problem-data-report.dto";
import { DeviceDataReport } from "../service/basic/dto/device-data-report.dto";
import { AiDetectionDataReport } from "../service/basic/dto/ai-detection-data-report.dto";
import { NodeOverdueDataReport } from "../service/basic/dto/node-overdue-data-report.dto";
import { AcceptanceReportDataReport } from "../service/basic/dto/acceptance-report-data-report.dto";
import { CustomerEvaluateDataReport } from "../service/basic/dto/customer-evaluate-data-report.dto";

export namespace DateReport {

    export enum Subject {
        Delivery = 'dynamic_dashboards',//灵动仪表盘
        Report = 'project_report',//工地日报
        Project = 'project',//工地概况
        ProjectProblem = 'project_problem',//工地问题
        Device = 'device',//设备概况
        AiDetection = 'ai_detection',//AI监理
        OnsitePatrol = 'onsite_patrol',//现场巡检
        OnlinePatrol = 'online_patrol',//线上巡检
        ProjectUpdate = 'project_update',//工地更新
        NodeOverdue = 'node_overdue',//节点逾期
        AcceptanceReport = 'acceptance_report',//线上验收
        Dispatch = 'dispatch',//统筹派工
        Evaluate = 'customer_evaluate',//业主评价
        Live = 'owner_live',//业主看直播
    }

    export const SubjectName: Readonly<{ [key in Subject]: string }> = {
        [Subject.Delivery]: "灵动仪表盘",
        [Subject.Project]: "工地概况",
        [Subject.Device]: "设备概况",
        [Subject.AcceptanceReport]: "线上验收",
        [Subject.AiDetection]: "AI监理",
        [Subject.NodeOverdue]: "节点逾期",
        [Subject.Report]: "工地日报",
        [Subject.OnsitePatrol]: "现场巡检",
        [Subject.ProjectUpdate]: "工地更新",
        [Subject.OnlinePatrol]: "线上巡检",
        [Subject.Dispatch]: "统筹派工",
        [Subject.ProjectProblem]: "工地问题",
        [Subject.Evaluate]: "业主评价",
        [Subject.Live]: "业主看直播",
    }

    export const SubjectTopics: Readonly<{ [key in Subject]: { [key in string]: { name: string, value: string } } }> = {
        [Subject.Delivery]: undefined,
        [Subject.Report]: undefined,
        [Subject.Project]: ProjectDateReport.Topics,
        [Subject.ProjectProblem]: ProjectProblemDataReport.Topics,
        [Subject.Device]: DeviceDataReport.Topics,
        [Subject.AiDetection]: AiDetectionDataReport.Topics,
        [Subject.OnsitePatrol]: undefined,
        [Subject.OnlinePatrol]: undefined,
        [Subject.ProjectUpdate]: undefined,
        [Subject.NodeOverdue]: NodeOverdueDataReport.Topics,
        [Subject.AcceptanceReport]: AcceptanceReportDataReport.Topics,
        [Subject.Dispatch]: undefined,
        [Subject.Evaluate]: CustomerEvaluateDataReport.Topics,
        [Subject.Live]: undefined
    }

    export const SubjectRealTopics: Readonly<{ [key in Subject]: Readonly<string[]> }> = {
        [Subject.Delivery]: undefined,
        [Subject.Report]: undefined,
        [Subject.Project]: ProjectDateReport.RealTopic,
        [Subject.ProjectProblem]: ProjectProblemDataReport.RealTopic,
        [Subject.Device]: DeviceDataReport.RealTopic,
        [Subject.AiDetection]: undefined,
        [Subject.OnsitePatrol]: undefined,
        [Subject.OnlinePatrol]: undefined,
        [Subject.ProjectUpdate]: undefined,
        [Subject.NodeOverdue]: undefined,
        [Subject.AcceptanceReport]: AcceptanceReportDataReport.RealTopic,
        [Subject.Dispatch]: undefined,
        [Subject.Evaluate]: CustomerEvaluateDataReport.RealTopic,
        [Subject.Live]: undefined
    }

    export const SubjectTopicDefaultDimension: Readonly<{ [key in Subject]: { [key in string]: { name: string, value: string, gridColumn: string; } } }> = {
        [Subject.Delivery]: undefined,
        [Subject.Report]: undefined,
        [Subject.Project]: ProjectDateReport.TopicDefaultDimension,
        [Subject.ProjectProblem]: ProjectProblemDataReport.TopicDefaultDimension,
        [Subject.Device]: DeviceDataReport.TopicDefaultDimension,
        [Subject.AiDetection]: AiDetectionDataReport.TopicDefaultDimension,
        [Subject.OnsitePatrol]: undefined,
        [Subject.OnlinePatrol]: undefined,
        [Subject.ProjectUpdate]: undefined,
        [Subject.NodeOverdue]: NodeOverdueDataReport.TopicDefaultDimension,
        [Subject.AcceptanceReport]: AcceptanceReportDataReport.TopicDefaultDimension,
        [Subject.Dispatch]: undefined,
        [Subject.Evaluate]: CustomerEvaluateDataReport.TopicDefaultDimension,
        [Subject.Live]: undefined
    }

    export const SubjectDimensions: Readonly<{ [key in Subject]: { [key in string]: { name: string, value: string, gridColumn: string } } }> = {
        [Subject.Delivery]: undefined,
        [Subject.Report]: undefined,
        [Subject.Project]: ProjectDateReport.Dimensions,
        [Subject.ProjectProblem]: ProjectProblemDataReport.Dimensions,
        [Subject.Device]: DeviceDataReport.Dimensions,
        [Subject.AiDetection]: AiDetectionDataReport.Dimensions,
        [Subject.OnsitePatrol]: undefined,
        [Subject.OnlinePatrol]: undefined,
        [Subject.ProjectUpdate]: undefined,
        [Subject.NodeOverdue]: NodeOverdueDataReport.Dimensions,
        [Subject.AcceptanceReport]: AcceptanceReportDataReport.Dimensions,
        [Subject.Dispatch]: undefined,
        [Subject.Evaluate]: CustomerEvaluateDataReport.Dimensions,
        [Subject.Live]: undefined
    }

    export const SubjectDimensionCharts: Readonly<{ [key in Subject]: { [key in string]: { name: string, value: Chart.ChartType }[] } }> = {
        [Subject.Delivery]: undefined,
        [Subject.Report]: undefined,
        [Subject.Project]: ProjectDateReport.DimensionCharts,
        [Subject.ProjectProblem]: ProjectProblemDataReport.DimensionCharts,
        [Subject.Device]: DeviceDataReport.DimensionCharts,
        [Subject.AiDetection]: AiDetectionDataReport.DimensionCharts,
        [Subject.OnsitePatrol]: undefined,
        [Subject.OnlinePatrol]: undefined,
        [Subject.ProjectUpdate]: undefined,
        [Subject.NodeOverdue]: NodeOverdueDataReport.DimensionCharts,
        [Subject.AcceptanceReport]: AcceptanceReportDataReport.DimensionCharts,
        [Subject.Dispatch]: undefined,
        [Subject.Evaluate]: CustomerEvaluateDataReport.DimensionCharts,
        [Subject.Live]: undefined
    }

    export const SubjectTopicDimensions: Readonly<{ [key in Subject]: { [key in string]: { name: string, value: string, gridColumn: string }[] } }> = {
        [Subject.Delivery]: undefined,
        [Subject.Report]: undefined,
        [Subject.Project]: ProjectDateReport.TopicDimensions,
        [Subject.ProjectProblem]: ProjectProblemDataReport.TopicDimensions,
        [Subject.Device]: DeviceDataReport.TopicDimensions,
        [Subject.AiDetection]: AiDetectionDataReport.TopicDimensions,
        [Subject.OnsitePatrol]: undefined,
        [Subject.OnlinePatrol]: undefined,
        [Subject.ProjectUpdate]: undefined,
        [Subject.NodeOverdue]: NodeOverdueDataReport.TopicDimensions,
        [Subject.AcceptanceReport]: AcceptanceReportDataReport.TopicDimensions,
        [Subject.Dispatch]: undefined,
        [Subject.Evaluate]: CustomerEvaluateDataReport.TopicDimensions,
        [Subject.Live]: undefined
    }

}