import { Default } from "@src/util/default-data.util"
import { DateReport } from "./data-report.dto"
import { Sql } from "../../sql-generate/type/sql.type"
import { ObjUtil } from "@src/util/obj.util"

export namespace DataReportConfigDefault {

    const device = {
        "deviceExpire": "30",
        "inIdleTimeout": "1",
        "outIdleTimeout": "1",
        // "exceptionOfflineScope": "bindDevice",//all bindDevice 异常离线关注设备
        "exceptionOfflineReport": "all",//all -全部 no_report -未报备 异常离线关注离线记录
        "exceptionOfflineCondition": "4",
        "exceptionOfflineConditionUnit": "hour",
        // "offlineFocusScope": "all",//all bindDevice
        "offlineReport": "all",//all -全部 no_report -未报备
        "offlineCondition": "7",
    }

    const project = {
        "todoCompleted": "180",
        "noworkProject": "7",
        "ownerFocusProjectScope": "one_of",//all -全部 one_of -任一条件
        "ownerFocusProjectOneDay": "10",
        "ownerFocusProjectTime": "10"
    }

    const projectProblem = {
        "problemStatus": "all",// all-全部 part_of-部分状态
        "conditions": []//Obj label-显示名  value-值
    }

    const onsitePatrol = {
        "patrolSignStyle": ["device", "app"],
        "roleScope": "all",//all special -指定岗位
        "roles": [],
        "noPatrolProjectShow": "N"
    }

    const onlinePatrol = {
        "roleScope": "all",
        "roles": [],
        "noPatrolProjectShow": "N"
    }

    const projectUpdate = {
        "roleScope": "all",
        "roles": [],
        "noPatrolProjectShow": "N"
    }

    const nodeOverdue = {
        "delayOverdue": "Y"
    }

    const evaluate = {
        "badEvaluationScope": "one_of",//all -全部 one_of -任一条件
        "overallScore": "5",
        "individualScore": "5"
    }

    export const DefaultConfig: Record<DateReport.Subject, any> = {
        [DateReport.Subject.Delivery]: undefined,
        [DateReport.Subject.Report]: undefined,
        [DateReport.Subject.Project]: project,
        [DateReport.Subject.ProjectProblem]: projectProblem,
        [DateReport.Subject.Device]: device,
        [DateReport.Subject.AiDetection]: undefined,
        [DateReport.Subject.OnsitePatrol]: onsitePatrol,
        [DateReport.Subject.OnlinePatrol]: onlinePatrol,
        [DateReport.Subject.ProjectUpdate]: projectUpdate,
        [DateReport.Subject.NodeOverdue]: nodeOverdue,
        [DateReport.Subject.AcceptanceReport]: undefined,
        [DateReport.Subject.Dispatch]: undefined,
        [DateReport.Subject.Evaluate]: evaluate,
        [DateReport.Subject.Live]: undefined,
    }

    export const defList = Default.getConfigList(DefaultConfig)

    export const defKeys = Default.getConfigKeys(DefaultConfig)

    const logic = {
        "all": 'AND',
        "one_of": 'OR'
    }

    const logicMap = ObjUtil.createMappings(logic)

    // 属性映射规则，定义字段及其操作符和嵌套关系
    export const PropertyMapping: Record<string, { operator: string|Function, logic?: 'AND' | 'OR'|Function, nestedKeys?: string[] }> = {
        "todoCompletedDay": { operator: Sql.Operator.greater_than_or_equal },
        "noworkProjectDay": { operator: Sql.Operator.greater_than_or_equal },
        "ownerFocusProjectScope": {
            operator: "no_filter",
            logic: logicMap,
            nestedKeys: ["ownerFocusProjectOneDay", "ownerFocusProjectTime"],
        },
        "ownerFocusProjectOneDay": { operator: Sql.Operator.greater_than_or_equal },
        "ownerFocusProjectTime": { operator: Sql.Operator.greater_than_or_equal },
        "deviceExpireDay": { operator: Sql.Operator.less_than_or_equal },
        "inIdleTimeoutDay": { operator: Sql.Operator.greater_than_or_equal },
        "outIdleTimeoutDay": { operator: Sql.Operator.greater_than_or_equal },
        "exceptionFocusScope": { operator: Sql.Operator.is_not_null },
        "exceptionOfflineScope": { operator: Sql.Operator.is_not_null },
        "exceptionOfflineCondition": { operator: Sql.Operator.greater_than_or_equal },
        "offlineFocusScope": { operator: Sql.Operator.is_not_null },
        "offlineReport": { operator: Sql.Operator.is_not_null },
        "offlineCondition": { operator: Sql.Operator.greater_than_or_equal },
        "delayOverdue": { operator: Sql.Operator.greater_than_or_equal },
        "badEvaluationScope": {
            operator: "no_filter",
            logic: logicMap,
            nestedKeys: ["overallScore", "individualScore"],
        },
        "overallScore": { operator: Sql.Operator.less_than_or_equal },
        "individualScore": { operator: Sql.Operator.less_than_or_equal },
    };
}