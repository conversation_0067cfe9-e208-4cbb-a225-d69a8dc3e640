import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString, MaxLength } from "class-validator";
import { ObjectId } from "mongodb";
import { DateReport } from "./data-report.dto";
import { BaseResp } from "@src/types/report-req.type";

/**
 * 仪表盘模板相关DTO
 */
export namespace DashboardTemplate {
    /**
     * 仪表盘配置块
     */
    export interface DashboardBlock {
        moduleId: string | ObjectId;
        gridColumn: string;
    }

    // /**
    //  * 仪表盘配置
    //  */
    // export class DashboardConfig {
    //     subject?: DateReport.Subject;
    //     blocks: DashboardBlock[];
    // }

    /**
     * 仪表盘模板实体
     */
    export class DashboardTemplateEntity {
        _id?: ObjectId;
        templateName: string;
        templateDesc?: string;
        companyId?: string;
        dashboardConfig: DashboardBlock[];
        createBy: string;
        createTime: Date;
        updateBy?: string;
        updateTime?: Date;
        lastUpdateBy?: string;
        lastUpdateTime?: Date;
        deleteFlag: 'Y' | 'N';
        version: number;
    }

    /**
     * 仪表盘模板列表项
     */
    export class TemplateListItem {
        @ApiProperty({ description: '模板ID' })
        templateId: string;

        @ApiProperty({ description: '模板名称' })
        templateName: string;

        @ApiProperty({ description: '模板描述' })
        templateDesc?: string;

        @ApiProperty({ description: '当前用户是否正在使用此模板' })
        isCurrentlyUsed: boolean;

        @ApiProperty({ description: '当前应用的模板自上次应用后是否有更新' })
        hasUpdates: boolean;

        @ApiProperty({ description: '创建人昵称' })
        creatorNickname: string;

        @ApiProperty({ description: '创建时间' })
        createdTime: string;

        @ApiProperty({ description: '最后更新人昵称' })
        lastUpdaterNickname: string;

        @ApiProperty({ description: '最后更新时间' })
        lastUpdatedTime: string;

        @ApiProperty({ description: '版本号' })
        version: number;
    }

    /**
     * 模块位置信息
     */
    export class ModulePosition {
        @ApiProperty({ description: '模块ID' })
        moduleId: string;

        @ApiProperty({ description: '模块位置信息' })
        position: any;
    }

    /**
     * 创建模板请求
     */
    export class CreateTemplateRequest {
        @IsNotEmpty({ message: '模板名称不能为空' })
        @IsString({ message: '模板名称必须是字符串' })
        @MaxLength(10, { message: '模板名称不能超过10个字符' })
        @ApiProperty({ description: '模板名称' })
        templateName: string;

        @IsOptional()
        @IsString({ message: '模板描述必须是字符串' })
        @MaxLength(200, { message: '模板描述不能超过200个字符' })
        @ApiProperty({ description: '模板描述', required: false })
        templateDesc?: string;
    }

    /**
     * 创建模板响应
     */
    export class CreateTemplateResponse {
        @ApiProperty({ description: '模板ID' })
        templateId: string;
    }

    /**
     * 查询模板列表请求
     */
    export class ListTemplatesRequest {
        @ApiProperty({ description: '页码', required: false, default: 1 })
        pageNo?: number;

        @ApiProperty({ description: '每页条数', required: false, default: 10 })
        pageSize?: number;

        @ApiProperty({ description: '模板名称', required: false })
        templateName?: string;
    }

    export class TemplateItem {
        @ApiProperty({ description: '模板ID' })
        templateId: string;

        @ApiProperty({ description: '模板名称' })
        templateName: string;

        @ApiProperty({ description: '模板描述' })
        templateDesc: string;

        @ApiProperty({ description: '模板标签' })
        tags: string[];

        @ApiProperty({ description: '创建时间' })
        createTime: string;

        @ApiProperty({ description: '更新时间' })
        updateTime: string;

        @ApiProperty({ description: '创建者ID' })
        creatorId: string;

        @ApiProperty({ description: '公司ID' })
        companyId: string;

        @ApiProperty({ description: '版本号' })
        version: number;
    }

    /**
     * 查询模板列表响应
     */
    export class ListTemplatesResponse {
        @ApiProperty({ description: '总条数' })
        total: number;

        @ApiProperty({ description: '模板列表', type: [TemplateItem] })
        items: TemplateItem[];
    }

    /**
     * 查询模板详情请求
     */
    export class GetTemplateDetailRequest {
        @ApiProperty({ description: '模板ID' })
        templateId: string;
    }

    /**
     * 查询模板详情响应 (继承自TemplateListItem并添加dashboardConfig)
     */
    export class GetTemplateDetailResponse extends TemplateListItem {
        @ApiProperty({ description: '仪表盘配置' })
        dashboardConfig: DashboardBlock[];
        
        @ApiProperty({ description: '当前模板版本号' })
        version: number;
    }

    /**
     * 更新模板基本信息请求
     */
    export class UpdateTemplateBasicInfoRequest {
        @ApiProperty({ description: '模板ID' })
        templateId: string;

        @ApiProperty({ description: '版本号' })
        version: number;

        @IsOptional()
        @IsString({ message: '模板名称必须是字符串' })
        @MaxLength(50, { message: '模板名称不能超过50个字符' })
        @ApiProperty({ description: '模板名称', required: false })
        templateName?: string;

        @IsOptional()
        @IsString({ message: '模板描述必须是字符串' })
        @MaxLength(200, { message: '模板描述不能超过200个字符' })
        @ApiProperty({ description: '模板描述', required: false })
        templateDesc?: string;
    }

    /**
     * 更新模板配置请求
     */
    export class UpdateTemplateConfigRequest {
        @IsNotEmpty({ message: '模板ID不能为空' })
        @IsString({ message: '模板ID必须是字符串' })
        @ApiProperty({ description: '模板ID' })
        templateId: string;

        @IsNotEmpty({ message: '仪表盘配置不能为空' })
        @ApiProperty({ description: '仪表盘配置' })
        dashboardConfig: DashboardBlock[];
        
        @IsNotEmpty({ message: '版本号不能为空' })
        @ApiProperty({ description: '当前模板版本号' })
        version: number;
    }

    /**
     * 删除模板请求
     */
    export class DeleteTemplateRequest {
        @ApiProperty({ description: '模板ID' })
        templateId: string;
        
        @ApiProperty({ description: '版本号' })
        version: number;
    }

    /**
     * 应用模板请求
     */
    export class ApplyTemplateRequest {
        @ApiProperty({ description: '模板ID' })
        templateId: string;
    }

    /**
     * 清除模板更新标记请求
     */
    export class ClearTemplateUpdateFlagRequest {
        @ApiProperty({ description: '模板ID' })
        templateId: string;
    }

    /**
     * 用户仪表盘设置实体
     */
    export class UserDashboardSettingsEntity {
        _id?: ObjectId;
        managerId: string;
        companyId?: string;
        currentTemplateId?: string;
        lastAppliedTime?: Date;
        deleteFlag?: string;
        createBy?: string;
        createTime?: Date;
        updateBy?: string;
        updateTime?: Date;
    }
} 