import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, ValidateNested } from "class-validator";
import { DateReport } from "./data-report.dto";
import { Type } from "class-transformer";
import { ObjectId } from "mongodb";

export namespace DateReportBoard {

    export enum BoardType {
        Company = 'company',
        Manager = 'manager'
    }

    export class DataReportBoardEntity {
        id?: ObjectId; // 用于映射 MongoDB 的 `_id`
        boardType: BoardType;
        companyId: string;
        managerId?: string;
        subject: DateReport.Subject;
        blocks: DataReportBoardBlock[];
        createBy?: string;
        createTime?: Date;
        updateBy?: string;
        updateTime?: Date;
    }

    export class DataReportBoardBlock {
        moduleId: ObjectId;
        gridColumn: string;
    }

    export class DataBoardReq {
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        @ApiProperty({ description: '成员id' })
        managerId?: string;
        @IsNotEmpty({ message: "主体不能为空" })
        @ApiProperty({ description: '查询主体' })
        subject: DateReport.Subject;
    }

    export class Block {
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @ApiProperty({ description: '所占网格列' })
        gridColumn: string;
    }

    export class DataBoardRes {
        @ApiProperty({ description: '数据看板布局id' })
        boardId: string;
        @ApiProperty({ description: '查询主体' })
        subject: DateReport.Subject;
        @ApiProperty({ description: '数据看板布局' })
        blocks: Block[];
    }

    export class DataBoardUpdateReq {
        @ApiProperty({ description: '公司id' })
        companyId?: string;
        @ApiProperty({ description: '成员id' })
        managerId?: string;
        @IsNotEmpty({ message: "id不能为空" })
        @ApiProperty({ description: '数据看板布局id' })
        boardId: string;
        @IsArray({ message: "参数必须是数组" })
        @ValidateNested()//嵌套校验
        @Type(() => Block)
        @ApiProperty({ description: '数据看板布局' })
        blocks: Block[];
    }

    export class DataBoardUpdateRes {
        @ApiProperty({ description: '数据看板布局id' })
        boardId: string;
    }

}