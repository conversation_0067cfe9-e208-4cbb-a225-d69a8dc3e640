import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, ValidateNested } from "class-validator";
import { DateReport } from "./data-report.dto";
import { Chart } from "../chart/chart.type";
import { ObjectId } from "mongodb";
import { DateReportInterface } from "../service/basic/dto/data-report.interface.dto";
import { UtilType } from "../../../bgw/types/util.type";
import { DataReportModuleFilter } from "./data-report-module-filter.dto";
import { Type } from "class-transformer";

export namespace DateReportModule {

    export class DataReportModuleEntity {
        id?: ObjectId; // 用于映射 MongoDB 的 `_id`
        boardId: ObjectId;// 看板id
        type?: string;//类型 person-个人 template-仪表盘模版
        companyId: string;
        subject: DateReport.Subject;
        topic: string;
        dimension: string;
        title: string;
        chartType: Chart.ChartType;
        createBy?: string;
        createTime?: Date;
        updateBy?: string;
        updateTime?: Date;
    }

    export class SubjectOptionReq {
        @ApiProperty({ description: "公司id" })
        companyId: string;
        @IsNotEmpty({ message: "主体不能为空" })
        @ApiProperty({ description: "查询主体" })
        subject: DateReport.Subject;
    }

    export class FilterOptionReq extends DateReportInterface.AnalysisReq {
        @ApiProperty({ description: "公司id" })
        companyId: string;
        @ApiProperty({ description: "成员id" })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: "部门id" })
        departmentId: string;
        @IsNotEmpty({ message: "主体不能为空" })
        @ApiProperty({ description: "查询主体" })
        subject: DateReport.Subject;
        @IsNotEmpty({ message: "主题不能为空" })
        @ApiProperty({ description: "查询主题" })
        topic: string;
    }

    export class SubjectDefaultOptionReq {
        @ApiProperty({ description: "公司id" })
        companyId: string;
        @IsNotEmpty({ message: "主体不能为空" })
        @ApiProperty({ description: "查询主体" })
        subject: DateReport.Subject;
        @IsNotEmpty({ message: "主题不能为空" })
        @ApiProperty({ description: "查询主题" })
        topic: string;
    }

    export class DataModuleReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "开始时间不能为空" })
        @ApiProperty({ description: '开始时间' })
        from: string;
        @IsNotEmpty({ message: "结束时间不能为空" })
        @ApiProperty({ description: '结束时间' })
        to: string;
        @IsNotEmpty({ message: "时间类型不能为空" })
        @ApiProperty({ description: '时间类型' })
        dataType: 'day' | 'week' | 'month';//时间类型 day｜week｜month
        @ApiProperty({ description: '模块id' })
        moduleId?: string;
        @ApiProperty({ description: '查询主体' })
        subject?: DateReport.Subject;
        @ApiProperty({ description: '主题' })
        topic?: string;
        @ApiProperty({ description: '维度' })
        dimension?: string;
        @ApiProperty({ description: '标题' })
        title?: string;
        @ApiProperty({ description: '图表类型' })
        chartType?: Chart.ChartType;
        @ApiProperty({ description: '所占网格列' })
        gridColumn?: string;
        @ApiProperty({ description: '数据范围' })
        filters?: DataReportModuleFilter.DataReportModuleFilter[];
        @ApiProperty({ description: '类型： person-个人 template-仪表盘模版' })
        type?: string;
    }

    export class DataModuleUpdateReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @IsNotEmpty({ message: "主体不能为空" })
        @IsEnum(DateReport.Subject, { message: "主体信息有误" })
        @ApiProperty({ description: '查询主体' })
        subject: DateReport.Subject;
        @IsNotEmpty({ message: "主题不能为空" })
        @ApiProperty({ description: '主题主体' })
        topic: string;
        // @IsNotEmpty({ message: "标题不能为空" })
        @ApiProperty({ description: '标题' })
        title: string;
        @IsNotEmpty({ message: "维度不能为空" })
        @ApiProperty({ description: '维度' })
        dimension: string;
        @IsNotEmpty({ message: "图表类型不能为空" })
        @IsEnum(Chart.ChartType, { message: "图表类型有误" })
        @ApiProperty({ description: '图表类型' })
        chartType: Chart.ChartType;
        @IsOptional()
        @ValidateNested()
        @Type(() => DataReportModuleFilter.DataReportModuleFilter)
        @ApiProperty({ description: '筛选配置' })
        filters: DataReportModuleFilter.DataReportModuleFilter[];
        @ApiProperty({ description: '类型： person-个人 template-仪表盘模版' })
        type?: string;
    }

    export class DataModuleUpdateRes {
        @ApiProperty({ description: '模块id' })
        moduleId: string;
    }

    export class DataModuleCopyReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "布局id不能为空" })
        @ApiProperty({ description: '布局id' })
        boardId: string;
        @ApiProperty({ description: '保存的布局id' })
        saveBoardId?: string;
        @ApiProperty({ description: '布局类型： person-个人 template-仪表盘模版' })
        type?: string;
        @ApiProperty({ description: '布局的保存类型： person-个人 template-仪表盘模版' })
        saveType?: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '复制的模块id' })
        copyModuleId: string;
        @ApiProperty({ description: '所占网格列(默认应该都是1)' })
        gridColumn?: string;
    }

    export class DataModuleCopyRes {
        @ApiProperty({ description: '模块id' })
        moduleId: string;
    }

    export class DataModuleSaveReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "布局id不能为空" })
        @ApiProperty({ description: '布局id' })
        boardId: string;
        @ApiProperty({ description: '类型： person-个人 template-仪表盘模版' })
        type?: string;
        @IsNotEmpty({ message: "主体不能为空" })
        @ApiProperty({ description: '查询主体' })
        subject: DateReport.Subject;
        @IsNotEmpty({ message: "主题不能为空" })
        @ApiProperty({ description: '主题主体' })
        topic: string;
        // @IsNotEmpty({ message: "标题不能为空" })
        @ApiProperty({ description: '标题' })
        title: string;
        @IsNotEmpty({ message: "维度不能为空" })
        @ApiProperty({ description: '维度' })
        dimension: string;
        @IsNotEmpty({ message: "图表类型不能为空" })
        @ApiProperty({ description: '图表类型' })
        chartType: Chart.ChartType;
        @ApiProperty({ description: '所占网格列(默认应该都是1)' })
        gridColumn?: string;
        @IsOptional()
        @ValidateNested()
        @Type(() => DataReportModuleFilter.DataReportModuleFilter)
        @ApiProperty({ description: '筛选配置' })
        filters?: DataReportModuleFilter.DataReportModuleFilter[];
    }

    export class DataModuleSaveRes {
        @ApiProperty({ description: '模块id' })
        moduleId: string;
    }

    export class DataModuleDeleteReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "布局id不能为空" })
        @ApiProperty({ description: '布局id' })
        boardId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @ApiProperty({ description: '类型： person-个人 template-仪表盘模版' })
        type?: string;
    }

    export class DataModuleDeleteRes {
        @ApiProperty({ description: '模块id' })
        moduleId: string;
    }

    export class DataModuleDetailReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
    }

    export class DataModuleAnalysisOptionReq extends DateReportInterface.AnalysisReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
    }

    export class DataModuleAnalysisListReq extends DateReportInterface.AnalysisReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @ApiProperty({ description: '返回格式' })
        format?: "csv" | 'json';
        pageNo?: number;
        pageSize?: number;
        //还有其他筛选条件
    }

    export class DataModuleAnalysisDistributeReq extends DateReportInterface.AnalysisReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        //还有其他筛选条件
    }

    export class DistributeDataModuleRes {
        @ApiProperty({ description: '查询主体' })
        subject: DateReport.Subject;
        @ApiProperty({ description: '主题主体' })
        topic: string;
        @ApiProperty({ description: '维度' })
        dimension: string;
        @ApiProperty({ description: '标题' })
        title: string;
        @ApiProperty({ description: '图表类型' })
        chartType: Chart.ChartType;
        @ApiProperty({ description: '图表数据' })
        data: Chart.ChartResDto;
        @ApiProperty({ description: '所占网格列' })
        gridColumn: string;
        @ApiProperty({ description: '模块id' })
        moduleId?: string;
    }

    export class DataModuleAnalysisDistributeRes {
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @ApiProperty({ description: '各分布模块列表' })
        distributeList: DistributeDataModuleRes[]
    }

    export class DataModuleAnalysisDistributeDetailReq extends DateReportInterface.AnalysisReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @IsNotEmpty({ message: "分析维度不能为空" })
        @ApiProperty({ description: '分析维度' })
        dimension: string;
        @ApiProperty({ description: '返回格式' })
        format?: "csv" | 'json';
    }

    export class DataModuleAnalysisDistributeDetailRes {
        @ApiProperty({ description: 'key' })
        key: string;
        @ApiProperty({ description: '名称' })
        name: string;
        @ApiProperty({ description: '数值' })
        value: number;
        @ApiProperty({ description: '占比' })
        rate: number;
        @ApiProperty({ description: '环比' })
        ratio?: string;
    }

    export class DataModuleAnalysisExportReq extends DateReportInterface.AnalysisReq {
        @ApiProperty({ description: '公司id' })
        companyId: string;
        @ApiProperty({ description: '成员id' })
        managerId: string;
        @IsNotEmpty({ message: "部门id不能为空" })
        @ApiProperty({ description: '部门id' })
        departmentId: string;
        @IsNotEmpty({ message: "模块id不能为空" })
        @ApiProperty({ description: '模块id' })
        moduleId: string;
        @ApiProperty({ description: '分析维度' })
        dimension?: string;
        @ApiProperty({ description: '返回格式' })
        format?: "csv" | 'json';
    }

    export const ModuleAnalysisDistributeDetailCsv: UtilType.propertyCsv[] = [
        { key: "name", value: "名称" },
        { key: "value", value: "数值" },
        { key: "rate", value: "占比", options: { formatter: (value: number) => `${value}%` } },
        { key: "ratio", value: "环比", options: { formatter: (value: string) => value !== '--' ? `${Number(value) > 0 ? '+' : ''}${value}%` : value } },
    ];

}