import { Injectable } from "@nestjs/common";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { MyLogger, YqzException } from "@yqz/nest";
import { ObjectId } from "mongodb";
import * as _ from "lodash";
import { DateReportModule } from "../dto/data-report-module.dto";
import { DateReport } from "../dto/data-report.dto";
import { Chart } from "../chart/chart.type";

/**
 * @summary 数据看板 - 数据模块
 * @class DataReportModuleDao
 */
@Injectable()
export class DataReportModuleDao {
    private readonly logger = new MyLogger(DataReportModuleDao.name);

    constructor() { }

    /**
     * 查询数据模块
     * @param params 
     * @returns 
     */
    async findOne(params: { moduleId: string }): Promise<DateReportModule.DataReportModuleEntity> {
        if (!params.moduleId || !ObjectId.isValid(params.moduleId)) throw new YqzException('必传参数错误');
        const result = await MyMongoDb.collections.bgw.dataReportModule.findOne({ _id: new ObjectId(params.moduleId) });
        if (!result) return null;
        return { ...result, id: result?._id }
    }

    /**
     * 更新数据看板数据布局
     * @param moduleId 
     * @param updateDto 
     */
    async update(moduleId: string, updateDto: { title?: string, subject?: DateReport.Subject, topic?: string, dimension?: string, chartType?: Chart.ChartType, updateBy?: string }): Promise<any> {
        if (!moduleId || !ObjectId.isValid(moduleId)) throw new YqzException('必传参数错误');
        const set = {};
        if (null !== updateDto.title || undefined !== updateDto.title) set['title'] = updateDto.title;
        if (updateDto.subject) set['subject'] = updateDto.subject;
        if (updateDto.topic) set['topic'] = updateDto.topic;
        if (updateDto.dimension) set['dimension'] = updateDto.dimension;
        if (updateDto.chartType) set['chartType'] = updateDto.chartType;
        if (updateDto.updateBy) set['updateBy'] = updateDto.updateBy;
        if (_.isEmpty(set)) return;//无更新内容
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dataReportModule.updateOne({ _id: new ObjectId(moduleId) }, { $set: set });
    }

    /**
     * 新增数据模块
     * @param saveDto 
     * @returns 
     */
    async save(saveDto: DateReportModule.DataReportModuleEntity): Promise<ObjectId> {
        if (!saveDto) return null;
        saveDto.createTime = saveDto?.createTime || new Date();
        const data = await MyMongoDb.collections.bgw.dataReportModule.insertOne(saveDto);
        return data.insertedId;
    }

    /**
     * 批量新增数据模块
     * @param saveDto 
     * @returns 
     */
    async batchSave(saveDto: DateReportModule.DataReportModuleEntity[]): Promise<ObjectId[]> {
        if (_.isEmpty(saveDto)) return [];
        saveDto.forEach(item => item.createTime = item?.createTime || new Date());
        const data = await MyMongoDb.collections.bgw.dataReportModule.insertMany(saveDto);
        return Object.keys(data.insertedIds).map(key => data.insertedIds[key]);
    }

    /**
     * 删除数据模块
     * @param moduleId
     * @returns
     */
    async delete(moduleId: string): Promise<any> {
        if (!moduleId || !ObjectId.isValid(moduleId)) throw new YqzException('必传参数错误');
        return await MyMongoDb.collections.bgw.dataReportModule.deleteOne({ _id: new ObjectId(moduleId) });
    }

}