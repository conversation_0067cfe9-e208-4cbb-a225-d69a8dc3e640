import { Injectable } from "@nestjs/common";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { MyLogger, YqzException } from "@yqz/nest";
import { DashboardTemplate } from "../dto/dashboard-template.dto";
import { ObjectId } from "mongodb";
import * as _ from "lodash";
import { v4 as uuidv4 } from 'uuid';
import { DeleteFlag } from "@src/types/delete-flag.enum";
import { BaseResp } from "@src/types/report-req.type";
import { Flag } from "@yqz/nest/lib/types/common.type";

/**
 * 仪表盘模板数据访问对象
 */
@Injectable()
export class DashboardTemplateDao {
    private readonly logger = new MyLogger(DashboardTemplateDao.name);

    constructor() { }

    /**
     * 创建或更新模板
     * @param templateData 模板数据
     * @returns 创建的模板ID
     */
    async saveTemplate(templateData: DashboardTemplate.DashboardTemplateEntity): Promise<string> {
        if (!templateData.templateName || !templateData.dashboardConfig) {
            throw new YqzException('模板名称和配置不能为空');
        }

        try {
            // 设置初始版本号
            templateData.version = 1;
            
            // 插入数据库
            const result = await MyMongoDb.collections.bgw.dashboardTemplates.insertOne(templateData);
            
            return result.insertedId.toString();
        } catch (error) {
            this.logger.error(`保存模板出错: ${error.message}`, error.stack);
            throw new YqzException('保存模板失败');
        }
    }

    /**
     * 根据模板ID查询模板
     * @param templateId 模板ID
     * @returns 模板实体
     */
    async findTemplateById(templateId: string): Promise<DashboardTemplate.DashboardTemplateEntity> {
        if (!templateId) {
            throw new YqzException('模板ID不能为空');
        }

        try {
            // 通过_id字段查询
            let template = await MyMongoDb.collections.bgw.dashboardTemplates.findOne({
                _id: new ObjectId(templateId),
                deleteFlag: 'N'
            });

            return template;
        } catch (error) {
            this.logger.error(`查询模板出错: ${error.message}`, error.stack);
            throw new YqzException('查询模板失败');
        }
    }

    /**
     * 查询模板列表
     * @param companyId 公司ID
     * @param templateName 模板名称（可选，用于模糊查询）
     * @returns 模板列表和总数量
     */
    async findTemplates(params: { companyId?: string, templateName?: string, pageNo: string, pageSize: string }): Promise<BaseResp<DashboardTemplate.DashboardTemplateEntity>> {
        const { companyId, templateName, pageNo, pageSize } = params;

        if (!companyId || !pageNo || !pageSize) {
            throw new YqzException("必传参数不能为空");
        }
        
        try {
            // 构建查询条件
            const query: any = { deleteFlag: DeleteFlag.N };
            
            // 如果提供了公司ID，添加到查询条件
            if (companyId) {
                query.companyId = companyId;
            }
            
            // 如果提供了模板名称，添加模糊查询条件
            if (templateName) {
                query.templateName = { $regex: templateName, $options: 'i' };
            }
            
            // 计算分页参数
            const skip = (parseInt(pageNo) - 1) * parseInt(pageSize);
            const limit = parseInt(pageSize);
            
            // 获取总记录数
            const total = await MyMongoDb.collections.bgw.dashboardTemplates.countDocuments(query);
            
            // 执行分页查询
            const items = await MyMongoDb.collections.bgw.dashboardTemplates.find(query)
                .sort({ updateTime: -1 })
                .skip(skip)
                .limit(limit)
                .toArray();
                
            return { items, total };
        } catch (error) {
            this.logger.error(`查询模板列表出错: ${error.message}`, error.stack);
            throw new YqzException('查询模板列表失败');
        }
    }

    /**
     * 更新模板基本信息
     * @param templateId 模板ID
     * @param updateData 更新数据
     * @returns 更新结果
     */
    async updateTemplateBasicInfo(templateId: string, updateData: { templateName: string, templateDesc?: string, version: number }, managerId: string): Promise<{templateId: string,version: number}> {
        if (!templateId || !updateData.templateName) {
            throw new YqzException('模板ID和名称不能为空');
        }

        try {
            // 使用乐观锁进行更新
            const result = await MyMongoDb.collections.bgw.dashboardTemplates.updateOne(
                {
                    _id: new ObjectId(templateId),
                    version: updateData.version // 只有当版本匹配时才更新
                },
                {
                    $set: {
                        templateName: updateData.templateName,
                        templateDesc: updateData.templateDesc,
                        updateBy: managerId,
                        updateTime: new Date(),
                        lastUpdateBy: managerId,
                        lastUpdateTime: new Date(),
                        version: updateData.version + 1 // 更新版本号
                    }
                }
            );

            if (result.matchedCount === 0) {
                throw new YqzException('版本冲突: 模板已被修改，请刷新后重试');
            }

            return {
                templateId: templateId,
                version: updateData.version + 1
            };
        } catch (error) {
            this.logger.error(`更新模板基本信息出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('更新模板基本信息失败');
        }
    }

    /**
     * 更新模板配置
     * @param templateId 模板ID
     * @param updateData 更新数据
     * @returns 更新结果
     */
    async updateTemplateConfig(templateId: string, dashboardConfig: any, version: number, managerId: string): Promise<{templateId: string,version: number}> {
        if (!templateId || !dashboardConfig) {
            throw new YqzException('模板ID和配置不能为空');
        }

        try {
            // 使用乐观锁进行更新
            const result = await MyMongoDb.collections.bgw.dashboardTemplates.updateOne(
                {
                    _id: new ObjectId(templateId),
                    version: version // 只有当版本匹配时才更新
                },
                {
                    $set: {
                        dashboardConfig: dashboardConfig,
                        updateBy: managerId,
                        updateTime: new Date(),
                        lastUpdateBy: managerId,
                        lastUpdateTime: new Date(),
                        version: version + 1 // 更新版本号
                    }
                }
            );

            if (result.matchedCount === 0) {
                throw new YqzException('版本冲突: 模板已被修改，请刷新后重试');
            }

            return {
                templateId: templateId,
                version: version + 1
            };
        } catch (error) {
            this.logger.error(`更新模板配置出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('更新模板配置失败');
        }
    }

    /**
     * 模版 新增模块
     * @param param0 
     */
    async addModule({ templateId, moduleId, gridColumn, updateBy }: { templateId: string, moduleId: string, gridColumn: string, updateBy?: string }): Promise<any> {
        if (!templateId || !moduleId || !gridColumn) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(templateId) || !ObjectId.isValid(moduleId)) throw new YqzException('必传参数错误');
        const set = {};
        if (updateBy) {
            set['updateBy'] = updateBy;
            set['lastUpdateBy'] = updateBy;
            set['lastUpdateTime'] = new Date();
        }
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dashboardTemplates.updateOne({ _id: new ObjectId(templateId) }, { $set: set, $push: { dashboardConfig: { moduleId: moduleId, gridColumn } } });
    }

    /**
     * 模版 更新模块
     * @param param0 
     */
    async updateModule({ templateId, updateBy }: { templateId: string, updateBy?: string }): Promise<any> {
        if (!templateId) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(templateId)) throw new YqzException('必传参数错误');
        const set = {};
        if (updateBy) {
            set['updateBy'] = updateBy;
            set['lastUpdateBy'] = updateBy;
            set['lastUpdateTime'] = new Date();
        }
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dashboardTemplates.updateOne({ _id: new ObjectId(templateId) }, { $set: set });
    }

    /**
     * 模版 删除模块
     * @param param0 
     * @returns 
     */
    async delModule({ boardId, moduleId, updateBy }: { boardId: string, moduleId: string, updateBy?: string }): Promise<any> {
        if (!boardId || !moduleId) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(boardId) || !ObjectId.isValid(moduleId)) throw new YqzException('必传参数错误');
        const set = {};
        if (updateBy) set['updateBy'] = updateBy;
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dashboardTemplates.updateOne({ _id: new ObjectId(boardId) }, { $pull: { dashboardConfig: { moduleId: moduleId } }, $set: set });
    }

    /**
     * 删除模板（逻辑删除）
     * @param templateId 模板ID
     * @param updateBy 更新者
     * @returns 删除结果
     */
    async deleteTemplate(templateId: string, version: number, managerId: string): Promise<boolean> {
        if (!templateId) {
            throw new YqzException('模板ID不能为空');
        }

        try {
            // 使用乐观锁进行逻辑删除
            const result = await MyMongoDb.collections.bgw.dashboardTemplates.updateOne(
                {
                    _id: new ObjectId(templateId),
                    version: version // 只有当版本匹配时才更新
                },
                {
                    $set: {
                        deleteFlag: 'Y',
                        updateBy: managerId,
                        updateTime: new Date(),
                        version: version + 1 // 更新版本号
                    }
                }
            );

            if (result.matchedCount === 0) {
                throw new YqzException('版本冲突: 模板已被修改，请刷新后重试');
            }

            return result.modifiedCount > 0;
        } catch (error) {
            this.logger.error(`删除模板出错: ${error.message}`, error.stack);
            throw error instanceof YqzException ? error : new YqzException('删除模板失败');
        }
    }

    /**
     * 获取用户仪表盘设置
     * @param managerId 用户ID
     * @param companyId 公司ID
     * @returns 用户仪表盘设置
     */
    async getUserDashboardSettings(managerId: string, companyId?: string): Promise<DashboardTemplate.UserDashboardSettingsEntity> {
        if (!managerId) {
            throw new YqzException('用户ID不能为空');
        }

        try {
            const query: any = { managerId };
            
            if (companyId) {
                query.companyId = companyId;
            }
            
            const settings = await MyMongoDb.collections.bgw.userDashboardSettings.findOne(query);
            return settings;
        } catch (error) {
            this.logger.error(`获取用户仪表盘设置出错: ${error.message}`, error.stack);
            throw new YqzException('获取用户仪表盘设置失败');
        }
    }

    /**
     * 保存用户仪表盘设置
     * @param settingsData 用户仪表盘设置数据
     * @returns 保存结果
     */
    async saveUserDashboardSettings(settingsData: DashboardTemplate.UserDashboardSettingsEntity): Promise<string> {
        if (!settingsData.managerId) {
            throw new YqzException('用户ID不能为空');
        }

        try {
            settingsData.deleteFlag = Flag.N;
            // 插入数据库
            const result = await MyMongoDb.collections.bgw.userDashboardSettings.insertOne(settingsData);
            
            return result.insertedId.toString();
        } catch (error) {
            this.logger.error(`保存用户仪表盘设置出错: ${error.message}`, error.stack);
            throw new YqzException('保存用户仪表盘设置失败');
        }
    }

    /**
     * 更新用户仪表盘设置
     * @param settingsId 设置ID
     * @param updateData 更新数据
     * @returns 更新结果
     */
    async updateUserDashboardSettings(settingsId: string, updateData: Partial<DashboardTemplate.UserDashboardSettingsEntity>): Promise<boolean> {
        if (!settingsId) {
            throw new YqzException('设置ID不能为空');
        }

        try {
            const result = await MyMongoDb.collections.bgw.userDashboardSettings.updateOne(
                { _id: new ObjectId(settingsId) },
                { $set: updateData }
            );

            return result.modifiedCount > 0;
        } catch (error) {
            this.logger.error(`更新用户仪表盘设置出错: ${error.message}`, error.stack);
            throw new YqzException('更新用户仪表盘设置失败');
        }
    }

    /**
     * 查找使用指定模板的所有用户
     * @param companyId 公司ID
     * @param templateId 模板ID
     * @returns 使用该模板的用户列表
     */
    async findUsersWithTemplate(companyId: string, templateId: string): Promise<DashboardTemplate.UserDashboardSettingsEntity[]> {
        if (!companyId || !templateId) {
            throw new YqzException('公司ID和模板ID不能为空');
        }

        try {
            const users = await MyMongoDb.collections.bgw.userDashboardSettings.find({
                companyId,
                currentTemplateId: templateId
            }).toArray();

            return users;
        } catch (error) {
            this.logger.error(`查找使用模板的用户出错: ${error.message}`, error.stack);
            throw new YqzException('查找使用模板的用户失败');
        }
    }
} 