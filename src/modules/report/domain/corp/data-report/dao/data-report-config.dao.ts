import { Injectable } from "@nestjs/common";
import { DevTimeout, MyLogger, YqzException } from "@yqz/nest";
import { DateReportConfig } from "../dto/data-report-config.dto";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";

@Injectable()
export class DataReportDao {
    
    private readonly logger = new MyLogger(DataReportDao.name);

    constructor() { }

    async searchMenuConfig(params: {companyId: string}) {
        return await MyMongoDb.collections.bgw.dataReportMenuConfig.findOne({
            companyId: String(params.companyId),
        });
    }

    async updateMenuConfig(params: { companyId: string, columns: string[]}) {
        const { companyId, columns } = params;
        if (!companyId || !columns) throw new YqzException('必传参数不能为空');
        const result = await MyMongoDb.collections.bgw.dataReportMenuConfig.updateOne(
            { companyId: String(companyId) },
            { $set: { columns } },
            { upsert: true },
        );
        return result?.upsertedId;
    }

    async searchDataConfig(params: {companyId: string, module?: string}) {
        const { companyId, module } = params;
        const match = { companyId: String(companyId) };
        if (module) {
            match['module'] = module;
        }
        return await MyMongoDb.collections.bgw.dataReportDataConfig.find(match).toArray();
    }

    async updateDataConfig(params: { companyId: string, configs: any[]}) {
        const { companyId, configs } = params;
        if (!companyId || !(configs instanceof Array)) throw new YqzException('必传参数不能为空');

        // 创建批量操作
        const operations = configs.map(item => ({
            updateOne: {
                filter: { companyId: companyId, module: item.module }, // 根据 companyId 和 module 查找文档
                update: { $set: item }, // 如果存在，则覆盖更新
                upsert: true, // 没有则插入
            }
        }));
        const result = await MyMongoDb.collections.bgw.dataReportDataConfig.bulkWrite(operations);

        return result?.upsertedIds;
    }

    async updateDataColumnConfig(params: { companyId: string, columns: any[]}) {
        const { companyId, columns } = params;
        if (!companyId || !(columns instanceof Array)) throw new YqzException('必传参数不能为空');

        // 创建批量操作
        const operations = columns.map(item => ({
            updateOne: {
                filter: { companyId: companyId, key: item.key }, // 根据 companyId 和 key 查找文档
                update: { $set: item }, // 如果存在，则覆盖更新
                upsert: true, // 没有则插入
            }
        }));
        const result = await MyMongoDb.collections.bgw.dataReportColumnConfig.bulkWrite(operations);

        return result?.upsertedIds;
    }
}