import { Injectable } from "@nestjs/common";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { MyLogger, YqzException } from "@yqz/nest";
import { DateReport } from "../dto/data-report.dto";
import { DateReportBoard } from "../dto/data-report-board.dto";
import { ObjectId } from "mongodb";
import * as _ from "lodash";

/**
 * @summary 数据看板 - 布局
 * @class DataReportBoardDao
 */
@Injectable()
export class DataReportBoardDao {
    private readonly logger = new MyLogger(DataReportBoardDao.name);

    constructor() { }

    /**
     * 数据看板数据布局 新增布局
     * @param saveDto 
     * @returns 
     */
    async save(saveDto: DateReportBoard.DataReportBoardEntity): Promise<ObjectId> {
        if (!saveDto) return null;
        saveDto.createTime = saveDto?.createTime || new Date();
        const data = await MyMongoDb.collections.bgw.dataReportBoard.insertOne(saveDto);
        return data.insertedId;
    }

    /**
     * 数据看板数据布局 根据id查询单个
     * @param params 
     * @returns 
     */
    async findById(params: { boardId: string }): Promise<DateReportBoard.DataReportBoardEntity> {
        if (!params.boardId) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(params.boardId)) throw new YqzException('必传参数错误');
        const result = await MyMongoDb.collections.bgw.dataReportBoard.findOne({ _id: new ObjectId(params.boardId) });
        if (!result) return null;
        return { ...result, id: result?._id }
    }

    /**
     * 数据看板数据布局 查询
     * @param params 
     * @returns 
     */
    async findOne(params: { boardType: DateReportBoard.BoardType, companyId: string, managerId?: string, subject: DateReport.Subject }): Promise<DateReportBoard.DataReportBoardEntity> {
        if (!params.boardType || !params.companyId || !params.subject) throw new YqzException('必传参数不能为空');
        if (params.boardType === DateReportBoard.BoardType.Manager && !params.managerId) throw new YqzException('必传参数不能为空')
        const query = { companyId: params.companyId, subject: params.subject, boardType: params.boardType };
        if (params.boardType === DateReportBoard.BoardType.Manager && params.managerId) query['managerId'] = params.managerId;
        const result = await MyMongoDb.collections.bgw.dataReportBoard.findOne(query);
        if (!result) return null;
        return { ...result, id: result?._id }
    }

    /**
     * 数据看板数据布局 更新
     * @param boardId 
     * @param updateDto 
     */
    async update(boardId: string, updateDto: { blocks: DateReportBoard.Block[], updateBy?: string }): Promise<any> {
        if (!boardId || !updateDto) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(boardId)) throw new YqzException('必传参数错误');
        const set = {};
        if (updateDto.blocks) set['blocks'] = updateDto.blocks.map(item => ({ moduleId: new ObjectId(item.moduleId), gridColumn: item.gridColumn || '1' }));
        if (updateDto.updateBy) set['updateBy'] = updateDto.updateBy;
        if (_.isEmpty(set)) return;//无更新内容
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dataReportBoard.updateOne({ _id: new ObjectId(boardId) }, { $set: set });
    }

    /**
     * 数据看板数据布局 新增模块
     * @param param0 
     */
    async addModule({ boardId, moduleId, gridColumn, updateBy }: { boardId: string, moduleId: string, gridColumn: string, updateBy?: string }): Promise<any> {
        if (!boardId || !moduleId || !gridColumn) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(boardId) || !ObjectId.isValid(moduleId)) throw new YqzException('必传参数错误');
        const set = {};
        if (updateBy) set['updateBy'] = updateBy;
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dataReportBoard.updateOne({ _id: new ObjectId(boardId) }, { $set: set, $push: { blocks: { moduleId: new ObjectId(moduleId), gridColumn } } });
    }

    /**
     * 数据看板数据布局 删除模块
     * @param param0 
     * @returns 
     */
    async delModule({ boardId, moduleId, updateBy }: { boardId: string, moduleId: string, updateBy?: string }): Promise<any> {
        if (!boardId || !moduleId) throw new YqzException('必传参数不能为空');
        if (!ObjectId.isValid(boardId) || !ObjectId.isValid(moduleId)) throw new YqzException('必传参数错误');
        const set = {};
        if (updateBy) set['updateBy'] = updateBy;
        set['updateTime'] = new Date();
        return await MyMongoDb.collections.bgw.dataReportBoard.updateOne({ _id: new ObjectId(boardId) }, { $pull: { blocks: { moduleId: new ObjectId(moduleId) } }, $set: set });
    }

}