import { Injectable } from "@nestjs/common";
import { DataSource } from "typeorm";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { Sql } from "../../sql-generate/type/sql.type";

@Injectable()
export class CustomChartsDao {
  constructor(
    private readonly dataSource: DataSource,
  ) { }

  async getSqlConfigBySubjectAndTopic(params: { companyId: string, subject: DateReport.Subject, topic: string }): Promise<Sql.ColumnConfig[]> {
    return await MyMongoDb.collections.bgw.dataReportColumnConfig.find({
                companyId: params.companyId,
                subject: params.subject,
                topic: params.topic,
                deletion: {$ne: false}
            }).toArray();
  }

}