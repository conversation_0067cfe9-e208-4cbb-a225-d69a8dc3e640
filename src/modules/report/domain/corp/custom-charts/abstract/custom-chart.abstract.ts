
import * as DateFns from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YqzEx<PERSON> } from "@yqz/nest";
import * as _ from "lodash";
import { DateReportModule } from "../../data-report/dto/data-report-module.dto";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { SqlFactory } from "../../sql-generate/factory/sql-factory";
import { Sql } from "../../sql-generate/type/sql.type";
import { DateReportInterface } from "../../data-report/service/basic/dto/data-report.interface.dto";
import { DepartmentDao } from "../../../bgw/department/dao/department.dao";
import { CorpReport } from "../../corp-report/dto/corp-report.dto";
import { CustomChartType } from "../type/custom-chart.type";
import { generateDateList, parseDateString } from "@src/util/datetime.util";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { Chart } from "../../data-report/chart/chart.type";
import { DataReportConfigService } from "../../data-report/service/data-report-config.service";
import { DeviceDataReport } from "../../data-report/service/basic/dto/device-data-report.dto";
import { ProjectDateReport } from "../../data-report/service/basic/dto/project-data-report.dto";
import { DataReportModuleFilter } from "../../data-report/dto/data-report-module-filter.dto";


// 数据报表 抽象类
export abstract class CustomChart {
    public readonly logger = new MyLogger(CustomChart.name);
    public readonly sqlFactory: SqlFactory;
    public readonly departmentDao: DepartmentDao;
    public readonly dataReportConfigService: DataReportConfigService;

    constructor({ dataReportConfigService, sqlFactory, departmentDao }: { dataReportConfigService: DataReportConfigService, sqlFactory: SqlFactory, departmentDao: DepartmentDao }) {
        this.dataReportConfigService = dataReportConfigService;
        this.sqlFactory = sqlFactory;
        this.departmentDao = departmentDao;
    }

    getClassName(): string { return this.constructor.name; }

    //获取报告图表数据
    abstract getChartData(params: DateReportInterface.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes>;

    //获取图表必要筛选条件(处理topic筛选和sql筛选的映射关系)
    abstract getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string, subject: DateReport.Subject, topic: string, addFilters: Sql.Column[] }>;

    //获取数据范围选项映射关系
    abstract getFilterMap(topic?: string): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } };

    //数据分析筛选项
    abstract getAnalysisOption(params: DateReportInterface.AnalysisOptionReq): Promise<DateReportInterface.AnalysisOptionRes>;

    //数据分析列表
    abstract getAnalysisList(params: DateReportInterface.AnalysisListReq): Promise<{ total: number, items: any[] }>;

    //获取维度图表处理方法
    abstract getDimensionChartDataFunction(dimension: string): Function;

    //获取主题列表
    getTopics(subject: DateReport.Subject): { value: string; name: string; }[] {
        const topics = DateReport.SubjectTopics[subject];
        const result = [];
        if (_.isEmpty(topics)) return result;
        Object.keys(topics).map(key => {
            result.push({ value: topics[key]?.value, name: topics[key]?.name });
        })
        return result;
    }

    //获取默认布局模板
    getDefaultBoard(subject: DateReport.Subject, businessCategory: 'home_decor' | 'construction'): DateReportInterface.DefaultBoardRes {
        const DefaultBoard: DateReportInterface.DefaultBoardRes = { subject, blocks: [] };
        const topics = this.getTopics(subject);
        const topicDefaultDimension = DateReport.SubjectTopicDefaultDimension[subject];
        const dimensionCharts = DateReport.SubjectDimensionCharts[subject];
        for (const topic of topics) {
            const dimension = topicDefaultDimension[topic.value];// 默认维度
            if (_.isEmpty(dimension)) continue;
            const charts = dimensionCharts[dimension.value];
            if (_.isEmpty(charts)) continue;
            const chart = charts[0]; // 取第一个图表类型
            DefaultBoard.blocks.push({
                subject,
                gridColumn: dimension.gridColumn || '1',
                topic: topic.value,
                dimension: dimension.value,
                title: topic.name,
                chartType: chart.value
            });
        }
        // 过滤维度（家装公司不需要服务厂商分布&装修方式维度）
        if (businessCategory === 'home_decor') {
            DefaultBoard.blocks = DefaultBoard.blocks.filter(block => block.topic !== 'service_vendor_group' && block.topic !== 'decoration_model_group')
        }
        // 过滤维度（营建公司不需要业主相关）
        if (businessCategory === 'construction') {
            DefaultBoard.blocks = DefaultBoard.blocks.filter(block => block.topic !== 'owner_focus_project' && block.topic !== 'owner_negative_review_project')
        }
        return DefaultBoard;
    }

    //获取数据点筛选项
    async getSubjectOption(companyId: string, subject: DateReport.Subject, businessCategory: 'home_decor' | 'construction'): Promise<DateReportInterface.SubjectOptionRes[]> {
        // 获取主题列表
        const topics = this.getTopics(subject);
        const topicDimensions = DateReport.SubjectTopicDimensions[subject];
        const dimensionCharts = DateReport.SubjectDimensionCharts[subject];
        const topicOptions = topics.map((topic) => {
            const { value: topicValue, name: topicName } = topic;
            // 获取主题对应的维度
            const dimensions = (topicDimensions[topicValue] || []).map((dimension) => {
                const { value: dimensionValue, name: dimensionName } = dimension;
                // 获取维度对应的图表类型
                const charts = (dimensionCharts[dimensionValue] || []).map((chart) => { return { value: chart.value, name: chart.name, }; });
                return { value: dimensionValue, name: dimensionName, charts: charts };
            });
            // 过滤维度（家装公司不需要服务厂商分布&装修方式维度）
            let filteredDimensions = businessCategory === 'home_decor'
                ? dimensions.filter((dim) => dim.value !== 'service_vendor_group' && dim.value !== 'decoration_model_group')
                : dimensions;
            // 过滤关键岗位（暂不支持用个人看板查看关键岗位配置）
            filteredDimensions = filteredDimensions.filter(dim => dim.value != ProjectDateReport.Dimension.CustomKeyPosition1Group && dim.value != ProjectDateReport.Dimension.CustomKeyPosition2Group && dim.value != ProjectDateReport.Dimension.CustomKeyPosition3Group);
            return { value: topicValue, name: topicName, dimension: filteredDimensions };
        });
        // 获取主题名称
        for (const topic of topicOptions) {
            topic.name = await this.getTopicName({ companyId, topic: topic.value, subject });
        }
        // 组装主体
        const subjectOption = { name: DateReport.SubjectName[subject], value: subject, topic: topicOptions };
        return [{ subject: subjectOption }];
    }

    /**
     * 获取图表主题id列表(含时间、部门等筛选)
     * @param params 
     * @returns 默认返回类型为{ idList: string[] }
     *          设备主题返回类型为{ deviceIdList: string[], companyIdList: string[] }
     */
    async getTopicIdList(params: DateReportInterface.GetTopicIdListReq): Promise<{ idList: string[] } | { deviceIdList: string[], companyIdList: string[] }> {
        const { companyId, topic, from, to, dataType, departmentId } = params;
        if (!companyId || !topic || !from || !to || !dataType || !departmentId) throw new YqzException("必传参数不能为空");
        //查询图表筛选条件
        const filters = await this.getChartSqlFilters(params);
        //传了值，但是为空数组或undefined直接返回
        const values = (filters?.addFilters || []).map(item => item.value);
        if (values.some(value => value === undefined || (Array.isArray(value) && _.isEmpty(value)))) return { idList: [] };
        //创建sql
        const data = await this.sqlFactory.createSqlData(filters);
        //执行sql
        const result = await this.sqlFactory.executeSql(data.idSql, data.params);
        return { idList: _.union(result.map(item => item.id).filter(item => item)) };
    }

    /**
     * 获取主题名称
     * @param params 
     * @returns 
     */
    async getTopicName({ companyId, topic, subject }: { companyId: string, topic: string, subject: DateReport.Subject }): Promise<string> {
        const topics = DateReport.SubjectTopics[subject];
        if (_.isEmpty(topics)) return "";
        const res = topics[topic];
        if (!res) return "";
        //N天 替换为 公司设置的值
        if (subject === DateReport.Subject.Project) {
            //表单配置图表类型: 设备离线>=N天工地要把n替换为公司设置的值
            if (ProjectDateReport.Topic.InactiveProjectByDay === res?.value || ProjectDateReport.Topic.TodayNoWorkProjectByDay === res?.value) {
                const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Project });
                if (!_.isEmpty(config?.configs) && config?.configs[0]?.noworkProject) {
                    return res.name.replace('N天', `${config?.configs[0]?.noworkProject}天`);
                }
            }
        }
        if (subject === DateReport.Subject.Device) {
            //表单配置图表类型: 设备离线>=N天工地要把n替换为公司设置的值
            if (DeviceDataReport.Topic.OfflineDeviceByDay === res?.value || DeviceDataReport.Topic.CurrentOfflineDeviceByDay === res?.value) {
                const config = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
                if (!_.isEmpty(config?.configs) && config?.configs[0]?.offlineCondition) {
                    return res.name.replace('N天', `${config?.configs[0]?.offlineCondition}天`);
                }
            }
        }
        //默认返回主题名称
        return res.name;
    }

    /**
     * 获取主题维度
     * @param params 
     * @returns 
     */
    async getAnalysisDistributes(params: { companyId: string, topic: string, businessCategory: 'home_decor' | 'construction', subject: DateReport.Subject, dimension?: string }): Promise<{ name: string, value: string, gridColumn: string }[]> {
        const { companyId, topic, businessCategory, subject, dimension } = params;
        if (!companyId || !topic) return [];
        const topicDimensions = DateReport.SubjectTopicDimensions[subject];
        if (_.isEmpty(topicDimensions)) return [];
        let result = _.cloneDeep(topicDimensions[topic]);
        //如果传了维度，则过滤掉其他维度
        if (dimension) result = result.filter(item => item.value === dimension);
        if (_.isEmpty(result)) return [];
        //不需要 服务厂商分布 维度数据
        if (businessCategory === 'home_decor') result = result.filter(dimension => dimension.value !== 'service_vendor_group' && dimension.value !== 'decoration_model_group');
        //获取关键岗位配置
        if (result.some(item => item.value === ProjectDateReport.Dimension.CustomKeyPosition1Group || item.value === ProjectDateReport.Dimension.CustomKeyPosition2Group || item.value === ProjectDateReport.Dimension.CustomKeyPosition3Group)) {
            const matterSet = await AgileApi.searchMatterSet({ companyId });
            const projectKeyPositions = matterSet?.projectKeyPositions || [];
            if (_.isEmpty(projectKeyPositions)) return result;
            //过滤
            result = result.filter(item => {
                if (item.value === ProjectDateReport.Dimension.CustomKeyPosition1Group) {
                    const projectKeyPosition = projectKeyPositions.find(position => position.sort === 1);
                    if (!projectKeyPosition) return false;
                }
                if (item.value === ProjectDateReport.Dimension.CustomKeyPosition2Group) {
                    const projectKeyPosition = projectKeyPositions.find(position => position.sort === 2);
                    if (!projectKeyPosition) return false;
                }
                if (item.value === ProjectDateReport.Dimension.CustomKeyPosition3Group) {
                    const projectKeyPosition = projectKeyPositions.find(position => position.sort === 3);
                    if (!projectKeyPosition) return false;
                }
                return true;
            });
            //替换name
            result = result.map(item => {
                if (item.value === ProjectDateReport.Dimension.CustomKeyPosition1Group) {
                    const projectKeyPosition = projectKeyPositions.find(position => position.sort === 1);
                    return { ...item, name: `${projectKeyPosition?.name}分布` };
                }
                if (item.value === ProjectDateReport.Dimension.CustomKeyPosition2Group) {
                    const projectKeyPosition = projectKeyPositions.find(position => position.sort === 2);
                    return { ...item, name: `${projectKeyPosition?.name}分布` };
                }
                if (item.value === ProjectDateReport.Dimension.CustomKeyPosition3Group) {
                    const projectKeyPosition = projectKeyPositions.find(position => position.sort === 3);
                    return { ...item, name: `${projectKeyPosition?.name}分布` };
                }
                return item;
            });
        }
        return result;
    }

    /**
     * 获取主题维度(表单类型)
     * @param params 
     * @returns 
     */
    async getAnalysisFromDistributes(params: { companyId: string, topic: string, businessCategory: 'home_decor' | 'construction', subject: DateReport.Subject, dimension?: string }): Promise<{ name: string, value: string, gridColumn: string }[]> {
        const { companyId, topic, businessCategory, subject, dimension } = params;
        const result = [];
        if (!topic) return result;
        const dimensions = await this.getAnalysisDistributes({ companyId, topic, businessCategory, subject, dimension });
        if (_.isEmpty(dimensions)) return result;
        return dimensions.filter(dimension => {//过滤掉没有表单的维度
            const dimensionCharts = DateReport.SubjectDimensionCharts[subject];
            if (_.isEmpty(dimensionCharts[dimension.value])) return false;
            const chartType = dimensionCharts[dimension.value][0]?.value;
            if (!chartType) return false;
            if ((Chart.TypeOption[Chart.Type.DistributeChart] || []).includes(chartType)) return true;
            return false;
        });
    }

    //数据分析分布图
    async getAnalysisDistributeCharts(params: DateReportInterface.AnalysisDistributeReq): Promise<DateReportModule.DistributeDataModuleRes[]> {
        const { companyId, topic, businessCategory, subject } = params;
        const result: DateReportModule.DistributeDataModuleRes[] = [];
        if (!companyId || !topic) return result;
        //获取数据点id列表
        const topicIdRes = await this.getTopicIdList(params);
        //查询topic下 需要分析的维度 & 图表类型 & 图表排列顺序
        const promiseList = [];
        const dimensions = await this.getAnalysisDistributes({ companyId, topic, subject, businessCategory });
        if (_.isEmpty(dimensions)) return result;
        const dimensionCharts = DateReport.SubjectDimensionCharts[subject];
        for (const dimension of dimensions) {
            const method = this.getDimensionChartDataFunction(dimension.value);
            if (!method) continue;
            if (_.isEmpty(dimensionCharts[dimension.value])) continue;
            const chartType = dimensionCharts[dimension.value][0]?.value;//默认图表类型
            if (!chartType) continue;
            //家装公司 不需要 服务厂商分布 维度数据
            if (businessCategory === 'home_decor' && (dimension.value === "service_vendor_group" || dimension.value === "decoration_model_group")) continue;
            //分布数据actionType为form
            const actionType = (Chart.TypeOption[Chart.Type.DistributeChart] || []).includes(chartType) ? Chart.ActionType.Form : Chart.ActionType.None;
            promiseList.push(method({ ...params, ...topicIdRes, chartType, actionType }));
        }
        const promiseResult = await Promise.all(promiseList);
        promiseResult.map(res => { if (!_.isEmpty(res)) result.push(res) });
        return result;
    }

    //数据分析分布图详情列表
    @CheckFormatCsv(DateReportModule.ModuleAnalysisDistributeDetailCsv)
    async getAnalysisDistributeDetail(params: DateReportInterface.AnalysisDetailReq): Promise<{ dimension: string, total: number, items: DateReportModule.DataModuleAnalysisDistributeDetailRes[] }> {
        const { companyId, subject, topic, dimension } = params;
        if (!companyId || !topic) return { dimension, total: 0, items: [] };
        const result = await this.getBasicAnalysisDistributeDetail(params);
        //非实时数据要计算每个数据点的环比
        const isReal = (DateReport.SubjectRealTopics[subject] || []).includes(topic) ? "Y" : "N";
        if ('Y' === isReal) return result;
        //获取上一个周期的开始时间和结束时间
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from: params.from, to: params.to, dataType: params.dataType, cycle: 2 });
        //获取上一个周期的数据
        const beforeResult = await this.getBasicAnalysisDistributeDetail({ ...params, from: timeFrameList[0].startTime, to: timeFrameList[0].endTime });
        const beforeData = beforeResult.items;
        const beforeDataMap = _.keyBy(beforeData, 'key');
        //计算每个value的环比
        result.items.forEach(item => {
            const beforeItem = beforeDataMap[item.key];
            item.ratio = beforeItem && beforeItem?.value !== 0 ? String(Math.round((item.value - beforeItem.value) / beforeItem.value * 100)) : '--';
            return item;
        });
        return result;
    }

    private async getBasicAnalysisDistributeDetail(params: DateReportInterface.AnalysisDetailReq): Promise<{ dimension: string, total: number, items: DateReportModule.DataModuleAnalysisDistributeDetailRes[] }> {
        const { companyId, subject, topic, dimension } = params;
        const result: { dimension: string, total: number; items: DateReportModule.DataModuleAnalysisDistributeDetailRes[]; } = { dimension, total: 0, items: [] };
        if (!companyId || !topic) return result;
        //只有分布图才有详情
        const dimensionCharts = DateReport.SubjectDimensionCharts[subject];
        const chartType = dimensionCharts[dimension][0]?.value;
        if (!(Chart.TypeOption[Chart.Type.DistributeChart] || []).includes(chartType)) return result;
        //获取数据点id列表
        const topicIdRes = await this.getTopicIdList(params);
        //查询数据
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new YqzException(`维度类型不支持`);
        const detectionRes = await method({ ...params, ...topicIdRes, chartType: Chart.ChartType.Ring });
        const data = (detectionRes?.data as Chart.ModulePieDataRes)?.config?.data;
        if (!data) return result;
        //组装数据
        result.total = data.length;
        result.items = _.sortBy(data.map(item => {
            return {
                key: item.key,
                name: item.name,
                value: item.value,
                rate: item.rate
            }
        }), 'value').reverse();
        return result;
    }

    /**
     * 查询直属子部门id（时间范围原则左闭右开）（结果不包含自己）
     * @param param0 
     * @returns 
     */
    private async getDirectChildDeptIdList({ departmentId, deptDataType = 'real_time', startTime, endTime }: { departmentId: string, deptDataType: 'real_time' | 'history', startTime: string, endTime: string }) {
        this.logger.log('getDirectChildDeptIdList params: ', { departmentId, deptDataType, startTime, endTime });
        //实时部门数据
        if (deptDataType === 'real_time') return await this.getRealTimeDirectChildDeptIdList(departmentId);
        if (deptDataType === 'history') {
            const departmentIdList: string[] = [];
            const dimDeptList = await this.departmentDao.searchEtl({ departmentIdList: [departmentId], startTime: DateFns.format(parseDateString(startTime), 'yyyyMMdd'), endTime: DateFns.format(parseDateString(endTime), 'yyyyMMdd') });
            dimDeptList.forEach((dimDept) => {
                //直接子部门(不含自己)
                const directDepartmentIdList = dimDept?.directChildDepartmentIdList && '' !== dimDept.directChildDepartmentIdList ? dimDept.directChildDepartmentIdList.split(',') : [];
                departmentIdList.push(...directDepartmentIdList);
            });
            return _.union(departmentIdList);
        }
    }

    /**
     * 根据departmentId查询所有子部门id列表（结果包含自己）（时间范围原则左闭右开）
     * @param departmentId 部门id
     * @param deptDateType 查询部门切片类型(real_time实时、history历史)，默认：real_time实时
     * @param startTime 开始时间 格式：yyyy-MM-dd
     * @param endTime 结束时间 格式：yyyy-MM-dd
     */
    async getDeptIdList({ departmentId, deptDataType = 'real_time', startTime, endTime }: { departmentId: string, deptDataType: 'real_time' | 'history', startTime: string, endTime: string }) {
        //1、查询实时 本部门 及 子部门id（不校验删除、集团关联公司查询公司所有部门、包含自己）
        if (deptDataType === 'real_time') return await this.getRealTimeDeptIdList(departmentId);
        //2、查询历史 本部门 及 子部门id（不校验删除、包含自己）
        if (deptDataType === 'history') return await this.getHistoryDeptIdList(departmentId, startTime, endTime);
    }

    /**
     * 根据departmentId查询部门id切片数据列表（时间范围原则左闭右开）
     * @param departmentId 部门id
     * @param deptDateType 查询部门切片类型(real_time实时、history历史)，默认：real_time实时
     * @param startTime 开始时间 格式：yyyy-MM-dd
     * @param endTime 结束时间 格式：yyyy-MM-dd
     * @param etlDataKey 需要查询的数据key [projectIdList、managerIdList、deviceIdList、aiProjectIdList]
     * @returns departmentIdList 子部门（包含自己）
     * @returns directChildDepartmentIdList 直接子部门(不含自己)
     */
    async getDeptEtlDateList({ departmentId, deptDataType = 'real_time', startTime, endTime, etlDataKey }: { departmentId: string, deptDataType: 'real_time' | 'history', startTime: string, endTime: string, etlDataKey: CustomChartType.EtlDataKey[] }): Promise<CorpReport.DimDeptData> {
        this.logger.log('getDeptEtlDateList params: ', { departmentId, deptDataType, startTime, endTime, etlDataKey });
        const result: CorpReport.DimDeptData = { departmentIdList: [], companyIdList: [], directChildDepartmentIdList: [] };
        //1、查询部门 & 直属子部门
        if (deptDataType === 'real_time') {//实时部门数据
            //所有部门 + 自己
            result.departmentIdList = await this.getRealTimeDeptIdList(departmentId);
            //直接子部门
            result.directChildDepartmentIdList = await this.getRealTimeDirectChildDeptIdList(departmentId);
        }
        const historyDeptIdDateMap = {};//记录时间和当时切片部门id列表的关系(key:时间，value:当时切片部门id列表)
        if (deptDataType === 'history') {//切片部门数据
            const dimDeptList = await this.departmentDao.searchEtl({ departmentIdList: [departmentId], startTime: DateFns.format(parseDateString(startTime), 'yyyyMMdd'), endTime: DateFns.format(parseDateString(endTime), 'yyyyMMdd') });
            result.departmentIdList.push(departmentId);//子部门（包含自己）
            dimDeptList.forEach((dimDept) => {
                //子部门（包含自己）
                const deptIdList = dimDept?.childDepartmentIdList && '' !== dimDept.childDepartmentIdList ? dimDept.childDepartmentIdList.split(',') : [];
                result.departmentIdList.push(...deptIdList);
                result.departmentIdList = _.union(result.departmentIdList);
                //直接子部门(不含自己)
                const directDepartmentIdList = dimDept?.directChildDepartmentIdList && '' !== dimDept.directChildDepartmentIdList ? dimDept.directChildDepartmentIdList.split(',') : [];
                result.directChildDepartmentIdList.push(...directDepartmentIdList);
                result.directChildDepartmentIdList = _.union(result.directChildDepartmentIdList);
                //记录时间和当时切片部门id列表的关系(key:时间，value:当时切片部门id列表)
                const deptIds = [dimDept.departmentId];//包含自己
                deptIds.push(...deptIdList);//子部门
                historyDeptIdDateMap[dimDept.date] = _.union(deptIds);
            });
        }
        //2、查询所有部门关联公司信息
        const companyList = await this.departmentDao.searchCompanyByDept({ deptIds: result.departmentIdList });
        result.companyIdList = companyList.map((company) => company.companyId);
        //没有需要查询的数据key，直接返回
        if (_.isEmpty(etlDataKey)) return result;
        //3、查询部门下的数据
        const promiseList = [];// 收集所有查询任务
        const dateList = generateDateList({ startTime, endTime });
        for (const date of dateList) {
            const departmentIdList = deptDataType === 'real_time' ? result.departmentIdList : historyDeptIdDateMap[date];
            if (_.isEmpty(departmentIdList)) continue;
            promiseList.push(this.departmentDao.searchEtlDataList({ date, departmentIdList, selectedFields: etlDataKey }));
        }
        const allDimDeptDataLists = await Promise.all(promiseList);
        //合并结果
        allDimDeptDataLists.forEach((dimDeptDataList) => {
            for (const key of etlDataKey) {
                const list: string[] = [];
                dimDeptDataList.map((data) => { if (data[key] && data[key] !== '') list.push(...String(data[key]).split(',')); });
                result[key] = _.union(result[key], list); // 去重
            }
        });
        return result;
    }

    /**
     * 查询当前部门下 -> 直属子部门 -> 所有子部门(包含自己)
     * 结构：{
     *    直属子部门id: 直属子部门下子部门idList(包含直属子部门id自己),
     *    直属子部门id: 直属子部门下子部门idList(包含直属子部门id自己)
     * }
     * 时间范围原则左闭右开
     * @param param0 
     */
    async getCurrentDeptDirectChildDeptChildDeptIdMap({ departmentId, deptDataType = CustomChartType.DeptDataType.RealTime, startTime, endTime }: { departmentId: string, deptDataType: CustomChartType.DeptDataType, startTime: string, endTime: string }) {
        this.logger.log('getCurrentDeptDirectChildDeptChildDeptList params: ', { departmentId, deptDataType, startTime, endTime });
        const result: Record<string, { departmentId: string, departmentName: string, linkDepartmentId: string, childDeptIdList: string[] }> = {};
        const directChildDeptIdList = await this.getDirectChildDeptIdList({ departmentId, deptDataType, startTime, endTime });
        if (_.isEmpty(directChildDeptIdList)) return result;
        const directChildDeptInfoList = await this.departmentDao.getDepartmentInfoList({ departmentIds: directChildDeptIdList });
        const directChildDeptNameMap = {};
        directChildDeptInfoList.forEach((dept) => { directChildDeptNameMap[dept.departmentId] = dept });
        // 使用 Promise.all 并行查询每个直属子部门的子部门列表
        const promiseList = directChildDeptIdList.map(async (directChildDeptId) => {
            const childDeptIdList = await this.getDeptIdList({ departmentId: directChildDeptId, deptDataType, startTime, endTime });
            return { directChildDeptId, childDeptIdList };
        });
        // 等待所有查询完成
        const results = await Promise.all(promiseList);
        // 构建结果
        results.forEach(({ directChildDeptId, childDeptIdList }) => {
            result[directChildDeptId] = {
                departmentId: directChildDeptId,
                departmentName: directChildDeptNameMap[directChildDeptId]?.departmentName || "",
                linkDepartmentId: directChildDeptNameMap[directChildDeptId]?.linkDepartmentId || "",
                childDeptIdList
            }
        });
        return result;
    }

    /**
     * 查询实时 直属子部门id列表
     * @param param0 
     */
    private async getRealTimeDirectChildDeptIdList(departmentId: string) {
        const departmentIdList: string[] = [];
        //查询当前部门
        const dept = await this.departmentDao.getDepartmentInfo({ departmentId });
        if (!dept) return departmentIdList;
        //当前部门是集团公司部门, 查询关联公司的直接子部门信息
        if (dept?.type === 'division' && dept?.linkCompanyId) {
            const rootDept = await this.departmentDao.getRootDepartment({ companyId: dept?.linkCompanyId });
            const directChildDepartment = await this.departmentDao.getDirectChildDepartmentList({ departmentId: rootDept.departmentId });
            directChildDepartment.map(res => departmentIdList.push(res.departmentId));
        } else {
            const directChildDepartment = await this.departmentDao.getDirectChildDepartmentList({ departmentId });
            directChildDepartment.map(res => departmentIdList.push(res.departmentId));
        }
        return _.uniq(departmentIdList);
    }

    /**
     * 查询实时 本部门 及 子部门id（不校验删除、集团关联公司查询公司所有部门、包含自己）
     * @param departmentId 
     * @returns 
     */
    private async getRealTimeDeptIdList(departmentId: string) {
        const departmentIdList: string[] = [];
        const divisionCompanyIdList: string[] = [];//记录集团部门关联的公司id，实时查询要查询到集团关联公司下的所有部门
        //查询当前部门
        const dept = await this.departmentDao.searchDeptInfoList({ deptId: departmentId, excludeDel: true });
        dept.map(res => {
            departmentIdList.push(res.departmentId);//包含自己
            //查询集团部门关联公司的公司id
            if (res?.type === 'division' && res?.linkCompanyId) { divisionCompanyIdList.push(res?.linkCompanyId); }
        });
        //查询子部门
        const deptList = await this.departmentDao.searchDeptInfoList({ companyId: dept[0]?.companyId, includeDir: departmentId, excludeDel: true });
        deptList.map(res => {
            departmentIdList.push(res.departmentId);
            //查询集团部门关联公司的公司id
            if (res?.type === 'division' && res?.linkCompanyId) { divisionCompanyIdList.push(res?.linkCompanyId); }
        });
        //查询集团关联公司下的所有部门（不排除掉根部好像也没关系，数据后面会去重掉）
        if (!_.isEmpty(divisionCompanyIdList)) {
            const divisionDept = await this.departmentDao.searchDeptInfoList({ companyIds: divisionCompanyIdList, excludeDel: true });
            divisionDept.map(res => departmentIdList.push(res.departmentId));
        }
        return _.uniq(departmentIdList);
    }

    /**
     * 查询历史 本部门 及 子部门id（不校验删除、包含自己）（时间范围原则左闭右开）
     * @param departmentId 
     * @param startTime 
     * @param endTime 
     * @returns 
     */
    private async getHistoryDeptIdList(departmentId: string, startTime: string, endTime: string) {
        startTime = DateFns.format(parseDateString(startTime), 'yyyyMMdd');
        endTime = DateFns.format(parseDateString(endTime), 'yyyyMMdd');
        const departmentIdList: string[] = [];
        const dimDeptList = await this.departmentDao.searchEtl({ departmentIdList: [departmentId], startTime, endTime });
        departmentIdList.push(departmentId);//包含自己;
        for (const dimDept of dimDeptList) {
            const deptIdList = dimDept?.childDepartmentIdList && '' !== dimDept.childDepartmentIdList ? dimDept.childDepartmentIdList.split(',') : [];
            departmentIdList.push(...deptIdList);
        }
        const linkCompanyDeptIdList = await this.departmentDao.searchLinkCompanyDeptId({ departmentIds: departmentIdList });
        linkCompanyDeptIdList.map(res => departmentIdList.push(res.linkDepartmentId));
        return _.uniq(departmentIdList);
    }

    /**
     * 处理部门分组，只展示到子部门
     * @param params 
     * @returns 
     */
    async handelDepartmentGroup(params: { deptDataType: CustomChartType.DeptDataType, currentDeptId: string, from: string, to: string, data: { count: string; departmentId: string; departmentName: string; }[] }) {
        const result: { key: string, name: string, value: number }[] = [];
        const { deptDataType, data, currentDeptId, from, to } = params;
        if (!currentDeptId || !from || !to || _.isEmpty(data)) return result;
        const departmentDataMap = _.keyBy(data, 'departmentId');
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        //查询直接子部门和其下属部门id映射
        const directChildDeptIdMap = await this.getCurrentDeptDirectChildDeptChildDeptIdMap({ departmentId: currentDeptId, startTime: timeFrame.startTime, endTime: timeFrame.endTime, deptDataType });
        //本部门数据
        const dept = await this.departmentDao.getDepartmentInfo({ departmentId: currentDeptId });
        const currentDept = departmentDataMap[dept?.linkDepartmentId || dept?.departmentId]
        if (Number(currentDept?.count || 0) > 0) result.push({ key: dept.departmentId, name: dept.departmentName || '', value: Number(currentDept?.count) });
        //直接子部门数据
        for (const directChildDeptId of Object.keys(directChildDeptIdMap)) {
            const directChildDept = directChildDeptIdMap[directChildDeptId];
            if (!directChildDept) continue;
            const childDeptIdList = directChildDeptIdMap[directChildDeptId]?.childDeptIdList;
            if (_.isEmpty(childDeptIdList)) continue;
            let count = 0;
            childDeptIdList.forEach(childDeptId => { count += Number(departmentDataMap[childDeptId]?.count || 0); });
            if (count === 0) continue;
            result.push({ key: directChildDeptId, name: directChildDept?.departmentName || '', value: count });
        }
        return result;
    }

    //处理图表数据范围筛选参数
    async handelGetChartDataParams<T extends DateReportInterface.ModuleReq>(params: T) {
        if (_.isEmpty(params?.filters)) return params;
        const addFilters = DataReportModuleFilter.getChartModuleFilters(params.filters);
        delete params.filters;
        params = { ...params, ...addFilters } as T;
        return params;
    }

    /**
     * 获取数据模块-数据范围 筛选项
     * @param params 
     * @returns 
     */
    async getFilterOption(params: DateReportInterface.FilterOptionReq): Promise<DateReportInterface.FilterOptionRes[]> {
        const { companyId, managerId, topic } = params;
        if (!companyId || !topic) return [];
        //获取数据分析筛选项
        const analysisOption = await this.getAnalysisOption(params);
        const filterOption: DateReportInterface.FilterOptionRes[] = [];
        //获取数据范围选项映射关系
        const filterMap = this.getFilterMap(topic);
        if (_.isEmpty(filterMap)) return filterOption;
        for (const key in analysisOption) {
            const filter = filterMap[key];
            if (!filter || null == analysisOption[key] || undefined == analysisOption[key]) continue;
            filterOption.push({
                filterName: filter.filterName,
                filterField: filter.filterField,
                filterType: filter.filterType,
                filterValue: analysisOption[key]
            });
        }
        //是否需要查询部门归属
        const departmentFilter = filterMap["department"];
        if (departmentFilter) {
            const filterValue = await AgileApi.orgHirachySearch({ companyId, managerId, listMembers: 'N', deep: "Y", checkRole: "" });
            filterOption.push({
                filterName: departmentFilter?.filterName,
                filterField: departmentFilter?.filterField,
                filterType: departmentFilter?.filterType,
                filterValue: this.transformToSelectTree(filterValue)
            });
        }
        //获取filterMap的filterField字段顺序
        const filterFieldOrder = Object.values(filterMap).map(item => item.filterField);
        //根据filterFieldOrder对filterOption进行排序
        filterOption.sort((a, b) => {
            if (!a.filterField || filterFieldOrder.indexOf(a.filterField) === -1) return 1;
            if (!b.filterField || filterFieldOrder.indexOf(b.filterField) === -1) return -1;
            return filterFieldOrder.indexOf(a.filterField) - filterFieldOrder.indexOf(b.filterField);
        });
        return filterOption;
    }

    /**
     * 将原始树形数据 转换为 所需树形结构
     * @param node 
     * @returns 
     */
    private transformToSelectTree(node: any): DataReportModuleFilter.SelectTreeFilterValue {
        return {
            name: node.name,
            value: node.id,
            adminAccess: node.adminAccess || 'N',
            children: (node?.children || []).map(res => this.transformToSelectTree(res)),
        };
    }

}