export namespace CustomChartType {

    export enum DeptDataType {
        RealTime = "real_time",
        History = "history"
    }

    export enum EtlDataKey {
        ProjectIdList = 'projectIdList',
        ManagerIdList = 'managerIdList',
        DeviceIdList = 'deviceIdList',
        AiProjectIdList = 'aiProjectIdList'
    }

    //枚举类对应的sql字段
    export const EtlDataSqlKey: Readonly<{ [K in EtlDataKey]: string }> = {
        [EtlDataKey.ProjectIdList]: 'project_id_list',
        [EtlDataKey.ManagerIdList]: 'manager_id_list',
        [EtlDataKey.DeviceIdList]: 'device_id_list',
        [EtlDataKey.AiProjectIdList]: 'ai_project_id_list'
    }

}