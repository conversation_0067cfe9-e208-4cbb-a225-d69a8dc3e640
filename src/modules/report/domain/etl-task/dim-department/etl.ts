import { Injectable } from "@nestjs/common";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@yqz/nest";
import * as _ from "lodash";
import { DepartmentMemberDao } from "../../bgw/department/dao/department-member.dao";
import { DepartmentDao } from "../../bgw/department/dao/department.dao";
import { DimDepartmentDao } from "../../etl/dao/dim-department.dao";
import { DepartmentService } from "../../bgw/department/service/department.service";
import { DepartmentEtl } from "../../bgw/department/types/department-etl.type";
import { DepartmentEtlUtil } from "./util/department-etl.util";
import { ProjectDao } from "../../bgw/project/dao/project.dao";
import { Project } from "../../bgw/project/dto/project.dto";
import { DeviceDao } from "../../camera/device/dao/device.dao";
import { <PERSON>tl } from "../type";
import * as DateFns from "date-fns";
import { TimeUtil } from "@src/util";
import { Brackets, DataSource } from "typeorm";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { CompanyProductVersionEntity } from "@src/modules/report/entity/bgw/company-product-version.entity";
import { CompanyDecorationEntity } from "@src/modules/report/entity/bgw/company-decoration.entity";
import { DimDepartmentDataDao } from "../../etl/dao/dim-department-data.dao";
import { DimDepartmentDataEntity } from "@src/modules/report/entity/etl/dim-department-data.entity";
import { DepartmentMemberEntity } from "@src/modules/report/entity/bgw/department-member.entity";

@Injectable()
export class DimDepartmentEtl {
  private readonly logger = new MyLogger(DimDepartmentEtl.name)
  constructor(
    private readonly datasource: DataSource,
    private readonly departmentDao: DepartmentDao,
    private readonly departmentService: DepartmentService,
    private readonly dimDepartmentDao: DimDepartmentDao,
    private readonly departmentMemberDao: DepartmentMemberDao,
    private readonly projectDao: ProjectDao,
    private readonly deviceDao: DeviceDao,
    private readonly dimDepartmentDataDao: DimDepartmentDataDao
  ) { }

  @DevTimeout(500)
  private async test() {
    // this.etlCompany({ companyId: '65', date: '20230810' }).then(res => {
    //   this.logger.log(res)
    // })
    // const deviceStatusList = await this.deviceDao.searchDeviceStatus({ companyId:"65" });
    // const screenList = deviceStatusList.items.map(res => {
    //   return {
    //     yqzDeviceId: res?.yqzDeviceId ? String(res.yqzDeviceId) : null,
    //     departmentId: res?.departmentId ? String(res.departmentId) : null,
    //     projectManagerId: res?.managerId ? String(res.managerId) : null
    //   }
    // });
    // const yqzDeviceIds = await AgileApi.screenDeviceListByDeptId({ departmentId: "8233", list: screenList });
    // console.log(yqzDeviceIds)
    // this.extractDepartmentData({level:3, companyId:'65',  departmentId:'12423'}).then(res=>{
    //   this.logger.log(res)
    // })
    // this.etlDepartment( {departmentId:'13', companyId:'65', level:0}, '20221210').then(res=>{
    //   this.logger.log(res)
    // })
    // this.etlCompany({companyId:'65', date:'20221216'}).then((res)=>{
    //   this.logger.log('done', res)
    // })
    // this.etl({step:30}).then(res=>{
    //   this.logger.log(res)
    // })
    // this.searchCompanyIdList({hasDivisionDept:false}).then(res=>{
    //   this.logger.log(res)
    // })

    // this.etl().then(res=>{
    //   this.logger.log(res)
    // })
    // await this.divisionDataInitialize();
    // await this.dataInitialize();
    // console.log("end")

    // let date = new Date('2024-07-16');
    // console.log(111)
    // while (true) {
    //   console.log(222)
    //   const dim_deps = await this.dimDepartmentDao.search({ date: DateFns.format(date, 'yyyyMMdd') });
    //   console.log(dim_deps.length)
    //   if (_.isEmpty(dim_deps)) break;
    //   console.log('111')
    //   for (const dim_dep of dim_deps) {
    //     const companyId = dim_dep.companyId;
    //     const deptId = dim_dep.departmentId;
    //     const deviceList = (dim_dep?.deviceIdList || '').split(',');
    //     const devices = await this.deviceDao.device({ companyId, departmentId: deptId, date: DateFns.format(date, 'yyyyMMdd') });
    //     const deviceIds = devices.map(res => String(res.deviceId));
    //     const newIds = _.difference(deviceIds, deviceList).join(',');
    //     if (!_.isEmpty(newIds)) {
    //       dim_dep.deviceIdList = dim_dep?.deviceIdList ? `${dim_dep.deviceIdList},${newIds}` : newIds;
    //       console.log(companyId, deptId, dim_dep.deviceIdList.split(',').length);
    //       await this.dimDepartmentDao.save(dim_dep);
    //     }
    //   }
    //   console.log(DateFns.format(date, 'yyyyMMdd') + 'done')
    //   date = DateFns.subDays(date, 1);
    // }
    // console.log("end")

  }

  private async divisionDataInitialize() {
    const companyIds = ["*********","*********","*********","*********","*********","*********","*********"];//集团公司id
    for (const companyId of companyIds) {
      console.log(companyId);
      const depts = await this.departmentDao.searchDeptInfoList({ companyId });
      const startDate = '20230104';//最小的日期（切片最早一天减一）
      const endDate = '20240630';//最大的日期(不包含dim_department_data最早一天)

      const startTime = DateFns.parse(startDate, 'yyyyMMdd', new Date());
      const endTime = DateFns.parse(endDate, 'yyyyMMdd', new Date());
      let diff = DateFns.differenceInDays(endTime, startTime);
      diff = diff + 1;
      console.log(depts);
      for (const dept of depts) {
        for (let i = 0; i < diff; i++) {
          const date = TimeUtil.formatDate(DateFns.subDays(DateFns.parse(endDate, 'yyyyMMdd', new Date()), i), 'yyyyMMdd');
          console.log(`${date},${dept.departmentId} start`);
          if (date == startDate) break;
          const dimDepts = await this.dimDepartmentDao.searchByDate({ date, departmentIdList: [dept.departmentId] });
          if (_.isEmpty(dimDepts)) continue;
          const dimDept = dimDepts[0];

          //查询部门下的直属子部门（删除的部门不生成切片，那直接子部门应该也校验删除）
          const parents = await this.departmentDao.searchDeptInfoList({ deptId: dimDept.departmentId });
          const parent = parents[0];
          const parentPath = parent.dir ? [parent.dir, parent.departmentId].join(',') : parent.departmentId;
          const directChildDeptList = await this.departmentDao.searchDeptInfoList({ companyId: parent.companyId, dir: parentPath, excludeDel: true });
          const directChildDepartmentIdList = directChildDeptList.map(e => e.departmentId);//所有直属子部门id
          dimDept.directChildDepartmentIdList = directChildDepartmentIdList.join(',');

          //查询所有子部门
          const childDeptList = await this.departmentDao.searchDeptInfoList({ companyId: dept.companyId, includeDir: dept.departmentId });
          if (_.isEmpty(childDeptList)) continue;
          const childDeptId = childDeptList.map(res => res.departmentId);
          const dimChildDepts = await this.dimDepartmentDao.searchByDate({ date, departmentIdList: childDeptId });
          if (_.isEmpty(dimChildDepts)) continue;
          const dimChildDeptIds = [];
          dimChildDepts.map(res => {
            if (res.childDepartmentIdList && "" !== res.childDepartmentIdList) {
              dimChildDeptIds.push(...res.childDepartmentIdList.split(","));
            }
          });
          if (dimDept.directChildDepartmentIdList && "" !== dimDept.directChildDepartmentIdList) dimChildDeptIds.push(...dimDept.directChildDepartmentIdList.split(","));
          dimDept.childDepartmentIdList = _.union(dimChildDeptIds).join(",");
          console.log("childDepartmentIdList",dimDept.childDepartmentIdList)
          console.log("directChildDepartmentIdList", dimDept.directChildDepartmentIdList)
          await this.dimDepartmentDao.save(dimDept);
        }
      }
    }
    console.log('done')
  }
  
  /**
   * 数据初始化(dev部门测试的时候改过顺序，数据可能存在误差)
   */
  // private async dataInitialize() {
  //   const startDate = '20230104';//最小的日期（切片最早一天减一）
  //   // const endDate = '20240602';//最大的日期(不包含dim_department_data最早一天)
  //   // const endDate = '20240514';//最大的日期(不包含dim_department_data最早一天)
  //   // const endDate = '20240302';//最大的日期(不包含dim_department_data最早一天)
  //   // const endDate = '20240120';//最大的日期(不包含dim_department_data最早一天)
  //   // const endDate = '20231224';//最大的日期(不包含dim_department_data最早一天)
  //   const endDate = '20231211';//最大的日期(不包含dim_department_data最早一天)
  //   const startTime = DateFns.parse(startDate, 'yyyyMMdd', new Date());
  //   const endTime = DateFns.parse(endDate, 'yyyyMMdd', new Date());
  //   let diff = DateFns.differenceInDays(endTime, startTime);
  //   diff = diff + 1;


  //   for (let i = 0; i < diff; i++) {
  //     const date = TimeUtil.formatDate(DateFns.subDays(DateFns.parse(endDate, 'yyyyMMdd', new Date()), i), 'yyyyMMdd');
  //     // const date = TimeUtil.formatDate(DateFns.addDays(DateFns.parse(startDate, 'yyyyMMdd', new Date()), i), 'yyyyMMdd');
  //     console.log(`${date} start`);
  //     if (date == startDate) break;
  //     const dimDeptList = await this.dimDepartmentDao.searchByDate({ date });
  //     const dimDeptMap: { [key in string]: { managerIdList: string[], deviceIdList: string[], aiProjectIdList: string[] } } = {};
  //     dimDeptList.map(res => dimDeptMap[res.departmentId] = {
  //       managerIdList: res?.managerIdList ? res.managerIdList.split(",") : [],
  //       deviceIdList: res?.deviceIdList ? res.deviceIdList.split(",") : [],
  //       aiProjectIdList: res?.aiProjectIdList ? res.aiProjectIdList.split(",") : []
  //     });
  //     // const newDeptDataList = [];
  //     console.log(`${date} ${dimDeptList.length} foreach start`);
  //     for (const dimDept of dimDeptList) {
  //       console.log(`${date} ${dimDept.departmentId} start`);
  //       const departmentId = dimDept.departmentId;
  //       const deptData = await this.dimDepartmentDataDao.search({ date, departmentId });
  //       if (deptData) continue;//已经有了就跳过
  //       const directChildDeptIdList = dimDept?.directChildDepartmentIdList ? dimDept.directChildDepartmentIdList.split(",") : [];//直接子部门id
  //       const newDeptData = new DimDepartmentDataEntity();
  //       newDeptData.date = date;
  //       newDeptData.departmentId = departmentId;
  //       newDeptData.companyId = dimDept.companyId;
  //       const directChildDeptData = { managerIdList: [], deviceIdList: [], aiProjectIdList: [] };
  //       directChildDeptIdList.map(res => {
  //         if (dimDeptMap[res]) {
  //           directChildDeptData.managerIdList = [...directChildDeptData.managerIdList, ...dimDeptMap[res].managerIdList];
  //           directChildDeptData.deviceIdList = [...directChildDeptData.deviceIdList, ...dimDeptMap[res].deviceIdList];
  //           directChildDeptData.aiProjectIdList = [...directChildDeptData.aiProjectIdList, ...dimDeptMap[res].aiProjectIdList];
  //         }
  //       })
  //       directChildDeptData.managerIdList.forEach(value => {
  //         const index = dimDeptMap[departmentId].managerIdList.indexOf(value);
  //         if (index !== -1) {
  //           dimDeptMap[departmentId].managerIdList.splice(index, 1);
  //         }
  //       });
  //       newDeptData.managerIdList = dimDeptMap[departmentId].managerIdList.join(",");

  //       directChildDeptData.deviceIdList.forEach(value => {
  //         const index = dimDeptMap[departmentId].deviceIdList.indexOf(value);
  //         if (index !== -1) {
  //           dimDeptMap[departmentId].deviceIdList.splice(index, 1);
  //         }
  //       });
  //       newDeptData.deviceIdList = dimDeptMap[departmentId].deviceIdList.join(",");

  //       //查询时间断内创建的工地
  //       const projectList = await this.projectDao.searchProject({ companyId: dimDept.companyId, departmentId, endTime: (DateFns.format(DateFns.parse(endDate, 'yyyyMMdd', new Date()), 'yyyy-MM-dd') + ' 23:59:59') });
  //       const projectIds = _.union(projectList.map(e => e.PROJECT_ID));
  //       newDeptData.projectIdList = projectIds.join(",");

  //       //查询根部的ai工地列表
  //       const rootDept = await this.departmentDao.searchDept({ companyId: dimDept.companyId, level: 0 });
  //       const rootAiProjectIds = dimDeptMap[rootDept.items[0].departmentId]?.aiProjectIdList;
  //       newDeptData.aiProjectIdList = projectIds.filter(res => rootAiProjectIds.includes(res)).join(",");

  //       newDeptData.createTime = new Date("2024-06-30 12:00:00");
  //       await this.dimDepartmentDataDao.batchSave([newDeptData]);
  //       // newDeptDataList.push(newDeptData);
  //     }
  //     // await this.dimDepartmentDataDao.batchSave(newDeptDataList);
  //     console.log(`${date} done`);
  //   }
  //   console.log(`done`);
  // }

  @MyMeter
  async etl(): Promise<Etl.Result> {
    this.logger.prefix('etl').uuid().log('started');
    const date = TimeUtil.formatDate(DateFns.addDays(new Date(), -1), 'yyyyMMdd');
    const res1 = await this.etlByDivision({ hasDivisionDept: false, date });
    this.logger.log('etlByDivision false done')
    const res2 = await this.etlByDivision({ hasDivisionDept: true, date });
    this.logger.log('etlByDivision true done')
    this.logger.clearPrefix();
    return {
      fail: res1.fail + res2.fail,
      success: res1.success + res2.success
    }
  }

  async etlByDivision({ hasDivisionDept, date }: { hasDivisionDept: boolean, date: string }) {
    const companyIdList = await this.searchCompanyIdList({ hasDivisionDept });
    this.logger.log('companyIdList', companyIdList)
    const res: Etl.Result = { fail: 0, success: 0 }
    for (const companyId of companyIdList) {
      try {
        const { fail, success } = await this.etlCompany({ companyId, date })
        res.fail = res.fail + fail
        res.success = res.success + success;
        this.logger.log(res)
      } catch (err) {
        this.logger.log('error', err)
      }
    }
    return res;

  }

  async etlByDivisionV1({ hasDivisionDept, date, step = 10 }: { hasDivisionDept: boolean, date: string, step?: number }) {
    const companyIdList = await this.searchCompanyIdList({ hasDivisionDept });
    this.logger.log('companyIdList', companyIdList)

    const res: Etl.Result = { fail: 0, success: 0 }
    let offset = 0;
    while (true) {
      const batch = companyIdList.slice(offset, offset + step)
      if (batch.length === 0) break;
      offset = offset + step

      try {
        const jobs = batch.map(companyId => new Promise<Etl.Result>((resolve, reject) => {
          this.etlCompany({ companyId, date })
            .then(res => {
              resolve(res)
            }).catch(err => reject(err))
        }))
        const batchRes = await Promise.all(jobs)
        res.fail = res.fail + _.sumBy(batchRes, e => e.fail)
        res.success = res.success + _.sumBy(batchRes, e => e.success)
        this.logger.log(res)
      } catch (err) {
        this.logger.log('error', err)
      }
    }
    return res;

  }


  /**
   * 根据工地id和日期生成切片（删除的部门不生成）
   * @param param0 
   * @returns 
   */
  async etlCompany({ companyId, date }: { companyId: string, date: string }): Promise<Etl.Result> {
    const res: Etl.Result = { fail: 0, success: 0 }
    this.logger.log(`date ${date} company ${companyId}`)
    const { items: departmentList } = await this.departmentDao.searchDept({ companyId })
    const deptByLevel = _.groupBy(departmentList, e => e.level)
    // 从底层组织架构往上更新
    const levelList = _.orderBy(_.keys(deptByLevel), e => Number(e), 'desc')
    for await (const level of levelList) {
      for await (const dept of deptByLevel[level]) {
        try {
          await this.etlDepartment(dept, date)
          res.success++
        } catch (err) {
          this.logger.log('etlCompany error', err)
          res.fail++
        }
      }
    }
    // this.logger.log(`etlCompany done ${date} ${companyId}`)
    return res;
  }

  /**
   * 生成 部门关系 和 部门数据 切片（删除的部门不生成）
   * @param param0 
   * @param date 
   * @returns 
   */
  async etlDepartment({ departmentId, type, companyId, level, linkCompanyId }: { departmentId: string, type?: string, companyId: string, level: number, linkCompanyId: string }, date: string) {
    // this.logger.log(`level ${level} department ${departmentId}`)
    const [alreadyDone] = await this.dimDepartmentDao.searchByDate({ date, departmentIdList: [departmentId] });
    if (alreadyDone) return;//今天已经有切片了跳过
    if (type === 'division' && linkCompanyId) {//集团公司（将关联的部门的根部门切片给集团公司）
      // 获取linkCompanyId的根组织
      const rootDept = await this.departmentService.getRootDept(linkCompanyId);
      // 复制跟组织的etl数据 
      const [rootEtl] = await this.dimDepartmentDao.searchByDate({ date, departmentIdList: [rootDept.departmentId] });
      if (!rootEtl) {
        this.logger.log(`etlDepartment rootEtl is null params: ${date} ${rootDept.departmentId}`);
        return;
      }
      // 替换为集团公司的companyId, departmentId
      rootEtl.departmentId = departmentId;
      rootEtl.companyId = companyId;
      await this.dimDepartmentDao.save(rootEtl);
    } else {
      const deptData = await this.getDepartmentData({ companyId, departmentId });
      //查询部门下的直属子部门（删除的部门不生成切片，那直接子部门应该也校验删除）
      const parent = await this.departmentDao.get(departmentId);
      const parentPath = parent.dir ? [parent.dir, parent.departmentId].join(',') : parent.departmentId;
      const directChildDeptList = await this.departmentDao.searchDeptInfoList({ companyId: parent.companyId, dir: parentPath, excludeDel: true });
      const directChildDepartmentIdList = directChildDeptList.map(e => e.departmentId);//所有直属子部门id
      //查询直属子部门的数据
      const childEtlList = directChildDepartmentIdList.length > 0 ? await this.dimDepartmentDao.searchByDate({ date, departmentIdList: directChildDepartmentIdList }) : [];
      const mergedDeptDate = DepartmentEtlUtil.merge(deptData, childEtlList);//所有子部门的数据合并
      //当前部门的所有子部门 与 当前部门直属子部门的所有子部门数据合并去重（这里是为了集团数据，内部节点是不会关联部门的，所以需要根据直属部门的子部门获取）
      const childDeptIdList: string[] = [];
      childEtlList.map(res => { if (res.childDepartmentIdList) childDeptIdList.push(...res.childDepartmentIdList.split(",")); });
      //查询部门下的所有子部门（删除的部门不生成切片，那子部门应该也校验删除）
      const children = await this.departmentDao.searchDeptInfoList({ companyId, includeDir: departmentId, excludeDel: true });
      children.map(e => { if (!childDeptIdList.includes(e.departmentId)) childDeptIdList.push(e.departmentId) });//所有子部门id(集团公司会包含关联公司的子部门)
      //保存部门和子部门的切片
      await this.dimDepartmentDao.save({
        departmentId,
        companyId,
        date,
        directChildDepartmentIdList: !_.isEmpty(directChildDepartmentIdList) ? directChildDepartmentIdList.join(",") : '',
        childDepartmentIdList: !_.isEmpty(childDeptIdList) ? _.union(childDeptIdList).join(",") : '',
        ...mergedDeptDate//后续要删除掉，查询都根据本部门的数据切片
      });
      //保存本部门的数据切片
      const departmentData = await this.getDepartmentData({ companyId, departmentId });
      const dimDepartmentData = new DimDepartmentDataEntity();
      dimDepartmentData.departmentId = departmentId;
      dimDepartmentData.date = date;
      dimDepartmentData.companyId = companyId;
      dimDepartmentData.managerIdList = departmentData?.managerIdList || "";
      dimDepartmentData.projectIdList = departmentData?.projectIdList || "";
      dimDepartmentData.deviceIdList = departmentData?.deviceIdList || "";
      dimDepartmentData.aiProjectIdList = departmentData?.aiProjectIdList || "";
      await this.dimDepartmentDataDao.save(dimDepartmentData);
    }
  }

  /**
   * 获取部门切片数据（只包含本部门的数据）
   * @param param0 
   * @returns 
   */
  async getDepartmentData({ companyId, departmentId }: { companyId?: string, departmentId?: string }): Promise<DepartmentEtl.DataDto> {
    /** 1、部门所属成员 */
    const members = await this.departmentMemberDao.search({ departmentId })
    const managerIdList = members.map(e => e.managerId)
    /** 2、部门所属工地(校验工地删除) */
    const projectList = await this.projectDao.searchProject({ companyId, departmentId });
    const projectIdList = _.uniq(projectList.map(e => e.PROJECT_ID));
    /** 3、部门所属设备 */
    const deviceIdList: string[] = [];
    const deviceStatusList = await this.deviceDao.searchDeviceStatus({ companyId });
    const screenList = deviceStatusList?.items.map(res => {
      return {
        yqzDeviceId: res?.yqzDeviceId ? String(res.yqzDeviceId) : null,
        departmentId: res?.departmentId ? String(res.departmentId) : null,
        projectManagerId: res?.managerId ? String(res.managerId) : null
      }
    });
    //根据设备所在部门和领用人所在部门，确定设备归属，来确定部门下的设备
    const yqzDeviceIds = await this.getDeviceListByDeptId({ departmentId: departmentId, list: screenList });
    if (!_.isEmpty(yqzDeviceIds)) {
      const deviceList = await this.deviceDao.searchDeviceInfoList({ yqzDeviceIds });
      deviceList?.map(e => deviceIdList.push(e.yqzDeviceId));
    }
    /** 4、部门所属AI工地(校验工地删除) */
    const aiProjectIdList = [];
    if (!_.isEmpty(projectIdList)) {
      const { items: installedDeviceList } = await this.deviceDao.searchDeviceStatus({ projectIdList, installled: 'Y', projectIsDel: 'N' });
      installedDeviceList?.map(e => aiProjectIdList.push(e.projectId));
    }
    return { departmentId, managerIdList: managerIdList.join(","), projectIdList: projectIdList.join(","), deviceIdList: deviceIdList.join(","), aiProjectIdList: aiProjectIdList.join(",") };
  }

  /**
   * 根据归属查询设备列表（不包含子部门逻辑，设备归属部门 = 筛选的部门）
   * @param departmentId 筛选的部门id
   * @param list 需要过滤的列表(设备id、部门id、领用人id)
   * @returns 
   */
  async getDeviceListByDeptId({ departmentId, list }: { departmentId: string, list: { yqzDeviceId: string, departmentId: string, projectManagerId: string }[] }) {
    const yqzDeviceIdList: string[] = [];
    if (_.isEmpty(list)) return yqzDeviceIdList;
    if (!departmentId) return _.map(list, 'yqzDeviceId');
    const managerDeptMap = new Map();//记录manager和dept关系，减少io提高查询效率
    //过滤
    for (const dto of list) {
      if(!dto?.yqzDeviceId || !dto?.departmentId) continue;
      const deptMap = managerDeptMap.get(`${dto.projectManagerId}_${dto.departmentId}`);//先从map中拿
      let deviceDeptId = deptMap ? deptMap : String(dto.departmentId);
      if (!deptMap) { //如果map中没有
        if (dto.projectManagerId) {//设备有领用人
          //查询领用人，在子部门范围内最早加入的部门
          const managerDept = await this.searchFirstToJoinSubDept({ managerId: dto.projectManagerId, departmentId: dto.departmentId });
          if (managerDept?.departmentId) deviceDeptId = String(managerDept.departmentId);//如果没有领用人或不在设备归属部门范围内，则为设备归属部门
        }
        managerDeptMap.set(`${dto.projectManagerId}_${dto.departmentId}`, deviceDeptId);
      }
      //当前部门 =? 设备归属部门
      if (String(departmentId) === String(deviceDeptId)) yqzDeviceIdList.push(dto.yqzDeviceId);
    }
    return yqzDeviceIdList;
  }

  async searchCompanyIdList(params: { hasDivisionDept?: boolean }): Promise<string[]> {
    const { hasDivisionDept } = params || {}
    const qb = this.datasource
      .createQueryBuilder()
      .select(['cd.company_id companyId'])
      .from(CompanyProductVersionEntity, 'cpv')
      .leftJoin(CompanyDecorationEntity, 'cd', 'cpv.company_decoration_id = cd.company_decoration_id')
      .where("cpv.delete_flag <> 'Y'")
      .andWhere("cd.delete_flag <> 'Y'")
      .andWhere(new Brackets(mqb => {
        mqb
          .where("cpv.product_version > 1")
          .orWhere(new Brackets(mqb => {
            mqb.where("cpv.product_version <= 1")
              .andWhere("(cd.sales is not null and cd.sales <> '')")
              .andWhere("(cd.operators is not null and cd.operators <> '')")
              .andWhere("TIMESTAMPDIFF( DAY, cpv.expire_time, CURRENT_TIMESTAMP ) < 180 ")
          }))
      }))
      .andWhere(mqb => {
        const subq = mqb.createQueryBuilder()
          .createQueryBuilder()
          .select(['dp.company_id companyId'])
          .from(DepartmentEntity, 'dp')
          .where("dp.delete_flag <> 'Y'")
          .groupBy('dp.company_id')
          .having('max(dp.link_company_id) is not null');
        return hasDivisionDept ? `cd.company_id in (${subq.getQuery()})` : `cd.company_id not in (${subq.getQuery()})`
      })
    const entities = await qb.getRawMany<{ companyId: string }>();
    return entities.map(e => e.companyId)
  }

  /**
   * 查询manager，在子部门范围内最早加入的部门
   * @param param0 
   * @returns 
   */
  async searchFirstToJoinSubDept({ managerId, departmentId }: { managerId: string, departmentId: string }): Promise<{ departmentId: string }> {
    const qb = this.datasource
      .getRepository(DepartmentMemberEntity)
      .createQueryBuilder('dm')
      .select([
        'dm.department_id departmentId'
      ])
      .leftJoin(DepartmentEntity, 'd', 'dm.department_id = d.department_id')
      .where('dm.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere('d.delete_flag = :deleteFlag', { deleteFlag: 'N' })
      .andWhere("dm.manager_id = :managerId", { managerId })
      .andWhere("FIND_IN_SET(:departmentId, d.dir)", { departmentId })
      .orderBy('dm.createTime', 'ASC') //加入部门时间正序
      .addOrderBy('dm.department_member_id', 'ASC') //主键正序
      .limit(1);
    return await qb.getRawOne();
  }

}