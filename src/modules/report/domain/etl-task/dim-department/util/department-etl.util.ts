import { DimDepartmentEntity } from "@src/modules/report/entity/etl/dim-department.entity";
import { DepartmentEtl } from "../../../bgw/department/types/department-etl.type";
import * as _ from "lodash";
export const DepartmentEtlUtil = {
  merge
}

function merge(dto: DepartmentEtl.DataDto, entities: DimDepartmentEntity[]): Partial<DimDepartmentEntity> {
  const fieldNameList: (keyof Omit<DepartmentEtl.Dto, 'departmentId'>)[] = ['aiProjectIdList', 'deviceIdList', 'managerIdList', 'projectIdList']
  return _.fromPairs(
    fieldNameList.map(field => [
      field,
      _.filter(
        [...(dto[field] ? dto[field].split(",") : []), ...entities.map(e => e[field]).join(",").split(",")],
        e => !!e)
        .join(",") || undefined
    ])
  )
}