import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DeviceStatusEntity } from "@src/modules/report/entity/camera/device-status.entity";
import { FactCompanyDeviceEntity } from "@src/modules/report/entity/etl/fact-company-device.entity";
import { DataSource } from "typeorm";
import * as DateFns from "date-fns";

@Injectable()
export class FactCompanyDeviceEtl {
  private readonly logger = new MyLogger(FactCompanyDeviceEtl.name)

  constructor(
    private readonly datasource:DataSource
  ){}
  
  async etl(){
    const date = DateFns.format(new Date(), 'yyyyMMdd')
    return await this.datasource.transaction(async tm=>{
      this.logger.log(`${date} deleting existing rows`)
      await tm.createQueryBuilder()
        .delete()
        .from(FactCompanyDeviceEntity)
        .where('time_key = :date', {date})
        .execute();

      this.logger.log(`${date} calculating `)
      const entities = await tm.createQueryBuilder()
        .select([
          'ds.company_id company_key',
          `${date} time_key`,
          'count(1) device_qty',
          'sum(if(ds.is_online=1, 1,0)) online_qty',
          'sum(if(ds.project_id is not null, 1, 0)) install_qty '
        ])
        .from(DeviceStatusEntity,'ds')
        .where('delete_flag = :delFlag', {delFlag:"N"})
        .andWhere('company_id is not null')
        .groupBy("ds.company_id")
        .orderBy('device_qty','DESC')
        .getRawMany();

      this.logger.log(`${date} inserting into etl `)        
      return await tm.createQueryBuilder()
        .insert()
        .into(FactCompanyDeviceEntity)
        .values(entities)
        .execute();      
    })      
  }
}