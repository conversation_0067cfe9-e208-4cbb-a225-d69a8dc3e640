import { Injectable } from "@nestjs/common";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DataSource } from "typeorm";
import { Etl } from "../type";
import * as _ from "lodash";
import { NodeOverdueEventEntity } from "@src/modules/report/entity/bgw/node-overdue-event.entity";
import { ProjectNodeEntity } from "@src/modules/report/entity/bgw/project-node.entity";


@Injectable()
export class NodeOverdueEventEtl {
  private readonly logger = new MyLogger(NodeOverdueEventEtl.name)
  constructor(
    private readonly datasource:DataSource,
  ){}

  @DevTimeout(500)
  private test(){
    // this.int().then(res=>{
    //   this.logger.log(res)
    // })
    // this.int().then(res=>{
    //   this.logger.log(res)
    //   this.etl().then(res=>{
    //     this.logger.log(res)
    //   })
    // })
  }

  /**
   * 可以在任何时候运行
   * @returns 
   */
  async etl():Promise<Etl.Result>{
    this.logger.prefix('etl').uuid();
    const etlRes :Etl.Result = {
      fail:0,
      success:0
    }

    const res = await this.datasource.transaction(async em=>{
      // 检查所有open record(to_date为null的记录)
      // 如果节点状态已经close, 则close 
      const updateRes = await em.queryRunner.query(`
          UPDATE  node_overdue_event o,
                  project_node n 
          SET o.to_date = n.finish_time,
              o.duration_in_day = TIMESTAMPDIFF( DAY, o.from_date, n.finish_time ) + 1
          WHERE
            (
              o.to_date IS NULL 
              OR UNIX_TIMESTAMP( o.to_date ) = 0 
            ) 
            AND o.project_node_id = n.project_node_id 
            AND n.is_finish = 'Y'
            AND o.delete_flag = 'N'
            AND n.delete_flag = 'N'
      `)
      this.logger.log('updateRes',JSON.stringify(updateRes))
      etlRes.success = etlRes.success + updateRes.changedRows;
      
      // 如果节点过期而且还没有open record (toDate是空的),则新增一条记录
      const inseQb = em.createQueryBuilder()
        .select([
          'pn.project_node_id projectNodeId',
          'pn.project_id projectId',
          'DATE_ADD(pn.deadline_time, INTERVAL 1 DAY) fromDate'
        ])
        .from(ProjectNodeEntity, 'pn')
        .where("pn.delete_flag = 'N'")
        .andWhere(" (pn.is_finish='N' and CURRENT_TIMESTAMP>pn.deadline_time  )") //尚未完成,但是已经过期
        .andWhere(mqb=>{
          const subQb = mqb
            .createQueryBuilder()
            .select('project_node_id')
            .from(NodeOverdueEventEntity,'')
            .where("delete_flag = 'N'")
            .andWhere('(to_date is null or UNIX_TIMESTAMP(to_date) = 0)')
          return `pn.project_node_id not in (${subQb.getQuery()})`          
        })
      // this.logger.log(inseQb.getQueryAndParameters())
      const entities = await inseQb.execute();
      const insRes = await em.createQueryBuilder()
        .insert()
        .into(NodeOverdueEventEntity)
        .values(entities)
        .execute();
      this.logger.log('insRes count',insRes.identifiers.length)

      etlRes.success = etlRes.success + insRes.identifiers.length
      return etlRes



    })

    this.logger.clearPrefix();
    return res;

  }

  /**
   * 数据初始化,只需要运行一次(createBy='init'), 可重复运行
   * @returns 
   */
  async int():Promise<Etl.Result>{
    this.logger.prefix('init').uuid().log('started');
    const etlRes :Etl.Result = {
      fail:0,
      success:0
    }

    const res = await this.datasource.transaction(async em=>{
      
      // 如果节点已经完成,但是finish_time>deadline,则初始化一条记录
      const inseQb = em.createQueryBuilder()
        .select([
          'pn.project_node_id projectNodeId',
          'pn.project_id projectId',
          'DATE_ADD(pn.deadline_time, INTERVAL 1 DAY) fromDate',
          'pn.finish_time toDate',
          "TIMESTAMPDIFF(day,pn.deadline_time, pn.finish_time)+1 durationInDay",
          "'init' createBy"
        ])
        .from(ProjectNodeEntity, 'pn')
        .where("pn.delete_flag = 'N'")
        .andWhere(" (pn.is_finish='Y' and pn.finish_time>pn.deadline_time  )") //虽已完成,但是已经过期
        .andWhere(mqb=>{
          const subQb = mqb
            .createQueryBuilder()
            .select('project_node_id')
            .from(NodeOverdueEventEntity,'')
            .where("delete_flag = 'N'")
            .andWhere("createBy = 'init'")
          return `pn.project_node_id not in (${subQb.getQuery()})`          
        })


      // this.logger.log(inseQb.getQueryAndParameters())
      const entities = await inseQb.execute();
      

      const insRes = await em.createQueryBuilder()
        .insert()
        .into(NodeOverdueEventEntity)
        .values(entities)
        .execute();
      this.logger.log('insRes count',insRes.identifiers.length)

      etlRes.success = etlRes.success + insRes.identifiers.length
      return etlRes



    })

    this.logger.clearPrefix();
    return res;

  }










}