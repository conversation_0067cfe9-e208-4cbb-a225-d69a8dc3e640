import { Injectable } from "@nestjs/common";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DeviceInfoEntity } from "@src/modules/report/entity/camera/device-info.entity";
import { DeviceStatusEntity } from "@src/modules/report/entity/camera/device-status.entity";
import { DimDeviceEntity } from "@src/modules/report/entity/etl/dim-device.entity";
import { TimeUtil } from "@src/util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DataSource } from "typeorm";
import * as DateFns from "date-fns";
import { Etl } from "../type";
import * as _ from "lodash";
import { EtlStagingEntity } from "@src/modules/report/entity/etl/etl-staging.entity";
import * as uuid from "uuid"
import { EtlHelper } from "../helper/etl.helper";

const task = 'dim_device';

@Injectable()
export class DimDeviceEtl {
  private readonly logger = new MyLogger(DimDeviceEtl.name)
  constructor(
    private readonly datasource:DataSource,
    private etlHelper: EtlHelper
  ){}

  @DevTimeout(500)
  private test(){
    // this.etl().then(res=>{
    //   this.logger.log(res)
    // })
  }

  async etl():Promise<Etl.Result>{
    this.logger.prefix('etl').prefix(uuid.v4().replace(/-/g,''));
    const stagingList = await this.extract();
    const res = await this.load({stagingList})
    this.logger.log('done').clearPrefix();
    return res
  }


  async extract():Promise<string[]>{
    return await this.datasource.transaction(async tm=>{
      const date = DateFns.format(DateFns.startOfYesterday(),TimeUtil.Formats.yyyyMMdd)
      // extract
      const deviceList = await tm.createQueryBuilder()
      .select([
        'ds.yqz_device_id deviceId',
        `${date} date`,
        'ds.company_id companyId',
        'ds.project_manager_id managerId',
        'ds.project_id projectId',
        'p.project_director projectDirector',
        'ds.project_binding_time projectBindingTime',
        'ds.manager_revice_time managerReviceTime',
        'ds.department_id departmentId',
        'ds.link_department_time linkDepartmentTime'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = :delFlag')
      .andWhere('di.delete_flag = :delFlag')
      .setParameters({delFlag:"N"})
      .getRawMany();
      this.logger.log(`extracted ${deviceList.length} rows`)

      // backup
      const stagingRes = await tm.createQueryBuilder()
        .insert()
        .into(EtlStagingEntity)
        .values([{
          task,
          date,
          data:JSON.stringify(deviceList)
        }])
        .execute();
      this.logger.log(`backed up to staing ${stagingRes.identifiers.length} batches`)
      return stagingRes.identifiers.map(e=>e.id)
    });
  }  


  async load({stagingList}:{stagingList:string[]}):Promise<Etl.Result>{
    return await this.datasource.transaction(async tm=>{

      // load from staing
      const rawStaing = await this.etlHelper.loadingStaing(stagingList,tm);
      const staingByDate = _.groupBy(rawStaing,e=>e.date)
      const dates = _.keys(staingByDate);

      const res:Etl.Result = {success:0,fail:0}

      for await(const date of dates) {
        this.logger.prefix(date)
        // cleanup
        const delRes = await tm.createQueryBuilder()
          .delete()
          .from(DimDeviceEntity)
          .where('date = :date', {date})
          .execute();
        this.logger.log(`${DimDeviceEntity.name} ${delRes.affected} rows deleted`)

        const rows = _.flatten(staingByDate[date].map(e=>JSON.parse(e.data)))
        this.logger.log(`load from staing ${staingByDate[date].length} batches`);

        // load into target
        const loadRes = await tm.createQueryBuilder()
          .insert()
          .into(DimDeviceEntity)
          .values(rows)
          .execute();
        this.logger.log(`load into starget ${loadRes.identifiers.length} rows`)                

        // close staing
        const staingRes = await this.etlHelper.closeStaing(stagingList,tm)
        this.logger.log(`closing staing ${staingRes.affected} batches`)      ;

        res.success = res.success + loadRes.identifiers.length;

        this.logger.popPrefix();

      }
      return res
    });




    

  }  







}