import { Injectable } from "@nestjs/common";
import { EtlStagingEntity } from "@src/modules/report/entity/etl/etl-staging.entity";
import { MyLogger } from "@yqz/nest";
import { DataSource, EntityManager } from "typeorm";

@Injectable()
export class EtlHelper {
  private logger = new MyLogger(EtlHelper.name)
  constructor(
    private readonly datasource:DataSource
  ){}

  async closeStaing(stagingList:string[], em?:EntityManager){
    const staingRes = await (em||this.datasource).createQueryBuilder()
    .update(EtlStagingEntity)
    .set({status:'done'})
    .andWhere('id in (:...stagingList)',{stagingList})
    .execute();
    return staingRes;
  }

  async loadingStaing(stagingList:string[], em?:EntityManager){
    return await (em||this.datasource).createQueryBuilder()
      .select()
      .from(EtlStagingEntity, 'staging')
      .where('id in (:...stagingList)', {stagingList})
      .getRawMany<{id:string, task:string,date:string,data:string, status:string}>();
  }
}