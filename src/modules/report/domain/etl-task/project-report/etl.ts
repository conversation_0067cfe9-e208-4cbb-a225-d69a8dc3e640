import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { CompanyDecorationEntity } from "@src/modules/report/entity/bgw/company-decoration.entity";
import { CompanyProductVersionEntity } from "@src/modules/report/entity/bgw/company-product-version.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { TimeUtil } from "@src/util";
import { DevTimeout, MyMeter, MyRedis, YqzException } from "@yqz/nest";
import * as _ from "lodash";
import { DataSource, Brackets } from "typeorm";
import { ManagerDao } from "../../bgw/manager/dao/manager.dao";
import { ProjectReportBasicsService } from "../../bgw/project-report/service/basics/project-report.basics.service";
import { DeviceDao } from "../../camera/device/dao/device.dao";

@Injectable()
export class ProjectReportEtl {
  private readonly logger = new MyLogger(ProjectReportEtl.name)
  constructor(
    private readonly dataSource: DataSource,
    private readonly managerDao: ManagerDao,
    private readonly projectReportBasicsService: ProjectReportBasicsService,
    private readonly deviceDao: DeviceDao,
  ) { }

  @DevTimeout(500)
  private async test() {
    // await this.etl();
    // await this.myEtlByCompany({ date: new Date, companyId: '65', managerId: '4542' });
  }

  @MyMeter
  async etl() {
    this.logger.uuid().log('started');
    //查询符合推送日报条件的公司
    const companyIdList = await this.searchCompanyIdList();
    this.logger.log('companyIdList', companyIdList.join(', '))
    const generateDate = new Date();
    const date = TimeUtil.intervalDay(generateDate, -1);//定时任务凌晨跑，时间往前推一天
    //根据公司列表，推送日报
    await this.myEtlByCompanyList({ date, companyIdList });
    this.logger.log('done');
    this.logger.clearPrefix();
  }

  /**
   * 生成日报(公司层面)
   * @param param0 
   */
  async myEtlByCompanyList({ date, companyIdList }: { date: Date, companyIdList?: string[] }) {
    if (!date || _.isEmpty(companyIdList)) throw new YqzException(`必传参数不能为空`);
    for (const companyId of companyIdList) {
      try {
        this.logger.log(`myEtlByCompanyList date: ${date}, companyId: ${companyId}`);
        await this.myEtlByCompany({ date, companyId });
      } catch (err) {
        this.logger.log(`myEtlByCompanyList error date: ${date}, companyId: ${companyId}, err: ${JSON.stringify(err)}`);
        continue;
      }
    }
  }

  /**
   * 生成日报
   */
  async myEtlByCompany({ date, companyId, managerId }: { date: Date, companyId: string, managerId?: string }) {
    if (!date || !companyId) throw new YqzException(`必传参数不能为空`);
    const startTime = TimeUtil.formatDate(date, 'yyyy-MM-dd 00:00:00');
    const endTime = TimeUtil.formatDate(date, 'yyyy-MM-dd 23:59:59');
    const reportDateInfo = {
      reportDate: TimeUtil.formatDate(date, 'yyyy年MM月dd日'),
      reportWeek: TimeUtil.getWeek(date)
    };
    //生成日报manager信息
    const managerList = await this.managerDao.searchReportManagerInfo({ reportDate: TimeUtil.formatDate(date, 'yyyy-MM-dd'), companyId, managerId });
    let errCnt = 0;
    //查询公司设备数
    const companyDeviceNum = await this.deviceDao.searchReportDeviceCount({ companyId });
    for (const manager of managerList) {
      try {
        //查询可管理的工地
        const projectIds = await this.projectReportBasicsService.searchProjectScope({ managerId: manager.managerId });
        this.logger.log(`searchProjectReport manager ${manager.managerId} project scope ${projectIds} isDeviceAdmin ${manager.isDeviceAdmin} companyDeviceNum ${companyDeviceNum}`);
        /** 查询日报内容 */
        //工地概况
        const proConstruction = await this.projectReportBasicsService.searchProjectConstruction({
          companyId: manager.companyId, managerId: manager.managerId, projectIds, startTime, endTime,
          isCompanyAdmin: manager.isCompanyAdmin,
          isDeviceAdmin: manager.isDeviceAdmin
        });
        //（没有可管理的在建工地） 且 （不满足 该成员为设备管理员且公司有设备），则跳过
        if (0 == proConstruction.proConstructionNum && !('Y' == manager.isDeviceAdmin && companyDeviceNum > 0)) continue;
        //工地施工进度
        const proInactive = await this.projectReportBasicsService.searchProjectInactive({ companyId: manager.companyId, projectIds: projectIds, day: startTime, startTime, endTime });
        //工地AI监理
        const proDetection = await this.projectReportBasicsService.searchProjectDetection({ companyId: manager.companyId, projectIds: projectIds, startTime, endTime });
        //施工问题
        const proProblem = await this.projectReportBasicsService.searchProjectProblem({ companyId: manager.companyId, projectIds: projectIds, startTime, endTime });
        //员工签到
        const signIn = await this.projectReportBasicsService.searchSignIn({ companyId: manager.companyId, projectIds: projectIds, startTime, endTime });
        //设备情况(设备管理员展示,且公司要有设备)
        let deviceTotal = {};
        if ('Y' == manager.isDeviceAdmin && companyDeviceNum > 0) {
          deviceTotal = await this.projectReportBasicsService.searchDeviceTotal({ companyId: manager.companyId, startTime, endTime });
        }
        const reportContent = _.extend({}, reportDateInfo, deviceTotal, proConstruction, proInactive, proDetection, proProblem, signIn);
        //记录异常数
        const expCount = await this.expCountCalculator(reportContent)
        this.logger.log(`save project report companyId ${manager.companyId}, managerId ${manager.managerId}, reportContent ${JSON.stringify(reportContent)}, reportDate ${reportDateInfo.reportDate}, expCount ${expCount} `);
        await this.projectReportBasicsService.save({ companyId: manager.companyId, managerId: manager.managerId, reportContent: JSON.stringify(reportContent), reportDate: date, expCount });
        //向redis推送（未读红点）
        const key = manager.companyId + "-" + manager.personId;
        this.logger.log(`project report hmget key ${key} `);
        const num = await MyRedis.hmget('projectReportMessageCenter', key);
        this.logger.log(`projectReportMessageCenter num ${num} `);
        await MyRedis.hmset('projectReportMessageCenter', { [key]: num ? Number(num) + 1 : 1 });
      } catch (err) {
        errCnt++
        this.logger.log(`generateProjectReport err companyId ${manager.companyId} managerId ${manager.managerId}`), err;
        continue;
      }
    }
    if (errCnt > 0) throw new YqzException(`${ProjectReportEtl.name} errCnt: ${errCnt}`);
  }

  async expCountCalculator(content: any) {
    const propertiesToCheck = [
      'devTotalStockIdleNum',
      'devTotalOutStockIdleNum',
      'devTotalOfflineNum',
      'devIdleNum',
      'devOfflineNum',
      'proShutdownNum',
      'proShutdownNum15d',
      'proShutdownNum7to15',
      'proOverdueNum',
      'newNodeOverdueNum',
      'nodeOverdueNum',
      'detSmokeNum',//抽烟
      'detNoUniformNum',//未穿工服
      'detSlipperNum',//拖鞋
      'detSafetyHelmetNum',//安全帽
      'detClimbWorkNum',//登高
      'detFireNum',//动火
      'detElectroMobileNum',//电瓶车
      'detAngleGrinderNum',//角磨机
      'detCuttingMachineNum',//切割机
      'detFireExtinguisherNum',//灭火器
      'detTemporaryElectricityNum',//临时用电
      'detSafetyMeshNum',//脚手架未装防护网
      'detSafetyHoleNum',//临边洞口无防护
      'detSafetyStairsNum',//楼梯无防护
      'detTidinessNum',//整洁度
      'probNum',
      'probProjectNum',
      'probUnsolvedNum',
      'probUnSolvedDeadlineNum',
      'probTodaySolvedDeadlineNum'
    ];

    let expCount = 0;

    propertiesToCheck.forEach(property => {
      if (content?.[property] && content[property] > 0) {
        expCount++;
      }
    });
    return expCount
  }

  async searchCompanyIdList(): Promise<string[]> {
    const qb = this.dataSource.createQueryBuilder()
      .select(['cd.company_id companyId'])
      .from(CompanyProductVersionEntity, 'cpv')
      .leftJoin(CompanyDecorationEntity, 'cd', 'cpv.company_decoration_id = cd.company_decoration_id')
      .leftJoin(CompanyEntity, 'c', 'cd.company_id = c.company_id')
      .where("cpv.delete_flag <> 'Y'")
      .andWhere("cd.delete_flag <> 'Y'")
      .andWhere("c.delete_flag <> 'Y'")
      //不是集团公司
      .andWhere('c.`COMPANY_TYPE` <> "4"')
      .andWhere(new Brackets(mqb => {
        //专业版本、高级版本
        mqb.where("cpv.product_version in (2, 3)")
          //试用版、有销售、有客服、expireTime小于180天
          .orWhere(new Brackets(mqb => {
            mqb.where("cpv.product_version = 1")
              .andWhere("(cd.sales is not null and cd.sales <> '')")
              .andWhere("(cd.operators is not null and cd.operators <> '')")
              .andWhere("TIMESTAMPDIFF( DAY, cpv.expire_time, CURRENT_TIMESTAMP ) < 180 ")
          }))
      }))
    const entities = await qb.getRawMany<{ companyId: string }>();
    return entities.map(e => e.companyId)
  }

}