import { Injectable } from "@nestjs/common";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DataSource } from "typeorm";
import { Etl } from "../type";
import * as _ from "lodash";
import { ProjectSignInEntity } from "@src/modules/report/entity/bgw/project-sign-in.entity"
import { ProjectNoFacesignEventEntity } from "@src/modules/report/entity/camera/project-no-facesign-event.entity";
import {Project} from '@yqz/base';
import * as DateFns from "date-fns";
import { DeviceStatusEntity } from "@src/modules/report/entity/camera/device-status.entity";


@Injectable()
export class ProjectNoFacesignEventEtl {
  private readonly logger = new MyLogger(ProjectNoFacesignEventEtl.name)
  constructor(
    private readonly datasource:DataSource
  ){}

  @DevTimeout(500)
  private test(){

  }

  async etl():Promise<Etl.Result>{
    this.logger.prefix('etl').uuid();
    const etlRes :Etl.Result = {
      fail:0,
      success:0
    }

    const date = DateFns.startOfYesterday()
    const res = await this.datasource.transaction(async em=>{
      
      
      // 检查所有open record(to_date为null的记录)
      // 如果前一天有人脸签到, 则close (设置to_date为前一天)
      const qb = em.createQueryBuilder()
        .update(ProjectNoFacesignEventEntity)
        .set({
          toDate:date,
          durationInDay: ()=> "TIMESTAMPDIFF(day,from_date, :date)"
        })
        .where( mqb=>{
          const subQb = mqb
            .createQueryBuilder()
            .select('distinct project_id')
            .from(ProjectSignInEntity,'')
            .where('delete_flag = :delFlag')
            .andWhere('type = :signInType')
            .andWhere('SIGN_IN_TIME between :dayStart and :dayEnd') // 昨天有ai签到的
          return `project_id in (${subQb.getQuery()})`
        })
        .andWhere('(to_date is null or UNIX_TIMESTAMP(to_date) = 0)') //还处于无ai签到,to_date为null的记
        .setParameters({
          date,
          delFlag:"N",
          dayStart: DateFns.startOfYesterday(),
          dayEnd: DateFns.endOfYesterday(),
          signInType: Project.SignInType.AI
        })
      // this.logger.log(qb.getQueryAndParameters())
      const updateRes = await qb.execute()
      this.logger.log('updateRes', updateRes.affected)
      etlRes.success = etlRes.success + updateRes.affected;


     // 如果绑定了工地的设备前一天没有人脸签到而且还没有open record (toDate是空的),则新增一条记录
      const inseQb = em.createQueryBuilder()
        .select([
          'ds.project_id projectId',
          'ds.company_id companyId',
          ':date fromDate'
        ])
        .from(DeviceStatusEntity, 'ds')
        .where('ds.delete_flag = :delFlag')
        .andWhere('ds.project_id is not null')  //如果绑定了工地的设备
        .andWhere( mqb=>{
          const subQb = mqb
            .createQueryBuilder()
            .select('distinct project_id')
            .from(ProjectSignInEntity,'')
            .where('delete_flag = :delFlag')
            .andWhere('type = :signInType')
            .andWhere('SIGN_IN_TIME between :dayStart and :dayEnd') // 昨天有ai签到的
          return `ds.project_id not in (${subQb.getQuery()})`
        })
        .andWhere(mqb=>{
          const subQb = mqb
            .createQueryBuilder()
            .select('distinct project_id')
            .from(ProjectNoFacesignEventEntity,'')
            .where('delete_flag = :delFlag')
            .andWhere('(to_date is null or UNIX_TIMESTAMP(to_date) = 0)')  // open record
          return `ds.project_id not in (${subQb.getQuery()})`          
        })
        .setParameters({
          date,
          delFlag:"N",
          dayStart: DateFns.startOfYesterday(),
          dayEnd: DateFns.endOfYesterday(),
          signInType: Project.SignInType.AI
        })
      // this.logger.log(inseQb.getQueryAndParameters())

      const entities = await inseQb.execute();
      const insRes = await em.createQueryBuilder()
        .insert()
        .into(ProjectNoFacesignEventEntity)
        .values(entities)
        .execute();
      this.logger.log('insRes count',insRes.identifiers.length)

      etlRes.success = etlRes.success + insRes.identifiers.length
      return etlRes



    })

    this.logger.clearPrefix();
    return res;

  }










}