import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { CsvUtil } from "@src/util/csv.util";
import { DevTimeout, MyUser } from "@yqz/nest";
import { FactComapnyDeviceDao } from "../dao/fact-company-device.dao";
import { FactCompanyDevice } from "../type/fact-company-device.type";

@Injectable()
export class CompanyDeviceService {
  private readonly logger = new MyLogger(CompanyDeviceService.name)
  constructor(
    private readonly factCompanyDevice:FactComapnyDeviceDao
  ){}

  @DevTimeout(500)
  private test(){
    // this.searchFactCompanyDevice({companyId:'65', format:'csv'}).then(res=>{
    //   this.logger.log(res)
    // })
  }

  async searchFactCompanyDevice(params:FactCompanyDevice.SearchReq, @MyUser() user):Promise<string|FactCompanyDevice.SearcRes>{
    if(!params.format || params.format==='json') return await this.factCompanyDevice.search(params)
    // 返回csv格式
    delete params.pageNo;
    delete params.pageSize;
    const {items} = await this.factCompanyDevice.search(params)
    return CsvUtil.convert2Csv(items.map(e=>{
      delete e.companyId
      e.onlineRate = Number(e.onlineRate)
      e.installRate = Number(e.installRate)
      return e
    }))    
  }  



}