import { Injectable } from "@nestjs/common";
import { DimDepartmentEntity } from "@src/modules/report/entity/etl/dim-department.entity";
import { DaoUtil } from "@src/util/dao.util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { type } from "os";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { generateDateList } from "@src/util/datetime.util";

@Injectable()
export class DimDepartmentDao {
  private readonly logger = new MyLogger(DimDepartmentDao.name);
  constructor(
    private readonly datasource: DataSource
  ) { }

  @DevTimeout(500)
  private test() {
    // this.search({departmentIdList:['13']}).then(res=>{
    //   this.logger.log(res)
    // })
  }

  async save(dto: Partial<DimDepartmentEntity>) {
    const entity = new DimDepartmentEntity();
    Object.assign(entity, dto)
    await this.datasource.manager.save(DimDepartmentEntity, entity);
  }

  async searchByDate(params: { date: string, departmentIdList: string[] }) {
    DaoUtil.checkEmptyParams(params, DimDepartmentDao.name + '.searchByDate', 'FailIfAnyEmpty');
    const { departmentIdList, date } = params;
    const qb = this.datasource
      .getRepository(DimDepartmentEntity)
      .createQueryBuilder()
      .where('department_id in (:...departmentIdList)', { departmentIdList })
      .andWhere('date = :date', { date });
    return await qb.getMany();
  }

  async search(params: { departmentIdList: string[], startTime: string, endTime: string }) {
    DaoUtil.checkEmptyParams(params, DimDepartmentDao.name + '.search', 'FailIfAnyEmpty');
    const { departmentIdList, startTime, endTime } = params;
    //时间范围转换列表
    const dataList = generateDateList({ startTime, endTime });
    const qb = this.datasource
      .getRepository(DimDepartmentEntity)
      .createQueryBuilder()
      .where('department_id in (:...departmentIdList)', { departmentIdList })
      .andWhere('date in (:...dataList)', { dataList })
    return await qb.getMany();
  }

}