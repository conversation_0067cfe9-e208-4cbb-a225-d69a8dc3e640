import { Injectable } from "@nestjs/common";
import { DimDepartmentDataEntity } from "@src/modules/report/entity/etl/dim-department-data.entity";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DataSource } from "typeorm";

@Injectable()
export class DimDepartmentDataDao {
    private readonly logger = new MyLogger(DimDepartmentDataDao.name);
    constructor(
        private readonly dataSource: DataSource
    ) { }

    @DevTimeout(500)
    private test() {
    }

    async save(entity: DimDepartmentDataEntity) {
        await this.dataSource.manager.save(DimDepartmentDataEntity, entity);
    }

    async batchSave(list: DimDepartmentDataEntity[]) {
        await this.dataSource.manager.save(DimDepartmentDataEntity, list);
    }

    async search(params: { date: string, departmentId: string }) {
        const { departmentId, date } = params;
        const qb = this.dataSource.getRepository(DimDepartmentDataEntity).createQueryBuilder()
            .where('department_id = :departmentId', { departmentId })
            .andWhere('date = :date', { date });
        return await qb.getOne();
    }

    async searchList(params: { date?: string, departmentIdList: string[], from?: string, to?: string}) {
        const { departmentIdList, date, from, to } = params;
        const qb = this.dataSource.getRepository(DimDepartmentDataEntity).createQueryBuilder()
            .where('department_id in (:...departmentIdList)', { departmentIdList })
        if(date)  qb.andWhere('date = :date', { date });
        if(from && to) qb.andWhere('date between :from and :to', {from,to})
        return await qb.getMany();
    }

}