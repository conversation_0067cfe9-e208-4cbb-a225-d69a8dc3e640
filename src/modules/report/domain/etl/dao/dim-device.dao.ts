import { Injectable } from "@nestjs/common";
import { DimDeviceEntity } from "@src/modules/report/entity/etl/dim-device.entity";
import { DaoUtil } from "@src/util/dao.util";
import { generateDateList, parseDateString } from "@src/util/datetime.util";
import { DevTimeout, MyLogger } from "@yqz/nest";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import * as DateFns from "date-fns";

@Injectable()
export class DimDeviceDao {
  private readonly logger = new MyLogger(DimDeviceDao.name);
  constructor(
    private readonly datasource: DataSource
  ) { }

  @DevTimeout(500)
  private async test() {
    // const dataList = generateDateList({ startTime: '20240101', endTime: '20240101' });
    // console.log("🚀 ~ DimDeviceDao ~ test ~ dataList:", dataList)
    // const res = await this.findIdleDeviceWithDaysContinuity({ deviceIdList: ['3'], companyIds: ['65'], startTime: '20230701', endTime: '20250101', consecutiveDays: '1', type: 'internal' });
    // console.log("🚀 ~ DimDeviceDao ~ test ~ res:", res)
  }

  async searchByDateList(params: { dataList: string[], companyIdList: string[], deviceIdList?: string[], projectIdList?: string[], isBindProject?: 'Y' | 'N' }) {
    const { dataList, companyIdList, deviceIdList, projectIdList, isBindProject } = params || {};
    if (_.isEmpty(dataList) || _.isEmpty(companyIdList)) return { total: 0, items: [] }
    const qb = this.datasource
      .createQueryBuilder()
      .select()
      .from(DimDeviceEntity, 'dd')
      .where('dd.company_id in (:...companyIdList)', { companyIdList })
      .andWhere('dd.date in (:...dataList)', { dataList });
    if (!_.isEmpty(deviceIdList)) qb.andWhere('dd.device_id in (:...deviceIdList)', { deviceIdList })
    if (!_.isEmpty(projectIdList)) qb.andWhere('dd.project_id in (:...projectIdList)', { projectIdList })
    if (isBindProject == 'N') qb.andWhere('dd.project_id is null');
    if (isBindProject == 'Y') qb.andWhere('dd.project_id is not null');
    const res = await DaoUtil.getPageRes<DimDeviceEntity>(qb);
    res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
    return res;
  }

  async searchProjectDateList(params: { companyIdList: string[], deviceIdList?: string[], projectIdList?: string[], startTime: string, endTime: string }) {
    const { companyIdList, deviceIdList, projectIdList, startTime, endTime } = params || {};
    DaoUtil.checkEmptyParams({ companyIdList, startTime, endTime }, DimDeviceDao.name + '.search', 'FailIfAnyEmpty');
    //时间范围转换列表
    const dataList = generateDateList({ startTime, endTime });
    const qb = this.datasource
      .createQueryBuilder()
      .select([
        "project_id as projectId",
        "date as date"
      ])
      .from(DimDeviceEntity, 'dd')
      .where('dd.company_id in (:...companyIdList)', { companyIdList })
      .andWhere('dd.date in (:...dataList)', { dataList });
    if (!_.isEmpty(deviceIdList)) qb.andWhere('dd.device_id in (:...deviceIdList)', { deviceIdList })
    if (!_.isEmpty(projectIdList)) qb.andWhere('dd.project_id in (:...projectIdList)', { projectIdList })
    return await qb.getRawMany<{ projectId: string, date: string }>();
  }

  async search(params: { companyIdList: string[], deviceIdList?: string[], projectIdList?: string[], startTime: string, endTime: string, isBindProject?: 'Y' | 'N' }) {
    const { companyIdList, deviceIdList, projectIdList, startTime, endTime, isBindProject } = params || {};
    DaoUtil.checkEmptyParams({ companyIdList, startTime, endTime }, DimDeviceDao.name + '.search', 'FailIfAnyEmpty');
    //时间范围转换列表
    const dataList = generateDateList({ startTime, endTime });
    const qb = this.datasource
      .createQueryBuilder()
      .select()
      .from(DimDeviceEntity, 'dd')
      .where('dd.company_id in (:...companyIdList)', { companyIdList })
      .andWhere('dd.date in (:...dataList)', { dataList });
    if (!_.isEmpty(deviceIdList)) qb.andWhere('dd.device_id in (:...deviceIdList)', { deviceIdList })
    if (!_.isEmpty(projectIdList)) qb.andWhere('dd.project_id in (:...projectIdList)', { projectIdList })
    if (isBindProject == 'N') qb.andWhere('dd.project_id is null');
    if (isBindProject == 'Y') qb.andWhere('dd.project_id is not null');
    const res = await DaoUtil.getPageRes<DimDeviceEntity>(qb);
    res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
    return res;
  }

  async findIdleDeviceWithDaysContinuity(params: { companyIdList: string[]; deviceIdList: string[], startTime: string, endTime: string, consecutiveDays: string, type: 'internal' | 'external' }) {
    if (_.isEmpty(params.companyIdList) || _.isEmpty(params.deviceIdList) || !params.startTime || !params.endTime || !params.consecutiveDays || !params.type) return [];
    params.startTime = DateFns.format(parseDateString(params.startTime), 'yyyyMMdd');
    params.endTime = DateFns.format(parseDateString(params.endTime), 'yyyyMMdd');
    // 初始化变量
    const rowNumInitQuery = `SET @row_num = 0`;
    // 子查询 1：标记连续日期分组
    const subQuery1 = this.datasource
      .createQueryBuilder()
      .select([
        'dd.device_id AS device_id',
        'dd.company_id AS company_id',
        "'NULL_GROUP' AS project_group", // 固定分组名，表示 `project_id` 是 NULL
        "DATE_SUB(dd.date, INTERVAL @row_num := @row_num + 1 DAY) AS group_key", // 按日期和行号计算分组键
        "dd.date AS date",
      ])
      .from(DimDeviceEntity, 'dd')
      .where('dd.company_id IN (:...companyIds)', { companyIds: params.companyIdList })
      .andWhere('dd.device_id IN (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.type === 'external') subQuery1.andWhere('dd.manager_id IS NOT NULL');//库外闲置（被领用了）
    if (params.type === 'internal') subQuery1.andWhere('dd.manager_id IS NULL');//库内闲置
    subQuery1.andWhere('dd.project_id IS NULL') // 确保 `project_id` 为 NULL
      .andWhere('dd.date < :endTime', { endTime: params.endTime })
      .orderBy('dd.device_id, dd.date'); // 确保排序正确，支持连续性计算
    // 子查询 2：统计每个分组的连续天数
    const subQuery2 = this.datasource
      .createQueryBuilder()
      .select([
        'sub.device_id AS device_id',
        'sub.company_id AS company_id',
        'COUNT(1) AS consecutive_days',
        "MAX(sub.date) AS end_date",
        "MIN(sub.date) AS start_date"
      ])
      .from(`(${subQuery1.getQuery()})`, 'sub')
      .groupBy('sub.device_id, sub.group_key')
      .setParameters(subQuery1.getParameters());
    // 主查询：筛选连续天数 >= 7 的结果
    const mainQuery = this.datasource
      .createQueryBuilder()
      .select([
        'grouped.device_id AS deviceId',
        'grouped.company_id AS companyId',
        'grouped.consecutive_days AS consecutiveDays',
        'grouped.start_date AS startDate',
        'grouped.end_date AS endDate',
      ])
      .from(`(${subQuery2.getQuery()})`, 'grouped')
      .where('grouped.consecutive_days >= :days', { days: params.consecutiveDays })
      .andWhere('grouped.end_date >= :startTime', { startTime: params.startTime })
      .andWhere('grouped.end_date < :endTime', { endTime: params.endTime })
      .setParameters(subQuery2.getParameters());
    // 执行初始化变量的查询
    await this.datasource.query(rowNumInitQuery);
    // 执行主查询
    const results = await mainQuery.getRawMany<{ deviceId: string; companyId: string; consecutiveDays: number }>();
    return results;
  }

  async findBindDeviceWithDaysContinuity(params: { companyIdList: string[]; deviceIdList: string[], startTime: string, endTime: string }) {
    if (_.isEmpty(params.companyIdList) || _.isEmpty(params.deviceIdList) || !params.startTime || !params.endTime) return [];
    params.startTime = DateFns.format(parseDateString(params.startTime), 'yyyyMMdd');
    params.endTime = DateFns.format(parseDateString(params.endTime), 'yyyyMMdd');
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const qb = this.datasource.createQueryBuilder(DimDeviceEntity, 'dd');
    qb.select([
      'dd.device_id AS deviceId',
      'dd.company_id AS companyId',
      'dd.project_id AS projectId',
      'COUNT(1) AS consecutiveDays',
    ])
      .where('dd.company_id IN (:...companyIds)', { companyIds: params.companyIdList })
      .andWhere('dd.date in (:...dataList)', { dataList })
      .andWhere('dd.device_id IN (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dd.project_id IS NOT NULL')
      .groupBy('dd.device_id');
    // 执行查询
    const results = await qb.getRawMany<{ deviceId: string; companyId: string; projectId: string; consecutiveDays: number }>();
    return results;
  }

  async searchBindProjectIdList(params: { projectIdList: string[], startTime: string, endTime: string }) {
    const { projectIdList, startTime, endTime } = params || {};
    DaoUtil.checkEmptyParams({ startTime, endTime }, DimDeviceDao.name + '.searchBindProjectIdList', 'FailIfAnyEmpty');
    //时间范围转换列表
    const dataList = generateDateList({ startTime, endTime });
    const qb = this.datasource
      .createQueryBuilder()
      .select([
        "dd.project_id as projectId",
      ])
      .from(DimDeviceEntity, 'dd')
      .andWhere('dd.date in (:...dataList)', { dataList })
      .andWhere('dd.project_id in (:...projectIdList)', { projectIdList })
      .groupBy('dd.project_id');
    return await qb.getRawMany<{ projectId: string }>();
  }

}