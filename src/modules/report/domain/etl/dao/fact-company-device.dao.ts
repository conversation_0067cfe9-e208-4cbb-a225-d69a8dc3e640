import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { FactCompanyDeviceEntity } from "@src/modules/report/entity/etl/fact-company-device.entity";
import { DaoUtil } from "@src/util/dao.util";
import { DevTimeout } from "@yqz/nest";
import { DataSource } from "typeorm";
import { FactCompanyDevice } from "../type/fact-company-device.type";

@Injectable()
export class FactComapnyDeviceDao {
  private readonly logger = new MyLogger(FactComapnyDeviceDao.name)
  constructor(
    private readonly datasource:DataSource
  ){}
  @DevTimeout(500)
  private test(){
    // this.search({companyId:'65'}).then(res=>{
    //   this.logger.log(res)
    // })
  }

  async search(params:FactCompanyDevice.SearchReq):Promise<FactCompanyDevice.SearcRes>{
    DaoUtil.checkEmptyParams(params, 'FactComapnyDeviceDao.search', 'FailIfAllEmpty');
    const {companyId,fromDate,toDate} = params
    const qb = this.datasource
      .createQueryBuilder()
      .select([
        "fact.company_key companyId",
        "fact.time_key date",
        "fact.device_qty deviceQty",
        "fact.install_qty installQty",
        "fact.online_qty onlineQty",
        "fact.online_qty/fact.device_qty onlineRate",
        "fact.install_qty/fact.device_qty installRate",
      ])
      .from(FactCompanyDeviceEntity,'fact')
      .leftJoin(CompanyEntity,'c', 'fact.company_key = c.company_id')
      .where('fact.company_key = :companyId', {companyId})
    if(fromDate) qb.andWhere('fact.time >= :fromDate', {fromDate})
    if(toDate) qb.andWhere('fact.time >= :toDate', {toDate})

    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<FactCompanyDevice.Dto>(qb)

  }
}