import { ApiProperty } from "@nestjs/swagger";
import { BaseReportReq } from "@src/types/report-req.type";
import { IsNotEmpty } from "class-validator";

export namespace FactCompanyDevice {
  export class Dto {
    @ApiProperty()
    companyId:string;
    
    @ApiProperty()
    date:string;

    @ApiProperty({description:'当前设备总数'})
    deviceQty:number;

    @ApiProperty({description:'安装数(已绑定工地)'})
    installQty:number;

    @ApiProperty({description: '在线数'})
    onlineQty:number;

    @ApiProperty({description:'在线率'})
    onlineRate:number;

    @ApiProperty({description:'安装率'})
    installRate:number;    
  }

  export class SearchReq  extends BaseReportReq<Dto>{
      @ApiProperty()
      @IsNotEmpty()
      companyId:string;

      @ApiProperty()
      fromDate?:string;

      @ApiProperty()
      toDate?:string;
  }

  export class SearcRes {
    @ApiProperty()
    total:number;

    @ApiProperty({type:[Dto]})
    items:Dto[]
  }
}