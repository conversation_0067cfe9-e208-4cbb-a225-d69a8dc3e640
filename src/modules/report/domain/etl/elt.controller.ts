import { Body, Controller, Post } from "@nestjs/common";
import { MyUser } from "@yqz/nest";
import { CompanyDeviceService } from "./service/company-device.service";
import { FactCompanyDevice } from "./type/fact-company-device.type";

@Controller('etl')
export class EtlController {
  constructor(
    private readonly etlService:CompanyDeviceService
  ){}

  @Post('fact-company-device/search')
  async factCompanyDeviceSerach(@Body() params:FactCompanyDevice.SearchReq, @MyUser() user){
    return await this.etlService.searchFactCompanyDevice(params,user)
  }

}