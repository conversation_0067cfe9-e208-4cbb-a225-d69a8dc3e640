import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { ProjectInactiveRecord } from "../dto/detection.dto";
import { ProjectInactiveRecordEntity } from "@src/modules/report/entity/camera/project-inactive-record.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DevTimeout } from "@yqz/nest";
import * as DateFns from "date-fns";

@Injectable()
export class ProjectInactiveRecordDao {
  private readonly logger = new MyLogger(ProjectInactiveRecordDao.name)
  constructor(
    private dataSource: DataSource
  ) { }

  @DevTimeout(500)
  test() {

  }

  async searchProjectInactiveNum(params: ProjectInactiveRecord.Search) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(ps.id) AS shutdownNum',
        'IFNULL(SUM(DATEDIFF("' + params.day + '",ps.from_date) > 15),0) AS shutdownNum15d',
        'IFNULL(SUM(DATEDIFF("' + params.day + '",ps.from_date) BETWEEN 8 AND 15),0) AS shutdownNum7to15'
      ])
      .from(ProjectInactiveRecordEntity, 'ps')
      .leftJoin(ProjectEntity, 'p', 'ps.project_id = p.project_id')
      .where('ps.delete_flag = "N"')
      .andWhere('p.delete_flag = "N"')
      .andWhere('ps.company_id = :companyId', { companyId: params.companyId });
    if (!_.isEmpty(params.projectIds))
      qb.andWhere('ps.project_id in (:...projectIds)', { projectIds: params.projectIds });
    qb.andWhere('ps.to_date IS NULL ')
    return await qb.getRawOne();
  }

  /**
   * 查询无人施工工地
   * @param params 
   * @returns 
   */
  async searchInactiveProjectList(params: { projectIds: string[], startTime: string, endTime: string }) {
    params.endTime = new Date(params.endTime) > DateFns.addDays(new Date(), 1) ? DateFns.format(DateFns.addDays(new Date(), 1), 'yyyy-MM-dd') : params.endTime;//如果结束时间超过今天+1天，那结束时间就为今天+1天
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'MAX( ' +
        'CASE ' +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date is NULL THEN DATEDIFF('" + params.endTime + "',pir.from_date) " +
        "WHEN pir.to_date >= '" + params.startTime + "' AND pir.to_date < '" + params.endTime + "' THEN (DATEDIFF(pir.to_date,pir.from_date) + 1) " +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date >= '" + params.endTime + "' THEN DATEDIFF('" + params.endTime + "',pir.from_date) " +
        "END) AS inactiveDay"
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectInactiveRecordEntity, 'pir', 'p.project_id = pir.project_id and pir.delete_flag = "N"')
      .where('p.project_id in (:...projectIds)', { projectIds: params.projectIds })
      .groupBy('p.project_id');
    return await qb.getRawMany<{ projectId: string, inactiveDay: number }>();
  }

  async searchProjectIdListByInactiveDay(params: { projectIds: string[], startTime: string, endTime: string, inactiveDay: string }): Promise<string[]> {
    if (_.isEmpty(params.projectIds) || !params.endTime || !params.startTime || !params.inactiveDay) return [];
    params.endTime = new Date(params.endTime) > DateFns.addDays(new Date(), 1) ? DateFns.format(DateFns.addDays(new Date(), 1), 'yyyy-MM-dd') : params.endTime;//如果结束时间超过今天+1天，那结束时间就为今天+1天
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'MAX( ' +
        'CASE ' +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date is NULL THEN DATEDIFF('" + params.endTime + "',pir.from_date) " +
        "WHEN pir.to_date >= '" + params.startTime + "' AND pir.to_date < '" + params.endTime + "' THEN (DATEDIFF(pir.to_date,pir.from_date) + 1) " +
        "WHEN pir.from_date < '" + params.endTime + "' AND pir.to_date >= '" + params.endTime + "' THEN DATEDIFF('" + params.endTime + "',pir.from_date) " +
        "END) AS inactiveDay"
      ])
      .from(ProjectEntity, 'p')
      .leftJoin(ProjectInactiveRecordEntity, 'pir', 'p.project_id = pir.project_id and pir.delete_flag = "N"')
      .where('p.project_id in (:...projectIds)', { projectIds: params.projectIds })
      .groupBy('p.project_id')
      .having('inactiveDay >= :inactiveDay', { inactiveDay: params.inactiveDay });
    const res = await qb.getRawMany();
    return res.map(item => item.projectId);
  }

}