import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { DevTimeout } from "@yqz/nest";
import { DeviceOfflineEventEntity } from "@src/modules/report/entity/camera/device-offline-event.entity";
import { DimDeviceEntity } from "@src/modules/report/entity/etl/dim-device.entity";
import { generateDateList, parseDateString } from "@src/util/datetime.util";
import { OfflineReportOfDeviceEntity } from "@src/modules/report/entity/camera/offline-report-of-device.entity";
import * as DateFns from "date-fns";

@Injectable()
export class DeviceOfflineEventDao {
    private readonly logger = new MyLogger(DeviceOfflineEventDao.name)
    constructor(
        private dataSource: DataSource
    ) { }


    @DevTimeout(500)
    test() {
        // this.searchDeviceList({yqzDeviceIds:[
        // ]}).then(res => {
        //   this.logger.log(res)
        // })
    }

    /**
     * 查询时间范围内离线设备
     * @param params 
     * @returns 
     */
    async searchDeviceOfflineEventList(params: { companyIds: string[], devices: string[], startTime: string, endTime: string }) {
        if (_.isEmpty(params.companyIds) || _.isEmpty(params.devices) || !params.startTime || !params.endTime) return [];
        params.startTime = DateFns.format(parseDateString(params.startTime), 'yyyy-MM-dd');
        params.endTime = DateFns.format(parseDateString(params.endTime), 'yyyy-MM-dd');
        const offqb = this.dataSource.createQueryBuilder()
            .select([
                'de.yqz_device_id as yqz_device_id',
                'de.project_id as project_id',
                'MAX( ' +
                'CASE ' +
                "WHEN de.from_time < '" + params.endTime + "' AND de.to_time is NULL THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
                "WHEN de.to_time >= '" + params.startTime + "' AND de.to_time < '" + params.endTime + "'  THEN TIMESTAMPDIFF(SECOND,de.from_time,de.to_time) " +
                "WHEN de.from_time < '" + params.endTime + "' AND de.to_time >= '" + params.endTime + "' THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
                "ELSE 0 END) AS offLineSec"
            ])
            .from(DeviceOfflineEventEntity, 'de')
            .where('de.delete_flag = "N"')
            .andWhere('de.company_id in (:...companyIds)', { companyIds: params.companyIds })
            .andWhere('de.yqz_device_id in (:...devices)', { devices: params.devices })
            .andWhere('de.company_id is not null')
            .andWhere('de.project_id is not null')
            .andWhere('de.manager_id is not null')
            .groupBy('de.yqz_device_id').addGroupBy('de.project_id');

        const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'dm.device_id as yqzDeviceId',
                'off.offLineSec as offLineSec'
            ])
            .from(DimDeviceEntity, 'dm')
            .leftJoin("(" + offqb.getQuery() + ")", 'off', 'off.yqz_device_id = dm.device_id and off.project_id = dm.project_id')
            .where('dm.company_id in (:...companyIds)', { companyIds: params.companyIds })
            .andWhere('dm.device_id in (:...yqzDeviceId)', { yqzDeviceId: params.devices })
            .andWhere('dm.date in (:...dates)', { dates: dataList })
            .andWhere('dm.project_id is not null and off.offLineSec is not null')
            .groupBy('dm.device_id');
        qb.setParameters(offqb.getParameters());
        return await qb.getRawMany();
    }

    async searchDeviceOfflineEventListByOffLineSec(params: { companyIdList: string[], deviceIdList: string[], startTime: string, endTime: string, offLineSec: string, offlineReport?: 'Y' | 'N' }) {
        if (_.isEmpty(params.companyIdList) || _.isEmpty(params.deviceIdList) || !params.startTime || !params.endTime || !params.offLineSec) return [];
        params.startTime = DateFns.format(parseDateString(params.startTime), 'yyyy-MM-dd');
        params.endTime = DateFns.format(parseDateString(params.endTime), 'yyyy-MM-dd');
        const offqb = this.dataSource.createQueryBuilder()
            .select([
                'de.yqz_device_id as yqz_device_id',
                'de.project_id as project_id',
                'MAX( ' +
                'CASE ' +
                "WHEN de.from_time < '" + params.endTime + "' AND de.to_time is NULL THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
                "WHEN de.to_time >= '" + params.startTime + "' AND de.to_time < '" + params.endTime + "'  THEN TIMESTAMPDIFF(SECOND,de.from_time,de.to_time) " +
                "WHEN de.from_time < '" + params.endTime + "' AND de.to_time >= '" + params.endTime + "' THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
                "ELSE 0 END) AS offLineSec"

            ])
            .from(DeviceOfflineEventEntity, 'de')
            .where('de.delete_flag = "N"')
            .andWhere('de.company_id in (:...companyIds)', { companyIds: params.companyIdList })
            .andWhere('de.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList });
        if (params.offlineReport) {//离线上报
            offqb.leftJoin(OfflineReportOfDeviceEntity, 'ord', 'ord.device_offline_event_id = de.id and ord.delete_flag = "N"');
            if (params.offlineReport === 'Y') offqb.andWhere('ord.id is not null');//上报离线
            if (params.offlineReport === 'N') offqb.andWhere('ord.id is null');//未上报离线
        }
        offqb.andWhere('de.company_id is not null')
            .andWhere('de.project_id is not null')
            .andWhere('de.manager_id is not null')
            .groupBy('de.yqz_device_id').addGroupBy('de.project_id');
        const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'dm.device_id as yqzDeviceId',
                'dm.company_id as companyId',
                'dm.project_id as projectId',
                'off.offLineSec as offLineSec'
            ])
            .from(DimDeviceEntity, 'dm')
            .leftJoin("(" + offqb.getQuery() + ")", 'off', 'off.yqz_device_id = dm.device_id and off.project_id = dm.project_id')
            .where('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
            .andWhere('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
            .andWhere('dm.date in (:...dates)', { dates: dataList })
            .andWhere('dm.project_id is not null')
            .andWhere('off.offLineSec is not null')
            .groupBy('dm.device_id')
            .having('off.offLineSec >= :offLineSec', { offLineSec: params.offLineSec });
        qb.setParameters(offqb.getParameters());
        return await qb.getRawMany<{ yqzDeviceId: string, companyId: string, projectId: string, offLineSec: string }>();
    }

    async searchDeviceUnOfflineEventListByOffLineSec(params: { companyIdList: string[], deviceIdList: string[], startTime: string, endTime: string, offLineSec: string, offlineReport?: 'Y' | 'N' }) {
        if (_.isEmpty(params.companyIdList) || _.isEmpty(params.deviceIdList) || !params.startTime || !params.endTime || !params.offLineSec) return [];
        params.startTime = DateFns.format(parseDateString(params.startTime), 'yyyy-MM-dd');
        params.endTime = DateFns.format(parseDateString(params.endTime), 'yyyy-MM-dd');
        const offqb = this.dataSource.createQueryBuilder()
            .select([
                'de.yqz_device_id as yqz_device_id',
                'de.project_id as project_id',
                'MAX( ' +
                'CASE ' +
                "WHEN de.from_time < '" + params.endTime + "' AND de.to_time is NULL THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
                "WHEN de.to_time >= '" + params.startTime + "' AND de.to_time < '" + params.endTime + "'  THEN TIMESTAMPDIFF(SECOND,de.from_time,de.to_time) " +
                "WHEN de.from_time < '" + params.endTime + "' AND de.to_time >= '" + params.endTime + "' THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
                "ELSE 0 END) AS offLineSec"
            ])
            .from(DeviceOfflineEventEntity, 'de')
            .where('de.delete_flag = "N"')
            .andWhere('de.company_id in (:...companyIds)', { companyIds: params.companyIdList })
            .andWhere('de.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList });
        if (params.offlineReport) {//离线上报
            offqb.leftJoin(OfflineReportOfDeviceEntity, 'ord', 'ord.device_offline_event_id = de.id and ord.delete_flag = "N"');
            if (params.offlineReport === 'Y') offqb.andWhere('ord.id is not null');//上报离线
            if (params.offlineReport === 'N') offqb.andWhere('ord.id is null');//未上报离线
        }
        offqb.andWhere('de.company_id is not null')
            .andWhere('de.project_id is not null')
            .andWhere('de.manager_id is not null')
            .groupBy('de.yqz_device_id').addGroupBy('de.project_id');
        const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'dm.device_id as yqzDeviceId',
                'dm.company_id as companyId',
                'dm.project_id as projectId',
                'ifnull(off.offLineSec, 0) as offLineSec'
            ])
            .from(DimDeviceEntity, 'dm')
            .leftJoin("(" + offqb.getQuery() + ")", 'off', 'off.yqz_device_id = dm.device_id and off.project_id = dm.project_id')
            .where('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
            .andWhere('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
            .andWhere('dm.date in (:...dates)', { dates: dataList })
            .andWhere('dm.project_id is not null')
            .groupBy('dm.device_id')
            .having('offLineSec < :offLineSec', { offLineSec: params.offLineSec })
        qb.setParameters(offqb.getParameters());
        return await qb.getRawMany<{ yqzDeviceId: string, companyId: string, projectId: string, offLineSec: string }>();
    }

    /**
     * 查询设备在时间段内的总离线时长(开始和结束都在时间范围内)
     * @param params 
     * @returns 
     */
    async searchDeviceOfflineTotalInTimeRange(params: { companyIdList: string[], deviceIdList: string[], startTime: string, endTime: string }) {
        if (_.isEmpty(params.companyIdList) || _.isEmpty(params.deviceIdList) || !params.startTime || !params.endTime) return [];
        params.startTime = DateFns.format(parseDateString(params.startTime), 'yyyy-MM-dd');
        params.endTime = DateFns.format(parseDateString(params.endTime), 'yyyy-MM-dd');
        const offqb = this.dataSource.createQueryBuilder()
            .select([
                'de.yqz_device_id as yqz_device_id',
                'de.project_id as project_id',
                `SUM(
                 CASE
                    WHEN de.to_time is NULL and de.from_time < '${params.endTime}' and de.from_time >= '${params.startTime}' THEN TIMESTAMPDIFF(SECOND, de.from_time, '${params.endTime}')
                    WHEN de.to_time is NULL and de.from_time < '${params.endTime}' and de.from_time < '${params.startTime}' THEN TIMESTAMPDIFF(SECOND, '${params.startTime}', '${params.endTime}')
                    WHEN de.to_time is NOT NULL AND de.to_time < '${params.endTime}' and de.to_time >= '${params.startTime}' and de.from_time < '${params.endTime}' and de.from_time >= '${params.startTime}' THEN TIMESTAMPDIFF(SECOND, de.from_time, de.to_time)
                    WHEN de.to_time is NOT NULL AND de.to_time < '${params.endTime}' and de.to_time >= '${params.startTime}' and de.from_time < '${params.startTime}' THEN TIMESTAMPDIFF(SECOND, '${params.startTime}', de.to_time)
                    WHEN de.to_time is NOT NULL AND de.to_time >= '${params.endTime}' and de.from_time < '${params.endTime}' and de.from_time >= '${params.startTime}' THEN TIMESTAMPDIFF(SECOND, de.from_time, '${params.endTime}')
                    WHEN de.to_time is NOT NULL AND de.to_time >= '${params.endTime}' and de.from_time < '${params.startTime}' THEN TIMESTAMPDIFF(SECOND, '${params.startTime}', '${params.endTime}')
                    ELSE 0 END) AS offLineSec`,
            ])
            .from(DeviceOfflineEventEntity, 'de')
            .where('de.delete_flag = "N"')
            .andWhere('de.company_id in (:...companyIds)', { companyIds: params.companyIdList })
            .andWhere('de.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
            .andWhere('de.company_id is not null')
            .andWhere('de.project_id is not null')
            .andWhere('de.manager_id is not null')
            .groupBy('de.yqz_device_id').addGroupBy('de.project_id');
        const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'dm.device_id as yqzDeviceId',
                'dm.company_id as companyId',
                'dm.project_id as projectId',
                'IFNULL(SUM(off.offLineSec), 0) as offLineSec'
            ])
            .from(DimDeviceEntity, 'dm')
            .leftJoin("(" + offqb.getQuery() + ")", 'off', 'off.yqz_device_id = dm.device_id and off.project_id = dm.project_id')
            .where('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
            .andWhere('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
            .andWhere('dm.date in (:...dates)', { dates: dataList })
            .andWhere('dm.project_id is not null')
            .groupBy('dm.device_id')
        qb.setParameters(offqb.getParameters());
        return await qb.getRawMany<{ yqzDeviceId: string, companyId: string, projectId: string, offLineSec: string }>();
    }

}