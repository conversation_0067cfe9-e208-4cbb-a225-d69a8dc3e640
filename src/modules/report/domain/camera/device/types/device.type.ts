import { ApiProperty } from "@nestjs/swagger";
import { BaseReportReq } from "@src/types/report-req.type";
import { IsNotEmpty } from "class-validator";
import { DeviceDataReport } from "../../../corp/data-report/service/basic/dto/device-data-report.dto";

export namespace Device {
  export class StatusDto {
    @ApiProperty()
    deviceSerial: string;
    @ApiProperty()
    yqzDeviceId: string;
    @ApiProperty()
    coverImage: string;
    @ApiProperty()
    companyId: string;
    @ApiProperty()
    startTime: Date;
    @ApiProperty()
    endTime: Date;
    @ApiProperty()
    onOfflineTIme: Date;
    @ApiProperty()
    companyName: string;
    @ApiProperty()
    projectId: string;
    @ApiProperty()
    managerId: string;
    @ApiProperty()
    managerName: string;
    @ApiProperty()
    address: string;
    @ApiProperty()
    houseNumber: string;
    @ApiProperty()
    managerReceiptTime: Date;
    @ApiProperty()
    isOnline: 1 | 0;
    @ApiProperty()
    status: string;
    @ApiProperty()
    offlineDurationInMin: number;
    @ApiProperty()
    idleDurationInDays: number;
    @ApiProperty()
    departmentId: string;
  }

  export class SearchReq extends BaseReportReq<StatusDto> {
    @IsNotEmpty()
    @ApiProperty()
    companyId?: string
    @ApiProperty({ type: [String] })
    managerIdList?: string[];
    @ApiProperty({ type: [String] })
    projectIdList?: string[]
    @ApiProperty({ description: '是否安装在工地' })
    installled?: 'Y' | 'N'
    @ApiProperty({ description: '工地是否删除' })
    projectIsDel?: 'Y' | 'N'
  }

  export class SearchRes {
    total: number;
    @ApiProperty({ type: [StatusDto] })
    items: StatusDto[]
  }

  export enum ActionType {
    Take = 1,
    Return = 2
  }
  export class EventDto {
    companyId: string;
    yqzDeviceId: string;
    managerId: string;
    actionType: ActionType;
    actionTime: Date;
  }

  export class SearchSituation {
    companyId?: string;
    projectIds?: string[];
    managerIds?: string[];
    startTime?: string;
    endTime?: string;
  }

  export class DeviceDetailReq extends BaseReportReq<DeviceDetail> {
    @ApiProperty({ description: "序列号", required: false })
    deviceSerial?: string;
    @ApiProperty({ description: "领用人", required: false })
    deviceManagerId?: string;
    @ApiProperty({ description: "绑定工地", required: false })
    projectAddress?: string;
    @ApiProperty({ description: "在线状态 Y-在线,N-离线", enum: ['Y', 'N'], required: false })
    isOnline?: 'Y' | 'N';
    @ApiProperty({ description: "工地负责人Id", required: false })
    projectManagerId?: string;
    @ApiProperty({ description: "所属公司Id", required: false })
    linkCompanyId?: string;
    @ApiProperty({ description: "领用时间排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
    reviceTimeSort?: 'DESC' | 'ASC';
    @ApiProperty({ description: "离线时长排序 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
    offLineTimeSort?: 'DESC' | 'ASC';
    yqzDeviceIds?: string[];
    companyIds?: string[];
    startTime: string;
    endTime: string;
    projectIds?: string[];
    bindProject?: 'Y' | 'N';
  }

  export class DeviceDetail {
    deviceId: string;
    @ApiProperty({ description: "序列号" })
    deviceSerial: string;
    @ApiProperty({ description: "领用人id" })
    deviceManagerId: string;
    @ApiProperty({ description: "领用人name" })
    deviceManagerName: string;
    @ApiProperty({ description: "领用时间" })
    managerReviceTime: string;
    @ApiProperty({ description: "绑定工地" })
    projectAddress: string;
    @ApiProperty({ description: "绑定时间" })
    projectBindTime: string;
    @ApiProperty({ description: "在线状态 Y-在线,N-离线" })
    isOnline: string;
    @ApiProperty({ description: "离线时长" })
    offLineTime: string;
    @ApiProperty({ description: "离线时长差(秒)" })
    diffSec: number;
    @ApiProperty({ description: "工地负责人id" })
    projectManagerId: string;
    @ApiProperty({ description: "工地负责人name" })
    projectManagerName: string;
    @ApiProperty({ description: "所属公司id" })
    linkCompanyId: string;
    @ApiProperty({ description: "所属公司name" })
    linkCompanyName: string;
    projectId: string;
    linkCompanyCreateTime: string;//所属公司创建时间
    linkCompanyTime: string;//绑定公司时间
  }

  export class SearchRealDeviceReq {
    deviceIdList: string[];
    linkCompanyId?: string;//所属公司
    deviceSerial?: string;//设备序列号
    reviceManagerId?: string;//领用人
    projectAddress?: string;//工地地址（绑定工地）
    isOnline?: DeviceDataReport.IsOnline;//在线状态
    projectManagerId?: string;//工地负责人
  }

  export class SearchRealDeviceListReq {
    companyType: string;
    businessCategory: 'home_decor' | 'construction';
    deviceIdList: string[];
    companyIdList: string[];
    linkCompanyId?: string;//所属公司
    deviceSerial?: string;//设备序列号
    reviceManagerId?: string;//领用人
    projectAddress?: string;//工地地址（绑定工地）
    isOnline?: DeviceDataReport.IsOnline;//在线状态
    projectManagerId?: string;//工地负责人
    pageNo?: number;
    pageSize?: number;
  }

  export class SearchEtlDeviceReq {
    deviceIdList: string[];
    companyIdList: string[];
    startTime: string;
    endTime: string;
    linkCompanyId?: string;//所属公司
    deviceSerial?: string;//设备序列号
    reviceManagerId?: string;//领用人
    projectAddress?: string;//工地地址（绑定工地）
    isOffline?: DeviceDataReport.IsOffline;//在线状态
    projectManagerId?: string;//工地负责人
  }

  export class SearchEtlDeviceListReq {
    companyType: string;
    companyId: string;
    businessCategory: 'home_decor' | 'construction';
    deviceIdList: string[];
    companyIdList: string[];
    startTime: string;
    endTime: string;
    linkCompanyId?: string;//所属公司
    deviceSerial?: string;//设备序列号
    reviceManagerId?: string;//领用人
    projectAddress?: string;//工地地址（绑定工地）
    isOffline?: DeviceDataReport.IsOffline;//在线状态
    projectManagerId?: string;//工地负责人
    pageNo?: number;
    pageSize?: number;
  }

}