import {  Body, Controller, Get, Post, Query, Req, Res, StreamableFile } from "@nestjs/common";
import { ControllerUtil } from "@src/util/controller.util";
import { MyApiOkResponse } from "@yqz/nest";
import { DeviceService } from "./service/device.service";
import { Device } from "./types/device.type";

@Controller("device")
export class DeviceController {
  constructor(
    private readonly deviceService:DeviceService
  ){}

  @MyApiOkResponse(Device.SearchRes)
  @Post("list/search") 
  async deviceListCsv(@Body() query:Device.SearchReq){
    const res = await this.deviceService.searchDevice(query)
    return res;
  }    

  @Get("list/download") 
  async listDownload(@Query() query:Device.SearchReq, @Res() response){
    const res = await this.deviceService.searchDevice(query)
    ControllerUtil.toDataOrFile(response, res)    
  }     
}