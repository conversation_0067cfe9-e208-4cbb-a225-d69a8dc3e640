import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import * as _ from "lodash";
import { DeviceDao } from "../dao/device.dao";
import { Device } from "../types/device.type";
import * as DateFns from "date-fns";
import { CsvUtil } from "@src/util/csv.util";
import { DevTimeout } from "@yqz/nest";
import { DataReportConfigService } from "../../../corp/data-report/service/data-report-config.service";
import { DateReport } from "../../../corp/data-report/dto/data-report.dto";
import { DeviceOfflineEventDao } from "../../device-offline-event/dao/device-offline-event.dao";
import { DeviceDataReport } from "../../../corp/data-report/service/basic/dto/device-data-report.dto";
import { DimDeviceDao } from "../../../etl/dao/dim-device.dao";

@Injectable()
export class DeviceService {
  private readonly logger = new MyLogger(DeviceService.name)
  constructor(
    private readonly deviceDao: DeviceDao,
    private readonly dataReportConfigService: DataReportConfigService,
    private readonly deviceOfflineEventDao: DeviceOfflineEventDao,
    private readonly dimDeviceDao: DimDeviceDao
  ) { }

  @DevTimeout(500)
  private test() {

  }

  async searchDevice(params: Device.SearchReq): Promise<string | Device.SearchRes> {
    if (!params.format || params.format === 'json') return await this.deviceDao.searchDeviceStatus(params)
    // 返回csv格式
    delete params.pageNo;
    delete params.pageSize;
    const { items } = await this.deviceDao.searchDeviceStatus(params)
    return CsvUtil.convert2Csv(items.map(e => {
      e.endTime = e.endTime ? DateFns.format(e.endTime, 'yyyy-MM-dd') as any : "";
      e.startTime = e.startTime ? DateFns.format(e.startTime, 'yyyy-MM-dd') as any : "";
      e.managerReceiptTime = e.managerReceiptTime ? DateFns.format(e.managerReceiptTime, 'yyyy-MM-dd') as any : ""
      e.onOfflineTIme = e.onOfflineTIme ? DateFns.format(e.onOfflineTIme, 'yyyy-MM-dd HH:mm:ss') as any : ""
      return e
    }))
  }

  /**
   * 获取未异常离线设备信息列表，结合公司id获取
   * @param param0 
   * @returns 
   */
  async getUnOfflineDeviceByCompany({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }): Promise<{ yqzDeviceId: string; companyId: string; projectId: string; offLineSec: string; }[]> {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return [];
    const deviceConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
    const exceptionOfflineConditionUnit = deviceConfig?.configs[0]?.exceptionOfflineConditionUnit;
    const exceptionOfflineCondition = deviceConfig?.configs[0]?.exceptionOfflineCondition;
    const exceptionOfflineReport = deviceConfig?.configs[0]?.exceptionOfflineReport;
    return await this.getUnOfflineDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport })
  }

  /**
   * 获取未异常离线设备信息列表，结合配置信息获取
   * @param param0 
   * @returns 
   */
  async getUnOfflineDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, exceptionOfflineCondition: string, exceptionOfflineConditionUnit: string, exceptionOfflineReport: string }): Promise<{ yqzDeviceId: string; companyId: string; projectId: string; offLineSec: string; }[]> {
    if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return [];
    let offLineSec = 4 * 60 * 60;//默认4小时
    if (exceptionOfflineConditionUnit === 'hour') offLineSec = Number(exceptionOfflineCondition) * 60 * 60;
    if (exceptionOfflineConditionUnit === 'minute') offLineSec = Number(exceptionOfflineCondition) * 60;
    const req = { deviceIdList, companyIdList, startTime, endTime, offLineSec: String(offLineSec) };
    if (exceptionOfflineReport === 'no_report') Object.assign(req, { offlineReport: 'N' });
    return await this.deviceOfflineEventDao.searchDeviceUnOfflineEventListByOffLineSec(req);
  }

  /**
   * 获取异常离线设备信息列表，结合公司id获取
   * @param param0 
   * @returns 
   */
  async getOfflineDeviceByCompany({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }): Promise<{ yqzDeviceId: string; companyId: string; projectId: string; offLineSec: string; }[]> {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return [];
    const deviceConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
    const exceptionOfflineConditionUnit = deviceConfig?.configs[0]?.exceptionOfflineConditionUnit;
    const exceptionOfflineCondition = deviceConfig?.configs[0]?.exceptionOfflineCondition;
    const exceptionOfflineReport = deviceConfig?.configs[0]?.exceptionOfflineReport;
    return await this.getOfflineDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport })
  }

  /**
   * 获取异常离线设备信息列表，结合配置信息获取
   * @param param0 
   * @returns 
   */
  async getOfflineDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, exceptionOfflineCondition, exceptionOfflineConditionUnit, exceptionOfflineReport }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, exceptionOfflineCondition: string, exceptionOfflineConditionUnit: string, exceptionOfflineReport: string }): Promise<{ yqzDeviceId: string; companyId: string; projectId: string; offLineSec: string; }[]> {
    if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return [];
    let offLineSec = 4 * 60 * 60;//默认4小时
    if (exceptionOfflineConditionUnit === 'hour') offLineSec = Number(exceptionOfflineCondition) * 60 * 60;
    if (exceptionOfflineConditionUnit === 'minute') offLineSec = Number(exceptionOfflineCondition) * 60;
    const req = { deviceIdList, companyIdList, startTime, endTime, offLineSec: String(offLineSec) };
    if (exceptionOfflineReport === 'no_report') Object.assign(req, { offlineReport: 'N' });
    return await this.deviceOfflineEventDao.searchDeviceOfflineEventListByOffLineSec(req);
  }

  /**
   * 获取异常离线设备信息Map，结合公司id获取
   * @param param0 
   * @returns 
   */
  async getOfflineDeviceByCompanyMap({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }) {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return {};
    const list = await this.getOfflineDeviceByCompany({ companyId, deviceIdList, companyIdList, startTime, endTime });
    const result = {};
    list.forEach(res => { result[res.yqzDeviceId] = res });
    return result;
  }

  /**
   * 获取离线设备天数>=N天的设备信息列表，结合配置信息获取
   * @param param0 
   * @returns 
   */
  async getOfflineDayDeviceByCompanyConfig({ deviceIdList, companyIdList, startTime, endTime, offlineFocusScope, offlineReport, offlineCondition }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, offlineFocusScope: string, offlineReport: 'all' | 'no_report', offlineCondition: string }) {
    if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return [];
    const req = { deviceIdList, companyIdList, startTime, endTime, offLineSec: String(Number(offlineCondition || 7) * 24 * 60 * 60) };
    if (offlineReport === 'no_report') Object.assign(req, { offlineReport: 'N' });
    return await this.deviceOfflineEventDao.searchDeviceOfflineEventListByOffLineSec(req);
  }

  /**
   * 查询库内闲置时长信息
   * @param param0 
   * @returns 
   */
  async searchInternalIdleDeviceMap({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }) {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return {};
    const deviceConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
    const inIdleTimeout = deviceConfig?.configs[0]?.inIdleTimeout;
    const list = await this.dimDeviceDao.findIdleDeviceWithDaysContinuity({ deviceIdList, companyIdList, startTime, endTime, consecutiveDays: inIdleTimeout, type: 'internal' });
    const result = {};
    list.forEach(res => { result[res.deviceId] = res });
    return result;
  }

  /**
   * 查询库外闲置时长信息
   * @param param0 
   * @returns 
   */
  async searchExternalIdleDeviceMap({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }) {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return {};
    const deviceConfig = await this.dataReportConfigService.dataConfigSearch({ companyId, module: DateReport.Subject.Device });
    const inIdleTimeout = deviceConfig?.configs[0]?.outIdleTimeout;
    const list = await this.dimDeviceDao.findIdleDeviceWithDaysContinuity({ deviceIdList, companyIdList, startTime, endTime, consecutiveDays: inIdleTimeout, type: 'external' });
    const result = {};
    list.forEach(res => { result[res.deviceId] = res });
    return result;
  }

  /**
   * 查询设备在时间段内的总离线时长(开始和结束都在时间范围内)
   * @param param0 
   * @returns 
   */
  async searchDeviceOfflineTotalInTimeRangeMap({ deviceIdList, companyIdList, startTime, endTime }: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }) {
    if (_.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return 0;
    const list = await this.deviceOfflineEventDao.searchDeviceOfflineTotalInTimeRange({ deviceIdList, companyIdList, startTime, endTime });
    const result = {};
    list.forEach(res => { result[res.yqzDeviceId] = res });
    return result;
  }

  /**
   * 查询绑定时长信息
   * @param param0 
   * @returns 
   */
  async searchBindDeviceMap({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }) {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return {};
    const list = await this.dimDeviceDao.findBindDeviceWithDaysContinuity({ deviceIdList, companyIdList, startTime, endTime });
    const result = {};
    list.forEach(res => { result[res.deviceId] = res });
    return result;
  }

  async countEtlDeviceGroupOnlineStatus({ companyId, deviceIdList, companyIdList, startTime, endTime }: { companyId: string, deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string }) {
    if (!companyId || _.isEmpty(deviceIdList) || _.isEmpty(companyIdList) || !startTime || !endTime) return [{ isOffline: DeviceDataReport.IsOffline.UnOffline, count: 0 }, { isOffline: DeviceDataReport.IsOffline.Offline, count: 0 }];;
    const offlineDeviceRes = await this.getOfflineDeviceByCompany({ companyId, deviceIdList, companyIdList, startTime, endTime });
    const offlineDeviceIds = offlineDeviceRes.map(item => item.yqzDeviceId);
    return [{ isOffline: DeviceDataReport.IsOffline.UnOffline, count: deviceIdList.length - offlineDeviceIds.length }, { isOffline: DeviceDataReport.IsOffline.Offline, count: offlineDeviceIds.length }];
  }

}