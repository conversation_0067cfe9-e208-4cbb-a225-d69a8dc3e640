import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import { CommunityEntity } from "../../../../entity/bgw/community.entity";
import { CompanyEntity } from "../../../../entity/bgw/company.entity";
import { ManagerEntity } from "../../../../entity/bgw/manager.entity";
import { PersonEntity } from "../../../../entity/bgw/person.entity";
import { ProjectEntity } from "../../../../entity/bgw/project.entity";
import { DeviceHistoryEntity } from "../../../../entity/camera/device-history.entity";
import { DeviceInfoEntity } from "../../../../entity/camera/device-info.entity";
import { DeviceStatusEntity } from "../../../../entity/camera/device-status.entity";
import { Device } from "../types/device.type";
import { DaoUtil } from "@src/util/dao.util"
import * as _ from "lodash";
import { DevTimeout } from "@yqz/nest";
import { DeviceOfflineEventEntity } from "@src/modules/report/entity/camera/device-offline-event.entity";
import { DimDeviceEntity } from "@src/modules/report/entity/etl/dim-device.entity";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { DeviceDataReport } from "../../../corp/data-report/service/basic/dto/device-data-report.dto";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { generateDateList } from "@src/util/datetime.util";
import { ProjectTidinessDetectionEntity } from "@src/modules/report/entity/camera/project-tidiness-detection.entity";

@Injectable()
export class DeviceDao {
  private readonly logger = new MyLogger(DeviceDao.name)
  constructor(
    private dataSource: DataSource
  ) { }


  @DevTimeout(500)
  async test() {
    // this.searchDeviceList({yqzDeviceIds:[

    // ]}).then(res => {
    //   this.logger.log(res)
    // })
    // const res = await this.searchCameraSituationNum({
    //   companyId: '65',
    //   startTime: '2023-11-21',
    //   endTime: '2023-11-22'
    // });
    // console.log(res)
  }

  async device({ companyId, departmentId, date }: { companyId: string, departmentId: string, date: string }) {
    return await this.dataSource.manager.find(DimDeviceEntity, {
      where: {
        companyId: companyId,
        departmentId: departmentId,
        managerId: null,
        projectId: null,
        date: date
      }
    });
  }

  /**
   * 
   * @param companyId 
   * @param managerIdList 
   * @param installled 是否安装在工地
   * @returns 
   */
  async searchDeviceStatus(params: Device.SearchReq): Promise<{ total: number, items: Device.StatusDto[] }> {
    DaoUtil.checkEmptyParams(params, this.searchDeviceStatus.name, 'FailIfAllEmpty')
    const { companyId, managerIdList, projectIdList, installled, projectIsDel } = params;
    const qb = this.dataSource
      .createQueryBuilder()
      .from(DeviceStatusEntity, 'ds')
      .select([
        'di.device_serial deviceSerial',
        'ds.yqz_device_id yqzDeviceId',
        'ds.status status',
        'ds.cover_image_url coverImage',
        'ds.company_id companyId',
        'ds.start_time startTime',
        'ds.end_time endTime',
        'ds.is_online isOnline',
        'ds.onoffline_time onOfflineTIme',
        'if( ds.is_online=1, 0,  TIMESTAMPDIFF(MINUTE, ds.onoffline_time, now()) ) offlineDurationInMin',
        'ds.manager_revice_time managerReceiptTime',
        //project_manager_id为空未领用, 则从开始时间算起
        //project_id为空未绑定, 则从开始时间算起; 否则从领用使用算起,给3天缓冲
        `if( 
            ds.project_manager_id is null,  
            TIMESTAMPDIFF(DAY, ds.start_time, now()), 
            if( ds.project_id is not null,  0,  TIMESTAMPDIFF(DAY, ds.manager_revice_time, now()) - 3 ) 
          ) idleDurationInDays`,
        'cpy.company_name companyName',
        'ds.project_id projectId',
        'ds.project_manager_id managerId',
        'p.name managerName',
        'com.address address',
        'prj.house_number houseNumber',
        'ds.department_id departmentId',
      ])
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(CompanyEntity, 'cpy', 'ds.company_id = cpy.company_id')
      .leftJoin(ProjectEntity, 'prj', 'ds.project_id = prj.project_id')
      .leftJoin(CommunityEntity, 'com', 'prj.community_id = com.community_id')
      .leftJoin(ManagerEntity, 'm', 'ds.project_manager_id = m.manager_id')
      .leftJoin(PersonEntity, 'p', 'm.person_id = p.person_id')
      .where('ds.delete_flag  = :delFlag', { delFlag: 'N' });
    if (companyId) qb.andWhere('cpy.company_id = :companyId', { companyId });
    if (!_.isEmpty(managerIdList)) qb.andWhere('ds.project_manager_id in (:...managerIdList)', { managerIdList });
    if (!_.isEmpty(projectIdList)) qb.andWhere('ds.project_id in (:...projectIdList)', { projectIdList });
    if (projectIsDel === 'N') qb.andWhere('prj.delete_flag  = :delFlag', { delFlag: 'N' });
    if (installled === 'Y') qb.andWhere("( ds.project_id is not null and ds.project_id <> '' )");
    if (installled === 'N') qb.andWhere("( ds.project_id is null or ds.project_id = '' )");
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<Device.StatusDto>(qb);
  }

  async searchDeviceInfoList({ yqzDeviceIds }: { yqzDeviceIds: string[] }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'di.device_serial deviceSerial',
        'di.yqz_device_id yqzDeviceId'
      ])
      .from(DeviceInfoEntity, 'di')
      .where('di.delete_flag  = :delFlag', { delFlag: 'N' });
    if (!_.isEmpty(yqzDeviceIds)) qb.andWhere('di.yqz_device_id in (:...yqzDeviceIds)', { yqzDeviceIds });
    return await qb.getRawMany<{ deviceSerial: string, yqzDeviceId: string }>();
  }

  async searchLatestDeviceHistory(params?: { companyId?: string, yqzDeviceIdList?: string[] }) {
    const { companyId, yqzDeviceIdList } = params || {}
    const subqb = this.dataSource.createQueryBuilder()
      .select('max(device_history_id)')
      .from(DeviceHistoryEntity, '')
      .groupBy('company_id')
      .addGroupBy('yqz_device_id')
    if (companyId) subqb.andWhere('company_id = :companyId', { companyId })
    if (yqzDeviceIdList) subqb.andWhere('yqz_device_id  in (:...yqzDeviceIdList)', { yqzDeviceIdList });
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'company_id companyId',
        'yqz_device_id yqzDeviceId',
        'receive_manager managerId',
        'action_type actionType',
        'action_time actionTime'
      ])
      .from(DeviceHistoryEntity, '')
      .where(`device_history_id in (${subqb.getQuery()})`)
      .setParameters(subqb.getParameters());
    return await qb.getRawMany<Device.EventDto>();
  }

  async searchCameraSituationNum(params: Device.SearchSituation) {
    const errqb = this.dataSource.createQueryBuilder()
      .select([
        'SUM((CASE ' +
        "WHEN de.from_time < '" + params.endTime + "' AND de.to_time is NULL THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
        "WHEN de.to_time >= '" + params.startTime + "' AND de.to_time < '" + params.endTime + "'  THEN TIMESTAMPDIFF(SECOND,de.from_time,de.to_time) " +
        "WHEN de.from_time < '" + params.endTime + "' AND de.to_time >= '" + params.endTime + "' THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
        "END) >= 14400) AS offlineNum"//大于等于4小时（单位：秒）
      ])
      .from(DeviceOfflineEventEntity, 'de')
      .leftJoin(DeviceStatusEntity, 'status', 'status.company_id = de.company_id and status.yqz_device_id = de.yqz_device_id and status.project_id = de.project_id')
      .where('de.delete_flag = "N"')
      .andWhere('status.DELETE_FLAG = "N"')
      .andWhere('de.company_id = :companyId', { companyId: params.companyId });
    if (!_.isEmpty(params.managerIds)) errqb.andWhere('de.manager_id in (:...managerIds)', { managerIds: params.managerIds });
    errqb.andWhere("((de.from_time between :startTime and :endTime) or (de.to_time between :startTime and :endTime) or (de.to_time is null))", { startTime: params.startTime, endTime: params.endTime })
      .andWhere('de.company_id is not null')
      .andWhere('de.project_id is not null')
      .andWhere('de.manager_id is not null');

    const qb = this.dataSource.createQueryBuilder()
      .select([
        'COUNT(ds.yqz_device_id) AS totalNum',
        'IFNULL(SUM(ds.project_manager_id IS NOT NULL),0) AS receiveNum',//设备已领用
        'IFNULL(SUM(ds.project_manager_id IS NULL),0) AS stockIdleNum',//设备未领用
        'IFNULL(SUM(ds.project_manager_id IS NOT NULL AND ds.project_id IS NULL),0) AS outStockIdleNum',//设备未绑定工地数
        'IFNULL((' + errqb.getQuery() + '),0) AS offlineNum' // 设备离线数4小时（秒）
      ])
      .from(DeviceStatusEntity, 'ds')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.company_id = :companyId', { companyId: params.companyId });
    if (!_.isEmpty(params.managerIds)) qb.andWhere('ds.project_manager_id in (:...managerIds)', { managerIds: params.managerIds });
    qb.setParameters(errqb.getParameters());
    return await qb.getRawOne();
  }

  //查询智慧工地
  async searchAiProjectIds({ projectIds }: { projectIds: string[] }) {
    if (_.isEmpty(projectIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.project_id as projectId'
      ])
      .from(DeviceStatusEntity, 'ds')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.project_id in (:...projectIds)', { projectIds })
      .groupBy('ds.project_id');
    const result = await qb.getRawMany<{ projectId: string }>();
    return result.map(x => x.projectId);
  }

  //查询智慧工地
  async searchDeviceIdsByDept({ departmentIds }: { departmentIds: string[] }) {
    if (_.isEmpty(departmentIds)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.yqz_device_id as yqzDeviceId'
      ])
      .from(DeviceStatusEntity, 'ds')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.department_id in (:...departmentIds)', { departmentIds })
      .groupBy('ds.yqz_device_id');
    const result = await qb.getRawMany<{ yqzDeviceId: string }>();
    return result.map(x => x.yqzDeviceId);
  }

  async searchEtlAiProjectIds({ projectIds, startTime, endTime }: { projectIds: string[], startTime: string, endTime: string }) {
    if (_.isEmpty(projectIds) || !startTime || !endTime) return [];
    const dataList = generateDateList({ startTime, endTime });
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.project_id as projectId'
      ])
      .from(DimDeviceEntity, 'ds')
      .where('ds.project_id in (:...projectIds)', { projectIds })
      .andWhere('ds.date in (:...dataList)', { dataList })
      // .andWhere('ds.date >= :startTime', { startTime })
      // .andWhere('ds.date < :endTime', { endTime })
      .groupBy('ds.project_id');
    const result = await qb.getRawMany<{ projectId: string }>();
    return result.map(x => x.projectId);
  }

  async searchBindDeviceProject(params: Device.SearchSituation) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.project_id as projectId'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('p.delete_flag = "N"')
    if (params.companyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.companyId })
    if (!_.isEmpty(params.projectIds)) qb.andWhere('ds.project_id in (:...projectIds)', { projectIds: params.projectIds })
    qb.andWhere('ds.project_id IS NOT NULL')
    qb.groupBy('ds.project_id');
    return await qb.getRawMany();
  }

  async searchDeviceList(params: Device.DeviceDetailReq) {
    const errqb = this.dataSource.createQueryBuilder()
      .select([
        'de.yqz_device_id as yqz_device_id',
        'de.project_id as project_id',
        'MAX( ' +
        'CASE ' +
        "WHEN de.from_time < '" + params.endTime + "' AND de.to_time is NULL THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
        "WHEN de.to_time >= '" + params.startTime + "' AND de.to_time < '" + params.endTime + "'  THEN TIMESTAMPDIFF(SECOND,de.from_time,de.to_time) " +
        "WHEN de.from_time < '" + params.endTime + "' AND de.to_time >= '" + params.endTime + "' THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
        "END) AS diffSec"
      ])
      .from(DeviceOfflineEventEntity, 'de')
      .where('de.delete_flag = "N"')
      .andWhere('de.company_id in (:...companyIds)', { companyIds: params.companyIds })
      .andWhere('de.yqz_device_id in (:...devices)', { devices: params.yqzDeviceIds })
      .andWhere('de.company_id is not null')
      .andWhere('de.project_id is not null')
      .andWhere('de.manager_id is not null')
      .groupBy('de.yqz_device_id').addGroupBy('de.project_id');

    const qb = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as deviceId',
        'di.device_serial as deviceSerial',
        'dm.manager_id as deviceManagerId',
        'm.nick_name as deviceManagerName',
        'DATE_FORMAT(dm.manager_revice_time, "%Y-%m-%d %H:%i:%s") as managerReviceTime',//领用时间
        'dm.project_id as projectId',
        'DATE_FORMAT(dm.project_binding_time, "%Y-%m-%d %H:%i:%s") as projectBindTime',//绑定时间
        'if(err.diffSec is null,"Y","N") as isOnline',
        'err.diffSec as diffSec',//离线时间（秒）
        'p.project_director as projectManagerId',
        'dir.nick_name as projectManagerName',
        'dm.company_id as linkCompanyId',
        'c.SHORT_NAME as linkCompanyName',
        'c.CREATE_TIME as linkCompanyCreateTime',
        'ds.link_company_time as linkCompanyTime'
      ])
      .from(DimDeviceEntity, 'dm')
      .leftJoin(DeviceStatusEntity, 'ds', 'dm.device_id = ds.yqz_device_id')
      .leftJoin(DeviceInfoEntity, 'di', 'dm.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'dm.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = dm.manager_id')
      .leftJoin(CompanyEntity, 'c', 'c.company_id = dm.company_id')
      .leftJoin(ManagerEntity, 'dir', 'dir.person_id = p.project_director and dir.company_id = p.company_id and dir.delete_flag = "N"')
      .leftJoin(CommunityEntity, 'com', 'p.community_id = com.community_id')
      .leftJoin("(" + errqb.getQuery() + ")", 'err', 'err.yqz_device_id = dm.device_id and err.project_id = dm.project_id')
      .where('dm.company_id in (:...companyIds)', { companyIds: params.companyIds })
      .andWhere('dm.device_id in (:...yqzDeviceId)', { yqzDeviceId: params.yqzDeviceIds });
    if (params.deviceManagerId) qb.andWhere('dm.manager_id = :deviceManagerId', { deviceManagerId: params.deviceManagerId });
    if (params.projectManagerId) qb.andWhere('p.project_director = :projectManagerId', { projectManagerId: params.projectManagerId });
    if (params.linkCompanyId) qb.andWhere('dm.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIds)) qb.andWhere('p.project_id in (:...projectIds)', { projectIds: params.projectIds });
    if (params.bindProject == 'Y') qb.andWhere('dm.project_id is not null');
    if (params.isOnline == 'Y') qb.andWhere('dm.project_id is not null and (err.diffSec < 14400 or err.diffSec is null)');//离线时间小于4个小时 或 为null
    if (params.isOnline == 'N') qb.andWhere('dm.project_id is not null and err.diffSec >= 14400');//离线时间大于4个小时
    if (params.deviceSerial) qb.andWhere('LOCATE(:deviceSerial, di.device_serial) > 0', { deviceSerial: params.deviceSerial });
    // qb.andWhere("dm.date >= :startTime and dm.date < :endTime", { startTime: params.startTime, endTime: params.endTime });
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    qb.andWhere('dm.date in (:...dateList)', { dateList: dataList });
    qb.orderBy('dm.date', 'DESC')
    qb.having('1');
    const queryBuilder = this.dataSource.createQueryBuilder()
      .from('(' + qb.getQuery() + ')', 'pcg')
      .groupBy('pcg.deviceId');
    qb.setParameters(errqb.getParameters());
    queryBuilder.setParameters(qb.getParameters());
    const total = (await queryBuilder.getRawMany()).length;
    if (params.pageNo) {
      const pageSize = params.pageSize || 10;
      queryBuilder.offset((params.pageNo - 1) * pageSize).limit(pageSize);
    }
    if (params.sortBy && params.sortBy.length > 0)
      params.sortBy.forEach((e) => {
        if (!e.order) return;
        queryBuilder.addOrderBy(
          `${String(e.field)}`,
          e.order === 'ascending' ? 'ASC' : 'DESC'
        );
      });
    const items = await queryBuilder.getRawMany();
    return { total, items };
  }

  async searchReportDeviceCount({ companyId }: { companyId: string }) {
    const qb = this.dataSource.createQueryBuilder()
      .select('COUNT(1) num')
      .from(DeviceStatusEntity, 'ds')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.company_id = :companyId', { companyId });
    const raw = await qb.getRawOne();
    return raw.num;
  }

  async countEtlDeviceGroupCompany(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.company_id as companyId',
        'c.company_name as companyName'
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(CompanyEntity, 'c', 'ds.company_id = c.company_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.company_id');
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
  }

  async countEtlDeviceGroupDepartment(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.department_id as departmentId',
        'dep.department_name as departmentName'
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(DepartmentEntity, 'dep', 'ds.department_id = dep.department_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.department_id');
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawMany<{ count: string, departmentId: string, departmentName: string }>();
  }

  async countEtlDeviceGroupReviceManager(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.revice_manager_id as reviceManagerId',
        'm.nick_name as reviceManagerName'
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = ds.revice_manager_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.revice_manager_id');
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawMany<{ count: string, reviceManagerId: string, reviceManagerName: string }>();
  }

  async countEtlDeviceGroupProjectManager(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName'
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(ProjectEntity, 'p', 'p.project_id = ds.project_id')
      .leftJoin(ManagerEntity, 'm', 'p.project_manager = m.manager_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('p.project_manager');
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  }

  async countEtlDevice(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return { count: '0' };
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawOne<{ count: string }>();
  }

  async countEtlDeviceGroupReviceStatus(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        `CASE WHEN ds.revice_manager_id IS NOT NULL THEN '${DeviceDataReport.IsRevice.Revice}' ELSE '${DeviceDataReport.IsRevice.UnRevice}' END AS isRevice`,
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('isRevice');
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawMany<{ count: string, isRevice: DeviceDataReport.IsRevice }>();
  }

  async countEtlDeviceGroupBindStatus(params: Device.SearchEtlDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
    // 子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    // 子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    // 主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        `CASE WHEN ds.project_id IS NOT NULL THEN '${DeviceDataReport.IsBind.Bind}' ELSE '${DeviceDataReport.IsBind.UnBind}' END AS isBind`,
      ])
      .from('(' + sub2Query.getQuery() + ')', 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('isBind');
    qb.setParameters(sub2Query.getParameters());
    return await qb.getRawMany<{ count: string, isBind: DeviceDataReport.IsBind }>();
  }

  //待完善
  // async countEtlDeviceGroupOnlineStatus(params: { deviceIdList: string[], companyIdList: string[], startTime: string, endTime: string, projectManagerId?: string, linkCompanyId?: string }) {
  //   //idList为空直接返回
  //   if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return [];
  //   // 子查询1：获取每个 device_id 的最大日期
  //   const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
  //   const sub1Query = this.dataSource.createQueryBuilder()
  //     .select([
  //       'dm.device_id as device_id',
  //       'MAX(dm.date) as max_date'
  //     ])
  //     .from(DimDeviceEntity, 'dm')
  //     .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
  //     .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
  //     // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
  //     .andWhere('dm.date in (:...dateList)', { dateList: dataList })
  //     .groupBy('dm.device_id');
  //   // 子查询2：结合子查询，获取最后一条记录
  //   const sub2Query = this.dataSource.createQueryBuilder()
  //     .select([
  //       'dm.device_id as device_id',
  //       'dm.company_id as company_id',
  //       'dm.department_id as department_id',
  //       'dm.manager_id as revice_manager_id',
  //       'dm.project_id as project_id',
  //       'dm.date as date'
  //     ])
  //     .from(DimDeviceEntity, 'dm')
  //     .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
  //     .setParameters(sub1Query.getParameters());
  //   // 主查询
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       'count(1) as count',
  //       `CASE WHEN d.project_id IS NOT NULL THEN '${DeviceDataReport.IsOnline.Offline}' ELSE '${DeviceDataReport.IsOnline.Online}' END AS isBind`,
  //     ])
  //     .from('(' + sub2Query.getQuery() + ')', 'd')
  //   //关联设备离线记录
  //   if (params.linkCompanyId) qb.andWhere('d.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
  //   // if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
  //   //所属公司
  //   //设备序列号
  //   //领用人
  //   //工地地址？
  //   //在线状态
  //   qb.groupBy('isBind');
  //   qb.setParameters(sub2Query.getParameters());
  //   return await qb.getRawMany<{ count: string, isOnline: DeviceDataReport.IsOnline }>();
  // }

  async countDeviceGroupDeviceStatus(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.status as status'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.status');
    return await qb.getRawMany<{ count: string, status: DeviceDataReport.Status }>();
  }

  async countDeviceGroupCompany(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.company_id as companyId',
        'c.company_name as companyName'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(CompanyEntity, 'c', 'ds.company_id = c.company_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.company_id');
    return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
  }

  async countDeviceGroupDepartment(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.department_id as departmentId',
        'dep.department_name as departmentName'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DepartmentEntity, 'dep', 'ds.department_id = dep.department_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.department_id');
    return await qb.getRawMany<{ count: string, departmentId: string, departmentName: string }>();
  }

  async countDeviceGroupReviceManager(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'ds.project_manager_id as reviceManagerId',
        'm.nick_name as reviceManagerName'
      ])
      .from(DeviceStatusEntity, 'ds')
      .innerJoin(ManagerEntity, 'm', 'ds.project_manager_id = m.manager_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.project_manager_id');
    return await qb.getRawMany<{ count: string, reviceManagerId: string, reviceManagerName: string }>();
  }

  async countDeviceGroupProjectManager(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'p.project_manager = m.manager_id')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('p.project_manager');
    return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  }

  async countDevice(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return { count: '0' };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList });
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    return await qb.getRawOne<{ count: string }>();
  }

  async countDeviceGroupReviceStatus(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        `CASE WHEN ds.project_manager_id IS NOT NULL THEN '${DeviceDataReport.IsRevice.Revice}' ELSE '${DeviceDataReport.IsRevice.UnRevice}' END AS isRevice`,
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('isRevice');
    return await qb.getRawMany<{ count: string, isRevice: DeviceDataReport.IsRevice }>();
  }

  async countDeviceGroupBindStatus(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        `CASE WHEN ds.project_id IS NOT NULL THEN '${DeviceDataReport.IsBind.Bind}' ELSE '${DeviceDataReport.IsBind.UnBind}' END AS isBind`,
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('isBind');
    return await qb.getRawMany<{ count: string, isBind: DeviceDataReport.IsBind }>();
  }

  async countDeviceGroupOnlineStatus(params: Device.SearchRealDeviceReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList)) return [];
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        `CASE WHEN ds.is_online = 1 THEN '${DeviceDataReport.IsOnline.Online}' ELSE '${DeviceDataReport.IsOnline.Offline}' END AS isOnline`,
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
    if (params.linkCompanyId) qb.andWhere('ds.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('isOnline');
    return await qb.getRawMany<{ count: string, isOnline: DeviceDataReport.IsOnline }>();
  }

  async searchRealDeviceList(params: Device.SearchRealDeviceListReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList)) return { total: 0, items: [] };
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.yqz_device_id as yqzDeviceId',
        'di.device_serial as deviceSerial',
        'ds.company_id as companyId',
        'ds.project_id as projectId',
        'ds.project_manager_id as reviceManagerId',
        'm.nick_name as reviceManagerName',
        'date_format(ds.manager_revice_time, "%Y-%m-%d") as reviceDate',
        'date_format(ds.project_binding_time, "%Y-%m-%d") as bindingDate',
        'if(ds.is_online = 1, "Y", "N") as isOnline',
        'date_format(ds.start_time, "%Y-%m-%d") as startDate',
        'date_format(ds.end_time, "%Y-%m-%d") as endDate',
        'case when ds.is_online = 1 then null else ds.onoffline_time end lastOnlineTime',
        'ds.department_id as departmentId',
        'dep.department_name as departmentName'
      ])
      .from(DeviceStatusEntity, 'ds')
      .leftJoin(DeviceInfoEntity, 'di', 'ds.yqz_device_id = di.yqz_device_id')
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'ds.project_manager_id = m.manager_id')
      .leftJoin(DepartmentEntity, 'dep', 'ds.department_id = dep.department_id')
      .where('ds.delete_flag = "N"')
      .andWhere('ds.yqz_device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('ds.company_id in (:...companyIds)', { companyIds: params.companyIdList });
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.isOnline && params.isOnline === 'Y') qb.andWhere('ds.is_online = 1');//在线
    if (params.isOnline && params.isOnline === 'N') qb.andWhere('ds.is_online = 0');//离线
    if (params.reviceManagerId) qb.andWhere('ds.project_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.yqz_device_id')
    qb.orderBy('ds.yqz_device_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<{ yqzDeviceId: string, deviceSerial: string, companyId: string, projectId: string, reviceManagerId: string, reviceManagerName: string, reviceDate: string, bindingDate: string, isOnline: string, startDate: string, endDate: string, lastOnlineTime: string, departmentId: string, departmentName: string }>(qb);
  }

  async searchEtlDeviceList(params: Device.SearchEtlDeviceListReq) {
    //idList为空直接返回
    if (_.isEmpty(params.deviceIdList) || _.isEmpty(params.companyIdList) || !params.startTime || !params.endTime) return { total: 0, items: [] };
    //子查询1：获取每个 device_id 的最大日期
    const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
    const sub1Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as device_id',
        'MAX(dm.date) as max_date'
      ])
      .from(DimDeviceEntity, 'dm')
      .where('dm.device_id in (:...deviceIds)', { deviceIds: params.deviceIdList })
      .andWhere('dm.company_id in (:...companyIds)', { companyIds: params.companyIdList })
      // .andWhere('dm.date >= :startTime and dm.date < :endTime', { startTime: params.startTime, endTime: params.endTime })
      .andWhere('dm.date in (:...dateList)', { dateList: dataList })
      .groupBy('dm.device_id');
    //子查询2：结合子查询，获取最后一条记录
    const sub2Query = this.dataSource.createQueryBuilder()
      .select([
        'dm.device_id as yqz_device_id',
        'dm.company_id as company_id',
        'dm.department_id as department_id',
        'dm.manager_id as revice_manager_id',
        'dm.manager_revice_time as manager_revice_time',
        'dm.project_binding_time as project_binding_time',
        'dm.project_id as project_id',
        'dm.date as date'
      ])
      .from(DimDeviceEntity, 'dm')
      .innerJoin('(' + sub1Query.getQuery() + ')', 'sub', 'dm.device_id = sub.device_id AND dm.date = sub.max_date')
      .setParameters(sub1Query.getParameters());
    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.yqz_device_id as yqzDeviceId',
        'di.device_serial as deviceSerial',
        'ds.company_id as companyId',
        'ds.project_id as projectId',
        'ds.department_id as departmentId',
        'dep.department_name as departmentName',
        'ds.revice_manager_id as reviceManagerId',
        'm.nick_name as reviceManagerName',
        'date_format(ds.manager_revice_time, "%Y-%m-%d") as reviceDate',
        'date_format(ds.project_binding_time, "%Y-%m-%d") as bindingDate',
      ])
      .from(DeviceInfoEntity, 'di')
      .innerJoin(`(${sub2Query.getQuery()})`, 'ds', 'ds.yqz_device_id = di.yqz_device_id') // 使用子查询结果作为临时表
      .leftJoin(ProjectEntity, 'p', 'ds.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'ds.revice_manager_id = m.manager_id')
      .leftJoin(DepartmentEntity, 'dep', 'ds.department_id = dep.department_id')
      .setParameters(sub2Query.getParameters())
    qb.where('di.delete_flag = "N"')
    if (params.linkCompanyId) qb.andWhere('p.company_id = :companyId', { companyId: params.linkCompanyId });//所属公司
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });//工地负责人
    if (params.reviceManagerId) qb.andWhere('ds.revice_manager_id = :reviceManagerId', { reviceManagerId: params.reviceManagerId });//领用人
    if (params.projectAddress) {//工地地址
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (params.deviceSerial) qb.andWhere('di.device_serial like :deviceSerial', { deviceSerial: `%${params.deviceSerial}%` });//设备序列号
    qb.groupBy('ds.yqz_device_id')
    qb.orderBy('ds.yqz_device_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<{ yqzDeviceId: string, deviceSerial: string, companyId: string, projectId: string, reviceManagerId: string, reviceManagerName: string, reviceDate: string, bindingDate: string, departmentId: string, departmentName: string }>(qb);
  }

  async searchSoonExpiredDeviceIds({ deviceIdList, deviceExpire }: { deviceIdList: string[], deviceExpire: string }) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'ds.yqz_device_id as yqzDeviceId',
      ])
      .from(DeviceStatusEntity, 'ds')
      .where('ds.yqz_device_id in (:...deviceIds)', { deviceIds: deviceIdList })
      .andWhere('ds.delete_flag = "N"')
      .andWhere('ds.status = 2')//只查询正常设备
      .andWhere('ds.end_time is not null')
      .andWhere('(DATEDIFF(DATE(ds.end_time), DATE(now())) + 1) <= :deviceExpire', { deviceExpire })
      .groupBy('ds.yqz_device_id')
    const result = await qb.getRawMany();
    return result.map(item => item.yqzDeviceId);
  }

  async searchInstallErrorDevice({ deviceIdList }: { deviceIdList: string[] }) {
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'ds.yqz_device_id as yqzDeviceId',
        'ds.project_id as projectId',
        'Max(ptd.project_tidiness_detection_id) as projectTidinessDetectionId'
      ])
      .from(DeviceStatusEntity, 'ds')
      .innerJoin(ProjectTidinessDetectionEntity, 'ptd', 'ds.yqz_device_id = ptd.yqz_device_id and ds.project_id = ptd.project_id and ptd.delete_flag = "N"')
      .where('ds.yqz_device_id in (:...deviceIds)', { deviceIds: deviceIdList })
      .andWhere('ds.delete_flag = "N"')
      .groupBy('ds.yqz_device_id');

    //主查询
    const qb = this.dataSource.createQueryBuilder()
      .select('p.yqz_device_id AS yqzDeviceId')
      .from(ProjectTidinessDetectionEntity, 'p')
      .innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_tidiness_detection_id = dpd.projectTidinessDetectionId') // 确保是最后一条记录
      .where('p.tidiness_type = 4')
      .setParameters(subQuery.getParameters())
    const res = await qb.getRawMany<{ yqzDeviceId: string }>();
    return res.map(item => item.yqzDeviceId);
  }

}