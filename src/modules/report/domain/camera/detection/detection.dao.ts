import { Injectable } from '@nestjs/common';
import { Connection } from "typeorm";
import * as _ from "lodash";
import { MyLogger } from '@yqz/nest';
import { DeviceFaceDetectionResultEntity } from '@src/modules/report/entity/camera/device-face-detection-result.entity';
import { MotiondetectEventEntity } from '@src/modules/report/entity/camera/motiondetect-event.entity';
import { CompanyEntity } from '@src/modules/report/entity/bgw/company.entity';
import { ProjectEntity } from '@src/modules/report/entity/bgw/project.entity';
import { ManagerEntity } from '@src/modules/report/entity/bgw/manager.entity';
import { PersonEntity } from '@src/modules/report/entity/bgw/person.entity';
import { Timeout } from '@nestjs/schedule';
import { ProjectSignInEntity } from '@src/modules/report/entity/bgw/project-sign-in.entity';
import { formatDate } from '@src/util/datetime.util';
import { ApiResultEntity } from '@src/modules/report/entity/camera/api-result.entity';


@Injectable()
export class DetectionDao {

    private readonly logger = new MyLogger(DetectionDao.name)

    constructor(
        private readonly connection: Connection
    ) { }

    // async searchDetectionFaceAnalysis(params: { companyId?: string, personId?: string, startTime?: string, endTime?: string, type?: "1" | "2" | "3", maxNum?: number, minNum?: number, pageNo: number, pageSize: number }) {
    //     const ty1 = this.connection.getRepository(DeviceFaceDetectionResultEntity)
    //     .createQueryBuilder('dr')
    //     .select([
    //         'dr.manager_id managerId',
    //         'DATE_FORMAT(dr.event_time,"%Y-%m-%d") eventTime'
    //     ])
    //     .where('confidence > 65')

    //     const ty2 = this.connection.getRepository(DeviceFaceDetectionResultEntity)
    //     .createQueryBuilder('dr')
    //     .select([
    //         'dr.manager_id managerId',
    //         'DATE_FORMAT(dr.event_time,"%Y-%m-%d") eventTime'
    //     ])
    //     .groupBy('manager_id')
    //     .addGroupBy('DATE_FORMAT(dr.event_time,"%Y-%m-%d")')
    //     .having('COUNT(1) >= ' + params.minNum)
    //     if (params.maxNum > 0) {
    //         ty2.andHaving('COUNT(1) <= ' + params.maxNum);
    //     }

    //     const queryBuilder = this.connection.getRepository(DeviceFaceDetectionResultEntity)
    //         .createQueryBuilder('ddr')
    //         .select([
    //             'ddr.oss_photo_url as ossPhotoUrl',
    //             'ddr.front_face_photo as frontFacePhoto',
    //             'ddr.nick_name as nickName',
    //             'ddr.confidence as confidence',
    //             'ddr.PERSON_ID as personId',
    //             'DATE_FORMAT(ddr.event_time,"%Y-%m-%d") as eventDay',
    //             'DATE_FORMAT(ddr.event_time,"%Y-%m-%d %H:%i:%s") as eventTime',
    //             'ddr.SHORT_NAME as companyName',
    //             'ddr.COMPANY_ID as companyId',
    //             'ddr.PROJECT_NAME as projectName',
    //         ])
    //     if(params.type == '1') {
    //         queryBuilder.leftJoin('(' + ty1.getQuery() + ")",'a','ddr.manager_id = a.managerId AND DATE_FORMAT(ddr.event_time,"%Y-%m-%d") = a.eventTime');
    //     }
    //     if (params.type == '2') {
    //         queryBuilder.leftJoin('(' + ty2.getQuery() + ")", 'b', 'ddr.manager_id = b.managerId AND DATE_FORMAT(ddr.event_time,"%Y-%m-%d") = b.eventTime');
    //     }
    //     if (params.companyId) queryBuilder.andWhere('ddr.COMPANY_ID = :companyId', { companyId: params.companyId });
    //     if (params.personId) queryBuilder.andWhere('ddr.PERSON_ID = :personId', { personId: params.personId });
    //     if (params.startTime && params.endTime) {
    //         if (params.startTime.length > 14) {
    //             queryBuilder.andWhere('ddr.event_time >= :startTime', { startTime: params.startTime });
    //         } else {
    //             queryBuilder.andWhere('ddr.event_time >= :startTime', { startTime: params.startTime + ' 00:00:00' });
    //         }
    //         if (params.endTime.length > 14) {
    //             queryBuilder.andWhere('ddr.event_time < :endTime', { endTime: params.endTime });
    //         } else {
    //             queryBuilder.andWhere('ddr.event_time < :endTime', { endTime: params.endTime + ' 23:59:59' });
    //         }
    //     }
    //     if (params.type == '1') {
    //         queryBuilder.andWhere('a.managerId is not NULL');
    //         queryBuilder.andWhere('a.eventTime is not NULL');
    //     }
    //     if (params.type == '2') {
    //         queryBuilder.andWhere('b.managerId is not NULL');
    //         queryBuilder.andWhere('b.eventTime is not NULL');
    //     }
    //     if (params.type == '3') {
    //         queryBuilder.andWhere('ddr.type = "2"');
    //     }
    //     queryBuilder.groupBy('ddr.id')
    //     queryBuilder.orderBy('eventDay','DESC');
    //     queryBuilder.addOrderBy('ddr.COMPANY_ID','ASC');
    //     queryBuilder.addOrderBy('ddr.PROJECT_ID','ASC');
    //     queryBuilder.addOrderBy('ddr.PERSON_ID','ASC');
    //     queryBuilder.addOrderBy('eventTime','ASC');
    //     console.log(queryBuilder.getQuery())
    //     const count = await queryBuilder.getCount();
    //     if (params.pageSize) {
    //         queryBuilder.limit(params.pageSize).offset((params.pageNo - 1) * params.pageSize);
    //     }
    //     if (params.type == '1') {
    //         queryBuilder.setParameters(ty1.getParameters())
    //     }
    //     if (params.type == '2') {
    //         queryBuilder.setParameters(ty2.getParameters())
    //     }
    //     const res = await queryBuilder.getRawMany();
    //     return {
    //         count,
    //         rows: res
    //     };
    // }

    async getDeviceFaceDetectionResult() {
        const queryBuilder = this.connection.getRepository(ApiResultEntity)
            .createQueryBuilder('pdr')
            .select([
                'pdr.api_result as aliFaceRecognitionResult',
                'pdr.event_id as eventId',
                'm.oss_photo_url as ossPhotoUrl',
                'm.event_time as eventTime',
                'c.SHORT_NAME as companyName',
                'c.COMPANY_ID as companyId',
                'p.PROJECT_NAME as projectName',
                'p.PROJECT_ID as projectId'
            ])
            .leftJoin(MotiondetectEventEntity,'m','`pdr`.`event_id` = `m`.`id`')
            .leftJoin(CompanyEntity,'c','`c`.`COMPANY_ID` = m.COMPANY_ID')
            .leftJoin(ProjectEntity,'p','`p`.`PROJECT_ID` = m.PROJECT_ID')
            .where('pdr.type = "ali_search_face"')
            const res = await queryBuilder.getRawMany();
        return res;
    }

    async getManagerInfo(managerId: string) {
        const queryBuilder = this.connection.getRepository(ManagerEntity)
            .createQueryBuilder('m')
            .select([
                'p.person_id as personId',
                'p.front_face_photo as frontFacePhoto',
                'm.nick_name as nickName',
            ])
            .leftJoin(PersonEntity, 'p', 'm.person_id = p.person_id')
            .where('m.manager_id = :managerId', { managerId })
        const res = await queryBuilder.getRawOne();
        return res;
    }

    async saveDeviceFaceDetectionResultEntity(param: DeviceFaceDetectionResultEntity) {
        const res = await this.connection.manager.save(DeviceFaceDetectionResultEntity, param);
        return res;
    }

    async getProjectSignIn(projectId: string, managerId: string, event: string) {
        const queryBuilder = this.connection.getRepository(ProjectSignInEntity)
            .createQueryBuilder('psi')
            .select([
                'psi.PROJECT_SIGN_IN_ID id'
            ])
            .where('delete_flag = "N"')
            .andWhere("PROJECT_ID = :projectId", { projectId })
            .andWhere("MANAGER_ID = :managerId", { managerId })
            .andWhere("type = 1")
            .andWhere('DATE_FORMAT(SIGN_IN_TIME,"%Y-%m-%d") = :event', { event })
        const res = await queryBuilder.getRawMany();
        return res;
    }

    // @Timeout(100)
    async test() {
        let list = await this.getDeviceFaceDetectionResult();
        console.log(list.length)
        for (let res of list) {
            let obj = JSON.parse(res.aliFaceRecognitionResult);
            if (obj.code == '200') {
                const eventId = String(res.eventId)
                const matchList = obj.data.MatchList
                for (let match of matchList) {
                    let entity = new DeviceFaceDetectionResultEntity();
                    entity.eventId = eventId;
                    entity.managerId = match.FaceItems[0].EntityId
                    entity.faceId = match.FaceItems[0].FaceId;
                    entity.confidence = match.FaceItems[0].Confidence;
                    entity.score = match.FaceItems[0].Score;
                    entity.qualitieScore = match.QualitieScore;
                    entity.ossPhotoUrl = res.ossPhotoUrl;
                    entity.eventTime = res.eventTime;
                    entity.companyId = res.companyId;
                    entity.shortName = res.companyName;
                    entity.projectId = res.projectId;
                    entity.projectName = res.projectName;
                    const time = formatDate(entity.eventTime, 'yyyy-MM-dd');
                    const list = await this.getProjectSignIn(entity.projectId, entity.managerId, time);
                    if (!_.isEmpty(list)) { entity.type = '2' } else { entity.type = '1' }
                    const managerInfo = await this.getManagerInfo(match.FaceItems[0].EntityId);
                    if (managerInfo) {
                        entity.personId = managerInfo.personId;
                        entity.nickName = managerInfo.nickName;
                        entity.frontFacePhoto = managerInfo.frontFacePhoto;
                    } else {
                        continue;
                    }
                    await this.saveDeviceFaceDetectionResultEntity(entity);
                }
            }
        }
        console.log("end")
    }

}