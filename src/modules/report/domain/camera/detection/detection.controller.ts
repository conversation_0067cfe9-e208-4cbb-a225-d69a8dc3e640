import { Body, Controller, Post } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { DetectionService } from "./detection.service";

@ApiTags('detections')
@Controller('detections')
export class DetectionController {

  constructor(
    private readonly detectionService: DetectionService,
  ) { }

  // /**
  //  * 临时分析人脸签到接口
  //  * @param requestDTO 
  //  * @returns 
  //  */
  // @Post('face/analysis')
  // async searchDetectionFaceAnalysis(@Body() requestDTO: { companyId?: string, personId?: string, startTime?: string, endTime?: string, type?: "1" | "2", maxNum?: number, minNum?: number, pageNo:number, pageSize:number }) {
  //   const r = await this.detectionService.searchDetectionFaceAnalysis(requestDTO);
  //   return {
  //     code: 200,
  //     msg: "success",
  //     data: r
  //   }
  // }

}