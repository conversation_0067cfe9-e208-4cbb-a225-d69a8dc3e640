import { Injectable } from '@nestjs/common';
import { MyLogger } from '@yqz/nest';
import { DetectionDao } from './detection.dao';

@Injectable()
export class DetectionService {

    private readonly logger: MyLogger = new MyLogger(DetectionService.name);

    constructor(
        private readonly detectionDao: DetectionDao,
    ) { }

    // async searchDetectionFaceAnalysis({ companyId, personId, startTime, endTime, type, maxNum, minNum, pageNo, pageSize }: { companyId?: string, personId?: string, startTime?: string, endTime?: string, type?: "1" | "2", maxNum?: number, minNum?: number, pageNo: number, pageSize: number }) {
    //     if (type == '2' && (!maxNum || !minNum || maxNum <= 0 || minNum <= 0)) return { count: 0, rows: [] };
    //     let res = await this.detectionDao.searchDetectionFaceAnalysis({ companyId, personId, startTime, endTime, type, maxNum, minNum, pageNo, pageSize });
    //     // console.log(res.rows.length)
    //     return res;
    // }

}