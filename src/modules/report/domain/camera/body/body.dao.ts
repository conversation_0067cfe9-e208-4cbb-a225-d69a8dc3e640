import { Injectable } from "@nestjs/common";
import { MyMongoDb } from "@src/modules/report/mongodb/mongodb.connection";
import { My<PERSON><PERSON>, MyLogger, YqzException } from "@yqz/nest";
import * as _ from "lodash";
import * as DateFns from "date-fns";
import { OnsitePatrolType } from "../../corp/data-old-report/type/onsite-patrol.type";

@Injectable()
export class BodyDao {
    private readonly logger = new MyLogger(BodyDao.name);

    constructor() { }

    parseMatch(body: OnsitePatrolType.BodyAnalyticsByPersonReq) {
        const { companyIds, faceEntityIds, projectIds, from, to, faceIdentifyTypes, tags } = body;
        if (_.isEmpty(companyIds)) throw new YqzException("companyId 参数不能为空");
        if (!faceEntityIds) throw new YqzException("faceEntityIds 参数不能为空");
        if (faceEntityIds.length == 0) throw new YqzException("faceEntityIds 长度不能为空");
        let companyParam: any;
        if (!_.isEmpty(companyIds)) companyParam = { $in: companyIds };
        const match: any = {
            companyId: companyParam,
            trustedIdentity: true,
            faceEntityId: { $in: faceEntityIds }
        };
        if (!_.isEmpty(projectIds)) match.projectId = { $in: projectIds };
        if (!_.isEmpty(faceIdentifyTypes)) match.faceIdentifyType = { $in: faceIdentifyTypes.filter(res => !!res) };
        if (!_.isEmpty(tags)) match.tags = { $in: tags };
        const eventTime: any = {};
        let isTimeFlag = false;
        if (!isNaN(new Date(from).getTime())) {
            isTimeFlag = true;
            eventTime["$gte"] = DateFns.startOfDay(new Date(from));
        }
        if (!isNaN(new Date(to).getTime())) {
            isTimeFlag = true;
            eventTime["$lte"] = DateFns.endOfDay(new Date(to));
        }
        if (isTimeFlag) match.eventTime = eventTime;
        return { match };
    }

    private async aggregate(pipelines: any[]) {
        try {
            // const startTime = Date.now();
            const cursor = MyMongoDb.collections.camera.body.aggregate(pipelines, { allowDiskUse: true });
            const total = await cursor.toArray();
            // const endTime = Date.now();
            // console.log("================================================")
            // // console.log(JSON.stringify(pipelines))
            // console.log(`aggregate 耗时: ${endTime - startTime}ms`, total.length)
            // console.log("================================================")
            return total;
        } catch (e) {
            throw new YqzException("bodyDao aggregate, mongodb 语法错误")
        }
    }

    /**
     * 使用 hint 优化聚合列表查询
     * @param pipelines 
     * @param hint 
     * @returns 
     */
    private async aggregateByHint(pipelines: any[], hint: string) {
        try {
            // const startTime = Date.now();
            const cursor = MyMongoDb.collections.camera.body.aggregate(pipelines, { allowDiskUse: true, hint });
            const items = await cursor.toArray();
            // const endTime = Date.now();
            // console.log("================================================")
            // // console.log(JSON.stringify(pipelines))
            // console.log(`aggregate 耗时: ${endTime - startTime}ms`, total.length)
            // console.log("================================================")
            return items;
        } catch (e) {
            throw new YqzException("bodyDao aggregateByHint, mongodb 语法错误")
        }
    }

    /**
     * 使用 hint 优化聚合统计查询
     * @param pipelines 
     * @param hint 
     * @returns 
     */
    private async aggregateCountByHint(pipelines: any[], hint: string) {
        try {
            // const startTime = Date.now();
            pipelines.push({ $count: "total" })
            const cursor = MyMongoDb.collections.camera.body.aggregate(pipelines, { allowDiskUse: true, hint });
            const res = await cursor.toArray();
            const total = !_.isEmpty(res) ? Number(res[0]?.total || '0') : 0;
            // const endTime = Date.now();
            // console.log("================================================")
            // // console.log(JSON.stringify(pipelines))
            // console.log(`aggregate 耗时: ${endTime - startTime}ms`, total.length)
            // console.log("================================================")
            return total;
        } catch (e) {
            throw new YqzException("bodyDao countByHint, mongodb 语法错误")
        }
    }

    /**
     * 获取body中的faceEntityId
     * @param match 
     * @returns 
     */
    async getBodyFaceEntityIds(match: { comapnyIds: string[]; startTime: string; endTime: string; faceEntityIds?: string[]; tags?: string[]; }): Promise<string[]> {
        const { comapnyIds, startTime, endTime, faceEntityIds, tags } = match;
        if (_.isEmpty(comapnyIds) || !startTime || !endTime) throw new YqzException("comapnyId, startTime, endTime 不能为空");
        const pipelines: any = [];
        // 1. 查询公司ID和时间区间
        pipelines.push({ $match: { companyId: { $in: comapnyIds } } });
        // 2. 如果有faceEntityIds条件，增加筛选
        if (faceEntityIds && faceEntityIds.length > 0) {
            pipelines.push({ $match: { faceEntityId: { $in: faceEntityIds } } });
        }
        if (!_.isEmpty(tags)) pipelines.push({ $match: { tags: { $in: tags } } });
        pipelines.push({ $match: { eventTime: { $gte: new Date(startTime), $lte: new Date(endTime) } } });
        // 3. 按faceEntityId分组（去重）
        pipelines.push({ $group: { _id: "$faceEntityId" } });
        // 4. 只返回faceEntityId字段，不返回_id
        pipelines.push({ $project: { _id: 0, faceEntityId: "$_id" } });
        // 5. 执行聚合查询
        const items = await this.aggregate(pipelines);
        // 6. 返回faceEntityId数组
        return items.map((item: any) => item.faceEntityId);
    }

    /**
     * 获取body中的projectId
     * @param match 
     * @returns 
     */
    async getBodyProjectIds(match: { comapnyIds: string[]; startTime: string; endTime: string; projectIds?: string[]; tags?: string[]; }): Promise<string[]> {
        const { comapnyIds, startTime, endTime, projectIds, tags } = match;
        if (_.isEmpty(comapnyIds) || !startTime || !endTime) throw new YqzException("comapnyId, startTime, endTime 不能为空");
        const pipelines: any = [];
        // 1. 查询公司ID和时间区间
        pipelines.push({ $match: { companyId: { $in: comapnyIds } } });
        // 2. 如果有projectIds条件，增加筛选
        if (projectIds && projectIds.length > 0) {
            pipelines.push({ $match: { projectId: { $in: projectIds } } });
        }
        if (!_.isEmpty(tags)) pipelines.push({ $match: { tags: { $in: tags } } });
        pipelines.push({ $match: { eventTime: { $gte: new Date(startTime), $lte: new Date(endTime) } } });
        // 3. 按projectId分组（去重）
        pipelines.push({ $group: { _id: "$projectId" } });
        // 4. 只返回projectId字段，不返回_id
        pipelines.push({ $project: { _id: 0, projectId: "$_id" } });
        // 5. 执行聚合查询
        const items = await this.aggregate(pipelines);
        // 6. 返回projectId数组
        return items.map((item: any) => item.projectId);
    }

    /**
     * 获取body中的projectId
     * @param body 
     * @returns 
     */
    @MyCache({ cacheKey: 'BodyDao:getAnalyticsProjectIdList', ttl: 60 })//单位秒
    async getAnalyticsProjectIdList(body: OnsitePatrolType.BodyAnalyticsByPersonReq): Promise<string[]> {
        const match: any = {
            companyId: { $in: body.companyIds },
            trustedIdentity: true,
            faceEntityId: { $in: body.faceEntityIds }
        };
        if (!_.isEmpty(body.projectIds)) match.projectId = { $in: body.projectIds };
        if (!_.isEmpty(body.faceIdentifyTypes)) match.faceIdentifyType = { $in: body.faceIdentifyTypes.filter(res => !!res) };
        if (!_.isEmpty(body.tags)) match.tags = { $in: body.tags };
        const eventTime: any = {};
        let isTimeFlag = false;
        if (!isNaN(new Date(body.from).getTime())) {
            isTimeFlag = true;
            eventTime["$gte"] = DateFns.startOfDay(new Date(body.from));
        }
        if (!isNaN(new Date(body.to).getTime())) {
            isTimeFlag = true;
            eventTime["$lte"] = DateFns.endOfDay(new Date(body.to));
        }
        if (isTimeFlag) match.eventTime = eventTime;
        const pipelines: any = [{ $match: match }];
        pipelines.push({ $group: { _id: "$projectId" } });
        pipelines.push({ $project: { _id: 0, projectId: "$_id" } });
        const items = await this.aggregate(pipelines);
        return items.map((item: any) => item.projectId);
    }

    /**
     * 获取body中的roleName
     * @param body 
     * @returns 
     */
    @MyCache({ cacheKey: 'BodyDao:getAnalyticsRoleNameList', ttl: 60 })//单位秒
    async getAnalyticsRoleNameList(body: OnsitePatrolType.BodyAnalyticsByPersonReq): Promise<string[]> {
        const match: any = {
            companyId: { $in: body.companyIds },
            trustedIdentity: true,
            faceEntityId: { $in: body.faceEntityIds }
        };
        if (!_.isEmpty(body.projectIds)) match.projectId = { $in: body.projectIds };
        if (!_.isEmpty(body.faceIdentifyTypes)) match.faceIdentifyType = { $in: body.faceIdentifyTypes.filter(res => !!res) };
        if (!_.isEmpty(body.tags)) match.tags = { $in: body.tags };
        const eventTime: any = {};
        let isTimeFlag = false;
        if (!isNaN(new Date(body.from).getTime())) {
            isTimeFlag = true;
            eventTime["$gte"] = DateFns.startOfDay(new Date(body.from));
        }
        if (!isNaN(new Date(body.to).getTime())) {
            isTimeFlag = true;
            eventTime["$lte"] = DateFns.endOfDay(new Date(body.to));
        }
        if (isTimeFlag) match.eventTime = eventTime;
        const pipelines: any = [{ $match: match }];
        pipelines.push({ $group: { _id: "$tags" } });
        pipelines.push({ $project: { _id: 0, tags: "$_id" } });
        const items = await this.aggregate(pipelines);
        return items.map((item: any) => item.tags);
    }

    /**
     * 人员统计
     * @param body 
     * @returns 
     */
    @MyCache({ cacheKey: 'BodyDao:analyticsPerson', ttl: 60 })//单位秒
    async analyticsPerson(body: OnsitePatrolType.BodyAnalyticsByPersonReq): Promise<{ managerId: string, totalPatrol: number, projectIdList: string[] }[]> {
        const { match } = this.parseMatch(body);
        const pipelines: any = [{ $match: match }];
        // 1. 先过滤掉 tags 为空的数据
        // pipelines.push({ $match: { tags: { $ne: null } } });
        // 2. 只保留用到的字段
        pipelines.push({ $project: { faceEntityId: 1, projectId: 1, eventTime: 1, tags: 1 } });
        // 3. 按天、员工、工地分组，取最早时间和最早那条的tags
        pipelines.push({ $sort: { eventTime: 1 } });
        pipelines.push({
            $group: {
                _id: {
                    faceEntityId: "$faceEntityId",
                    projectId: "$projectId",
                    eventDay: {
                        $dateToString: { date: "$eventTime", format: "%Y-%m-%d", timezone: 'Asia/Shanghai' }
                    }
                },
                earliestTime: { $first: "$eventTime" },
                tagsAtEarliest: { $first: "$tags" }
            }
        });
        // 4. 只保留需要的字段
        pipelines.push({
            $project: {
                _id: 0,
                managerId: "$_id.faceEntityId",
                projectId: "$_id.projectId",
                eventTime: "$earliestTime",
                roleName: "$tagsAtEarliest"
            }
        });
        /** 人员的个数 */
        pipelines.push({
            $group: {
                _id: "$managerId",
                totalPatrol: { $sum: 1 },
                uniqueProjectIds: { $addToSet: "$projectId" },
            }
        });
        /** 查看长度 */
        pipelines.push({
            $project: {
                _id: 0,
                managerId: "$_id",
                totalPatrol: 1,
                projectIdList: "$uniqueProjectIds",
            }
        });
        const items = await this.aggregateByHint(pipelines, 'query_onsite_patrol_idx');
        return items as { managerId: string, totalPatrol: number, projectIdList: string[] }[];
    }

    /**
     * 岗位统计
     * @param body 
     * @returns 
     */
    @MyCache({ cacheKey: 'BodyDao:analyticsPosition', ttl: 60 })//单位秒
    async analyticsPosition(body: OnsitePatrolType.BodyAnalyticsByRoleReq): Promise<{ totalPatrol: number, roleName: string, projectIdList: string[], managerIdList: string[] }[]> {
        const { match } = this.parseMatch(body);
        const pipelines: any = [{ $match: match }];
        // 1. 先过滤掉 tags 为空的数据
        // pipelines.push({ $match: { tags: { $ne: null } } });
        // 2. 只保留用到的字段
        pipelines.push({ $project: { faceEntityId: 1, projectId: 1, eventTime: 1, tags: 1 } });
        // 3. 按天、员工、工地分组，取最早时间和最早那条的tags
        pipelines.push({ $sort: { eventTime: 1 } });
        pipelines.push({
            $group: {
                _id: {
                    faceEntityId: "$faceEntityId",
                    projectId: "$projectId",
                    eventDay: {
                        $dateToString: { date: "$eventTime", format: "%Y-%m-%d", timezone: 'Asia/Shanghai' }
                    }
                },
                earliestTime: { $min: "$eventTime" },
                // 下面这行是关键，找到最早时间对应的tags
                tagsAtEarliest: { $first: "$tags" }
            }
        });
        // 4. 只保留需要的字段
        pipelines.push({
            $project: {
                _id: 0,
                managerId: "$_id.faceEntityId",
                projectId: "$_id.projectId",
                eventTime: "$earliestTime",
                roleName: "$tagsAtEarliest"
            }
        });
        /** 岗位的个数 */
        pipelines.push({
            $group: {
                _id: "$roleName",
                totalPatrol: { $sum: 1 },
                uniqueProjectIds: { $addToSet: "$projectId" },
                uniqueManagerIds: { $addToSet: "$managerId" }
            }
        });
        /** 查看长度 */
        pipelines.push({
            $project: {
                _id: 0,
                roleName: "$_id",
                totalPatrol: 1,
                projectIdList: "$uniqueProjectIds",
                managerIdList: "$uniqueManagerIds",
            }
        })
        const items = await this.aggregateByHint(pipelines, 'query_onsite_patrol_idx');
        return items as { totalPatrol: number, roleName: string, projectIdList: string[], managerIdList: string[] }[];
    }

    /**
     * 获取bodyDB 统计数量
     * @param query 
     * @returns 
     */
    async analyticsCount(query: OnsitePatrolType.BodyAnalyticsListReq) {
        const { match } = this.parseMatch(query);
        const pipelines: any = [{ $match: match }];
        /** 一个员工 同一天在 在同一个工地上 只算一个记录 */
        pipelines.push({
            $group: {
                _id: {
                    faceEntityId: "$faceEntityId",
                    projectId: "$projectId",
                    companyId: "$companyId",
                    eventDay: {
                        $dateToString: { date: "$eventTime", format: "%Y-%m-%d", timezone: 'Asia/Shanghai' }
                    }
                }
            },
        });
        const total = await this.aggregateCountByHint(pipelines, 'query_onsite_patrol_idx');
        return total;
    }

    /**
     * 获取bodyDB 数据
     * @param pipelines 
     * @param options 
     * @returns 
     */
    async analyticsList(query: OnsitePatrolType.BodyAnalyticsListReq, options?: { pageNo?: number, pageSize?: number, sort?: OnsitePatrolType.Sort[] }) {
        const { match } = this.parseMatch(query);
        const pipelines: any = [{ $match: match }];
        this.getItemPipelines(pipelines);
        const { pageNo, pageSize, sort } = options || {};
        const sortStage: any = {};
        if (sort && sort.length > 0) {
            sort.forEach(s => {
                // To prevent NoSQL injection, only allow specific fields.
                if (['eventTime', 'earliestTime', 'latestTime', 'timeInterval', 'managerId', 'arrivalTime', 'leaveTime'].includes(s.field)) {
                    sortStage[s.field] = s.order === 'DESC' ? -1 : 1;
                }
            });
        }
        /** 排序 */
        if (Object.keys(sortStage).length > 0) {
            pipelines.push({ $sort: sortStage });
        } else {
            // 默认排序
            pipelines.push({
                $sort: {
                    eventTime: -1,
                    managerId: -1,
                    projectId: -1,
                }
            });
        }
        if (pageNo > 0 && pageSize > 0) {
            pipelines.push({ $skip: (pageNo - 1) * pageSize });
            pipelines.push({ $limit: pageSize });
        }
        //强制使用索引
        const items = await this.aggregateByHint(pipelines, 'query_onsite_patrol_idx');
        return items;
    }

    /**
     * 获取bodyDB 数据 v2
     * @param query 
     * @returns 
     */
    @MyCache({ cacheKey: 'BodyDao:analyticsListV2', ttl: 60 })//单位秒
    async analyticsListV2(query: OnsitePatrolType.BodyAnalyticsListReq) {
        const { match } = this.parseMatch(query);
        const pipelines: any = [{ $match: match }];
        this.getItemPipelines(pipelines);
        //强制使用索引
        const items = await this.aggregateByHint(pipelines, 'query_onsite_patrol_idx');
        return items;
    }

    private getItemPipelines(pipelines: any[]) {
        /** 一个员工 同一天在 在同一个工地上 只算一个记录 */
        pipelines.push({
            $group: {
                _id: {
                    faceEntityId: "$faceEntityId",
                    projectId: "$projectId",
                    companyId: "$companyId",
                    eventDay: {
                        $dateToString: { date: "$eventTime", format: "%Y-%m-%d", timezone: 'Asia/Shanghai' }
                    }
                },
                firstDocument: { $first: "$$ROOT" },
                earliestTime: { $min: "$eventTime" }, // 获取最早时间  
                latestTime: { $max: "$eventTime" },    // 获取最晚时间  
                uniqueTypes: { $addToSet: "$faceIdentifyType" } // 获取faceIdentifyType字段中的唯一值  
            },
        });
        /** 只要最重要的三个数据 managerId, projectId, eventTime */
        pipelines.push({
            $project: {
                _id: 0,
                // _id: "$firstDocument._id",
                managerId: "$_id.faceEntityId",
                projectId: "$_id.projectId",
                companyId: "$_id.companyId",
                eventTime: "$firstDocument.eventTime",
                roleName: "$firstDocument.tags",
                earliestTime: "$earliestTime",
                latestTime: "$latestTime",
                // timeInterval: { $subtract: ["$latestTime", "$earliestTime"] }, // 计算时间间隔  
                uniqueTypes: "$uniqueTypes",
                // arrivalTime: { $dateToString: { date: "$earliestTime", format: "%H:%M:%S", timezone: 'Asia/Shanghai' } },
                // leaveTime: { $dateToString: { date: "$latestTime", format: "%H:%M:%S", timezone: 'Asia/Shanghai' } },
            }
        });
        // /** 去掉 roleName 为空的结果 */
        // pipelines.push({
        //     $match: {
        //         roleName: { $ne: null },
        //     }
        // });
    }

}