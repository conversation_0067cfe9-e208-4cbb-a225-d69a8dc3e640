import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { DevTimeout } from "@yqz/nest";
import { DimDeviceEntity } from "@src/modules/report/entity/etl/dim-device.entity";
import { DeviceOfflineEventEntity } from "@src/modules/report/entity/camera/device-offline-event.entity";
import { generateDateList } from "@src/util/datetime.util";

@Injectable()
export class DeviceErrorDao {
    private readonly logger = new MyLogger(DeviceErrorDao.name)
    constructor(
        private dataSource: DataSource
    ) { }


    @DevTimeout(500)
    test() {
        // this.searchDeviceList({yqzDeviceIds:[
        // ]}).then(res => {
        //   this.logger.log(res)
        // })
    }

    /**
     * 查询时间范围内离线设备
     * @param params 
     * @returns 
     */
    async searchDeviceErrorList(params: { companyIds: string[], devices: string[], startTime: string, endTime: string }) {
        const errqb = this.dataSource.createQueryBuilder()
        .select([
          'de.yqz_device_id as yqz_device_id',
          'de.project_id as project_id',
          'MAX( ' +
          'CASE ' +
          "WHEN de.from_time < '" + params.endTime + "' AND de.to_time is NULL THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
          "WHEN de.to_time >= '" + params.startTime + "' AND de.to_time < '" + params.endTime + "'  THEN TIMESTAMPDIFF(SECOND,de.from_time,de.to_time) " +
          "WHEN de.from_time < '" + params.endTime + "' AND de.to_time >= '" + params.endTime + "' THEN TIMESTAMPDIFF(SECOND,de.from_time,'" + params.endTime + "') " +
          "END) AS diffSec"
        ])
        .from(DeviceOfflineEventEntity, 'de')
        .where('de.delete_flag = "N"')
        .andWhere('de.company_id in (:...companyIds)', { companyIds: params.companyIds })
        .andWhere('de.yqz_device_id in (:...devices)', { devices: params.devices })
        .andWhere('de.company_id is not null')
        .andWhere('de.project_id is not null')
        .andWhere('de.manager_id is not null')
        .groupBy('de.yqz_device_id').addGroupBy('de.project_id');

        const dataList = generateDateList({ startTime: params.startTime, endTime: params.endTime });
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'dm.device_id as yqzDeviceId',
            ])
            .from(DimDeviceEntity, 'dm')
            .leftJoin("(" + errqb.getQuery() + ")", 'err', 'err.yqz_device_id = dm.device_id and err.project_id = dm.project_id')
            .where('dm.company_id in (:...companyIds)', { companyIds: params.companyIds })
            .andWhere('dm.device_id in (:...yqzDeviceId)', { yqzDeviceId: params.devices })
            .andWhere('dm.project_id is not null')
            .andWhere('err.diffSec >= 14400')//离线时间大于4个小时
            // .andWhere("dm.date >= :startTime and dm.date < :endTime", { startTime: params.startTime, endTime: params.endTime })
            .andWhere("dm.date in (:...dates)", { dates: dataList })
            .groupBy('dm.device_id');
        qb.setParameters(errqb.getParameters());
        return await qb.getRawMany();
    }

}