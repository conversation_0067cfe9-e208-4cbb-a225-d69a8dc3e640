import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { Brackets, DataSource } from "typeorm";
import { Detection } from "../dto/detection.dto";
import { ProjectTidinessDetectionEntity } from "@src/modules/report/entity/camera/project-tidiness-detection.entity";
import { ProjectSecurityDetectionEntity } from "@src/modules/report/entity/camera/project-security-detection.entity";
import * as _ from "lodash";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { DaoUtil, DevTimeout } from "@yqz/nest";
import { CommunityEntity } from "@src/modules/report/entity/bgw/community.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { CompanyEntity } from "@src/modules/report/entity/bgw/company.entity";
import { ProjectDetectionDetailEntity } from "@src/modules/report/entity/camera/project-detection-detail.entity";
import { ProjectSignProblem } from "../../../bgw/project/dto/project-sign-problem.dto";
import { DepartmentEntity } from "@src/modules/report/entity/bgw/department.entity";
import { AiDetectionDataReport } from "../../../corp/data-report/service/basic/dto/ai-detection-data-report.dto";
import { CompanyCustomConfigEntity } from "@src/modules/report/entity/bgw/company-custom-config.entity";
import { DeviceProjectInfoEntity } from "@src/modules/report/entity/camera/device-project-info.entity";

@Injectable()
export class ProjectDetectionDao {
  private readonly logger = new MyLogger(ProjectDetectionDao.name)
  constructor(
    private dataSource: DataSource
  ) { }

  @DevTimeout(500)
  test() {

  }

  async searchProjectTidinessDetectionNum(params: Detection.Search) {
    const qb = this.dataSource.createQueryBuilder()
      .select('COUNT(pt.project_tidiness_detection_id) as tidinessNum')
      .from(ProjectTidinessDetectionEntity, 'pt')
      .leftJoin(ProjectEntity, 'p', 'pt.project_id = p.project_id')
      .where('pt.delete_flag = "N"')
      .andWhere('p.delete_flag = "N"')
      .andWhere('pt.company_id = :companyId', { companyId: params.companyId })
      .andWhere('pt.tidiness_type = 3', { tidinessType: 3 });
    if (!_.isEmpty(params.projectIds)) qb.andWhere('pt.project_id in (:...projectIds)', { projectIds: params.projectIds });
    qb.andWhere("pt.detection_time between :startTime and :endTime", { startTime: params.startTime, endTime: params.endTime });
    return await qb.getRawOne<{ tidinessNum: string }>();
  }

  async searchProjectSecurityDetectionNum(params: Detection.Search) {
    const qb = this.dataSource.createQueryBuilder()
      .select([
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.SMOKE}"), 0) AS smokeNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.NOT_WORK_CLOTHES}"), 0) AS noUniformNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.SLIPPERS}"), 0) AS slipperNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.ELECTRO_MOBILE}"), 0) AS electroMobileNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.SAFETY_HELMET}"), 0) AS safetyHelmetNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.CLIMB_WORK}"), 0) AS climbWorkNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.ACTIVE_FIRE}"), 0) AS fireNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.ANGLE_GRINDER}"), 0) AS angleGrinderNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.CUTTING_MACHINE}"), 0) AS cuttingMachineNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.FIRE_EXTINGUISHER}"), 0) AS fireExtinguisherNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.TEMPORARY_ELECTRICITY}"), 0) AS temporaryElectricityNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.SAFETY_MESH}"), 0) AS safetyMeshNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.SAFETY_HOLE}"), 0) AS safetyHoleNum`,
        `IFNULL(SUM(pdd.risk_type_detail = "${ProjectSignProblem.RiskType.SAFETY_STAIRS}"), 0) AS safetyStairsNum`,
      ])
      .from(ProjectSecurityDetectionEntity, 'ps')
      .leftJoin(ProjectDetectionDetailEntity, 'pdd', 'pdd.project_security_detection_id = ps.project_security_detection_id')
      .leftJoin(ProjectEntity, 'p', 'ps.project_id = p.project_id')
      .where('ps.delete_flag = "N"')
      .andWhere('p.delete_flag = "N"')
      .andWhere('pdd.delete_flag = "N"')
      .andWhere('ps.company_id = :companyId', { companyId: params.companyId });
    if (!_.isEmpty(params.projectIds)) qb.andWhere('ps.project_id in (:...projectIds)', { projectIds: params.projectIds });
    qb.andWhere("ps.detection_time between :startTime and :endTime", { startTime: params.startTime, endTime: params.endTime });
    qb.groupBy('ps.company_id');
    return await qb.getRawOne<{ smokeNum: string, noUniformNum: string, slipperNum: string, electroMobileNum: string, safetyHelmetNum: string, climbWorkNum: string, fireNum: string, angleGrinderNum: string, cuttingMachineNum: string, fireExtinguisherNum: string, temporaryElectricityNum: string, safetyMeshNum: string, safetyHoleNum: string, safetyStairsNum: string }>();
  }

  async searchTidiness(params: { projectIds: string[], startTime?: string, endTime?: string }) {
    DaoUtil.checkEmptyParams(params, ProjectDetectionDao.name + '.search', 'FailIfAllEmpty');
    const { projectIds, startTime, endTime } = params || {};
    const qb = this.dataSource
      .createQueryBuilder()
      .select()
      .from(ProjectTidinessDetectionEntity, 'pd')
      .where('pd.delete_flag = "N"')
      .andWhere('pd.project_id in (:...projectIds)', { projectIds })
    if (startTime) qb.andWhere('pd.detection_time >= :startTime', { startTime });
    if (endTime) qb.andWhere('pd.detection_time < :endTime', { endTime });
    const res = await DaoUtil.getPageRes<ProjectTidinessDetectionEntity>(qb);
    res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
    return res;
  }

  async searchSecurity(params: { projectIds: string[], startTime?: string, endTime?: string, riskType?: string[] }) {
    DaoUtil.checkEmptyParams(params, ProjectDetectionDao.name + '.search', 'FailIfAllEmpty');
    const { projectIds, startTime, endTime, riskType } = params || {};
    const qb = this.dataSource
      .createQueryBuilder()
      .select()
      .from(ProjectSecurityDetectionEntity, 'pd')
      .where('pd.delete_flag = "N"')
      .andWhere('pd.project_id in (:...projectIds)', { projectIds })
    if (startTime) qb.andWhere('pd.detection_time >= :startTime', { startTime });
    if (endTime) qb.andWhere('pd.detection_time < :endTime', { endTime });
    if (!_.isEmpty(riskType)) {
      qb.andWhere(new Brackets(mqb => {
        for (const index in riskType) {
          if ('0' == index) mqb.where("find_in_set('" + riskType[index] + "',risk_type)");
          else mqb.orWhere("find_in_set('" + riskType[index] + "',risk_type)");
        }
      }));
    }
    const res = await DaoUtil.getPageRes<ProjectSecurityDetectionEntity>(qb);
    res.items = res.items.map(e => DaoUtil.convertRaw2Dto(e));
    return res;
  }

  async searchTidinessList(params: Detection.SearchReq) {
    if (!_.isEmpty(params.reportType)) {
      params.reportType = params.reportType.map(reportType => AiDetectionDataReport.TidinessReportType.getCodeByValue(reportType));
    }
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return { total: 0, items: [] };
    const qb = this.dataSource
      .createQueryBuilder()
      .select([
        'pd.project_id as projectId',
        'pd.tidiness_type as tidinessType',
        'pd.motiondetect_event_id as eventId',
        'pd.oss_photo_url as photoUrl',
        'DATE_FORMAT(pd.detection_time, "%Y-%m-%d %H:%i:%s") as detectionDate',
        'p.project_manager as projectManagerId',
        'pd.company_id as linkCompanyId',
      ])
      .from(ProjectTidinessDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .where('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      qb.andWhere("pd.tidiness_type in (:...tidinessType)", { tidinessType: params.reportType });
    }
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectTidinessDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    qb.orderBy('pd.project_tidiness_detection_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<Detection.DetectionDetail>(qb);
  }

  async searchSecurityList(params: Detection.SearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return { total: 0, items: [] };
    if (!_.isEmpty(params.reportType)) {
      if (params.detectionType === 'action') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.ActionReportType.getCodeByValue(reportType));
      if (params.detectionType === 'body') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.BodyReportType.getCodeByValue(reportType));
    }
    //查询
    const qb = this.dataSource
      .createQueryBuilder()
      .select([
        'p.project_id as projectId',
        'pd.risk_type as securityType',
        'pd.motiondetect_event_id as eventId',
        'pd.oss_photo_url as photoUrl',
        'DATE_FORMAT(pd.detection_time, "%Y-%m-%d %H:%i:%s") as detectionDate',
        'p.project_manager as projectManagerId',
        'pd.company_id as linkCompanyId',
      ])
      .from(ProjectSecurityDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      // qb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
      qb.andWhere(new Brackets(mqb => {
        params.reportType.forEach(reportType => {
          mqb.orWhere(`find_in_set(:${`riskType_${reportType}`}, pd.risk_type)`, { [`riskType_${reportType}`]: reportType })
        });
      }));
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectSecurityDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    qb.orderBy('pd.project_security_detection_id', 'DESC');
    DaoUtil.processPageAndSort(qb, params);
    return await DaoUtil.getPageRes<Detection.DetectionDetail>(qb);
  }

  async countTidiness(params: Detection.SearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return { count: '0' };
    if (!_.isEmpty(params.reportType)) {
      params.reportType = params.reportType.map(reportType => AiDetectionDataReport.TidinessReportType.getCodeByValue(reportType));
    }
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count'
      ])
      .from(ProjectTidinessDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .where('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      qb.andWhere("pd.tidiness_type in (:...tidinessType)", { tidinessType: params.reportType });
    }
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectTidinessDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    return await qb.getRawOne<{ count: string }>();
  }

  async countSecurity(params: Detection.SecuritySearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return { count: '0' };
    if (!_.isEmpty(params.reportType)) {
      if (params.detectionType === 'action') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.ActionReportType.getCodeByValue(reportType));
      if (params.detectionType === 'body') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.BodyReportType.getCodeByValue(reportType));
    }
    //查询
    const select = []
    if (!_.isEmpty(params.reportType)) {
      select.push(...params.reportType.map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    } else if (params.detectionType === 'action') {//行为识别
      select.push(...AiDetectionDataReport.ActionReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    } else if (params.detectionType === 'body') {//物体识别
      select.push(...AiDetectionDataReport.BodyReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    }
    const subqb = this.dataSource.createQueryBuilder()
      .select(select)
      .from(ProjectSecurityDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) subqb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) subqb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) subqb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //工地地址
    if (params.projectAddress) {
      subqb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      subqb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      subqb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      // subqb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
      subqb.andWhere(new Brackets(mqb => {
        params.reportType.forEach(reportType => {
          mqb.orWhere(`find_in_set(:${`riskType_${reportType}`}, pd.risk_type)`, { [`riskType_${reportType}`]: reportType })
        });
      }));
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectSecurityDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      subqb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      subqb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      subqb.setParameters(subQuery.getParameters());
    }
    //主查询，统计总数
    const qb = this.dataSource.createQueryBuilder()
      .select([
        // params.reportType ? `reportType_${params.reportType} as count` : `ifnull(sum(${AiDetectionDataReport.TidinessReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count`,
        // params.reportType ? `reportType_${params.reportType} as count` :
        !_.isEmpty(params.reportType) ? `ifnull(sum(${params.reportType.map(res => `reportType_${res}`).join(' + ')}), 0) as count` :
          params.detectionType === 'action' ? `ifnull(sum(${AiDetectionDataReport.ActionReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count` :
            `ifnull(sum(${AiDetectionDataReport.BodyReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count`,
      ])
      .from(`(${subqb.getQuery()})`, 'sub')
      .setParameters(subqb.getParameters());
    return await qb.getRawOne<{ count: string }>();
  }

  async countTidinessProjectGroupDeviceDepartment(params: Detection.SearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    if (!_.isEmpty(params.reportType)) {
      params.reportType = params.reportType.map(reportType => AiDetectionDataReport.TidinessReportType.getCodeByValue(reportType));
    }
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'dpi.project_id AS project_id',
        'dpi.department_id AS department_id'
      ])
      .from(DeviceProjectInfoEntity, 'dpi')
      .innerJoin(ProjectTidinessDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
      .where('dpi.delete_flag = "N"')
      .andWhere('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
    subQuery.andWhere(qb => {
      const subQuery = qb.subQuery()
        .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
        .from(DeviceProjectInfoEntity, 'dpi2')
        .where('dpi2.project_id = dpi.project_id')
        .andWhere('dpi2.delete_flag = "N"');
      return `dpi.project_binding_time = (${subQuery.getQuery()})`;
    });
    subQuery.groupBy('dpi.project_id');

    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'dpi.department_id as deviceDepartmentId',
        'dep.department_name as deviceDepartmentName'
      ])
      .from(ProjectTidinessDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .leftJoin(`(${subQuery.getQuery()})`, 'dpi', 'pd.project_id = dpi.project_id') // 确保是最后一条记录
      .leftJoin(DepartmentEntity, 'dep', 'dpi.department_id = dep.department_id')
      .where('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      qb.andWhere("pd.tidiness_type in (:...tidinessType)", { tidinessType: params.reportType });
    }
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      qb.andWhere('dpi.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
    }
    qb.groupBy('dpi.department_id');
    qb.setParameters(subQuery.getParameters());
    return await qb.getRawMany<{ count: string, deviceDepartmentId: string, deviceDepartmentName: string }>();
  }

  async countTidinessGroupDepartment(params: Detection.SearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    if (!_.isEmpty(params.reportType)) {
      params.reportType = params.reportType.map(reportType => AiDetectionDataReport.TidinessReportType.getCodeByValue(reportType));
    }
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'dep.department_id as departmentId',
        'dep.department_name as departmentName'
      ])
      .from(ProjectTidinessDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .leftJoin(DepartmentEntity, 'dep', 'p.department_id = dep.department_id')
      .where('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      qb.andWhere("pd.tidiness_type in (:...tidinessType)", { tidinessType: params.reportType });
    }
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectTidinessDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    qb.groupBy('p.department_id');
    return await qb.getRawMany<{ count: string, departmentId: string, departmentName: string }>();
  }

  async countSecurityProjectGroupDeviceDepartment(params: Detection.SecuritySearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    if (!_.isEmpty(params.reportType)) {
      if (params.detectionType === 'action') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.ActionReportType.getCodeByValue(reportType));
      if (params.detectionType === 'body') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.BodyReportType.getCodeByValue(reportType));
    }
    // 子查询：获取每个 project_id 对应的最新记录
    const subQuery = this.dataSource.createQueryBuilder()
      .select([
        'dpi.project_id AS project_id',
        'dpi.department_id AS department_id'
      ])
      .from(DeviceProjectInfoEntity, 'dpi')
      .innerJoin(ProjectSecurityDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
      .where('dpi.delete_flag = "N"')
      .andWhere('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
    subQuery.andWhere(qb => {
      const subQuery = qb.subQuery()
        .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
        .from(DeviceProjectInfoEntity, 'dpi2')
        .where('dpi2.project_id = dpi.project_id')
        .andWhere('dpi2.delete_flag = "N"');
      return `dpi.project_binding_time = (${subQuery.getQuery()})`;
    });
    subQuery.groupBy('dpi.project_id');

    //查询
    const select = [
      'dpi.department_id as deviceDepartmentId',
      'dep.department_name as deviceDepartmentName'
    ];
    if (!_.isEmpty(params.reportType)) {
      select.push(...params.reportType.map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    } else if (params.detectionType === 'action') {//行为识别
      select.push(...AiDetectionDataReport.ActionReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    } else if (params.detectionType === 'body') {//物体识别
      select.push(...AiDetectionDataReport.BodyReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    }
    const subqb = this.dataSource.createQueryBuilder()
      .select(select)
      .from(ProjectSecurityDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .leftJoin(`(${subQuery.getQuery()})`, 'dpi', 'pd.project_id = dpi.project_id') // 确保是最后一条记录
      .leftJoin(DepartmentEntity, 'dep', 'dpi.department_id = dep.department_id')
      .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) subqb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) subqb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) subqb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //工地地址
    if (params.projectAddress) {
      subqb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      subqb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      subqb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      // subqb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
      subqb.andWhere(new Brackets(mqb => {
        params.reportType.forEach(reportType => {
          mqb.orWhere(`find_in_set(:${`riskType_${reportType}`}, pd.risk_type)`, { [`riskType_${reportType}`]: reportType })
        });
      }));
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      subqb.andWhere('dpi.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
    }
    subqb.groupBy('dpi.department_id');
    //主查询，统计总数
    const qb = this.dataSource.createQueryBuilder()
      .select([
        !_.isEmpty(params.reportType) ? `ifnull(sum(${params.reportType.map(res => `reportType_${res}`).join(' + ')}), 0) as count` :
          params.detectionType === 'action' ? `ifnull(sum(${AiDetectionDataReport.ActionReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count` :
            `ifnull(sum(${AiDetectionDataReport.BodyReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count`,
        'sub.deviceDepartmentId as deviceDepartmentId',
        'sub.deviceDepartmentName as deviceDepartmentName'
      ])
      .from(`(${subqb.getQuery()})`, 'sub')
      .groupBy(`sub.deviceDepartmentId`)
    subqb.setParameters(subQuery.getParameters());
    qb.setParameters(subqb.getParameters());
    return await qb.getRawMany<{ count: string, deviceDepartmentId: string, deviceDepartmentName: string }>();
  }

  async countSecurityGroupDepartment(params: Detection.SecuritySearchReq) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    if (!_.isEmpty(params.reportType)) {
      if (params.detectionType === 'action') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.ActionReportType.getCodeByValue(reportType));
      if (params.detectionType === 'body') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.BodyReportType.getCodeByValue(reportType));
    }
    //查询
    const select = [
      'dep.department_id as departmentId',
      'dep.department_name as departmentName'
    ];
    if (!_.isEmpty(params.reportType)) {
      select.push(...params.reportType.map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    } else if (params.detectionType === 'action') {//行为识别
      select.push(...AiDetectionDataReport.ActionReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    } else if (params.detectionType === 'body') {//物体识别
      select.push(...AiDetectionDataReport.BodyReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as 'reportType_${riskType}'`
      ));
    }
    const subqb = this.dataSource.createQueryBuilder()
      .select(select)
      .from(ProjectSecurityDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .leftJoin(DepartmentEntity, 'dep', 'p.department_id = dep.department_id')
      .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) subqb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) subqb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) subqb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //工地地址
    if (params.projectAddress) {
      subqb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      subqb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      subqb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      // subqb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
      subqb.andWhere(new Brackets(mqb => {
        params.reportType.forEach(reportType => {
          mqb.orWhere(`find_in_set(:${`riskType_${reportType}`}, pd.risk_type)`, { [`riskType_${reportType}`]: reportType })
        });
      }));
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectSecurityDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      subqb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      subqb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      subqb.setParameters(subQuery.getParameters());
    }
    subqb.groupBy('p.department_id');
    console.log()
    //主查询，统计总数
    const qb = this.dataSource.createQueryBuilder()
      .select([
        !_.isEmpty(params.reportType) ? `ifnull(sum(${params.reportType.map(res => `reportType_${res}`).join(' + ')}), 0) as count` :
          params.detectionType === 'action' ? `ifnull(sum(${AiDetectionDataReport.ActionReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count` :
            `ifnull(sum(${AiDetectionDataReport.BodyReportType.getEnums().map(res => `reportType_${res}`).join(' + ')}), 0) as count`,
        'sub.departmentId as departmentId',
        'sub.departmentName as departmentName'
      ])
      .from(`(${subqb.getQuery()})`, 'sub')
      .groupBy(`sub.departmentId`)
      .setParameters(subqb.getParameters());
    return await qb.getRawMany<{ count: string, departmentId: string, departmentName: string }>();
  }

  async countTidinessGroupType(params: Detection.SearchReq): Promise<{ count: string, reportType: string }[]> {
    if (!_.isEmpty(params.reportType)) {
      params.reportType = params.reportType.map(reportType => AiDetectionDataReport.TidinessReportType.getCodeByValue(reportType));
    }
    //idList为空直接返回
    if (_.isEmpty(params.idList)) {
      if (!_.isEmpty(params.reportType)) return params.reportType.map(reportType => { return { count: '0', reportType } });
      return AiDetectionDataReport.TidinessReportType.getEnums().map(tidinessType => ({ count: '0', reportType: tidinessType }));
    }
    //查询
    const select = [];
    if (!_.isEmpty(params.reportType)) {
      select.push(...params.reportType.map(
        tidinessType => `ifnull(sum(case when pd.tidiness_type = '${tidinessType}' then 1 else 0 end), 0) as '${tidinessType}'`
      ));
    } else {
      select.push(...AiDetectionDataReport.TidinessReportType.getEnums().map(
        tidinessType => `ifnull(sum(case when pd.tidiness_type = '${tidinessType}' then 1 else 0 end), 0) as '${tidinessType}'`
      ));
    }
    const qb = this.dataSource.createQueryBuilder()
      .select(select)
      .from(ProjectTidinessDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .where('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      qb.andWhere("pd.tidiness_type in (:...tidinessType)", { tidinessType: params.reportType });
    }
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectTidinessDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_tidiness_detection_id in (:...tidinessDetectionIds)', { tidinessDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    const res = await qb.getRawOne<{ [key: string]: string }>();
    return Object.keys(res).map(key => ({ reportType: key, count: res[key] }));
  }

  async countSecurityGroupType(params: Detection.SecuritySearchReq): Promise<{ count: string, reportType: string }[]> {
    if (!_.isEmpty(params.reportType)) {
      if (params.detectionType === 'action') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.ActionReportType.getCodeByValue(reportType));
      if (params.detectionType === 'body') params.reportType = params.reportType.map(reportType => AiDetectionDataReport.BodyReportType.getCodeByValue(reportType));
    }
    //idList为空直接返回
    if (_.isEmpty(params.idList)) {
      if (!_.isEmpty(params.reportType)) {
        return params.reportType.map(riskType => ({ count: '0', reportType: riskType }));
      } else if (params.detectionType === 'action') {//行为识别
        return AiDetectionDataReport.ActionReportType.getEnums().map(riskType => ({ count: '0', reportType: riskType }));
      } else if (params.detectionType === 'body') {//物体识别
        return AiDetectionDataReport.BodyReportType.getEnums().map(riskType => ({ count: '0', reportType: riskType }));
      }
    }
    //查询
    const select = [];
    if (!_.isEmpty(params.reportType)) {
      select.push(...params.reportType.map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as '${riskType}'`
      ));
    } else if (params.detectionType === 'action') {//行为识别
      select.push(...AiDetectionDataReport.ActionReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as '${riskType}'`
      ));
    } else if (params.detectionType === 'body') {//物体识别
      select.push(...AiDetectionDataReport.BodyReportType.getEnums().map(
        riskType => `ifnull(sum(case when find_in_set('${riskType}', pd.risk_type) then 1 else 0 end), 0) as '${riskType}'`
      ));
    }
    const qb = this.dataSource.createQueryBuilder()
      .select(select)
      .from(ProjectSecurityDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
    //工地负责人
    if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
    //所属公司
    if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
    if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
    //工地地址
    if (params.projectAddress) {
      qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
      qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
      qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
    }
    //报告类型
    if (!_.isEmpty(params.reportType)) {
      // qb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
      qb.andWhere(new Brackets(mqb => {
        params.reportType.forEach(reportType => {
          mqb.orWhere(`find_in_set(:${`riskType_${reportType}`}, pd.risk_type)`, { [`riskType_${reportType}`]: reportType })
        });
      }));
    }
    //服务厂商（设备归属）
    if (params.deviceDepartmentId) {
      // 子查询：获取每个 project_id 对应的最新记录
      const subQuery = this.dataSource.createQueryBuilder()
        .select([
          'dpi.project_id AS project_id',
          'dpi.department_id AS department_id'
        ])
        .from(DeviceProjectInfoEntity, 'dpi')
        .innerJoin(ProjectSecurityDetectionEntity, 'pd', 'dpi.project_id = pd.project_id')
        .where('dpi.delete_flag = "N"')
        .andWhere('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
      subQuery.andWhere(qb => {
        const subQuery = qb.subQuery()
          .select('MAX(dpi2.project_binding_time)', 'maxBindingTime')
          .from(DeviceProjectInfoEntity, 'dpi2')
          .where('dpi2.project_id = dpi.project_id')
          .andWhere('dpi2.delete_flag = "N"');
        return `dpi.project_binding_time = (${subQuery.getQuery()})`;
      });
      subQuery.groupBy('dpi.project_id');
      qb.innerJoin(`(${subQuery.getQuery()})`, 'dpd', 'p.project_id = dpd.project_id') // 确保是最后一条记录
      qb.andWhere('dpd.department_id = :deviceDepartmentId', { deviceDepartmentId: params.deviceDepartmentId });
      qb.setParameters(subQuery.getParameters());
    }
    const res = await qb.getRawOne<{ [key: string]: string }>();
    return Object.keys(res).map(key => ({ reportType: key, count: res[key] }));
  }

  async getTidinessGroupProjectManager(params: { idList: string[] }) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName',
      ])
      .from(ProjectTidinessDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .where('pd.project_tidiness_detection_id in (:...idList)', { idList: params.idList })
      .groupBy('p.project_manager');
    return await qb.getRawMany<{ projectManagerId: string, projectManagerName: string }>();
  }

  async getSecurityGroupProjectManager(params: { idList: string[] }) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'count(1) as count',
        'p.project_manager as projectManagerId',
        'm.nick_name as projectManagerName',
      ])
      .from(ProjectSecurityDetectionEntity, 'pd')
      .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
      .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
      .where('pd.project_security_detection_id in (:...idList)', { idList: params.idList })
      .groupBy('p.project_manager');
    return await qb.getRawMany<{ projectManagerId: string, projectManagerName: string }>();
  }

  async getTidinessGroupCompany(params: { idList: string[] }) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'pd.company_id as companyId',
        'c.company_name as companyName',
      ])
      .from(ProjectTidinessDetectionEntity, 'pd')
      .innerJoin(CompanyEntity, 'c', 'c.company_id = pd.company_id')
      .where('pd.project_tidiness_detection_id in (:...idList)', { idList: params.idList })
      .groupBy('pd.company_id');
    return await qb.getRawMany<{ companyId: string, companyName: string }>();
  }

  async getSecurityGroupCompany(params: { idList: string[] }) {
    //idList为空直接返回
    if (_.isEmpty(params.idList)) return [];
    //查询
    const qb = this.dataSource.createQueryBuilder()
      .select([
        'pd.company_id as companyId',
        'c.company_name as companyName',
      ])
      .from(ProjectSecurityDetectionEntity, 'pd')
      .innerJoin(CompanyEntity, 'c', 'c.company_id = pd.company_id')
      .where('pd.project_security_detection_id in (:...idList)', { idList: params.idList })
      .groupBy('pd.company_id');
    return await qb.getRawMany<{ companyId: string, companyName: string }>();
  }

  // async countTidinessGroupProjectManager(params: Detection.SearchReq) {
  //   //idList为空直接返回
  //   if (_.isEmpty(params.idList)) return [];
  //   //查询
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       'count(1) as count',
  //       'p.project_manager as projectManagerId',
  //       'm.nick_name as projectManagerName',
  //     ])
  //     .from(ProjectTidinessDetectionEntity, 'pd')
  //     .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
  //     .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
  //     .where('pd.project_tidiness_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
  //   //工地负责人
  //   if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
  //   //所属公司
  //   if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
  //   if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
  //   //工地地址
  //   if (params.projectAddress) {
  //     qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
  //     qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
  //     qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
  //   }
  //   //报告类型
  //   if (params.reportType) qb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
  //   qb.groupBy('p.project_manager');
  //   return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  // }

  // async countSecurityGroupProjectManager(params: Detection.SearchReq) {
  //   //idList为空直接返回
  //   if (_.isEmpty(params.idList)) return [];
  //   //查询
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       'count(1) as count',
  //       'p.project_manager as projectManagerId',
  //       'm.nick_name as projectManagerName',
  //     ])
  //     .from(ProjectSecurityDetectionEntity, 'pd')
  //     .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
  //     .leftJoin(ManagerEntity, 'm', 'm.manager_id = p.project_manager')
  //     .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
  //   //工地负责人
  //   if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
  //   //所属公司
  //   if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
  //   if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
  //   //工地地址
  //   if (params.projectAddress) {
  //     qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
  //     qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
  //     qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
  //   }
  //   //报告类型
  //   if (params.reportType) qb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
  //   qb.groupBy('p.project_manager');
  //   return await qb.getRawMany<{ count: string, projectManagerId: string, projectManagerName: string }>();
  // }

  // async countTidinessGroupCompany(params: Detection.SearchReq) {
  //   //idList为空直接返回
  //   if (_.isEmpty(params.idList)) return [];
  //   //查询
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       'count(1) as count',
  //       'p.company_id as companyId',
  //       'c.company_name as companyName',
  //     ])
  //     .from(ProjectTidinessDetectionEntity, 'pd')
  //     .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
  //     .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
  //     .where('pd.project_tidiness_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
  //   //工地负责人
  //   if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
  //   //所属公司
  //   if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
  //   if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
  //   //工地地址
  //   if (params.projectAddress) {
  //     qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
  //     qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
  //     qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
  //   }
  //   //报告类型
  //   if (params.reportType) qb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
  //   qb.groupBy('p.company_id');
  //   return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
  // }

  // async countSecurityGroupCompany(params: Detection.SearchReq) {
  //   //idList为空直接返回
  //   if (_.isEmpty(params.idList)) return [];
  //   //查询
  //   const qb = this.dataSource.createQueryBuilder()
  //     .select([
  //       'count(1) as count',
  //       'p.company_id as companyId',
  //       'c.company_name as companyName',
  //     ])
  //     .from(ProjectSecurityDetectionEntity, 'pd')
  //     .leftJoin(ProjectEntity, 'p', 'pd.project_id = p.project_id')
  //     .leftJoin(CompanyEntity, 'c', 'c.company_id = p.company_id')
  //     .where('pd.project_security_detection_id in (:...securityDetectionIds)', { securityDetectionIds: params.idList });
  //   //工地负责人
  //   if (params.projectManagerId) qb.andWhere('p.project_manager = :projectManagerId', { projectManagerId: params.projectManagerId });
  //   //所属公司
  //   if (params.linkCompanyId) qb.andWhere('pd.company_id = :companyId', { companyId: params.linkCompanyId });
  //   if (!_.isEmpty(params.projectIdList)) qb.andWhere('p.project_id in (:...projectIdList)', { projectIdList: params.projectIdList });
  //   //工地地址
  //   if (params.projectAddress) {
  //     qb.innerJoin(CommunityEntity, 'com', 'com.community_id = p.community_id');
  //     qb.leftJoin(CompanyCustomConfigEntity, 'cc', "cc.company_id = p.company_id and cc.DELETE_FLAG = 'N' AND cc.custom_type = 'project_address'");
  //     qb.andWhere(`locate(:projectAddress, (IF(cc.id is not null, CONCAT(IFNULL(p.project_name,''),IFNULL(p.HOUSE_NUMBER,'')), CONCAT(IFNULL(com.community_name,''),IFNULL(p.HOUSE_NUMBER,''))))) > 0`, { projectAddress: params.projectAddress });
  //   }
  //   //报告类型
  //   if (params.reportType) qb.andWhere("find_in_set(:riskType, pd.risk_type)", { riskType: params.reportType });
  //   qb.groupBy('p.company_id');
  //   return await qb.getRawMany<{ count: string, companyId: string, companyName: string }>();
  // }

}