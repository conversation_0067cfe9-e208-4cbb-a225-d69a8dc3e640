import { ApiProperty } from "@nestjs/swagger";
import { CorpReport } from "../../../corp/corp-report/dto/corp-report.dto";
import { BaseReportReq } from "@src/types/report-req.type";

export namespace Detection {

    export class Search {
        companyId: string;
        projectIds?: string[];
        startTime?: string;
        endTime?: string;
    }

    export class Response {
        detSmokeNum: number;//抽烟
        detNoUniformNum: number;//未穿工服
        detSlipperNum: number;//拖鞋
        detClimbWorkNum: number;//登高
        detFireNum: number;//动火
        detElectroMobileNum: number;//电瓶车
        detAngleGrinderNum: number;//角磨机
        detCuttingMachineNum: number;//切割机
        detFireExtinguisherNum: number;//灭火器
        detTemporaryElectricityNum: number;//临时用电
        detTidinessNum: number;//整洁度
        detSafetyHelmetNum: number;//安全帽
        detSafetyMeshNum: number;//脚手架未装防护网
        detSafetyHoleNum: number;//临边洞口无防护
        detSafetyStairsNum: number;//楼梯无防护
        constructor() {
            this.detTidinessNum = 0;
            this.detTemporaryElectricityNum = 0;
            this.detFireExtinguisherNum = 0;
            this.detCuttingMachineNum = 0;
            this.detAngleGrinderNum = 0;
            this.detElectroMobileNum = 0;
            this.detFireNum = 0;
            this.detClimbWorkNum = 0;
            this.detSlipperNum = 0;
            this.detNoUniformNum = 0;
            this.detSmokeNum = 0;
            this.detSafetyHelmetNum = 0;
            this.detSafetyMeshNum = 0;
            this.detSafetyHoleNum = 0;
            this.detSafetyStairsNum = 0;
        }
    }

    // export class TidinessDetailReq extends BaseReportReq<DetectionDetail> {
    //     @ApiProperty({ description: "工地地址", required: false })
    //     projectAddress?: string;
    //     @ApiProperty({ description: "整洁度 good-好,middle-中,bad-差,unknown-未知", enum: ['good', 'middle', 'bad', 'unknown'], required: false })
    //     tidinessType?: string;
    //     @ApiProperty({ description: "工地负责人Id", required: false })
    //     projectManagerId?: string;
    //     @ApiProperty({ description: "所属公司Id", required: false })
    //     linkCompanyId?: string;
    //     @ApiProperty({ description: "监测时间 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
    //     eventTimeSort?: 'DESC' | 'ASC';
    //     tidinessDetectionIds?: string[];
    //     projectIdList?: string[];
    // }

    // export class SecurityDetailReq extends BaseReportReq<DetectionDetail> {
    //     @ApiProperty({ description: "工地地址", required: false })
    //     projectAddress?: string;
    //     @ApiProperty({ description: "报告类型 0-smoke-抽烟,5-slippers-穿拖鞋,4-workClothes-未穿工服,7-electroMobiles-停放电瓶车", enum: ['smoke', 'slippers', 'workClothes', 'electroMobiles'], required: false })
    //     securityType?: string;
    //     @ApiProperty({ description: "工地负责人Id", required: false })
    //     projectManagerId?: string;
    //     @ApiProperty({ description: "所属公司Id", required: false })
    //     linkCompanyId?: string;
    //     @ApiProperty({ description: "监测时间 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
    //     eventTimeSort?: 'DESC' | 'ASC';
    //     securityDetectionIds?: string[];
    //     projectIdList?: string[];
    // }

    export class DetectionDetailReq {
        @ApiProperty({ description: "工地地址", required: false })
        projectAddress?: string;
        @ApiProperty({ description: "报告类型" })
        reportType?: string[];
        @ApiProperty({ description: "工地负责人Id", required: false })
        projectManagerId?: string;
        @ApiProperty({ description: "所属公司Id", required: false })
        linkCompanyId?: string;
        @ApiProperty({ description: "监测时间 DESC-降序,ASC-升序", enum: ['DESC', 'ASC'] })
        eventTimeSort?: 'DESC' | 'ASC';
        securityDetectionIds?: string[];
        projectIdList?: string[];
    }

    export class DetectionDetail {
        @ApiProperty({ description: "工地地址" })
        projectAddress: string;
        @ApiProperty({ description: "工地整洁度 good-好,middle-中,bad-差,unknown-未知", enum: ['good', 'middle', 'bad', 'unknown'] })
        tidinessType?: string;
        @ApiProperty({ description: "工地文明施工 smoke-抽烟,slippers-穿拖鞋,workClothes-未穿工服,electroMobiles-停放电瓶车", enum: ['smoke', 'slippers', 'workClothes', 'electroMobiles'] })
        securityType?: string;
        @ApiProperty({ description: "报告类型" })
        reportType?: string[];
        @ApiProperty({ description: "抓拍id" })
        eventId: string;
        @ApiProperty({ description: "图片地址" })
        photoUrl: string;
        @ApiProperty({ description: "图片" })
        photo: CorpReport.Img;
        @ApiProperty({ description: "监测时间" })
        detectionDate: string;
        @ApiProperty({ description: "工地负责人id" })
        projectManagerId: string;
        @ApiProperty({ description: "工地负责人name" })
        projectManagerName: string;
        @ApiProperty({ description: "所属公司id" })
        linkCompanyId: string;
        @ApiProperty({ description: "所属公司name" })
        linkCompanyName: string;
        @ApiProperty({ description: "工地归属id" })
        departmentId: string;
        @ApiProperty({ description: "工地归属name" })
        departmentName: string;
        linkCompanyCreateTime?: string;//所属公司创建时间
        projectId: string;
    }

    export class SearchReq {
        idList: string[];
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;
        projectAddress?: string;
        linkCompanyId?: string;
        projectIdList?: string[];
        reportType?: string[];
        pageNo?: number;
        pageSize?: number;
        detectionType?: 'action' | 'body';
    }

    export class SecuritySearchReq {
        idList: string[];
        detectionType: 'action' | 'body';
        deviceDepartmentId?: string;//服务厂商
        projectManagerId?: string;
        projectAddress?: string;
        linkCompanyId?: string;
        projectIdList?: string[];
        reportType?: string[];
        pageNo?: number;
        pageSize?: number;
    }

}