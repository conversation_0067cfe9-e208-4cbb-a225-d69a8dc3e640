import { Injectable } from "@nestjs/common";
import { ProjectNoFacesignEventEntity } from "@src/modules/report/entity/camera/project-no-facesign-event.entity";
import { DaoUtil, MyLogger } from "@yqz/nest";
import { DataSource } from "typeorm";

@Injectable()
export class ProjectNoFacesignEventDao {
    private readonly logger = new MyLogger(ProjectNoFacesignEventDao.name);
    constructor(
        private readonly datasource: DataSource
    ) { }


    async search(params: { projectIdList: string[], startTime?: string, endTime?: string, signInNoBodyProjectDay?: number }) {
        DaoUtil.checkEmptyParams(params, ProjectNoFacesignEventDao.name + '.search', 'FailIfAllEmpty');
        const { projectIdList, startTime, endTime, signInNoBodyProjectDay } = params || {};
        const qb = this.datasource
            .createQueryBuilder()
            .select([
                'nf.project_id AS projectId'
            ])
            .from(ProjectNoFacesignEventEntity, 'nf')
            .where('nf.delete_flag = "N" and nf.project_id in (:...projectIdList)', { projectIdList })
            .andWhere(" ((nf.to_date IS NULL AND TIMESTAMPDIFF(day,nf.from_date,:endTime) >= :signInNoBodyProjectDay) OR (nf.to_date > :startTime and nf.to_date <= :endTime  AND TIMESTAMPDIFF(day,nf.from_date,nf.to_date) >= :signInNoBodyProjectDay) or (nf.to_date > :endTime and TIMESTAMPDIFF(day,nf.from_date,:endTime) >= :signInNoBodyProjectDay)) ",{startTime: startTime,endTime: endTime, signInNoBodyProjectDay})
            .groupBy('nf.project_id')
        const res = await qb.getRawMany()
        return res;
    }

}