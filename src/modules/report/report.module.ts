import { <PERSON>du<PERSON> } from "@nestjs/common";
import { CronService } from "./cron/cron.service";
import { <PERSON>ce<PERSON>ontroller } from "./domain/camera/device/device.controller";
import { DeviceDao } from "./domain/camera/device/dao/device.dao";
import { DeviceService } from "./domain/camera/device/service/device.service";
import { FactComapnyDeviceDao } from "./domain/etl/dao/fact-company-device.dao";
import { CompanyDeviceService } from "./domain/etl/service/company-device.service";
import { EtlController } from "./domain/etl/elt.controller";
import { ManagerDao } from "./domain/bgw/manager/dao/manager.dao";
import { ProjectReportBasicsService } from "./domain/bgw/project-report/service/basics/project-report.basics.service";
import { ProjectDao } from "./domain/bgw/project/dao/project.dao";
import { ProjectNodeDao } from "./domain/bgw/project/dao/project-node.dao";
import { ProjectDetectionDao } from "./domain/camera/project-detection/dao/detection.dao";
import { ProjectProblemDao } from "./domain/bgw/project-problem/dao/project-problem.dao";
import { ProjectReportDao } from "./domain/bgw/project-report/dao/project-report.dao";
import { ProjectSignInDao } from "./domain/bgw/project-sign-in/dao/project-sign-in.dao";
import { ProjectInactiveRecordDao } from "./domain/camera/project-inactive-record/dao/project-inactive-record.dao";
import { ProjectMemberDao } from "./domain/bgw/project-member/dao/project-member.dao";
import { DepartmentDao } from "./domain/bgw/department/dao/department.dao";
import { DimDepartmentDao } from "./domain/etl/dao/dim-department.dao";
import { DepartmentService } from "./domain/bgw/department/service/department.service";
import { DepartmentMemberDao } from "./domain/bgw/department/dao/department-member.dao";
import { FactCompanyDeviceEtl } from "./domain/etl-task/fact-company-device/etl";
import { DimDepartmentEtl } from "./domain/etl-task/dim-department/etl";
import { ProjectReportEtl } from "./domain/etl-task/project-report/etl";
import { CorpReportController } from "./domain/corp/corp-report/corp-report.controller";
import { CorpReportService } from "./domain/corp/corp-report/service/corp-report.service";
import { DimDeviceEtl } from "./domain/etl-task/dim-device/etl";
import { EtlHelper } from "./domain/etl-task/helper/etl.helper";
import { ProjectNoFacesignEventEtl } from "./domain/etl-task/project-no-facesign-event/etl";
import { CorpReportBasicsService } from "./domain/corp/corp-report/service/basics/corp-report-basics.service";
import { NodeOverdueEventEtl } from "./domain/etl-task/node-overdue-event/etl";
import { DimDeviceDao } from "./domain/etl/dao/dim-device.dao";
import { CorpReportDeviceService } from "./domain/corp/corp-report/service/basics/corp-report-device.service";
import { CorpReportTidinessService } from "./domain/corp/corp-report/service/basics/corp-report-tidiness.service";
import { CorpReportSecurityService } from "./domain/corp/corp-report/service/basics/corp-report-security.service";
import { CorpReportSignInService } from "./domain/corp/corp-report/service/basics/corp-report-signin.service";
import { CorpReportPatrolService } from "./domain/corp/corp-report/service/basics/corp-report-patrol.service";
import { ProjectNoFacesignEventDao } from "./domain/camera/project-no-facesign-event/dao/project-no-facesign-event.dao";
import { CompanyDao } from "./domain/bgw/company/dao/company.dao";
import { DeviceErrorDao } from "./domain/camera/device-error/dao/device-error.dao";
import { ProjectReportController } from "./domain/bgw/project-report/project-report.controller";
import { DeviceOfflineEventDao } from "./domain/camera/device-offline-event/dao/device-offline-event.dao";
import { ProjectPatrolDao } from "./domain/bgw/project-patrol/project-patrol.dao";
import { CommonParameterDao } from "./domain/bgw/common/common-parameter.dao";
import { CmsPhotoDao } from "./domain/bgw/common/cms-photo.dao";
import { CorpReportProjectService } from "./domain/corp/corp-report/service/basics/corp-report-project.service";
import { NodeOverdueEventDao } from "./domain/bgw/node-overdue-event/dao/node-overdue-event.dao";
import { CompanyDecorationDao } from "./domain/bgw/common/company-decoration.dao";
import { DetectionController } from "./domain/camera/detection/detection.controller";
import { DetectionDao } from "./domain/camera/detection/detection.dao";
import { DetectionService } from "./domain/camera/detection/detection.service";
import { ProjectService } from "./domain/bgw/project/service/project.service";
import { CompanyCustomConfigDao } from "./domain/bgw/company/dao/company-custom-config.dao";
import { CompanyCustomConfigService } from "./domain/bgw/company/service/company-custom-config.service";
import { MarketingController } from "./domain/bgw/marketing/marketing.controller";
import { MarketingService } from "./domain/bgw/marketing/service/marketing.service";
import { MarketingDao } from "./domain/bgw/marketing/dao/marketing.dao";
import { OrderManageDao } from "./domain/bgw/order/dao/order-manage.dao";
import { SalesCustomerDao } from "./domain/bgw/sales-customer/dao/sales-customer.dao";
import { RoleDao } from "./domain/bgw/role/dao/role.dao";
import { ManagerCreditDao } from "./domain/bgw/marketing/dao/manager-credit.dao";
import { ProjectSignUpdateController } from "./domain/bgw/project/project-sign-update.controller";
import { ProjectSignUpdateService } from "./domain/bgw/project/service/project-sign-update.service";
import { ProjectSignUpdateDao } from "./domain/bgw/project/dao/project-sign-update.dao";
import { ProjectController } from "./domain/bgw/project/project.controller";
import { ProjectSignProblemController } from "./domain/bgw/project/project-sign-problem.controller";
import { ProjectSignProblemService } from "./domain/bgw/project/service/project-sign-problem.service";
import { ProjectSignProblemDao } from "./domain/bgw/project/dao/project-sign-problem.dao";
import { ProjectSignNodeService } from "./domain/bgw/project/service/project-sign-node.service";
import { ProjectSignNodeDao } from "./domain/bgw/project/dao/project-sign-node.dao";
import { ProjectSignNodeController } from "./domain/bgw/project/project-sign-node.controller";
import { DataAvailabilityService } from "./domain/bgw/statistics/service/data-availability.service";
import { DataAvailabilityDao } from "./domain/bgw/statistics/dao/data-availability.dao";
import { ProjectViewAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/project-view-availability.strategy";
import { StatisticsController } from "./domain/bgw/statistics/statistics.controller";
import { DataAvailabilityStrategyProvider } from "./domain/bgw/statistics/strategy/data-availability.strategy.provider";
import { AiSignAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/ai-sign-availability.strategy";
import { ProjectProblemAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/project-problem-availability.strategy";
import { ProjectSecurityAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/project-security-availability.strategy";
import { ProjectSignAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/project-sign-availability.strategy";
import { ProjectTidinessAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/project-tidiness-availability.strategy";
import { LiveSilhouettePatrolAvailabilityStrategy } from "./domain/bgw/statistics/strategy/impl/live-silhouette-patrol.strategy";
import { StatisticsDao } from "./domain/bgw/statistics/dao/statistics.dao";
import { StatisticsService } from "./domain/bgw/statistics/service/statistics.service";
import { AdjacentIdStrategyProvider } from "./domain/bgw/statistics/strategy/pre-next-id/adjacent-id.strategy.provider";
import { DimDepartmentDataDao } from "./domain/etl/dao/dim-department-data.dao";
import { ProjectAddressFilter } from "./domain/bgw/filter/handler/impl/address-project-filter.handler";
import { DeptProjectFilter } from "./domain/bgw/filter/handler/impl/dept-project-filter.handler";
import { ScopeProjectFilter } from "./domain/bgw/filter/handler/impl/scope-project-filter.handler";
import { SubDeptFilter } from "./domain/bgw/filter/handler/impl/sub-dept-filter.handler";
import { DataReportConfigService } from "./domain/corp/data-report/service/data-report-config.service";
import { DataReportConfigController } from "./domain/corp/data-report/data-report-config.controller";
import { DataReportModuleController } from "./domain/corp/data-report/data-report-module.controller";
import { DataReportBoardController } from "./domain/corp/data-report/data-reprot-board.controller";
import { DataReportModuleService } from "./domain/corp/data-report/service/data-report-module.service";
import { DataReportBoardService } from "./domain/corp/data-report/service/data-report-board.service";
import { ChartFactory } from "./domain/corp/data-report/chart/chart.factory";
import { DataReportBoardDao } from "./domain/corp/data-report/dao/data-report-board.dao";
import { DataReportModuleDao } from "./domain/corp/data-report/dao/data-report-module.dao";
import { DataReportStrategy } from "./domain/corp/data-report/service/basic/data-report.strategy";
import { AiDetectionDataReportService } from "./domain/corp/data-report/service/basic/service/ai-detection-data-report.service";
import { CustomChartsDao } from "./domain/corp/custom-charts/dao/custom-charts.dao";
import { DataReportDao } from "./domain/corp/data-report/dao/data-report-config.dao";
import { ProjectUpdateBoardDao } from "./domain/corp/data-old-report/dao/project-update.dao";
import { MediaService } from "./domain/bgw/media/service/media.service";
import { ProjectUpdateService } from "./domain/corp/data-old-report/service/project-update.service";
import { AnalyticsService } from "./domain/corp/data-old-report/service/analytics.service";
import { OnlinePatrolService } from "./domain/corp/data-old-report/service/online-patrol.service";
import { ProjectPatrolBoardDao } from "./domain/corp/data-old-report/dao/project-patrol.dao";
import { CompanyConfigDao } from "./domain/corp/data-old-report/dao/company-config.dao";
import { OnsitePatrolService } from "./domain/corp/data-old-report/service/onsite-patrol.service";
import { OnsitePatrolBoardDao } from "./domain/corp/data-old-report/dao/onsite-patrol.dao";
import { SqlFactory } from "./domain/corp/sql-generate/factory/sql-factory";
import { ProjectDataReportService } from "./domain/corp/data-report/service/basic/service/project-data-report.service";
import { DeviceDataReportService } from "./domain/corp/data-report/service/basic/service/device-data-report.service";
import { AcceptanceReportDataReportService } from "./domain/corp/data-report/service/basic/service/acceptance-report-data-report.service";
import { CustomerEvaluateDataReportService } from "./domain/corp/data-report/service/basic/service/customer-evaluate-data-report.service";
import { NodeOverdueDataReportService } from "./domain/corp/data-report/service/basic/service/node-overdue-data-report.service";
import { ProjectProblemDataReportService } from "./domain/corp/data-report/service/basic/service/project-problem-data-report.service";
import { ProjectUpdateController } from "./domain/corp/data-old-report/project-update.controller";
import { OnlinePatrolController } from "./domain/corp/data-old-report/online-patrol.controller";
import { OnsitePatrolController } from "./domain/corp/data-old-report/onsite-patrol.controller";
import { DispatchBoardDao } from "./domain/corp/data-old-report/dao/dispatch.dao";
import { DispatchService } from "./domain/corp/data-old-report/service/dispatch.service";
import { DispatchController } from "./domain/corp/data-old-report/dispatch.controller";
import { AcceptanceReportDao } from "./domain/bgw/acceptance-report/dao/acceptance-report.dao";
import { DeliveryDataReportService } from "./domain/corp/data-report/service/basic/service/delivery-data-report.service";
import { CompanyService } from "./domain/bgw/company/service/company.service";
import { RoleService } from "./domain/bgw/role/service/role.service";
import { CommonParameterService } from "./domain/bgw/common/common-paramet.serervice";
import { AcceptanceReportService } from "./domain/bgw/acceptance-report/service/acceptance-report.service";
import { ManagerService } from "./domain/bgw/manager/service/manager.service";
import { DashboardTemplateController } from "./domain/corp/data-report/dashboard-template.controller";
import { DashboardTemplateService } from "./domain/corp/data-report/service/dashboard-template.service";
import { DashboardTemplateDao } from "./domain/corp/data-report/dao/dashboard-template.dao";
import { DataReportModuleFilterDao } from "./domain/corp/data-report/dao/data-report-module-filter.dao";
import { DashboardTemplateTagService } from "./domain/corp/data-report/service/dashboard-template-tag.service";
import { ExternalProjectDao } from "./domain/bgw/project/dao/external-project.dao";
import { BodyDao } from "./domain/camera/body/body.dao";
import { ProjectProblemIdStrategy } from "./domain/bgw/statistics/strategy/pre-next-id/impl/project-problem-id.strategy";

@Module({
  providers: [
    NodeOverdueEventEtl,
    ProjectNoFacesignEventEtl,
    EtlHelper,
    DimDeviceEtl,
    DepartmentMemberDao,
    DepartmentDao,
    DepartmentService,
    DimDepartmentDao,
    CronService,
    DeviceDao,
    DeviceService,
    FactComapnyDeviceDao,
    CompanyDeviceService,
    ManagerDao,
    ProjectReportBasicsService,
    ProjectDetectionDao,
    ProjectDao,
    ProjectNodeDao,
    ProjectProblemDao,
    ProjectReportDao,
    ProjectSignInDao,
    ProjectInactiveRecordDao,
    ProjectMemberDao,
    FactCompanyDeviceEtl,
    DimDepartmentEtl,
    ProjectReportEtl,
    CorpReportService,
    CorpReportBasicsService,
    CorpReportDeviceService,
    CorpReportProjectService,
    CorpReportTidinessService,
    CorpReportSecurityService,
    DetectionService,
    DimDeviceDao,
    CorpReportSignInService,
    CorpReportPatrolService,
    ProjectSignUpdateService,
    ProjectSignProblemService,
    ProjectSignNodeService,
    ProjectNoFacesignEventDao,
    DeviceErrorDao,
    DeviceOfflineEventDao,
    CompanyDao,
    ProjectPatrolDao,
    CommonParameterDao,
    NodeOverdueEventDao,
    CmsPhotoDao,
    CompanyDecorationDao,
    DetectionDao,
    ProjectService,
    CompanyCustomConfigService,
    CompanyCustomConfigDao,
    MarketingService,
    MarketingDao,
    OrderManageDao,
    SalesCustomerDao,
    RoleDao,
    ManagerCreditDao,
    ProjectSignUpdateDao,
    ProjectSignProblemDao,
    ProjectSignNodeDao,
    DataAvailabilityService,
    DataAvailabilityDao,
    DataAvailabilityStrategyProvider,
    ProjectViewAvailabilityStrategy,
    AiSignAvailabilityStrategy,
    ProjectProblemAvailabilityStrategy,
    ProjectSecurityAvailabilityStrategy,
    ProjectSignAvailabilityStrategy,
    ProjectTidinessAvailabilityStrategy,
    LiveSilhouettePatrolAvailabilityStrategy,
    StatisticsDao,
    StatisticsService,
    AdjacentIdStrategyProvider,
    ProjectProblemIdStrategy,
    DimDepartmentDataDao,
    ProjectAddressFilter,
    DeptProjectFilter,
    ScopeProjectFilter,
    SubDeptFilter,
    DataReportConfigService,
    DataReportModuleService,
    DataReportBoardService,
    ChartFactory,
    DataReportBoardDao,
    DataReportModuleDao,
    AiDetectionDataReportService,
    DataReportStrategy,
    SqlFactory,
    CustomChartsDao,
    DataReportDao,
    ProjectUpdateBoardDao,
    MediaService,
    ProjectUpdateService,
    AnalyticsService,
    OnlinePatrolService,
    ProjectPatrolBoardDao,
    CompanyConfigDao,
    OnsitePatrolService,
    OnsitePatrolBoardDao,
    AcceptanceReportDataReportService,
    DeviceDataReportService,
    ProjectDataReportService,
    CustomerEvaluateDataReportService,
    NodeOverdueDataReportService,
    ProjectProblemDataReportService,
    DispatchBoardDao,
    DispatchService,
    AcceptanceReportDao,
    DeliveryDataReportService,
    CompanyService,
    RoleService,
    CommonParameterService,
    AcceptanceReportService,
    ManagerService,
    DashboardTemplateService,
    DashboardTemplateDao,
    DataReportModuleFilterDao,
    DashboardTemplateTagService,
    ExternalProjectDao,
    BodyDao
  ],
  controllers: [
    ProjectReportController,
    DeviceController,
    EtlController,
    CorpReportController,
    DetectionController,
    MarketingController,
    ProjectSignUpdateController,
    ProjectController,
    ProjectSignProblemController,
    ProjectSignNodeController,
    StatisticsController,
    DataReportConfigController,
    DataReportModuleController,
    DataReportBoardController,
    ProjectUpdateController,
    OnlinePatrolController,
    OnsitePatrolController,
    DispatchController,
    DashboardTemplateController
  ],
  exports: [ExternalProjectDao]
})
export class ReportModule { }