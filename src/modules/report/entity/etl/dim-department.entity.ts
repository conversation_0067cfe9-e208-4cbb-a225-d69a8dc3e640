import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity } from "typeorm";

@Entity(DaoUtil.parseDbName('etl.dim_department'))
export class DimDepartmentEntity {
  @Column("varchar", { primary: true, name: "department_id", length: 50 })
  departmentId: string;

  @Column("char", { primary: true, name: "date", length: 8 })
  date: string;

  @Column("bigint", { name: "company_id" })
  companyId: string;

  @Column("longtext", { name: "child_department_id_list", nullable: true })
  childDepartmentIdList: string | "";

  @Column("longtext", { name: "direct_child_department_id_list", nullable: true })
  directChildDepartmentIdList: string | "";

  @Column("longtext", { name: "manager_id_list", nullable: true })
  managerIdList: string | null;

  // @Column("longtext", { name: "person_id_list", nullable: true})
  // personIdList: string | null;

  @Column("longtext", { name: "device_id_list", nullable: true })
  deviceIdList: string | null;

  @Column("longtext", {
    name: "project_id_list",
    nullable: true
  })
  projectIdList: string | null;

  @Column("longtext", {
    name: "ai_project_id_list",
    nullable: true
  })
  aiProjectIdList: string | null;

  @Column("datetime", {
    name: "create_time",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;
}
