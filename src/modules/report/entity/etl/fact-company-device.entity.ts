import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('etl.fact_company_device'))
export class FactCompanyDeviceEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("int", { name: "company_key", nullable: true })
  company_key: number | null;

  @Column("int", { name: "time_key", nullable: true })
  time_key: number | null;

  @Column("int", { name: "device_qty", nullable: true, comment: "设备数" })
  device_qty: number | null;

  @Column("int", {
    name: "install_qty",
    nullable: true,
    comment: "安装数(绑定工地)",
  })
  install_qty: number | null;

  @Column("int", { name: "online_qty", nullable: true, comment: "在线数" })
  online_qty: number | null;
}
