import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index } from "typeorm";

@Index("device_date_idx", ["deviceId", "date"], {})
@Entity(DaoUtil.parseDbName('etl.dim_device'))
export class DimDeviceEntity {
  @Column("varchar", { primary: true, name: "device_id", length: 50 })
  deviceId: string;

  @Column("char", { primary: true, name: "date", length: 8 })
  date: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("bigint", { name: "project_director", nullable: true })
  projectDirector: string | null;

  @Column("datetime", { name: "manager_revice_time", nullable: true })
  managerReviceTime: Date | null;

  @Column("datetime", { name: "project_binding_time", nullable: true })
  projectBindingTime: Date | null;

  @Column("bigint", { name: "department_id", nullable: true })
  departmentId: string | null;

  @Column("datetime", { name: "link_department_time", nullable: true })
  linkDepartmentTime: Date | null;

  @Column("datetime", {
    name: "create_time",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("datetime", { name: "update_time", nullable: true })
  updateTime: Date | null;
}
