import { Column, Entity, Index } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("idx_createTime", ["createTime"], {})
@Index("idx_date", ["date"], {})
@Entity(DaoUtil.parseDbName('etl.dim_department_data'))
export class DimDepartmentDataEntity {
  @Column("bigint", { primary: true, name: "department_id", comment: "部门id" })
  departmentId: string;

  @Column("char", { primary: true, name: "date", comment: "日期", length: 8 })
  date: string;

  @Column("bigint", { name: "company_id", nullable: true, comment: "公司id" })
  companyId: string | null;

  @Column("longtext", {
    name: "manager_id_list",
    nullable: true,
    comment: "部门成员manager id列表",
  })
  managerIdList: string | null;

  @Column("longtext", {
    name: "device_id_list",
    nullable: true,
    comment: "部门归属设备id列表",
  })
  deviceIdList: string | null;

  @Column("longtext", {
    name: "project_id_list",
    nullable: true,
    comment: "部门归属工地id列表",
  })
  projectIdList: string | null;

  @Column("longtext", {
    name: "ai_project_id_list",
    nullable: true,
    comment: "部门归属智慧工地id列表",
  })
  aiProjectIdList: string | null;

  @Column("datetime", {
    name: "create_time",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;
}
