import { Dao<PERSON>til } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("task_date_idx", ["task", "date"], {})
@Entity(DaoUtil.parseDbName('etl.etl_staging'))
export class EtlStagingEntity {
  @PrimaryGeneratedColumn({ type: "int", name: "id" })
  id: number;

  @Column("varchar", { name: "task", nullable: true, length: 100 })
  task: string | null;

  @Column("varchar", { name: "date", nullable: true, length: 8 })
  date: string | null;

  @Column("longtext", { name: "data", nullable: true })
  data: string | null;

  @Column("enum", {
    name: "status",
    nullable: true,
    enum: ["open", "failed", "done"],
    default: () => "'open'",
  })
  status: "open" | "failed" | "done" | null;

  @Column("datetime", {
    name: "create_time",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("datetime", { name: "update_time", nullable: true })
  updateTime: Date | null;
}
