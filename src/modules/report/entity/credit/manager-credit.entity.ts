import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName("credit.manager_credit"))
export class ManagerCreditEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "manager_credit_id" })
  managerCreditId: string;

  @Column("bigint", { name: "credit_rules_id", nullable: true })
  creditRulesId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("varchar", { name: "credit_type", nullable: true, length: 5 })
  creditType: string | null;

  @Column("varchar", { name: "action_type", nullable: true, length: 30 })
  actionType: string | null;

  @Column("varchar", { name: "target_type", nullable: true, length: 5 })
  targetType: string | null;

  @Column("bigint", { name: "target_id", nullable: true })
  targetId: string | null;

  @Column("datetime", { name: "action_time", nullable: true })
  actionTime: Date | null;

  @Column("bigint", { name: "credit", nullable: true })
  credit: string | null;

  @Column("bigint", { name: "department_id", nullable: true })
  departmentId: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 5,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;
}
