import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";


@Entity(DaoUtil.parseDbName('credit.credit_rules'))
export class CreditRulesEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "credit_rules_id" })
  credit_rules_id: string;

  @Column("bigint", { name: "company_id", nullable: true })
  company_id: string | null;

  @Column("varchar", { name: "action_type", nullable: true, length: 30 })
  action_type: string | null;

  @Column("varchar", { name: "target_type", nullable: true, length: 5 })
  target_type: string | null;

  @Column("bigint", { name: "credit", nullable: true })
  credit: string | null;

  @Column("varchar", { name: "credit_reward_role", nullable: true, length: 30 })
  credit_reward_role: string | null;

  @Column("varchar", { name: "upper_limit_period", nullable: true, length: 30 })
  upper_limit_period: string | null;

  @Column("bigint", { name: "upper_limit_times", nullable: true })
  upper_limit_times: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 5,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;
}
