import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "idx_company_id_manager_id_report_date",
  ["companyId", "managerId", "reportDate"],
  {}
)
@Entity(DaoUtil.parseDbName('bgw.project_report'))
export class ProjectReportEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("text", {
    name: "report_content",
    nullable: true,
    comment: "日报内容",
  })
  reportContent: string | null;

  @Column("date", {
    name: "report_date",
    nullable: true,
    comment: "日报生成时间",
  })
  reportDate: Date | null;

  @Column("int", {
    name: "exp_count",
    comment: "异常数",
  })
  expCount: number;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
