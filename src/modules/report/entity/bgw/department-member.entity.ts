import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_departmentId_managerId", ["departmentId", "managerId"], {})
@Index("idx_managerId", ["managerId"], {})
@Entity(DaoUtil.parseDbName("bgw.department_member"))
export class DepartmentMemberEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "department_member_id" })
  departmentMemberId: string;

  @Column("bigint", { name: "department_id", nullable: true })
  departmentId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("varchar", { name: "role", nullable: true, length: 100 })
  role: string | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 20 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 20 })
  updateBy: string | null;
}
