import { Dao<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

/**
 * @summary 节点模板表
 * @class NodeTemplateEntity
 */
@Entity(DaoUtil.parseDbName('bgw.node_template'))
export class NodeTemplateEntity {
    @PrimaryGeneratedColumn({ name: "node_template_id", type: "bigint" })
    nodeTemplateId: string;

    @Column({ name: "node_template_name", type: "varchar", length: 20 })
    nodeTemplateName: string;

    @Column({ name: "stage_template_id", type: "bigint" })
    stageTemplateId: string;

    @Column({ name: "company_id", type: "bigint" })
    companyId: string;

    @Column({ name: "DELETE_FLAG", type: "varchar", length: 1, default: "N" })
    deleteFlag: string;

    @Column({ name: "createTime", type: "datetime" })
    createTime: Date;

    @Column({ name: "createBy", type: "varchar", length: 30 })
    createBy: string;

    @Column({ name: "updateTime", type: "datetime" })
    updateTime: Date;

    @Column({ name: "updateBy", type: "varchar", length: 30 })
    updateBy: string;

    @Column({ name: "late_update_time", type: "datetime" })
    lateUpdateTime: Date;

    @Column({ name: "skip_holiday", type: "varchar", length: 30 })
    skipHoliday: string;
} 