import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { Company } from "../../domain/bgw/company/dto/company.dto";

@Entity(DaoUtil.parseDbName('bgw.company'))
export class CompanyEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "COMPANY_ID" })
  COMPANY_ID: string;

  @Column("varchar", { name: "COMPANY_NAME", nullable: true, length: 50 })
  COMPANY_NAME: string | null;

  @Column("varchar", { name: "SHORT_NAME", nullable: true, length: 30 })
  SHORT_NAME: string | null;

  @Column("varchar", { name: "COMPANY_ADDRESS", nullable: true, length: 100 })
  COMPANY_ADDRESS: string | null;

  @Column("varchar", { name: "<PERSON><PERSON><PERSON>", nullable: true, length: 600 })
  BRIEF: string | null;

  @Column("varchar", { name: "PHONE_NO", nullable: true, length: 20 })
  PHONE_NO: string | null;

  @Column("varchar", { name: "LOGO", nullable: true, length: 50 })
  LOGO: string | null;

  @Column("varchar", { name: "HOMEPAGE", nullable: true, length: 10 })
  HOMEPAGE: string | null;

  @Column("varchar", { name: "LONGITUDE", nullable: true, length: 50 })
  LONGITUDE: string | null;

  @Column("varchar", { name: "LATITUDE", nullable: true, length: 50 })
  LATITUDE: string | null;

  @Column("varchar", { name: "PROVINCE", nullable: true, length: 100 })
  PROVINCE: string | null;

  @Column("varchar", { name: "CITY", nullable: true, length: 100 })
  CITY: string | null;

  @Column("enum", { name: "ISVERIFY", nullable: true, enum: ["N", "Y"] })
  ISVERIFY: "N" | "Y" | null;

  @Column("bigint", { name: "PAGE_VIEW_COUNT", nullable: true })
  PAGE_VIEW_COUNT: string | null;

  @Column("datetime", { name: "CREATE_TIME", nullable: true })
  CREATE_TIME: Date | null;

  @Column("varchar", { name: "COMPANY_TYPE", nullable: true, length: 10 })
  COMPANY_TYPE: Company.CompanyType | null;

  @Column("varchar", { name: "SLOGAN", nullable: true, length: 60 })
  SLOGAN: string | null;

  @Column("enum", {
    name: "CREATE_FROM",
    nullable: true,
    enum: ["3", "2", "1"],
  })
  CREATE_FROM: "3" | "2" | "1" | null;

  @Column("bigint", { name: "CREATE_PERSON", nullable: true })
  CREATE_PERSON: string | null;

  @Column("enum", { name: "VALID", nullable: true, enum: ["1", "2", "3", "4"] })
  VALID: "1" | "2" | "3" | "4" | null;

  @Column("bigint", { name: "TECHNICAL_SUPPORT_ID", nullable: true })
  TECHNICAL_SUPPORT_ID: string | null;

  @Column("varchar", { name: "POOL_CODE", nullable: true, length: 255 })
  POOL_CODE: string | null;

  @Column("enum", { name: "ONLINE_SERVICE", nullable: true, enum: ["Y", "N"] })
  ONLINE_SERVICE: "Y" | "N" | null;

  @Column("varchar", { name: "show_sales_card", nullable: true, length: 10 })
  show_sales_card: string | null;

  @Column("varchar", {
    name: "show_water_mark",
    nullable: true,
    comment:
      "1-基础水印相机（公司logo、公司简称），2-高级水印相机（拍摄时间地点）",
    length: 10,
  })
  show_water_mark: string | null;

  @Column("bigint", { name: "agentorg_id", nullable: true })
  agentorg_id: string | null;

  @Column("bigint", { name: "company_decoration_id", nullable: true })
  company_decoration_id: string | null;

  @Column("int", { name: "TOTAL_PAGE_VIEW", nullable: true })
  TOTAL_PAGE_VIEW: number | null;

  @Column("varchar", { name: "district", nullable: true, length: 100 })
  district: string | null;

  @Column("varchar", {
    name: "SALES_MANAGER_LEADS",
    nullable: true,
    comment: "公司层面预约管理权限控制",
    length: 10,
    default: () => "'N'",
  })
  SALES_MANAGER_LEADS: string | null;

  @Column("varchar", { name: "wx_app_id", nullable: true, length: 100 })
  wx_app_id: string | null;

  @Column("bigint", { name: "UPDATE_REMIND_TIMER", nullable: true })
  UPDATE_REMIND_TIMER: string | null;

  @Column("varchar", {
    name: "SEND_UPDATE_REMIND_PUSH",
    nullable: true,
    length: 10,
  })
  SEND_UPDATE_REMIND_PUSH: string | null;

  @Column("datetime", { name: "LAST_VISIT_TIME", nullable: true })
  LAST_VISIT_TIME: Date | null;

  @Column("varchar", { name: "is_first_register", nullable: true, length: 1 })
  is_first_register: string | null;

  @Column("varchar", { name: "business_category", nullable: true })
  business_category: 'home_decor' | 'construction';

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
