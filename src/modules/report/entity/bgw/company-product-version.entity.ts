import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_company", ["companyDecorationId"], {})
@Entity(DaoUtil.parseDbName('bgw.company_product_version'))
export class CompanyProductVersionEntity {
  @PrimaryGeneratedColumn({
    type: "bigint",
    name: "company_product_version_id",
  })
  companyProductVersionId: string;

  @Column("bigint", { name: "company_decoration_id", nullable: true })
  companyDecorationId: string | null;

  @Column("tinyint", { name: "product_version", nullable: true, width: 1 })
  productVersion: boolean | null;

  @Column("datetime", { name: "vip_start_time", nullable: true })
  vipStartTime: Date | null;

  @Column("datetime", { name: "expire_time", nullable: true })
  expireTime: Date | null;

  @Column("varchar", { name: "has_tried_vip", nullable: true, length: 2 })
  hasTriedVip: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("varchar", { name: "DELETE_FLAG", nullable: true, length: 1 })
  deleteFlag: string | null;
}
