import { Base } from "@src/entity/base.entity";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index(
  "idx_companyId_parentDepartmentId",
  ["companyId", "parentDepartmentId"],
  {}
)
@Entity(DaoUtil.parseDbName('bgw.department'))
export class DepartmentEntity extends Base{
  @PrimaryGeneratedColumn({ type: "bigint", name: "department_id" })
  departmentId: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("varchar", { name: "department_name", nullable: true, length: 20 })
  departmentName: string | null;

  @Column("varchar", { name: "manage_leads_status", nullable: true, length: 1 })
  manageLeadsStatus: string | null;

  @Column("bigint", {
    name: "parent_department_id",
    nullable: true,
    comment: "父级部门id",
    default: () => "'0'",
  })
  parentDepartmentId: string | null;

  @Column("int", {
    name: "level",
    nullable: true,
    comment: "部门树形结构层级",
    default: () => "'0'",
  })
  level: number | null;

  @Column("varchar", {
    name: "dir",
    nullable: true,
    comment: "部门树形结构路径",
    length: 250,
  })
  dir: string | null;

  @Column("enum", { name: "type", nullable: true, enum: ["division"] })
  type: "division" | null;

  @Column("varchar", { name: "link_company_id", nullable: true, length: 50 })
  linkCompanyId: string | null;


}
