import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "idx_deleteflag_companyid_rolename",
  ["deleteFlag", "companyId", "roleName"],
  {}
)
@Entity(DaoUtil.parseDbName("bgw.company_member_role"))
export class CompanyMemberRoleEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "COMPANY_MEMBER_ROLE_ID" })
  companyMemberRoleId: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("varchar", { name: "ROLE_NAME", nullable: true, length: 20 })
  roleName: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
