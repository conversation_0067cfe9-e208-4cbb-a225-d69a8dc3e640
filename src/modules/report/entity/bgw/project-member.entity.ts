import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("PERSON_ID_index", ["personId"], {})
@Index(
  "idx_PROJECTID_PERSONID_DELETEFLAG_createTime",
  ["projectId", "personId", "deleteFlag", "createTime"],
  {}
)
@Entity(DaoUtil.parseDbName('bgw.project_member'))
export class ProjectMemberEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "PROJECT_MEMBER_ID" })
  projectMemberId: string;

  @Column("bigint", { name: "PROJECT_ID", nullable: true })
  projectId: string | null;

  @Column("bigint", { name: "PERSON_ID", nullable: true })
  personId: string | null;

  @Column("varchar", {
    name: "PROJECT_MEMBER_NAME",
    nullable: true,
    length: 255,
  })
  projectMemberName: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
