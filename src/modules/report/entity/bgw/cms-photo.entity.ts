import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity } from "typeorm";

@Entity(DaoUtil.parseDbName('bgw.cms_photo'))
export class CmsPhotoEntity {
  @Column("bigint", { primary: true, name: "photo_id" })
  photoId: string;

  @Column("varchar", { name: "file_name", nullable: true, length: 125 })
  fileName: string | null;

  @Column("enum", {
    name: "media_type",
    nullable: true,
    enum: ["1", "2", "3", "4", "5"],
  })
  mediaType: "1" | "2" | "3" | "4" | "5" | null;

  @Column("varchar", { name: "oss_url", nullable: true, length: 225 })
  ossUrl: string | null;

  @Column("varchar", {
    name: "media_resource_url",
    nullable: true,
    length: 512,
  })
  mediaResourceUrl: string | null;

  @Column("varchar", { name: "resource_url", nullable: true, length: 512 })
  resourceUrl: string | null;

  @Column("varchar", { name: "latitude", nullable: true, length: 50 })
  latitude: string | null;

  @Column("varchar", { name: "longitude", nullable: true, length: 50 })
  longitude: string | null;

  @Column("varchar", { name: "address", nullable: true, length: 200 })
  address: string | null;

  @Column("datetime", { name: "creation_date", nullable: true })
  creationDate: Date | null;

  @Column("timestamp", {
    name: "publish_time",
    default: () => "CURRENT_TIMESTAMP",
  })
  publishTime: Date;

  @Column("bigint", {
    name: "width",
    nullable: true,
    comment: "图片宽度",
    default: () => "'0'",
  })
  width: string | null;

  @Column("bigint", {
    name: "height",
    nullable: true,
    comment: "图片高度",
    default: () => "'0'",
  })
  height: string | null;

  @Column("varchar", { name: "BAIDU_OSS_URL", nullable: true, length: 200 })
  baiduOssUrl: string | null;

  @Column("varchar", { name: "tag", nullable: true, length: 20 })
  tag: string | null;

  @Column("bigint", { name: "person_id", nullable: true, comment: "用户" })
  personId: string | null;

  @Column("varchar", {
    name: "publish_by",
    nullable: true,
    comment: "上传人",
    length: 5,
  })
  publishBy: string | null;

  @Column("bigint", {
    name: "order_by",
    nullable: true,
    comment: "排序",
    default: () => "'0'",
  })
  orderBy: string | null;

  @Column("varchar", { name: "created_by", nullable: true, length: 100 })
  createdBy: string | null;

  @Column("datetime", { name: "created_date", nullable: true })
  createdDate: Date | null;

  @Column("varchar", { name: "updated_by", nullable: true, length: 100 })
  updatedBy: string | null;

  @Column("datetime", { name: "updated_date", nullable: true })
  updatedDate: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 2,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
