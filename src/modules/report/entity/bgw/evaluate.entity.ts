import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('bgw.evaluate'))
export class EvaluateEntity {
    @PrimaryGeneratedColumn({ type: "bigint", name: "EVALUATE_ID" })
    EVALUATE_ID: string;
    @Column("enum", { name: "EVALUATE_TYPE", enum: ["1", "3"] })
    EVALUATE_TYPE: string;

    @Column("bigint", { name: "TARGET_ID" })
    TARGET_ID: number;

    @Column("bigint", { name: "PERSON_ID" })
    PERSON_ID: number;

    @Column("bigint", { name: "CONSTRUCT_VALUE" })
    CONSTRUCT_VALUE: number;

    @Column("varchar", { name: "EVALUATE_DESC", length: 512 })
    EVALUATE_DESC: string;

    @Column("datetime", { name: "EVALUATE_TIME" })
    EVALUATE_TIME: Date;

    @Column("varchar", { name: "createBy", length: 30 })
    createBy: string;

    @Column("varchar", { name: "DELETE_FLAG", length: 30, default: "N" })
    DELETE_FLAG: string;

    @Column("datetime", { name: "createTime" })
    createTime: Date;

    @Column("varchar", { name: "updateBy", length: 30 })
    updateBy: string;

    @Column("datetime", { name: "updateTime" })
    updateTime: Date;

    @Column("bigint", { name: "DESIGH_VALUE" })
    DESIGH_VALUE: number;

    @Column("bigint", { name: "COST_VALUE" })
    COST_VALUE: number;

    @Column("bigint", { name: "COMPANY_ID" })
    COMPANY_ID: number;

    @Column("varchar", { name: "STAGE_LABEL", length: 20 })
    STAGE_LABEL: string;

    @Column("varchar", { name: "evaluate_img", length: 200 })
    evaluate_img: string;

    @Column("varchar", { name: "is_show", length: 5, default: "Y" })
    is_show: string;
}