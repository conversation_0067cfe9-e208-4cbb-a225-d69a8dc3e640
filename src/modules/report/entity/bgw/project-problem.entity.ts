import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("project_id_problem_status_idx", ["projectId", "problemStatus"], {})
@Entity(DaoUtil.parseDbName('bgw.project_problem'))
export class ProjectProblemEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "project_problem_id" })
  projectProblemId: string;

  @Column("varchar", {
    name: "problem_type",
    nullable: true,
    comment:
      "1-施工安全-抽烟/穿拖鞋,2-工地形象-工地整洁度差,3-供应链,4-设计,5-其他,6-设备安装,7-员工形象-未穿工服",
    length: 2,
  })
  problemType: string | null;

  @Column("varchar", {
    name: "problem_description",
    nullable: true,
    length: 128,
  })
  problemDescription: string | null;

  @Column("varchar", { name: "problem_img", nullable: true, length: 250 })
  problemImg: string | null;

  @Column("varchar", {
    name: "problem_source",
    nullable: true,
    comment: "1-摄像头 2-成员",
    length: 2,
  })
  problemSource: string | null;

  @Column("bigint", {
    name: "problem_record_person",
    nullable: true,
    comment: "问题记录manager",
  })
  problemRecordPerson: string | null;

  @Column("datetime", { name: "problem_time", nullable: true })
  problemTime: Date | null;

  @Column("varchar", {
    name: "problem_status",
    nullable: true,
    comment: "0-误判 1-待处理 2-进行中 3-已整改 4-非工作人员",
    length: 2,
  })
  problemStatus: string | null;

  @Column("datetime", { name: "problem_solved_time", nullable: true })
  problemSolvedTime: Date | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("varchar", {
    name: "problem_solved_description",
    nullable: true,
    length: 128,
  })
  problemSolvedDescription: string | null;

  @Column("varchar", {
    name: "problem_solved_img",
    nullable: true,
    length: 250,
  })
  problemSolvedImg: string | null;

  @Column("varchar", {
    name: "problem_source_type",
    nullable: true,
    comment: "1-安全,2-整洁度,3-移动侦测",
    length: 2,
  })
  problemSourceType: string | null;

  @Column("bigint", { name: "problem_source_id", nullable: true })
  problemSourceId: string | null;

  @Column("varchar", {
    name: "problem_source_result_type",
    nullable: true,
    comment: "检测结果",
    length: 20,
  })
  problemSourceResultType: string | null;

  @Column("bigint", {
    name: "problem_solved_person",
    nullable: true,
    comment: "问题解决manager",
  })
  problemSolvedPerson: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 255,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("bigint", {
    name: "problem_record_owner",
    nullable: true,
    comment: "问题记录owner",
  })
  problemRecordOwner: string | null;
}
