import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('bgw.community'))
export class CommunityEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "COMMUNITY_ID" })
  COMMUNITY_ID: string;

  @Column("varchar", { name: "community_name", nullable: true, length: 100 })
  community_name: string | null;

  @Column("varchar", { name: "address", nullable: true, length: 200 })
  address: string | null;

  @Column("varchar", { name: "longitude", nullable: true, length: 50 })
  longitude: string | null;

  @Column("varchar", { name: "latitude", nullable: true, length: 50 })
  latitude: string | null;

  @Column("varchar", { name: "province", nullable: true, length: 50 })
  province: string | null;

  @Column("varchar", { name: "city", nullable: true, length: 50 })
  city: string | null;

  @Column("varchar", { name: "district", nullable: true, length: 50 })
  district: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
