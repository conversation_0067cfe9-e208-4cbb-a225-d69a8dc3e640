import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("project_delete_flag_index", ["DELETE_FLAG"], {})
@Index("project_companyid_index", ["COMPANY_ID"], {})
@Index("idx_node_template_id", ["node_template_id"], {})
@Index("community_id_idx", ["COMMUNITY_ID"], {})
@Entity(DaoUtil.parseDbName('bgw.project'))
export class ProjectEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "PROJECT_ID" })
  PROJECT_ID: string;

  @Column("varchar", { name: "PROJECT_NAME", nullable: true, length: 255 })
  PROJECT_NAME: string | null;

  @Column("float", {
    name: "DECORATION_BUDGET",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  DECORATION_BUDGET: number | null;

  @Column("enum", {
    name: "CONTRACT",
    nullable: true,
    enum: ["1", "2", "3", "4", "5", "6", "7", "8", "9"],
  })
  CONTRACT: "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | null;

  @Column("enum", {
    name: "DECORATION_STYLE",
    nullable: true,
    enum: [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "20",
      "21",
      "22",
      "23",
      "24",
      "25",
      "26",
      "27",
      "28",
      "29",
      "30",
    ],
  })
  DECORATION_STYLE:
    | "1"
    | "2"
    | "3"
    | "4"
    | "5"
    | "6"
    | "7"
    | "8"
    | "9"
    | "10"
    | "11"
    | "12"
    | "13"
    | "14"
    | "15"
    | "16"
    | "17"
    | "18"
    | "19"
    | "20"
    | "21"
    | "22"
    | "23"
    | "24"
    | "25"
    | "26"
    | "27"
    | "28"
    | "29"
    | "30"
    | null;

  @Column("bigint", { name: "PERSON_ID", nullable: true })
  PERSON_ID: string | null;

  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  COMPANY_ID: string | null;

  @Column("varchar", { name: "PROJECT_IMG", nullable: true, length: 30 })
  PROJECT_IMG: string | null;

  @Column("datetime", { name: "EVENT_UPDATE_TIME", nullable: true })
  EVENT_UPDATE_TIME: Date | null;

  @Column("enum", { name: "PROJECT_STATUS", nullable: true, enum: ["Y", "N"] })
  PROJECT_STATUS: "Y" | "N" | null;

  @Column("varchar", { name: "CURRENT_STAGE", nullable: true, length: 10 })
  CURRENT_STAGE: string | null;

  @Column("bigint", { name: "IMG_NUM_COUNT", nullable: true })
  IMG_NUM_COUNT: string | null;

  @Column("enum", { name: "ISDISPLAY", nullable: true, enum: ["1", "2"] })
  ISDISPLAY: "1" | "2" | null;

  @Column("bigint", { name: "PAGE_VIEW_COUNT", nullable: true })
  PAGE_VIEW_COUNT: string | null;

  @Column("enum", { name: "IS_SHOW_LIVE", nullable: true, enum: ["Y", "N"] })
  IS_SHOW_LIVE: "Y" | "N" | null;

  @Column("bigint", { name: "VR_NUM", nullable: true })
  VR_NUM: string | null;

  @Column("enum", {
    name: "IS_TEMPLATE_PROJECT",
    nullable: true,
    enum: ["Y", "N"],
  })
  IS_TEMPLATE_PROJECT: "Y" | "N" | null;

  @Column("bigint", { name: "STAGE_TEMPLATE_ID", nullable: true })
  STAGE_TEMPLATE_ID: string | null;

  @Column("bigint", { name: "BUSINESS_STAGE_ID", nullable: true })
  BUSINESS_STAGE_ID: string | null;

  @Column("varchar", { name: "PROJECT_NUMBER", nullable: true, length: 40 })
  PROJECT_NUMBER: string | null;

  @Column("varchar", { name: "Customer_Name", nullable: true, length: 50 })
  Customer_Name: string | null;

  @Column("varchar", { name: "Customer_tel", nullable: true, length: 20 })
  Customer_tel: string | null;

  @Column("varchar", {
    name: "is_top",
    nullable: true,
    length: 20,
    default: () => "'N'",
  })
  is_top: string | null;

  @Column("datetime", { name: "set_top_time", nullable: true })
  set_top_time: Date | null;

  @Column("bigint", { name: "video_num", nullable: true })
  video_num: string | null;

  @Column("bigint", { name: "ref_project_id", nullable: true })
  ref_project_id: string | null;

  @Column("varchar", { name: "camera_watch_flag", nullable: true, length: 1 })
  camera_watch_flag: string | null;

  @Column("varchar", {
    name: "is_gif_show",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  is_gif_show: string | null;

  @Column("bigint", { name: "node_template_id", nullable: true })
  node_template_id: string | null;

  @Column("datetime", { name: "project_start_time", nullable: true })
  project_start_time: Date | null;

  @Column("enum", {
    name: "DOOR_MODEL",
    nullable: true,
    enum: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
  })
  DOOR_MODEL: "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9" | "10" | null;

  @Column("float", {
    name: "DECORATION_AREA",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  DECORATION_AREA: number | null;

  @Column("varchar", { name: "ROOM", nullable: true, length: 10 })
  ROOM: string | null;

  @Column("varchar", { name: "HALL", nullable: true, length: 10 })
  HALL: string | null;

  @Column("varchar", { name: "BATHROOM", nullable: true, length: 10 })
  BATHROOM: string | null;

  @Column("varchar", { name: "COOKROOM", nullable: true, length: 10 })
  COOKROOM: string | null;

  @Column("varchar", { name: "HOUSE_NUMBER", nullable: true, length: 60 })
  HOUSE_NUMBER: string | null;

  @Column("bigint", { name: "COMMUNITY_ID", nullable: true })
  COMMUNITY_ID: string | null;

  @Column("datetime", { name: "node_template_assign_time", nullable: true })
  node_template_assign_time: Date | null;

  @Column("varchar", {
    name: "skip_holiday",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  skip_holiday: string | null;

  // @Column("bigint", { name: "current_node_id", nullable: true })
  // current_node_id: string | null;

  @Column("bigint", { name: "last_project_patrol_id", nullable: true })
  last_project_patrol_id: string | null;

  @Column("bigint", {
    name: "project_director",
    nullable: true,
    comment: "工地负责人",
  })
  project_director: string | null;

  @Column("bigint", {
    name: "project_manager",
    nullable: true,
    comment: "工地负责人managerId",
  })
  project_manager: string | null;

  @Column("bigint", {
    name: "department_id",
    nullable: true,
    comment: "所属部门",
  })
  department_id: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
