import { Dao<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('bgw.project_patrol'))
export class ProjectPatrolEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "project_patrol_id" })
  projectPatrolId: string;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("datetime", { name: "patrol_time", nullable: true })
  patrolTime: Date | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
