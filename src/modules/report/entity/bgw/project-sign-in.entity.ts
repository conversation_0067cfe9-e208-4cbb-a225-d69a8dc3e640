import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("idx_project_id", ["projectId"], {})
@Entity(DaoUtil.parseDbName('bgw.project_sign_in'))
export class ProjectSignInEntity {
  @PrimaryGeneratedColumn({
    type: "bigint",
    name: "PROJECT_SIGN_IN_ID",
    comment: "编号自增",
  })
  projectSignInId: string;

  @Column("bigint", { name: "PROJECT_ID", nullable: true, comment: "工地id" })
  projectId: string | null;

  @Column("bigint", { name: "MANAGER_ID", nullable: true, comment: "管理员id" })
  managerId: string | null;

  @Column("datetime", {
    name: "SIGN_IN_TIME",
    nullable: true,
    comment: "签到时间",
    default: () => "CURRENT_TIMESTAMP",
  })
  signInTime: Date | null;

  @Column("varchar", { name: "LATITUDE", nullable: true, length: 50 })
  latitude: string | null;

  @Column("varchar", { name: "LONGITUDE", nullable: true, length: 50 })
  longitude: string | null;

  @Column("varchar", { name: "CURRENT_ADDRESS", nullable: true, length: 250 })
  currentAddress: string | null;

  @Column("varchar", { name: "sign_in_oss_url", nullable: true, length: 150 })
  signInOssUrl: string | null;

  @Column("tinyint", { name: "type", nullable: true, width: 1 })
  type: boolean | null;

  @Column("int", { name: "confidence", nullable: true })
  confidence: number | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    comment: "删除标记",
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;
}
