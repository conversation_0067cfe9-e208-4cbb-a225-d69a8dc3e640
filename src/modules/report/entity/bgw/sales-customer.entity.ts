import { <PERSON><PERSON><PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "sales_customer_companyId_personId",
  ["companyId", "salesPersonId", "deleteFlag"],
  {}
)
@Index("customerPersonId", ["customerPersonId"], {})
@Entity(DaoUtil.parseDbName("bgw.sales_customer"))
export class SalesCustomerEntity {
  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "SALES_PERSON_ID", nullable: true })
  salesPersonId: string | null;

  @Column("bigint", { name: "CUSTOMER_PERSON_ID", nullable: true })
  customerPersonId: string | null;

  @PrimaryGeneratedColumn({ type: "bigint", name: "SALES_CUSTOMER_ID" })
  salesCustomerId: string;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "is_fan", nullable: true, length: 1 })
  isFan: string | null;
}
