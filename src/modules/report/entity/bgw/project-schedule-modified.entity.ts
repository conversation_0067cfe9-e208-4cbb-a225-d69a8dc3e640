import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("idx_project", ["projectId"], {})
@Entity(DaoUtil.parseDbName('bgw.project_schedule_modified'))
export class ProjectScheduleModifiedEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "project_node_id", nullable: true })
  projectNodeId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("varchar", {
    name: "type",
    nullable: true,
    comment: "delay -延期  ahead -提前",
    length: 10,
  })
  type: string | null;

  @Column("bigint", { name: "day", nullable: true, comment: "延期/提前天数" })
  day: string | null;

  @Column("varchar", {
    name: "description",
    nullable: true,
    comment: "申请原因",
    length: 256,
  })
  description: string | null;

  @Column("varchar", {
    name: "photo_list",
    nullable: true,
    comment: "凭证图片列表",
    length: 500,
  })
  photoList: string | null;

  @Column("bigint", {
    name: "modified_manager_id",
    nullable: true,
    comment: "申请人",
  })
  modifiedManagerId: string | null;

  @Column("datetime", {
    name: "modified_time",
    nullable: true,
    comment: "申请时间",
  })
  modifiedTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
