import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("company_person", ["COMPANY_ID", "PERSON_ID"], {})
@Index("person", ["PERSON_ID"], {})
@Entity(DaoUtil.parseDbName('bgw.manager'))
export class ManagerEntity {
  @Column("bigint", { name: "PERSON_ID", nullable: true })
  PERSON_ID: string | null;

  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  COMPANY_ID: string | null;

  @PrimaryGeneratedColumn({ type: "bigint", name: "<PERSON>NAGER_ID" })
  MANAGER_ID: string;

  @Column("enum", {
    name: "MANAGER_TYPE",
    nullable: true,
    comment:
      "1-公司管理员3-公司成员4-商户管理员5-公司外部成员&&工地更新临时管理员",
    enum: ["1", "3", "4", "5"],
  })
  MANAGER_TYPE: "1" | "3" | "4" | "5" | null;

  @Column("varchar", { name: "IS_SHOW", nullable: true, length: 10 })
  IS_SHOW: string | null;

  @Column("bigint", { name: "ROLE_ID", nullable: true })
  ROLE_ID: string | null;

  @Column("bigint", { name: "PAGE_VIEW_COUNT", nullable: true })
  PAGE_VIEW_COUNT: string | null;

  @Column("enum", { name: "RECEIVE_MESSAGE", nullable: true, enum: ["Y", "N"] })
  RECEIVE_MESSAGE: "Y" | "N" | null;

  @Column("enum", { name: "GROUP_CHAT", nullable: true, enum: ["Y", "N"] })
  GROUP_CHAT: "Y" | "N" | null;

  @Column("varchar", { name: "receive_orders", nullable: true, length: 10 })
  receive_orders: string | null;

  @Column("datetime", { name: "last_updateTime", nullable: true })
  last_updateTime: Date | null;

  @Column("varchar", { name: "order_admin", nullable: true, length: 1 })
  order_admin: string | null;

  @Column("varchar", { name: "content_admin", nullable: true, length: 1 })
  content_admin: string | null;

  @Column("varchar", { name: "nick_name", nullable: true, length: 50 })
  nick_name: string | null;

  @Column("bigint", { name: "profile_photo", nullable: true })
  profile_photo: string | null;

  @Column("varchar", { name: "person_resume", nullable: true, length: 1024 })
  person_resume: string | null;

  @Column("varchar", {
    name: "baidu_admin",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  baidu_admin: string | null;

  @Column("varchar", { name: "device_admin", nullable: true, length: 1 })
  device_admin: string | null;

  @Column("varchar", {
    name: "login_flag",
    nullable: true,
    length: 10,
    default: () => "'Y'",
  })
  login_flag: string | null;

  @Column("bigint", {
    name: "TOTAL_PAGE_VIEW",
    nullable: true,
    default: () => "'0'",
  })
  TOTAL_PAGE_VIEW: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
