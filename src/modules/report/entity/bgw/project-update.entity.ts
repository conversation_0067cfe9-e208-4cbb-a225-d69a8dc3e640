import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("idx_project_id_node_id", ["projectId", "nodeId"], {})
@Index("idx_type", ["projectUpdateType"], {})
@Index("source_id_idx", ["projectUpdateSourceId"], {})
@Index(
  "idx_projectid_projectupdatetype_DELETEFLAG_projectupdatetaglist",
  ["projectId", "projectUpdateType", "projectUpdateTagList"],
  {}
)
@Index(
  "idx_delete_type_display_project",
  ["projectId", "projectUpdateType", "isDisplay"],
  {}
)
@Index("company_create_time_idx", ["companyId", "createTime"], {})
@Index("department_create_time_idx", ["departmentId", "createTime"], {})
@Entity(DaoUtil.parseDbName('bgw.project_update'))
export class ProjectUpdateEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "project_update_id" })
  projectUpdateId: string;

  @Column("varchar", {
    name: "project_update_name",
    nullable: true,
    length: 50,
  })
  projectUpdateName: string | null;

  @Column("varchar", {
    name: "project_update_type",
    nullable: true,
    length: 50,
  })
  projectUpdateType: string | null;

  @Column("enum", {
    name: "is_display",
    nullable: true,
    comment: "1-公开  3-隐藏",
    enum: ["1", "2", "3"],
  })
  isDisplay: "1" | "2" | "3" | null;

  @Column("enum", { name: "top", nullable: true, enum: ["Y", "N"] })
  top: "Y" | "N" | null;

  @Column("datetime", { name: "top_time", nullable: true })
  topTime: Date | null;

  @Column("datetime", { name: "project_update_time", nullable: true })
  projectUpdateTime: Date | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("bigint", { name: "person_id", nullable: true })
  personId: string | null;

  @Column("bigint", { name: "ref_project_update_id", nullable: true })
  refProjectUpdateId: string | null;

  @Column("bigint", { name: "create_person_id", nullable: true })
  createPersonId: string | null;

  @Column("varchar", {
    name: "project_update_text",
    nullable: true,
    length: 1000,
  })
  projectUpdateText: string | null;

  @Column("varchar", {
    name: "project_update_media_list",
    nullable: true,
    length: 3000,
  })
  projectUpdateMediaList: string | null;

  @Column("varchar", {
    name: "project_update_tag_list",
    nullable: true,
    length: 200,
  })
  projectUpdateTagList: string | null;

  @Column("bigint", { name: "node_id", nullable: true })
  nodeId: string | null;

  @Column("bigint", { name: "stage_template_id", nullable: true })
  stageTemplateId: string | null;

  @Column("bigint", { name: "business_stage_id", nullable: true })
  businessStageId: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 5,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("bigint", { name: "project_node_id", nullable: true })
  projectNodeId: string | null;

  @Column("varchar", {
    name: "project_update_source",
    nullable: true,
    comment: "更新来源 1-摄像头 2-成员 3-业主 4-系统 5-外部成员",
    length: 2,
  })
  projectUpdateSource: string | null;

  @Column("bigint", {
    name: "project_update_source_id",
    nullable: true,
    comment: "更新来源id 1-摄像头deviceId 2-成员managerId 3-业主personId",
  })
  projectUpdateSourceId: string | null;

  @Column("bigint", { name: "company_id", nullable: true, comment: "公司id" })
  companyId: string | null;

  @Column("bigint", {
    name: "department_id",
    nullable: true,
    comment: "部门id",
  })
  departmentId: string | null;

  @Column("varchar", {
    name: "special_text",
    nullable: true,
    comment: "特殊文本处理保存",
    length: 1000,
  })
  specialText: string | null;
}
