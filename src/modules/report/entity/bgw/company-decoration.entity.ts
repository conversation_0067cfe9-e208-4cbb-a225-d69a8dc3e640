import { Dao<PERSON>til } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("company_id", ["companyId"], {})
@Index("delete_flag", ["deleteFlag"], {})
@Entity(DaoUtil.parseDbName('bgw.company_decoration'))
export class CompanyDecorationEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "COMPANY_DECORATION_ID" })
  companyDecorationId: string;

  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "PROJECT_POWER_NUM", nullable: true })
  projectPowerNum: string | null;

  @Column("varchar", { name: "SALES", nullable: true, length: 50 })
  sales: string | null;

  @Column("varchar", { name: "OPERATORS", nullable: true, length: 20 })
  operators: string | null;

  @Column("varchar", {
    name: "GENERAL_MINI_QRCODE",
    nullable: true,
    length: 128,
  })
  generalMiniQrcode: string | null;

  @Column("varchar", {
    name: "EXCLUSIVE_MINI_QRCODE",
    nullable: true,
    length: 10,
  })
  exclusiveMiniQrcode: string | null;

  @Column("varchar", { name: "SPREAD_WORD", nullable: true, length: 20 })
  spreadWord: string | null;

  @Column("enum", { name: "IS_SHOW_SUPPORT", nullable: true, enum: ["Y", "N"] })
  isShowSupport: "Y" | "N" | null;

  @Column("bigint", { name: "STYLE_ID", nullable: true })
  styleId: string | null;

  @Column("bigint", { name: "case_max_num", nullable: true })
  caseMaxNum: string | null;

  @Column("bigint", { name: "Promotion_max_num", nullable: true })
  promotionMaxNum: string | null;

  @Column("bigint", { name: "product_max_num", nullable: true })
  productMaxNum: string | null;

  @Column("bigint", { name: "member_max_num", nullable: true })
  memberMaxNum: string | null;

  @Column("bigint", {
    name: "ARTICLE_MAX_NUM",
    nullable: true,
    default: () => "'2'",
  })
  articleMaxNum: string | null;

  @Column("int", {
    name: "video_max_num",
    nullable: true,
    default: () => "'5'",
  })
  videoMaxNum: number | null;

  @Column("varchar", {
    name: "leads_management",
    nullable: true,
    comment: "1-未开启,2-开启",
    length: 1,
  })
  leadsManagement: string | null;

  @Column("bigint", { name: "vr_case_owner_num", nullable: true })
  vrCaseOwnerNum: string | null;

  @Column("varchar", { name: "vr_case_owner_img", nullable: true, length: 250 })
  vrCaseOwnerImg: string | null;

  @Column("varchar", { name: "division_company_list", nullable: true, length: 2048 })
  divisionCompanyList: string | null;

  @Column("text", { name: "deliver_kanban_set", nullable: true, comment: "集团数据报表看板配置" })
  deliverKanbanSet: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
