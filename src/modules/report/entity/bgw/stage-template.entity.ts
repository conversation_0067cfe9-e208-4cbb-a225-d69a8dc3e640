import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('bgw.stage_template'))
export class StageTemplateEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "STAGE_TEMPLATE_ID" })
  STAGE_TEMPLATE_ID: string;

  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  COMPANY_ID: string | null;

  @Column("varchar", { name: "TEMPLATE_NAME", nullable: true, length: 10 })
  TEMPLATE_NAME: string | null;

  @Column("enum", { name: "IS_DEFAULT", nullable: true, enum: ["Y", "N"] })
  IS_DEFAULT: "Y" | "N" | null;

  @Column("bigint", { name: "ref_stage_template_id", nullable: true })
  ref_stage_template_id: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
