import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.acceptance_report_criteria'))
export class AcceptanceReportCriteriaEntity {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
    id: string;

    @Column("bigint", { name: "acceptance_report_id", comment: "验收报告id" })
    acceptanceReportId: string;

    @Column("varchar", {
        name: "criteria_name",
        comment: "验收检查名称/验收检查模板名",
        length: 255,
    })
    criteriaName: string;

    @Column("tinyint", {
        name: "criteria_type",
        comment: "验收检查类型: 1-通用验收标准, 2-节点验收标准",
        width: 1,
    })
    criteriaType: number;

    @Column("int", { name: "sort", comment: "排序", default: () => "'0'" })
    sort: number;

    @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
    deleteFlag: string;

    @Column("datetime", {
        name: "createTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    createTime: Date | null;

    @Column("varchar", { name: "createBy", nullable: true, length: 30 })
    createBy: string | null;

    @Column("datetime", {
        name: "updateTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    updateTime: Date | null;

    @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
    updateBy: string | null;
}
