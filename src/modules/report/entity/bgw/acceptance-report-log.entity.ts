import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.acceptance_report_log'))
export class AcceptanceReportLogEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "acceptance_report_id", comment: "验收报告id" })
  acceptanceReportId: string;

  @Column("bigint", {
    name: "operator_manager_id",
    nullable: true,
    comment: "操作人managerId",
  })
  operatorManagerId: string | null;

  @Column("tinyint", {
    name: "operate_type",
    comment:
      "操作类型: 1-创建验收报告, 2-验收报告编辑, 3-验收报告删除, 4-更改状态, 5-业主验收结果(业主验收id), 6-验收报告恢复, 7-邀请业主验收",
    width: 1,
  })
  operateType: number;

  @Column("bigint", {
    name: "owner_acceptance_id",
    nullable: true,
    comment: "业主验收id",
  })
  ownerAcceptanceId: string | null;

  @Column("tinyint", {
    name: "from_status",
    nullable: true,
    comment: "报告之前状态",
    width: 1,
  })
  fromStatus: number | null;

  @Column("tinyint", {
    name: "to_status",
    nullable: true,
    comment: "报告之后状态",
    width: 1,
  })
  toStatus: number | null;

  @Column("datetime", {
    name: "operate_time",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
    comment: "操作时间",
  })
  operateTime: Date | null;

  @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
  deleteFlag: string;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("bigint", {
    name: "acceptance_approval_flow_id",
    nullable: true,
    comment: "当前审核流id",
  })
  acceptanceApprovalFlowId: string | null;

  @Column("bigint", {
    name: "acceptance_approval_record_id",
    nullable: true,
    comment: "当前审核操作id",
  })
  acceptanceApprovalRecordId: string | null;

}
