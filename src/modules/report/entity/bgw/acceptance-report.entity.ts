import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { Dao<PERSON>til } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.acceptance_report'))
export class AcceptanceReportEntity {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
    id: string;

    @Column("bigint", { name: "company_id", comment: "公司id" })
    companyId: string;

    @Column("bigint", { name: "project_id", comment: "项目id" })
    projectId: string;

    @Column("bigint", { name: "create_manager_id", comment: "创建人managerId" })
    createManagerId: string;

    @Column("varchar", {
        name: "acceptance_report_name",
        comment: "验收标准节点名称",
        length: 255,
    })
    acceptanceReportName: string;

    @Column("bigint", {
        name: "acceptance_criteria_id",
        comment: "验收标准模板id",
    })
    acceptanceCriteriaId: string;

    @Column("tinyint", {
        name: "status",
        comment: "验收报告状态: 1-验收完成, 2-业主拒绝验收, 3-待业主验收",
        width: 1,
    })
    status: number;

    @Column("varchar", {
        name: "owner_name",
        nullable: true,
        comment: "业主姓名",
        length: 255,
    })
    ownerName: string | null;

    @Column("varchar", {
        name: "address",
        comment: "项目地址",
        length: 255,
    })
    address: string;

    @Column("varchar", {
        name: "decorator",
        comment: "施工单位: 装企",
        length: 255,
    })
    decorator: string;

    @Column("varchar", {
        name: "supervisor",
        comment: "验收单位: 第三方监理或者装企质检部门",
        length: 255,
    })
    supervisor: string;

    @Column("varchar", {
        name: "inspectors",
        comment: "验收人员: 关联岗位ids",
        length: 255,
    })
    inspectors: string;

    @Column("datetime", { name: "acceptance_date", comment: "验收日期" })
    acceptanceDate: Date;

    @Column("varchar", {
        name: "owner_selfie_flag",
        nullable: true,
        comment: "业主是否自拍",
        length: 1,
        default: () => "'N'",
    })
    ownerSelfieFlag: string | null;

    @Column("bigint", {
        name: "owner_acceptance_id",
        nullable: true,
        comment: "关联业主自拍签字表主键id: 即最后一个业主验收id",
    })
    ownerAcceptanceId: string | null;

    @Column("bigint", {
        name: "approval_flow_id",
        nullable: true,
        comment: "当前审核流id",
    })
    approvalFlowId: string | null;

    @Column("bigint", {
        name: "owner_signature_flag",
        nullable: true,
        comment: "是否需要业主签名",
    })
    ownerSignatureFlag: string | null;

    @Column("bigint", {
        name: "is_owner_acceptance_required",
        nullable: true,
        comment: "是否需要业主验收",
    })
    isOwnerAcceptanceRequired: string | null;

    @Column("varchar", {
        name: "approval_status",
        nullable: true,
        comment: "审核状态",
    })
    approvalStatus: string | null;

    @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
    deleteFlag: string;

    @Column("datetime", {
        name: "createTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    createTime: Date | null;

    @Column("varchar", { name: "createBy", nullable: true, length: 30 })
    createBy: string | null;

    @Column("datetime", {
        name: "updateTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    updateTime: Date | null;

    @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
    updateBy: string | null;

    @Column("varchar", {
        name: "is_display",
        length: 30,
    })
    isDisplay: string;

    @Column("bigint", { name: "page_view_count", comment: "浏览量" })
    pageViewCount: string;
}
