import { <PERSON><PERSON><PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "idx_STAGETEMPLATEID_BUSINESSSTAGEID_DELETEFLAG_STAGENAME",
  ["STAGE_TEMPLATE_ID", "BUSINESS_STAGE_ID", "DELETE_FLAG", "STAGE_NAME"],
  {}
)
@Index(
  "idx_STAGETEMPLATEID_DELETEFLAG_STAGENAME_BUSINESSSTAGEID_SORT",
  [
    "STAGE_TEMPLATE_ID",
    "DELETE_FLAG",
    "STAGE_NAME",
    "BUSINESS_STAGE_ID",
    "SORT",
  ],
  {}
)
@Entity(DaoUtil.parseDbName('bgw.stage'))
export class StageEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "STAGE_ID" })
  STAGE_ID: string;

  @Column("varchar", { name: "STAGE_NAME", nullable: true, length: 50 })
  STAGE_NAME: string | null;

  @Column("bigint", { name: "SORT", nullable: true })
  SORT: string | null;

  @Column("bigint", { name: "STAGE_TEMPLATE_ID", nullable: true })
  STAGE_TEMPLATE_ID: string | null;

  @Column("bigint", { name: "BUSINESS_STAGE_ID", nullable: true })
  BUSINESS_STAGE_ID: string | null;

  @Column("bigint", { name: "ref_stage_id", nullable: true })
  ref_stage_id: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
