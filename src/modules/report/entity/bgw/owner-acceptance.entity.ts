import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.owner_acceptance'))
export class OwnerAcceptanceEntity {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
    id: string;

    @Column("bigint", { name: "acceptance_report_id", comment: "验收报告id" })
    acceptanceReportId: string;

    @Column("varchar", {
        name: "owner_name",
        nullable: true,
        comment: "业主姓名",
        length: 255,
    })
    ownerName: string | null;

    @Column("tinyint", {
        name: "acceptance_result",
        comment: "验收结果 1-同意, 0-拒绝",
        width: 1,
    })
    acceptanceResult: number;

    @Column("bigint", {
        name: "electronic_signature",
        nullable: true,
        comment: "电子签名图片id",
    })
    electronicSignature: string | null;

    @Column("bigint", { name: "selfie", nullable: true, comment: "自拍图片id" })
    selfie: string | null;

    @Column("text", { name: "refuse_text", nullable: true, comment: "拒绝原因" })
    refuseText: string | null;

    @Column("bigint", { name: "person_id", comment: "验收personId" })
    personId: string;

    @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
    deleteFlag: string;

    @Column("datetime", {
        name: "createTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    createTime: Date | null;

    @Column("varchar", { name: "createBy", nullable: true, length: 30 })
    createBy: string | null;

    @Column("datetime", {
        name: "updateTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    updateTime: Date | null;

    @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
    updateBy: string | null;
}
