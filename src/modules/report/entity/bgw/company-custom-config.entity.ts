import { Dao<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('bgw.company_custom_config'))
export class CompanyCustomConfigEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("varchar", {
    name: "custom_type",
    nullable: true,
    comment:
      "定制类型 project_address:工地地址展示,ai_live:ai直播大屏,project_completed:工地完工",
    length: 50,
  })
  customType: string | null;

  @Column("bigint", { 
    name: "company_id",
    nullable: true 
  })
  companyId: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
