import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.acceptance_approval_record'))
export class AcceptanceApprovalRecordEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "acceptance_approval_level_id" })
  acceptanceApprovalLevelId: string;

  @Column("bigint", { name: "approval_manager", comment: "审核人" })
  approvalManager: string;

  @Column("datetime", {
    name: "approval_time",
    nullable: true,
    comment: "审核时间",
  })
  approvalTime: Date | null;

  @Column("varchar", {
    name: "approval_result",
    comment: "审核结果 同意/拒绝",
    length: 30,
  })
  approvalResult: string;

  @Column("varchar", {
    name: "text",
    nullable: true,
    comment: "审核文本",
    length: 100,
  })
  text: string | null;

  @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
  deleteFlag: string;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
