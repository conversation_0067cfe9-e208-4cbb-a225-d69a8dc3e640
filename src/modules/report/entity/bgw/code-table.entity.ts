import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

/**
 * @summary 字典表
 * <AUTHOR>
 * @class CodeTableEntity
 */
@Entity(DaoUtil.parseDbName('bgw.code_table'))
export class CodeTableEntity {
    @PrimaryGeneratedColumn({ name: "CODE_TABLE_ID", type: "bigint" })
    codeTableId: string;

    @Column({ name: "PARAM_TYPE", type: "varchar", length: 50 })
    paramType: string;

    @Column({ name: "TYPE_NAME", type: "varchar", length: 50 })
    typeName: string;

    @Column({ name: "PARAM_CODE", type: "varchar", length: 50 })
    paramCode: string;

    @Column({ name: "PARAM_VALUE", type: "varchar", length: 50 })
    paramValue: string;

    @Column({ name: "DESCRIPTION", type: "varchar", length: 50 })
    description: string;

    @Column({ name: "DELETE_FLAG", type: "varchar", length: 30, default: "N" })
    deleteFlag: string;

    @Column({ name: "createBy", type: "varchar", length: 30 })
    createBy: string;

    @Column({ name: "createTime", type: "datetime" })
    createTime: Date;

    @Column({ name: "updateBy", type: "varchar", length: 30 })
    updateBy: string;

    @Column({ name: "updateTime", type: "datetime" })
    updateTime: Date;
}
