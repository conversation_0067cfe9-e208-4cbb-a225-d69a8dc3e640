import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Entity(DaoUtil.parseDbName('bgw.problem_solved'))
export class ProblemSolvedEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "project_problem_id", nullable: true })
  projectProblemId: string | null;

  @Column("bigint", { name: "person", nullable: true, comment: "整改人" })
  person: string | null;

  @Column("datetime", { name: "time", nullable: true, comment: "整改时间" })
  time: Date | null;

  @Column("varchar", {
    name: "description",
    nullable: true,
    comment: "整改描述",
    length: 128,
  })
  description: string | null;

  @Column("varchar", {
    name: "image",
    nullable: true,
    comment: "整改图片",
    length: 500,
  })
  image: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("varchar", {
    name: "capture_img",
    nullable: true,
    comment: "抓拍图片",
    length: 512,
  })
  captureImg: string | null;
}
