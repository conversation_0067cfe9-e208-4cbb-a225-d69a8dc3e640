import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("project_id_node_id_index", ["projectNodeId"], {})
@Entity(DaoUtil.parseDbName('bgw.node_overdue_event'))
export class NodeOverdueEventEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "project_node_id", nullable: true })
  projectNodeId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;  

  @Column("date", { name: "from_date", nullable: true })
  fromDate: string | null;

  @Column("date", { name: "to_date", nullable: true })
  toDate: string | null;

  @Column("int", { name: "duration_in_day", nullable: true })
  durationInDay: number | null;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("varchar", { name: "DELETE_FLAG", nullable: true, length: 1 })
  deleteFlag: string | null;
}
