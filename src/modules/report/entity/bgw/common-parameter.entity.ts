import { <PERSON><PERSON><PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("PARAMETER_CODE", ["parameterCode"], {})
@Index("PARAMETER_TYPE", ["parameterType"], {})
@Entity(DaoUtil.parseDbName('bgw.common_parameter'))
export class CommonParameterEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "COMMON_PARAMETER_ID" })
  commonParameterId: string;

  @Column("varchar", { name: "PARAMETER_TYPE", length: 255 })
  parameterType: string;

  @Column("varchar", { name: "PARAMETER_CODE", length: 50 })
  parameterCode: string;

  @Column("varchar", { name: "PARAMETER_NAME", nullable: true, length: 1024 })
  parameterName: string | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 10,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
