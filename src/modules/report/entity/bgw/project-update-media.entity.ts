import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_project_update_id", ["projectUpdateId"], {})
@Entity(DaoUtil.parseDbName('bgw.project_update_media'))
export class ProjectUpdateMediaEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "project_update_id", default: () => "'0'" })
  projectUpdateId: string;

  @Column("bigint", { name: "photo_id", default: () => "'0'" })
  photoId: string;

  @Column("char", {
    name: "media_type",
    comment: "资源类型",
    length: 1,
    default: () => "'0'",
  })
  mediaType: string;

  @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
  deleteFlag: string;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
