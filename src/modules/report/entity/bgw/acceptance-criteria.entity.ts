import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.acceptance_criteria'))
export class AcceptanceCriteriaEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "company_id", comment: "公司id" })
  companyId: string;

  @Column("tinyint", {
    name: "type",
    comment: "验收标准模板类型: 1-通用验收标准、2-节点验收标准",
    width: 1,
  })
  type: number;

  @Column("varchar", { name: "name", comment: "验收标准模板名称", length: 255 })
  name: string;

  @Column("int", { name: "sort", comment: "排序", default: () => "'0'" })
  sort: number;

  @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
  deleteFlag: string;

  @Column("varchar", { name: "is_owner_acceptance_required", length: 1})
  isOwnerAcceptanceRequired: string;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
