import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_name_mobile", ["NICK_NAME", "PERSON_MOBILE"], {})
@Entity(DaoUtil.parseDbName('bgw.person'))
export class PersonEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "PERSON_ID" })
  PERSON_ID: string;

  @Column("varchar", { name: "NICK_NAME", nullable: true, length: 32 })
  NICK_NAME: string | null;

  @Column("varchar", { name: "IMGURL", nullable: true, length: 500 })
  IMGURL: string | null;

  @Column("varchar", { name: "PERSON_NAME", nullable: true, length: 50 })
  PERSON_NAME: string | null;

  @Column("enum", { name: "WX_SEX", nullable: true, enum: ["1", "2"] })
  WX_SEX: "1" | "2" | null;

  @Column("varchar", { name: "WX_LANGUAGE", nullable: true, length: 30 })
  WX_LANGUAGE: string | null;

  @Column("varchar", { name: "WX_CITY", nullable: true, length: 30 })
  WX_CITY: string | null;

  @Column("varchar", { name: "WX_PROVINCE", nullable: true, length: 30 })
  WX_PROVINCE: string | null;

  @Column("varchar", { name: "WX_COUNTRY", nullable: true, length: 30 })
  WX_COUNTRY: string | null;

  @Column("varchar", { name: "WX_HEAD_IMGURL", nullable: true, length: 500 })
  WX_HEAD_IMGURL: string | null;

  @Column("varchar", { name: "WX_NICK_NAME", nullable: true, length: 30 })
  WX_NICK_NAME: string | null;

  @Column("bigint", { name: "CUR_PROJECT", nullable: true })
  CUR_PROJECT: string | null;

  @Column("varchar", { name: "COMPANY", nullable: true, length: 50 })
  COMPANY: string | null;

  @Column("varchar", { name: "CUR_LATITUDE", nullable: true, length: 100 })
  CUR_LATITUDE: string | null;

  @Column("varchar", { name: "CUR_LONGITUDE", nullable: true, length: 100 })
  CUR_LONGITUDE: string | null;

  @Column("varchar", { name: "MOBILE", nullable: true, length: 30 })
  MOBILE: string | null;

  @Column("varchar", { name: "PERSON_MOBILE", nullable: true, length: 20 })
  PERSON_MOBILE: string | null;

  @Column("varchar", { name: "PRODUCT_SOURCE", nullable: true, length: 20 })
  PRODUCT_SOURCE: string | null;

  @Column("varchar", { name: "PERSON_RESUME", nullable: true, length: 1024 })
  PERSON_RESUME: string | null;

  @Column("varchar", { name: "CHANNEL_SOURCE", nullable: true, length: 20 })
  CHANNEL_SOURCE: string | null;

  @Column("bigint", { name: "PAGE_VIEW_COUNT", nullable: true })
  PAGE_VIEW_COUNT: string | null;

  @Column("varchar", { name: "OWNER_IM_ACCOUNT", nullable: true, length: 20 })
  OWNER_IM_ACCOUNT: string | null;

  @Column("varchar", { name: "name", nullable: true, length: 100 })
  name: string | null;

  @Column("varchar", { name: "id_card_number", nullable: true, length: 18 })
  id_card_number: string | null;

  @Column("varchar", {
    name: "id_card_front_image",
    nullable: true,
    length: 100,
  })
  id_card_front_image: string | null;

  @Column("varchar", {
    name: "id_card_back_image",
    nullable: true,
    length: 100,
  })
  id_card_back_image: string | null;

  @Column("varchar", { name: "experience_year", nullable: true, length: 10 })
  experience_year: string | null;

  @Column("varchar", {
    name: "PRIVACY_AGREEMENT",
    nullable: true,
    length: 50,
    default: () => "'N'",
  })
  PRIVACY_AGREEMENT: string | null;

  @Column("varchar", { name: "PERSON_LABEL", nullable: true, length: 30 })
  PERSON_LABEL: string | null;

  @Column("varchar", { name: "front_face_photo", nullable: true, length: 150 })
  front_face_photo: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  DELETE_FLAG: string | null;
}
