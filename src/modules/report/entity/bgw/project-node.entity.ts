import { <PERSON><PERSON><PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("project_id_node_id_index", ["projectId", "nodeId"], {})
@Entity(DaoUtil.parseDbName('bgw.project_node'))
export class ProjectNodeEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "project_node_id" })
  projectNodeId: string;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("bigint", { name: "node_id", nullable: true })
  nodeId: string | null;

  @Column("varchar", { name: "is_finish", nullable: true, length: 1 })
  isFinish: string | null;

  @Column("datetime", { name: "deadline_time", nullable: true })
  deadlineTime: Date | null;

  @Column("datetime", { name: "finish_time", nullable: true })
  finishTime: Date | null;

  @Column("bigint", { name: "finish_manager_id", nullable: true })
  finishManagerId: string | null;

  @Column("bigint", { name: "business_stage_id", nullable: true })
  businessStageId: string | null;

  @Column("varchar", { name: "title", nullable: true, length: 20 })
  title: string | null;

  @Column("varchar", {
    name: "company_member_role_id",
    nullable: true,
    length: 512,
  })
  companyMemberRoleId: string | null;

  @Column("varchar", {
    name: "standard_description",
    nullable: true,
    length: 256,
  })
  standardDescription: string | null;

  @Column("bigint", { name: "sort", nullable: true })
  sort: string | null;

  @Column("bigint", { name: "deadline", nullable: true })
  deadline: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("varchar", { name: "DELETE_FLAG", nullable: true, length: 1 })
  deleteFlag: string | null;
}
