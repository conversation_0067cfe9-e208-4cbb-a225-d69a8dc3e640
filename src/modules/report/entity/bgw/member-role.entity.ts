import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("project_member_role_index", ["projectMemberId", "roleId"], {})
@Entity(DaoUtil.parseDbName('bgw.member_role'))
export class MemberRoleEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "MEMBER_ROLE_ID" })
  memberRoleId: string;

  @Column("bigint", { name: "PROJECT_MEMBER_ID", nullable: true })
  projectMemberId: string | null;

  @Column("bigint", { name: "ROLE_ID", nullable: true })
  roleId: string | null;

  @Column("varchar", { name: "MEMBER_ROLE_NAME", nullable: true, length: 50 })
  memberRoleName: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 30,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
