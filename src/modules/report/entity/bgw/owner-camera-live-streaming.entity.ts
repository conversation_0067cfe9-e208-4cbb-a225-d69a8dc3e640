import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("idx_person_id", ["personId"], {})
@Index(
    "idx_company_project_device",
    ["companyId", "projectId", "yqzDeviceId"],
    {}
)
@Entity(DaoUtil.parseDbName('bgw.owner_camera_live_streaming'))
export class OwnerCameraLiveStreamingEntity {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id", comment: "id" })
    id: string;

    @Column("bigint", {
        name: "person_id",
        nullable: true,
        comment: "业主的personid",
    })
    personId: string | null;

    @Column("bigint", { name: "company_id", nullable: true, comment: "公司id" })
    companyId: string | null;

    @Column("bigint", { name: "project_id", nullable: true, comment: "工地id" })
    projectId: string | null;

    @Column("bigint", {
        name: "yqz_device_id",
        nullable: true,
        comment: "设备id",
    })
    yqzDeviceId: string | null;

    @Column("varchar", { name: "device_remark", comment: "设备备注", length: 20 })
    deviceRemark: string;

    @Column("datetime", {
        name: "live_streaming_time",
        nullable: true,
        comment: "观看时间",
    })
    liveStreamingTime: Date | null;

    @Column("varchar", { name: "source", comment: "观看产品线", length: 30 })
    source: string;

    @Column("varchar", {
        name: "delete_flag",
        nullable: true,
        length: 1,
        default: () => "'N'",
    })
    deleteFlag: string | null;

    @Column("datetime", {
        name: "createTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    createTime: Date | null;

    @Column("varchar", { name: "createBy", nullable: true, length: 30 })
    createBy: string | null;

    @Column("datetime", {
        name: "updateTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    updateTime: Date | null;

    @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
    updateBy: string | null;
}
