import { Dao<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("company_id", ["companyId"], {})
@Index("createTime", ["createTime"], {})
@Index("person_id", ["personId"], {})
@Index(
  "idx_del_target_type_create_time_company",
  ["deleteFlag", "targetType", "createTime", "companyId"],
  {}
)
@Entity(DaoUtil.parseDbName("bgw.order_manage"))
export class OrderManageEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "ORDER_MANAGE_ID" })
  orderManageId: string;

  @Column("varchar", { name: "PROVINCE", nullable: true, length: 20 })
  province: string | null;

  @Column("varchar", { name: "CITY", nullable: true, length: 20 })
  city: string | null;

  @Column("varchar", { name: "MOBILE", nullable: true, length: 20 })
  mobile: string | null;

  @Column("float", { name: "AREA", nullable: true, precision: 10, scale: 2 })
  area: number | null;

  @Column("varchar", { name: "NAME", nullable: true, length: 20 })
  name: string | null;

  @Column("bigint", { name: "PERSON_ID", nullable: true })
  personId: string | null;

  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  companyId: string | null;

  @Column("enum", { name: "WX_SEX", nullable: true, enum: ["1", "2"] })
  wxSex: "1" | "2" | null;

  @Column("varchar", { name: "TARGET", nullable: true, length: 20 })
  target: string | null;

  @Column("datetime", { name: "ORDER_TIME", nullable: true })
  orderTime: Date | null;

  @Column("varchar", { name: "HOUSE_TYPE", nullable: true, length: 30 })
  houseType: string | null;

  @Column("varchar", {
    name: "AFTER_SALES_PROBLEM",
    nullable: true,
    length: 40,
  })
  afterSalesProblem: string | null;

  @Column("enum", {
    name: "CUSTOMER_APP",
    nullable: true,
    enum: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "200"],
  })
  customerApp:
    | "1"
    | "2"
    | "3"
    | "4"
    | "5"
    | "6"
    | "7"
    | "8"
    | "9"
    | "200"
    | null;

  @Column("enum", {
    name: "target_type",
    nullable: true,
    enum: [
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
      "10",
      "11",
      "12",
      "13",
      "14",
      "15",
      "16",
      "17",
      "18",
      "19",
      "20",
      "21",
      "22",
      "23",
      "24",
      "25",
      "26",
      "201",
      "101",
      "102",
      "103",
      "104",
      "105",
      "106",
      "107",
      "28",
      "32",
      "30",
      "39",
      "40",
      "111",
    ],
  })
  targetType:
    | "1"
    | "2"
    | "3"
    | "4"
    | "5"
    | "6"
    | "7"
    | "8"
    | "9"
    | "10"
    | "11"
    | "12"
    | "13"
    | "14"
    | "15"
    | "16"
    | "17"
    | "18"
    | "19"
    | "20"
    | "21"
    | "22"
    | "23"
    | "24"
    | "25"
    | "26"
    | "201"
    | "101"
    | "102"
    | "103"
    | "104"
    | "105"
    | "106"
    | "107"
    | "28"
    | "32"
    | "30"
    | "39"
    | "40"
    | "111"
    | null;

  @Column("varchar", { name: "TARGET_NAME", nullable: true, length: 300 })
  targetName: string | null;

  @Column("bigint", { name: "SALES_PERSON_ID", nullable: true })
  salesPersonId: string | null;

  @Column("varchar", { name: "description", nullable: true, length: 100 })
  description: string | null;

  @Column("varchar", { name: "style", nullable: true, length: 100 })
  style: string | null;

  @Column("varchar", { name: "service_name", nullable: true, length: 100 })
  serviceName: string | null;

  @Column("varchar", { name: "entry_point", nullable: true, length: 100 })
  entryPoint: string | null;

  @Column("varchar", { name: "landing_page_id", nullable: true, length: 10 })
  landingPageId: string | null;

  // @Column("varchar", { name: "channel", nullable: true, length: 100 })
  // channel: string | null;

  @Column("bigint", { name: "ACTIVITY_PARTICIPANT_ID", nullable: true })
  activityParticipantId: string | null;

  @Column("varchar", { name: "reference_id", nullable: true, length: 100 })
  referenceId: string | null;

  @Column("varchar", { name: "free_flag", nullable: true, length: 10 })
  freeFlag: string | null;

  @Column("datetime", { name: "call_customer_time", nullable: true })
  callCustomerTime: Date | null;

  @Column("enum", {
    name: "call_customer_status",
    nullable: true,
    enum: ["1", "2", "3", "4", "5", "6", "7"],
  })
  callCustomerStatus: "1" | "2" | "3" | "4" | "5" | "6" | "7" | null;

  @Column("enum", {
    name: "decoration_plan_status",
    nullable: true,
    enum: ["1", "2", "3", "4", "5", "6"],
  })
  decorationPlanStatus: "1" | "2" | "3" | "4" | "5" | "6" | null;

  @Column("decimal", {
    name: "budget",
    nullable: true,
    precision: 10,
    scale: 2,
  })
  budget: string | null;

  @Column("datetime", { name: "contract_sign_time", nullable: true })
  contractSignTime: Date | null;

  @Column("varchar", { name: "leads_status", nullable: true, length: 3 })
  leadsStatus: string | null;

  // @Column("datetime", { name: "submit_time", nullable: true })
  // submitTime: Date | null;

  @Column("varchar", { name: "contract", nullable: true, length: 100 })
  contract: string | null;

  @Column("varchar", {
    name: "leads_status_explain",
    nullable: true,
    length: 30,
  })
  leadsStatusExplain: string | null;

  @Column("bigint", { name: "ref_order_id", nullable: true })
  refOrderId: string | null;

  @Column("bigint", { name: "create_person_id", nullable: true })
  createPersonId: string | null;

  @Column("bigint", { name: "community_id", nullable: true })
  communityId: string | null;

  @Column("varchar", { name: "house_number", nullable: true, length: 30 })
  houseNumber: string | null;

  @Column("varchar", { name: "customer_age", nullable: true, length: 10 })
  customerAge: string | null;

  @Column("varchar", {
    name: "customer_profession",
    nullable: true,
    length: 30,
  })
  customerProfession: string | null;

  @Column("varchar", { name: "elevator", nullable: true, length: 10 })
  elevator: string | null;

  @Column("datetime", { name: "measure_date", nullable: true })
  measureDate: Date | null;

  @Column("text", { name: "designer_evaluate", nullable: true })
  designerEvaluate: string | null;

  @Column("varchar", { name: "garbage_removal", nullable: true, length: 10 })
  garbageRemoval: string | null;

  @Column("varchar", { name: "house_structure", nullable: true, length: 10 })
  houseStructure: string | null;

  @Column("varchar", { name: "door_model", nullable: true, length: 10 })
  doorModel: string | null;

  @Column("varchar", { name: "living_number", nullable: true, length: 10 })
  livingNumber: string | null;

  @Column("varchar", { name: "feng_shui", nullable: true, length: 10 })
  fengShui: string | null;

  @Column("varchar", { name: "design_time", nullable: true, length: 30 })
  designTime: string | null;

  @Column("varchar", { name: "plan_start_time", nullable: true, length: 30 })
  planStartTime: string | null;

  @Column("bigint", { name: "designer_id", nullable: true })
  designerId: string | null;

  @Column("text", { name: "section_info", nullable: true })
  sectionInfo: string | null;

  @Column("bigint", { name: "department_id", nullable: true })
  departmentId: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
