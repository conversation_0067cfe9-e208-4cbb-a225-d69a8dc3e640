import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_security_id", ["projectSecurityDetectionId"], {})
@Index("idx_project_detection_type", ["projectId", "detectionType"], {})
@Entity(DaoUtil.parseDbName('camera.project_detection_detail'))
export class ProjectDetectionDetailEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "project_id", comment: "工地id" })
  projectId: string;

  @Column("bigint", {
    name: "project_security_detection_id",
    comment: "工地id",
  })
  projectSecurityDetectionId: string;

  @Column("int", {
    name: "detection_type",
    comment: "检测类型 1-行为检测 2-物体检测",
    default: () => "'0'",
  })
  detectionType: string;

  @Column("int", {
    name: "risk_type_detail",
    comment: "具体检测类型",
    default: () => "'0'",
  })
  riskTypeDetail: string;

  @Column("varchar", { name: "DELETE_FLAG", length: 1, default: () => "'N'" })
  deleteFlag: string;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
