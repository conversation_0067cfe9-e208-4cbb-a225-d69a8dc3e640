import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('camera.device_history'))
export class DeviceHistoryEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "device_history_id" })
  device_history_id: string;

  @Column("bigint", { name: "yqz_device_id", nullable: true })
  yqz_device_id: string | null;

  @Column("bigint", { name: "company_id", nullable: true })
  company_id: string | null;

  @Column("bigint", { name: "receive_manager", nullable: true })
  receiveManager: string | null;

  @Column("tinyint", {
    name: "action_type",
    nullable: true,
    comment: "1领用2归还",
    width: 1,
  })
  action_type: boolean | null;

  @Column("datetime", { name: "action_time", nullable: true })
  action_time: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  delete_flag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;
}
