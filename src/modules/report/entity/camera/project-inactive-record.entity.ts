import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('camera.project_inactive_record'))
export class ProjectInactiveRecordEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("varchar", { name: "from_date", nullable: true, length: 10 })
  fromDate: string | null;

  @Column("varchar", { name: "to_date", nullable: true, length: 10 })
  toDate: string | null;

  @Column("int", { name: "length", nullable: true })
  length: number | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;
}
