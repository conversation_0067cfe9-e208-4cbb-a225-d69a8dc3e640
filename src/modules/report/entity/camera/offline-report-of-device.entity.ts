import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Entity(DaoUtil.parseDbName('camera.offline_report_of_device'))
export class OfflineReportOfDeviceEntity {
    @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
    id: string;

    @Column("bigint", { name: "device_id", nullable: true })
    deviceId: string | null;

    @Column("bigint", { name: "device_offline_event_id", nullable: true })
    deviceOfflineEventId: string | null;

    @Column("varchar", {
        name: "offline_reason",
        nullable: true,
        comment: "离线原因",
    })
    offlineReason: string | null;

    @Column("datetime", { name: "start_time", nullable: true })
    startTime: Date | null;

    @Column("datetime", { name: "end_time", nullable: true })
    endTime: Date | null;

    @Column("bigint", { name: "manager_id", nullable: true })
    managerId: string | null;

    @Column("bigint", { name: "project_id", nullable: true })
    projectId: string | null;

    @Column("bigint", { name: "device_manager_id", nullable: true })
    deviceManagerId: string | null;

    @Column("datetime", { name: "report_time", nullable: true })
    reportTime: Date | null;

    @Column("datetime", {
        name: "createTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    createTime: Date | null;

    @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
    updateBy: string | null;

    @Column("datetime", {
        name: "updateTime",
        nullable: true,
        default: () => "CURRENT_TIMESTAMP",
    })
    updateTime: Date | null;

    @Column("enum", {
        name: "DELETE_FLAG",
        nullable: true,
        enum: ["Y", "N"],
        default: () => "'N'",
    })
    deleteFlag: "Y" | "N" | null;
}
