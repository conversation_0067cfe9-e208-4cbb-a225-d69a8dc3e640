import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('camera.device_info'))
export class DeviceInfoEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "yqz_device_id" })
  yqz_device_id: string;

  @Column("varchar", {
    name: "device_serial",
    comment: "设备序列号",
    length: 20,
  })
  device_serial: string;

  @Column("varchar", { name: "device_verify_code", length: 20 })
  device_verify_code: string;

  @Column("varchar", { name: "device_brand", length: 20 })
  device_brand: string;

  @Column("varchar", { name: "device_model", nullable: true, length: 20 })
  device_model: string | null;

  @Column("tinyint", {
    name: "is_embeded_mobile_card",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  is_embeded_mobile_card: boolean | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  delete_flag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", {
    name: "preset",
    nullable: true,
    comment: "预置点",
    length: 20,
  })
  preset: string | null;
}
