import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Index("company_project_index", ["companyId", "projectId"], {})
@Index("active_index", ["isActive"], {})
@Index("time_index", ["time"], {})
@Entity(DaoUtil.parseDbName('camera.project_active_history'))
export class ProjectActiveHistory {
  @PrimaryGeneratedColumn({ type: "bigint", name: "project_active_history_id" })
  projectActiveHistoryId: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("tinyint", {
    name: "is_active",
    nullable: true,
    comment: "1-施工 2-停工",
    width: 1,
  })
  isActive: boolean | null;

  @Column("datetime", { name: "time", nullable: true })
  time: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "has_human", nullable: true, length: 1 })
  hasHuman: string | null;
}
