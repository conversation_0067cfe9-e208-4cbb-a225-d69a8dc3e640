import { Dao<PERSON>til } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_type_event_id", ["type", "eventId"], {})
@Entity(DaoUtil.parseDbName('camera.api_result'))
export class ApiResultEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "event_id", comment: "移动侦测序列号" })
  eventId: string;

  @Column("varchar", {
    name: "type",
    comment:
      "检测类型 baidu_human百度人体检测,baidu_slippers百度拖鞋检测,baidu_tidiness百度整洁度,ez_detection萤石检测,ali_search_face阿里人脸识别",
    length: 20,
  })
  type: string;

  @Column("text", {
    name: "api_result",
    nullable: true,
    comment: "api调用结果",
  })
  apiResult: string | null;
}
