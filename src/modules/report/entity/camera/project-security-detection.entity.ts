import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_company_id_project_id", ["companyId", "projectId"], {})
@Entity(DaoUtil.parseDbName('camera.project_security_detection'))
export class ProjectSecurityDetectionEntity {
  @PrimaryGeneratedColumn({
    type: "bigint",
    name: "project_security_detection_id",
  })
  projectSecurityDetectionId: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("bigint", { name: "yqz_device_id", nullable: true })
  yqzDeviceId: string | null;

  @Column("varchar", {
    name: "risk_type",
    nullable: true,
    comment: "0-抽烟 1-明火 2-浓烟 3-正常 4-工服 5-拖鞋 6-非工作人员",
    length: 20,
  })
  riskType: string | null;

  @Column("varchar", { name: "oss_photo_url", nullable: true, length: 250 })
  ossPhotoUrl: string | null;

  @Column("bigint", { name: "motiondetect_event_id", nullable: true })
  motiondetectEventId: string | null;

  @Column("datetime", { name: "detection_time", nullable: true })
  detectionTime: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;
}
