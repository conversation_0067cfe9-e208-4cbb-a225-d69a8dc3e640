import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "company_id_project_id_idx",
  ["company_id", "project_id", "project_binding_time"],
  {}
)
@Index("yqz_device_id_idx", ["yqz_device_id"], {})
@Entity(DaoUtil.parseDbName('camera.device_status'))
export class DeviceStatusEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "device_status_id" })
  device_status_id: string;

  @Column("bigint", { name: "yqz_device_id", nullable: true })
  yqz_device_id: string | null;

  @Column("bigint", { name: "company_id", nullable: true })
  company_id: string | null;

  @Column("bigint", { name: "department_id", nullable: true })
  department_id: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  project_id: string | null;

  @Column("bigint", { name: "project_manager_id", nullable: true })
  project_manager_id: string | null;

  @Column("bigint", { name: "mobile_card_id", nullable: true })
  mobile_card_id: string | null;

  @Column("varchar", { name: "cloud_plt", nullable: true, length: 20 })
  cloud_plt: string | null;

  @Column("varchar", { name: "cover_image_url", nullable: true, length: 150 })
  cover_image_url: string | null;

  @Column("tinyint", {
    name: "is_online",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  is_online: boolean | null;

  @Column("tinyint", {
    name: "is_onshipping",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  is_onshipping: boolean | null;

  @Column("datetime", { name: "onoffline_time", nullable: true })
  onoffline_time: Date | null;

  @Column("datetime", { name: "start_time", nullable: true })
  start_time: Date | null;

  @Column("datetime", { name: "end_time", nullable: true })
  end_time: Date | null;

  @Column("datetime", {
    name: "recharge_last_time",
    nullable: true,
    comment: "最后充值时间",
  })
  recharge_last_time: Date | null;

  @Column("datetime", {
    name: "link_company_time",
    nullable: true,
    comment: "关联公司时间",
  })
  link_company_time: Date | null;

  @Column("datetime", {
    name: "manager_revice_time",
    nullable: true,
    comment: "冗余字段，管理员领取设备时间",
  })
  manager_revice_time: Date | null;

  @Column("tinyint", {
    name: "status",
    nullable: true,
    comment: "0-未激活，1-在途，2-正常，3-到期，4-维修，5-损坏，6-遗失",
    width: 1,
    default: () => "'0'",
  })
  status: boolean | null;

  @Column("varchar", { name: "video_password", nullable: true, length: 12 })
  video_password: string | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  delete_flag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("datetime", { name: "project_binding_time", nullable: true })
  project_binding_time: Date | null;

  @Column("varchar", {
    name: "device_first_motiondetect_event",
    nullable: true,
    length: 1,
  })
  device_first_motiondetect_event: string | null;
}
