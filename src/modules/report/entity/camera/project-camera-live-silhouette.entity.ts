import { <PERSON>o<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("project_id_idx", ["projectId"], {})
@Index(
  "company_id_project_id_idx",
  ["companyId", "projectId", "generateTime"],
  {}
)
@Entity(DaoUtil.parseDbName("camera.project_camera_live_silhouette"))
export class ProjectCameraLiveSilhouetteEntity {
  @PrimaryGeneratedColumn({
    type: "bigint",
    name: "project_camera_live_silhouette_id",
  })
  projectCameraLiveSilhouetteId: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "project_id" })
  projectId: string;

  @Column("bigint", { name: "yqz_device_id", nullable: true })
  yqzDeviceId: string | null;

  @Column("bigint", { name: "photo_id" })
  photoId: string;

  @Column("datetime", { name: "generate_time", nullable: true })
  generateTime: Date | null;

  @Column("varchar", {
    name: "material_type",
    nullable: true,
    comment: "素材类型 1-img 2-gif 3-video",
    length: 2,
  })
  materialType: string | null;

  @Column("varchar", { name: "device_remark", nullable: true, length: 20 })
  deviceRemark: string | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("text", {
    name: "event_ids",
    nullable: true,
    comment: "照片的ids motionEventIds",
  })
  eventIds: string | null;

  @Column("tinyint", {
    name: "generate_source",
    nullable: true,
    comment: "生成剪影的方式（手动2， 自动1）",
    width: 1,
  })
  generateSource: boolean | null;

  @Column("varchar", {
    name: "name",
    nullable: true,
    comment: "自定义合成的名称",
    length: 20,
  })
  name: string | null;

  @Column("bigint", {
    name: "manager_id",
    nullable: true,
    comment: "自定义合成人",
  })
  managerId: string | null;

  @Column("bigint", { name: "frame", nullable: true, comment: "桢数" })
  frame: string | null;
}
