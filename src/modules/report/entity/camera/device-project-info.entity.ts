import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { DaoUtil } from "@src/util/dao.util";

@Entity(DaoUtil.parseDbName('camera.device_project_info'))
export class DeviceProjectInfoEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", comment: "id" })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true, comment: "公司id" })
  companyId: string | null;

  @Column("bigint", { name: "project_id", nullable: true, comment: "工地id" })
  projectId: string | null;

  @Column("bigint", {
    name: "yqz_device_id",
    nullable: true,
    comment: "设备id",
  })
  yqzDeviceId: string | null;

  @Column("varchar", {
    name: "device_remark",
    nullable: true,
    comment: "设备名/位置",
    length: 30,
    default: () => "'摄像头'",
  })
  deviceRemark: string | null;

  @Column("datetime", {
    name: "project_binding_time",
    nullable: true,
    comment: "绑定工地时间",
  })
  projectBindingTime: Date | null;

  @Column("varchar", {
    name: "cover_image_url",
    nullable: true,
    comment: "工地设备封面",
    length: 150,
  })
  coverImageUrl: string | null;

  @Column("bigint", {
    name: "department_id",
    nullable: true,
    comment: "部门id",
  })
  departmentId: string | null;

  @Column("datetime", { name: "link_department_time", nullable: true })
  linkDepartmentTime: Date | null;

  @Column("varchar", {
    name: "DELETE_FLAG",
    nullable: true,
    comment: "删除标记",
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;
}
