import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index(
  "company_id_project_id_tidiness_type_idx",
  ["companyId", "projectId", "tidinessType"],
  {}
)
@Entity(DaoUtil.parseDbName('camera.project_tidiness_detection'))
export class ProjectTidinessDetectionEntity {
  @PrimaryGeneratedColumn({
    type: "bigint",
    name: "project_tidiness_detection_id",
  })
  projectTidinessDetectionId: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("tinyint", {
    name: "tidiness_type",
    nullable: true,
    comment: "1-好 2-中 3-差",
    width: 1,
  })
  tidinessType: number | null;

  @Column("varchar", { name: "oss_photo_url", nullable: true, length: 250 })
  ossPhotoUrl: string | null;

  @Column("bigint", { name: "motiondetect_event_id", nullable: true })
  motiondetectEventId: string | null;

  @Column("datetime", { name: "detection_time", nullable: true })
  detectionTime: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;
}
