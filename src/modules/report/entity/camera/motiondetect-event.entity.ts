import { Dao<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("idx_count", ["companyId", "createTime"], {})
@Index("device_id_project_id_idx", ["yqzDeviceId", "projectId"], {})
@Index("project_id_idx", ["projectId"], {})
@Index(
  "company_id_delete_flag_event_time_idx",
  ["companyId", "deleteFlag", "projectId", "eventTime", "isIncludePerson"],
  {}
)
@Index("delete_flag_event_time_idx", ["deleteFlag", "eventTime"], {})
@Entity(DaoUtil.parseDbName('camera.motiondetect_event'))
export class MotiondetectEventEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "yqz_device_id", nullable: true })
  yqzDeviceId: string | null;

  @Column("bigint", { name: "company_id" })
  companyId: string;

  @Column("bigint", { name: "project_id" })
  projectId: string;

  @Column("varchar", { name: "photo_url", nullable: true, length: 250 })
  photoUrl: string | null;

  @Column("varchar", { name: "oss_photo_url", nullable: true, length: 250 })
  ossPhotoUrl: string | null;

  @Column("tinyint", {
    name: "is_include_person",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  isIncludePerson: boolean | null;

  @Column("tinyint", {
    name: "event_type",
    nullable: true,
    comment: "1-移动侦测抓拍，2-手动截图",
    width: 1,
  })
  eventType: boolean | null;

  @Column("datetime", { name: "event_time", nullable: true })
  eventTime: Date | null;

  @Column("varchar", {
    name: "delete_flag",
    nullable: true,
    length: 1,
    default: () => "'N'",
  })
  deleteFlag: string | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", { name: "createTime", nullable: true })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", { name: "updateTime", nullable: true })
  updateTime: Date | null;

  @Column("tinyint", {
    name: "use_flag",
    nullable: true,
    width: 1,
    default: () => "'0'",
  })
  useFlag: boolean | null;

  @Column("bigint", { name: "stage_template_id", nullable: true })
  stageTemplateId: string | null;

  @Column("bigint", { name: "business_stage_id", nullable: true })
  businessStageId: string | null;

}
