import { Dao<PERSON><PERSON> } from "@yqz/nest";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity(DaoUtil.parseDbName('camera.device_face_detection_result'))
export class DeviceFaceDetectionResultEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "event_id", nullable: true })
  eventId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("varchar", { name: "faceId", nullable: true, length: 20 })
  faceId: string | null;

  @Column("varchar", { name: "score", nullable: true, length: 30 })
  score: string | null;

  @Column("varchar", { name: "confidence", nullable: true, length: 30 })
  confidence: string | null;

  @Column("varchar", { name: "qualitie_score", nullable: true, length: 30 })
  qualitieScore: string | null;

  @Column("varchar", { name: "oss_photo_url", nullable: true, length: 50 })
  ossPhotoUrl: string | null;

  @Column("varchar", { name: "front_face_photo", nullable: true, length: 50 })
  frontFacePhoto: string | null;

  @Column("varchar", { name: "nick_name", nullable: true, length: 20 })
  nickName: string | null;

  @Column("bigint", { name: "PERSON_ID", nullable: true })
  personId: string | null;

  @Column("datetime", { name: "event_time", nullable: true })
  eventTime: Date | null;

  @Column("varchar", { name: "SHORT_NAME", nullable: true, length: 20 })
  shortName: string | null;

  @Column("bigint", { name: "COMPANY_ID", nullable: true })
  companyId: string | null;

  @Column("varchar", { name: "PROJECT_NAME", nullable: true, length: 20 })
  projectName: string | null;

  @Column("bigint", { name: "PROJECT_ID", nullable: true })
  projectId: string | null;

  @Column("varchar", { name: "type", nullable: true, length: 2 })
  type: string | null;
}
