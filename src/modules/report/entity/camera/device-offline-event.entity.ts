import { DaoUtil } from "@src/util/dao.util";
import { Column, Entity, Index, PrimaryGeneratedColumn } from "typeorm";

@Index("device_error_index", ["yqzDeviceId", "companyId"], {})
@Entity(DaoUtil.parseDbName('camera.device_offline_event'))
export class DeviceOfflineEventEntity {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "company_id", nullable: true })
  companyId: string | null;

  @Column("bigint", { name: "yqz_device_id", nullable: true })
  yqzDeviceId: string | null;

  @Column("bigint", { name: "project_id", nullable: true })
  projectId: string | null;

  @Column("bigint", { name: "manager_id", nullable: true })
  managerId: string | null;

  @Column("datetime", { name: "from_time", nullable: true })
  fromTime: Date | null;

  @Column("datetime", { name: "to_time", nullable: true })
  toTime: Date | null;

  @Column("int", {
    name: "duration_in_min",
    nullable: true,
    comment: "离线时长(minutes)",
  })
  durationInMin: number | null;

  @Column("varchar", { name: "createBy", nullable: true, length: 30 })
  createBy: string | null;

  @Column("datetime", {
    name: "createTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  createTime: Date | null;

  @Column("varchar", { name: "updateBy", nullable: true, length: 30 })
  updateBy: string | null;

  @Column("datetime", {
    name: "updateTime",
    nullable: true,
    default: () => "CURRENT_TIMESTAMP",
  })
  updateTime: Date | null;

  @Column("enum", {
    name: "DELETE_FLAG",
    nullable: true,
    enum: ["Y", "N"],
    default: () => "'N'",
  })
  deleteFlag: "Y" | "N" | null;
}
