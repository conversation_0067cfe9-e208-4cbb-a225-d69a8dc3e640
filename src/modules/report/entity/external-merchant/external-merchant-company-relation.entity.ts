import { Column, Entity, PrimaryGeneratedColumn, Index } from "typeorm";
import { Base } from "../../../../entity/base.entity";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.external_merchant_company_relation'))
@Index("uk_merchant_company_is_delete", ["merchantId", "companyId", "is_delete"], { unique: true })
export class ExternalMerchantCompanyRelationEntity extends Base {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "merchant_id" })
  merchantId: string;

  @Column("bigint", { name: "company_id" })
  companyId: string;

  @Column({ type: "bigint", name: "is_delete", default: 0, comment: "0: not deleted, non-zero timestamp: deleted" })
  is_delete: number;
} 