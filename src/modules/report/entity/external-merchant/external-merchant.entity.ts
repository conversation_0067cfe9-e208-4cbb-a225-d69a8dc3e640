import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";
import { Base } from "../../../../entity/base.entity";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.external_merchant'))
export class ExternalMerchantEntity extends Base {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("varchar", { name: "name", length: 100 })
  name: string;

  @Column("varchar", { name: "account", length: 50, unique: true })
  account: string;

  @Column("varchar", { name: "password", length: 100 })
  password: string;

  @Column({ type: "bigint", name: "is_delete", default: 0, comment: "0: not deleted, non-zero timestamp: deleted" })
  is_delete: number;
} 