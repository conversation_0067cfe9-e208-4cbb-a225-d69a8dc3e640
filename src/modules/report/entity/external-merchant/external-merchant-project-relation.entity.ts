import { Column, Entity, PrimaryGeneratedColumn, Index } from "typeorm";
import { Base } from "../../../../entity/base.entity";
import { DaoUtil } from "@yqz/nest";

@Entity(DaoUtil.parseDbName('bgw.external_merchant_project_relation'))
@Index("uk_merchant_project_is_delete", ["merchantId", "projectId", "is_delete"], { unique: true })
export class ExternalMerchantProjectRelationEntity extends Base {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id" })
  id: string;

  @Column("bigint", { name: "merchant_id" })
  merchantId: string;

  @Column("bigint", { name: "project_id" })
  projectId: string;

  @Column("bigint", { name: "company_id", comment: "授权公司" })
  companyId: string;

  @Column("datetime", { name: "added_at", comment: "授权时间" })
  addedAt: Date;

  @Column({ type: "bigint", name: "is_delete", default: 0, comment: "0: not deleted, non-zero timestamp: deleted" })
  is_delete: number;
} 