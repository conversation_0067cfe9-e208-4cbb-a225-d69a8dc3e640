export const Default = {
    getConfiguration,
    getConfigKeys,
    getConfigList,
}

function getConfigList(config: Record<string, any>) {
    return Object.entries(config)
        .filter(([key, value]) => key && value)
        .map(([key, value]) => ({ module: key, ...value }));
}

function getConfiguration(config: Record<string, any>, type: string) {
    return config[type] || {};
}

function getConfigKeys(config: Record<string, any>) {
    return Object.keys(config);
}