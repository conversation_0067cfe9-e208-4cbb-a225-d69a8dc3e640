import * as _ from "lodash"
import * as fs from "fs"
import * as uuid from "uuid"
import * as iconv from 'iconv-lite'
import { parse as csvParse } from 'csv-parse/sync'
import * as ExcelJS from 'exceljs';

export const CsvUtil = {
  convert2Csv,
  decodeCsvBufferAutoEncoding,
  parseAndValidateCsvContent,
  parseXlsxToCSV
}

function convert2Csv(objectList: any[]) {
  if (!objectList || objectList.length === 0) return '';
  const keys = _.keys(objectList[0]);
  const fields = keys.map(res => '\uFEFF' + res);
  const lines = [];
  lines.push(fields.join(","));
  objectList.forEach(obj => {
    lines.push(keys.map(f => JSON.stringify(obj[f])).join(","));
  })
  return lines.join("\n");
}

/**
 * 自动识别并解码CSV文件Buffer（支持UTF-8和GBK）
 * @param buffer 文件内容Buffer
 * @returns 解码后的字符串
 */
export function decodeCsvBufferAutoEncoding(buffer: Buffer): string {
    const utf8Content = iconv.decode(buffer, 'UTF-8');
    const hasInvalidChars = /\uFFFD/.test(utf8Content);
    if (!hasInvalidChars) {
        return utf8Content;
    } else {
        return iconv.decode(buffer, 'GBK');
    }
}

/**
 * 解析CSV内容并校验表头，返回映射后的数据数组
 * @param csvContent CSV文件内容字符串
 * @param headerMap 表头映射（中文->英文）
 * @returns 映射后的数据数组
 * @throws 缺少必要列或无有效数据时抛出异常
 */
export function parseAndValidateCsvContent(csvContent: string, headerMap: Record<string, string>): any[] {
    const requiredColumns = Object.keys(headerMap);
    const records = csvParse(csvContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true
    });
    if (records.length === 0) {
        throw new Error('没有有效的数据记录');
    }
    const csvColumns = Object.keys(records[0]);
    const missingColumns = requiredColumns.filter(col => !csvColumns.includes(col));
    if (missingColumns.length > 0) {
        throw new Error(`CSV文件缺少必要的列: ${missingColumns.join(', ')}`);
    }
    return records.map(record => {
        const mapped: any = {};
        for (const [zh, en] of Object.entries(headerMap)) {
            mapped[en] = record[zh];
        }
        return mapped;
    });
}

/**
     * 将XLSX文件解析为CSV格式字符串
     * @param buffer XLSX文件Buffer
     * @returns 解析后的CSV格式字符串
     */
export async function parseXlsxToCSV(buffer: Buffer): Promise<string> {
    try {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(buffer);
        
        const worksheet = workbook.worksheets[0];
        if (!worksheet) {
            throw new Error('XLSX文件不包含任何工作表');
        }
        
        const rows = [];
        
        // 读取表头
        const headerRow = worksheet.getRow(1);
        const headers = [];
        headerRow.eachCell({ includeEmpty: false }, (cell) => {
            headers.push(cell.value?.toString() || '');
        });
        rows.push(headers);
        
        // 读取数据行
        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            if (rowNumber > 1) { // 跳过表头
                const rowData = [];
                row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                    rowData.push(cell.value?.toString() || '');
                });
                rows.push(rowData);
            }
        });
        
        // 转换为CSV格式
        return rows.map(row => 
            row.map(cell => 
                typeof cell === 'string' && (cell.includes(',') || cell.includes('"')) 
                    ? `"${cell.replace(/"/g, '""')}"` 
                    : cell
            ).join(',')
        ).join('\n');
    } catch (error) {
        throw new Error(`解析XLSX文件失败: ${error.message}`);
    }
}