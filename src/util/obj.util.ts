import * as _ from "lodash";

export const ObjUtil = {
    mapRequestParams,
    mergeByFields,
    createMappings
}

function mapRequestParams(
    request: Record<string, any>,
    paramMapping?: Record<string, string>,
    targetFields?: string[]
): Record<string, any> {
    let mappedRequest: Record<string, any> = request;

    // 选择指定字段
    if (targetFields) {
        mappedRequest = Object.fromEntries(Object.entries(mappedRequest).filter(([key]) => targetFields.includes(key)));
    }

    if (!paramMapping) {
        return mappedRequest;
    }

    // 字段映射
    for (const [targetKey, sourceKey] of Object.entries(paramMapping)) {
        if (sourceKey in mappedRequest) {
            mappedRequest[targetKey] = request[sourceKey];
        }
    }

    return mappedRequest;
}


function mergeByFields(personList, roleField, fieldsToSum = []) {
    if (_.isEmpty(personList) || !roleField) return []
    return _.reduce(personList, (acc, current) => {
        // 查找是否已有相同的 role
        const existing = acc.find(item => item[roleField] === current[roleField]);

        if (existing) {
            // 如果没有传入字段列表，则默认为对除了 roleField 外的所有字段进行累加
            if (fieldsToSum.length === 0) {
                Object.keys(current).forEach(field => {
                    if (field !== roleField && existing[field] instanceof String) {
                        existing[field] = (Number(existing[field]) || 0) + (Number(current[field]) || 0);
                    }
                });
            } else {
                // 否则根据 fieldsToSum 列表进行累加
                fieldsToSum.forEach(field => {
                    if (field !== roleField && existing[field] instanceof String)
                        existing[field] = (Number(existing[field]) || 0) + (Number(current[field]) || 0);
                });
            }
        } else {
            // 如果没有相同角色，则添加到结果数组中
            acc.push({ ...current });
        }

        return acc;
    }, []);
}

// 根据枚举类创建一个双向映射
function createMappings(operatorEnum: any) {
    const keyToValue = Object.keys(operatorEnum).reduce((acc, key) => {
        const value = operatorEnum[key];
        acc[value] = key;
        return acc;
    }, {} as Record<string, string>);

    const valueToKey = (value: string): string | undefined => {
        return keyToValue[value];
    };

    return valueToKey;
}