import { TagNode } from "@src/modules/report/mongodb/camera/tags_hirachy.collection"
import * as _ from "lodash"

export class TagUril {
    static addTag(tags: TagNode[], tag: string, parent?: string) {
        if (!parent) {
            tags.push({ tag })
            return true
        }
        for (const node of tags) {
            if (node.tag === parent) {
                if (!node.children) {
                    node.children = []
                }
                node.children.push({ tag })
                return true
            } else {
                if (node.children?.length > 0) {
                    const res = TagUril.addTag(node.children, tag, parent)
                    if (res) return res
                }
            }

        }
        return false
    }

    static getFlatTagList(tags: TagNode[]) {
        const tagList: string[] = []

        const _getList = (_tags: TagNode[]) => {
            for (const node of _tags || []) {
                if (!node) continue
                tagList.push(node.tag)
                if (node.children?.length > 0) _getList(node.children)
            }
        }

        _getList(tags)
        return _.uniq(tagList)
    }

    static getSubTagList(tags: TagNode[], tag: string) {

        const node = TagUril.findNode(tags, tag)
        if (!node) return [tag]
        const subTags = this.getFlatTagList(node.children)
        return [tag, ...subTags]
    }




    static findNode(tags: TagNode[], tag: string): TagNode {
        if (!tags) return
        for (const node of tags) {
            if (node.tag === tag) return node;
            const res = TagUril.findNode(node.children, tag)
            if (res) return res;
        }
    }

    static findParent(nodeList: TagNode[], tag: string): TagNode {

        const _findP = (_node: TagNode, parentNode?: TagNode) => {
            if (_node.tag === tag) return parentNode
            if (_node.children?.length > 0) {
                for (const child of _node.children) {
                    const res = _findP(child, _node)
                    if (res) return res
                }
            }
        }
        return _findP({ children: nodeList, tag: 'root' })
    }


}