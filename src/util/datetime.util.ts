import { format, parse, sub, addDays, isBefore, isValid } from "date-fns";
import * as _ from "lodash";


export enum Formats {
  Full = 'yyyy-MM-dd HH:mm:ss',
  yyyyMMdd = 'yyyyMMdd'
}


export function formatDate(date: Date, pattern?: string) {
  return format(date, pattern ? pattern : Formats.Full)
}

export function parseDate(date: string, pattern?: string) {
  return parse(date, pattern ? pattern : Formats.Full, new Date())
}

export function formatMinutesDuration(duration: number) {
  const days = Math.floor(duration / 60 / 24);
  const hours = Math.floor((duration % (24 * 60)) / 60);
  const minutes = duration % 60;
  const res: string[] = []
  if (days != 0) res.push(`${days}天`)
  if (hours != 0 || (hours == 0 && days != 0)) res.push(`${hours}时`)
  if (minutes != 0 || (minutes == 0 && (days != 0 || hours != 0))) res.push(`${minutes}分`)
  return res.join('')
}

export function formatMillisecondsDuration(  
  duration: number,  
  includeDays: boolean = true,  
  includeHours: boolean = true,  
  includeMinutes: boolean = true,  
  includeSeconds: boolean = true,  
) {  
  const totalSeconds = Math.floor(duration / 1000);  
  const seconds = totalSeconds % 60; // Remaining seconds after days, hours, and minutes  
  const totalMinutes = Math.floor(totalSeconds / 60);  
  const minutes = totalMinutes % 60; // Remaining minutes  
  const totalHours = Math.floor(totalMinutes / 60);  
  const hours = totalHours % 24; // Remaining hours  
  const days = Math.floor(totalHours / 24); // Total days  

  const res: string[] = [];  

  if (includeDays && days > 0) {  
    res.push(`${days}天`);  
  }  
  if (includeHours && (hours > 0 || days > 0)) {  
    res.push(`${hours}时`);  
  }  
  if (includeMinutes && (minutes > 0 || hours > 0 || days > 0)) {  
    res.push(`${minutes}分`);  
  }  
  if (includeSeconds && (seconds > 0 || minutes > 0 || hours > 0 || days > 0)) {  
    res.push(`${seconds}秒`);  
  }   

  return res?.join('');  
}

export function formatDateToYYYYMMDD(date: Date) {
  const year = date.getUTCFullYear();  
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1  
  const day = String(date.getUTCDate()).padStart(2, '0');  

  return `${year}${month}${day}`;  
}

export function formatSecondsDuration(duration: number) {
  if (!(duration > 0)) return '';
  const days = Math.floor(duration / 60 / 60 / 24);
  const hours = Math.floor(duration / 60 / 60 % 24);
  const minutes = Math.floor(duration / 60 % 60);
  const seconds = Math.floor(duration % 60);
  const res: string[] = [];
  if (days != 0) res.push(`${days}天`);
  if (hours != 0 || (hours == 0 && days != 0)) res.push(`${hours}时`);
  if (minutes != 0 || (minutes == 0 && (days != 0 || hours != 0))) res.push(`${minutes}分`);
  if (seconds != 0 || (seconds == 0 && (days != 0 || hours != 0 || minutes != 0))) res.push(`${minutes}秒`);
  return res.join('');
}

export function getWeek(date: Date) {
  const weekNum = date.getDay();
  let week = "";
  switch (weekNum) {
    case 0:
      week = "星期天";
      break;
    case 1:
      week = "星期一";
      break;
    case 2:
      week = "星期二";
      break;
    case 3:
      week = "星期三";
      break;
    case 4:
      week = "星期四";
      break;
    case 5:
      week = "星期五";
      break;
    case 6:
      week = "星期六";
      break;
  }
  return week;
};

export function intervalDay(date: Date, day: number) {
  let time = date.getTime() + day * 24 * 60 * 60 * 1000;
  return new Date(time);
}

export function dateDifference(d1: Date, d2: Date) {
  let nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
  let nh = 1000 * 60 * 60;// 一小时的毫秒数
  let nm = 1000 * 60;// 一分钟的毫秒数
  let ns = 1000;// 一秒钟的毫秒数
  let result = "";
  let dateDiffer = d1.getTime() - d2.getTime()
  //获取输入时间到当前时间的时间差，返回x天x小时x分x秒
  let day = Math.floor(dateDiffer / nd);// 计算差多少天
  let hour = (Math.floor(dateDiffer % nd / nh) + day * 24) - day * 24;// 计算差多少小时
  let min = (Math.floor(dateDiffer % nd % nh / nm) + day * 24 * 60) - day * 24 * 60;// 计算差多少分钟
  let sec = Math.floor(dateDiffer % nd % nh % nm / ns);// 计算差多少秒
  // 输出结果
  if (day > 0) {
    result = result + day + "天";
  }
  if (hour > 0) {
    result = result + hour + "小时";
  }
  if (min > 0) {
    result = result + min + "分";
  }
  if (sec > 0) {
    result = result + sec + "秒";
  }
  return result
}

export function dateDiff(d1: Date, d2: Date, type: string) {
  let nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
  let nh = 1000 * 60 * 60;// 一小时的毫秒数
  let nm = 1000 * 60;// 一分钟的毫秒数
  let ns = 1000;// 一秒钟的毫秒数
  let result = 0;
  let dateDiffer = d1.getTime() - d2.getTime()
  let day = Math.floor(dateDiffer / nd);// 计算差多少天
  let hour = Math.floor(dateDiffer / nh);// 计算差多少小时
  let min = Math.floor(dateDiffer / nm);// 计算差多少分钟
  let sec = Math.floor(dateDiffer / ns);// 计算差多少秒
  // 输出结果
  if (type == 'day') {
    result = day;
  }
  if (type == 'hour') {
    result = hour;
  }
  if (type == 'min') {
    result = min;
  }
  if (type == 'sec') {
    result = sec;
  }
  return result
}

export function getPreviousDay(date: Date, day?: number) {
  const days = day || 1
  // 当前日期前一天
  const previousDate = sub(date, { days });

  return previousDate;
}

// 生成指定范围内的日期列表（YYYYMMDD 格式），时间范围左闭右开
export function generateDateList({ startTime, endTime }: { startTime: string, endTime: string }): string[] {
  // 如果没有提供时间，则返回空列表
  if (!startTime || !endTime) return [];
  // 将 startTime 和 endTime 转换为标准日期对象
  const startDate = parseDateString(startTime);
  const endDate = parseDateString(endTime);
  // 初始化日期列表
  const dateList: Date[] = [];
  let currentDate = startDate;
  // 使用 while 循环生成日期范围
  while (!isBefore(currentDate, startDate) && isBefore(currentDate, endDate)) {
    dateList.push(currentDate);
    currentDate = addDays(currentDate, 1); // 每次加一天
  }
  // 格式化为 YYYYMMDD，并返回
  return _.map(dateList, date => format(date, 'yyyyMMdd'));
}

/**
 * 根据日期格式判断是 `yyyy-MM-dd`\`yyyyMMdd`\yyyy-MM-dd HH:mm:ss，并返回标准日期对象
 */
export function parseDateString(dateString: string): Date {
  if (isDateInFormat(dateString, 'yyyy-MM-dd')) {
    return parse(dateString, 'yyyy-MM-dd', new Date());
  } else if (isDateInFormat(dateString, 'yyyyMMdd')) {
    return parse(dateString, 'yyyyMMdd', new Date());
  } else if (isDateInFormat(dateString, 'yyyy-MM-dd HH:mm:ss')) {
    return parse(dateString, 'yyyy-MM-dd HH:mm:ss', new Date());
  }
}

/**
 * 检查日期字符串是否符合特定格式
 * @param dateString - 输入的日期字符串
 * @param formatString - 指定的日期格式
 * @returns 是否符合格式
 */
export function isDateInFormat(dateString: string, formatString: string): boolean {
  const parsedDate = parse(dateString, formatString, new Date());
  // 检查解析后的日期是否有效，并且解析后的日期和输入长度一致
  return isValid(parsedDate) && dateString === formatDate(parsedDate, formatString);
}

export function durationByMinute(minute) {
  if (!minute || isNaN(minute)) return "";
  const min = parseInt(minute, 10);
  if (min <= 0) return `${min}分`;
  const days = Math.floor(min / (24 * 60)); // 计算天数
  const hours = Math.floor((min % (24 * 60)) / 60); // 计算小时
  const minutes = min % 60; // 剩余分钟
  let result = "";
  if (days > 0) result += `${days}天`;
  if (hours > 0) result += `${hours}小时`;
  if (minutes > 0) result += `${minutes}分`;
  return result;
}