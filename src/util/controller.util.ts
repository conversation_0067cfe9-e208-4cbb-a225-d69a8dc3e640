import type {Response } from 'express';
import {StreamableFile } from "@nestjs/common";
import * as fs from "fs"


export const ControllerUtil = {
  toDataOrFile
}

function toDataOrFile(response:Response, res:any, fileName?:string) {
  if(typeof res ==='string') {
    response.set({
      'Content-Type': 'application/octet-stream',
      'Content-Disposition': `attachment; filename="${fileName||new Date().getTime()}.csv"`,
    });
    response.send(res)
  }
  else {
    response.send(res)
  }
}