import * as DateFns from "date-fns";
import * as _ from "lodash";

export const ChartDateUtil = {
    getTimeFrame,
    getTimeFrameList
}

/**
  * 获取图表时间范围(范围时间,原则左闭右开)
  * @param from 开始时间
  * @param to 结束时间
  * @param format 返回时间格式 默认yyyy-MM-dd
  */
function getTimeFrame({ from, to, format = "yyyy-MM-dd" }: { from: string, to: string, format?: string }): { startTime: string, endTime: string } {
    return {
        startTime: DateFns.format(new Date(from), format),
        endTime: DateFns.format(DateFns.addDays(new Date(to), 1), format)
    };
}

/**
 * 根据传入时间、周期 获取周期图表时间范围list(即每个周期的时间范围) => 多个范围时间(原则左闭右闭)
 * @param from 开始时间（最后一个周期的开始时间）
 * @param to 结束时间（最后一个周期的结束时间）
 * @param dataType 日期类型
 * @param cycle 周期 默认7个周期
 * @param format 返回时间格式 默认yyyy-MM-dd
 * @returns [{ startTime: string, endTime: string }] 范围时间list(原则左闭右闭),因为前端传的也是左闭右闭,所以这边与前端保持一致,在查询数据的时候,再把左闭右闭改成左闭右开)
 */
function getTimeFrameList({ from, to, dataType, cycle = 7, format = "yyyy-MM-dd" }: { from: string, to: string, dataType: 'day' | 'week' | 'month' | 'year', cycle?: number, format?: string }): { startTime: string, endTime: string }[] {
    const result: { startTime: string, endTime: string }[] = [];
    let fromDate = new Date(from);
    let toDate = new Date(to);
    /** 根据 dataType 获取时间 x次 范围list 每次push上一个时间段 */
    for (let i = 1; i <= cycle; i++) {
        let startTime = "";
        let endTime = "";
        switch (dataType) {
            case 'day':
                startTime = DateFns.format(fromDate, format);
                endTime = DateFns.format(toDate, format);
                const diffDays = DateFns.differenceInCalendarDays(toDate, fromDate) + 1;
                fromDate = DateFns.subDays(fromDate, diffDays);
                toDate = DateFns.subDays(toDate, diffDays);
                break;
            case 'week'://这边周一为一周的开始,所以这里逻辑不太一样
                startTime = DateFns.format(DateFns.addDays(DateFns.startOfWeek(fromDate), 1), format);
                endTime = DateFns.format(DateFns.startOfWeek(toDate), format);
                const diffWeeks = DateFns.differenceInCalendarWeeks(toDate, fromDate);
                fromDate = DateFns.subWeeks(fromDate, diffWeeks);
                toDate = DateFns.subWeeks(toDate, diffWeeks);
                break;
            case 'month':
                startTime = DateFns.format(DateFns.startOfMonth(fromDate), format);
                endTime = DateFns.format(DateFns.endOfMonth(toDate), format);
                const diffMonths = DateFns.differenceInCalendarMonths(toDate, fromDate) + 1;
                fromDate = DateFns.subMonths(fromDate, diffMonths);
                toDate = DateFns.subMonths(toDate, diffMonths);
                break;
            case 'year':
                startTime = DateFns.format(DateFns.startOfYear(fromDate), format);
                endTime = DateFns.format(DateFns.endOfYear(toDate), format);
                const diffYears = DateFns.differenceInCalendarYears(toDate, fromDate) + 1;
                fromDate = DateFns.subYears(fromDate, diffYears);
                toDate = DateFns.subYears(toDate, diffYears);
                break;
        }
        result.push({ startTime, endTime });
    }
    return _.reverse(result);
}