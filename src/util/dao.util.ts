import { DaoUtil as Yqz<PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest"
import { generateDateList } from "./datetime.util";
import * as _ from "lodash";

export const DaoUtil = {
    ...YqzDaoUtil,
    getDbName,
    etlBatchQueryAndMerge
}


function getDbName(tableName: string, separator?: string) {
    separator = separator || '.';
    const { database, name } = DaoUtil.parseDbName(tableName)
    return "`" + database?.trim() + "`" + separator + name
}

/**
 * 切片数据通用分批查询并聚合结果
 * @param startTime 开始日期
 * @param endTime 结束日期
 * @param groupSize 每组日期数量
 * @param queryFn (dateChunk) => Promise<结果数组>
 * @param mergeFn (所有批次结果) => 聚合后的结果
 */
async function etlBatchQueryAndMerge<T, R>(
    startTime: string,
    endTime: string,
    groupSize: number,
    queryFn: (dateChunk: string[]) => Promise<T[]>,
    mergeFn: (allResults: T[][]) => R
): Promise<R> {
    if (!startTime || !endTime || !groupSize || !queryFn || !mergeFn) throw new YqzException('必传参数不能为空');
    const dataList = generateDateList({ startTime, endTime });
    //每groupSize个日期一次查询
    const dateChunks = _.chunk(dataList, groupSize);
    // 1. 创建所有查询的 promise
    const promises = dateChunks.map(chunk => queryFn(chunk));
    // 2. 每批10个查询并行执行
    const promiseChunks = _.chunk(promises, 10);
    let allResults: T[][] = [];
    // 3. 依次执行每个批次的 promise
    for (const promiseChunk of promiseChunks) {
        //同一批的10个 promise 会并行执行
        const batchResults = await Promise.all(promiseChunk);
        allResults = allResults.concat(batchResults);
    }
    // 4. 合并所有批次的结果
    return mergeFn(allResults);
}