import { ApiProperty } from "@nestjs/swagger";

class SortItem<T> {
  @ApiProperty({type:String})
  field: keyof T;
  @ApiProperty({enum:['ascending', 'descending']})
  order: 'ascending' | 'descending'
}

export class BaseReportReq<T> {
  @ApiProperty()
  pageSize?:number;

  @ApiProperty()
  pageNo?:number;

  @ApiProperty({type:[SortItem]})
  sortBy?:SortItem<T>[]

  @ApiProperty({description:'默认json', enum:["csv", "json"]})
  format?: "csv" | 'json'
}

export class BaseResp<T> {
  total: number;
  items: T[];
}