import { TypeOrmModule } from "@nestjs/typeorm";
import { Module } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { LoggerModule } from "./modules/logger/my-logger.module";
import { ScheduleModule } from "@nestjs/schedule";
import { ReportModule } from "./modules/report/report.module";
import { ExternalMerchantModule } from "./modules/external-merchant/external-merchant.module";
import { MyMongoDb } from "./modules/report/mongodb/mongodb.connection";

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forRoot({
      type: process.env.TYPEORM_CONNECTION as any,
      database: process.env.TYPEORM_DATABASE,
      charset: process.env.TYPEORM_CHARSET,
      entities: [process.env.TYPEORM_ENTITIES],
      host: process.env.TYPEORM_HOST,
      logging: process.env.TYPEORM_LOGGING as any,
      password: process.env.TYPEORM_PASSWORD,
      port: Number(process.env.TYPEORM_PORT),
      username: process.env.TYPEORM_USERNAME,
      pool: {
        max: Number(process.env.TYPEORM_CONNECTION_LIMIT || 40),//最大连接数
        min: Number(process.env.TYPEORM_CONNECTION_MIN || 8),//最小连接数（保持20%的最小连接数）
        maxWaitingRequests: Number(process.env.TYPEORM_CONNECTION_MAX_WAITING_REQUESTS || 80),//最大等待请求数（等待队列是最大连接数的2倍）
        requestTimeout: Number(process.env.TYPEORM_CONNECTION_REQUEST_TIMEOUT || 15000),//请求超时时间（毫秒）
        checkInterval: Number(process.env.TYPEORM_CONNECTION_CHECK_INTERVAL || 30000),//连接检查间隔（毫秒）
        idleTimeout: Number(process.env.TYPEORM_CONNECTION_IDLE_TIMEOUT || 60000),//空闲连接超时时间（毫秒）
      }
    }),
    LoggerModule,
    ReportModule,
    ExternalMerchantModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {
    //使用的是原生 MongoDB 驱动
    MyMongoDb.clientConnect();
  }
}
