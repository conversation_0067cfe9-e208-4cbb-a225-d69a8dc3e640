import { Controller, Get, Query } from "@nestjs/common";
import { AppService } from "./app.service";
import * as fs from "fs"

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  default(): string {
    return `${process.env.YQZ_SERVICE_NAME} running in ${process.env.YQZ_TARGET_ENV} mode on ${process.env.HOSTNAME}`;
  }

  @Get('health')
  health(): string {
    return 'ok';
  }

  @Get('swagger')
  getSwagger(@Query() query:{name:string}) {
    return JSON.parse(fs.readFileSync(`swagger/${query.name||'default'}.json`).toString())
  }  
}
