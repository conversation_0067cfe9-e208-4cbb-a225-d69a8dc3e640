import { Get, Injectable, Query } from "@nestjs/common";
import * as fs from "fs";
@Injectable()
export class AppService {
  getHello(): string {
    return `Welcome to ${process.env.YQZ_SERVICE_NAME} ${process.env.YQZ_TARGET_ENV}!\n`;
  }

  @Get('swagger')
  getSwagger(@Query() query:{name:string}) {
    return JSON.parse(fs.readFileSync(`swagger/${query.name||'default'}.json`).toString())
  }    
}
